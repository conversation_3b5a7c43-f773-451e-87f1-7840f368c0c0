from azure.search.documents  import SearchClient
from azure.identity import DefaultAzureCredential
from azure.search.documents.models import VectorizedQuery, QueryType
from openai import AzureOpenAI
import requests
import logging
import json
import os
from dotenv import load_dotenv
load_dotenv()

class AzureAISearchClient:
    def __init__(self):
        self.credential = DefaultAzureCredential()
        self.token = self.credential.get_token("https://cognitiveservices.azure.com/.default").token

        self.AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
        self.DEPLOYMENT_NAME = os.getenv("AZURE_EMBEDDING")
        self.API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION")

    def get_embeddings(self, input_text: str, token: str):
        logging.info("Calling Azure OpenAI embedding model using direct REST API and Azure AD token")

        AZURE_OPENAI_ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT")
        DEPLOYMENT_NAME = os.getenv("AZURE_EMBEDDING")
        API_VERSION = os.getenv("AZURE_OPENAI_API_VERSION") 

        url = f"{AZURE_OPENAI_ENDPOINT}/openai/deployments/{DEPLOYMENT_NAME}/embeddings?api-version={API_VERSION}"

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        json_payload = {
            "input": input_text
        }

        try:
            response = requests.post(url, headers=headers, json=json_payload)
            response.raise_for_status()

            result = response.json()
            embedding = result["data"][0]["embedding"]
            return embedding

        except Exception as e:
            logging.error("Error while getting embeddings", exc_info=True)
            return f"Exception occurred: {str(e)}"


    def semantic_search_ras(self, query: str):
        ##AI Search Endpoint address
        search_client = SearchClient(
            endpoint="https://sdlc-test-ai-search.search.windows.net",
            credential=self.credential,
            index_name="ras-helper-index-v1"
        )

        try:
            embedding = self.get_embeddings(query, self.token)
            vector_query = [VectorizedQuery(vector=embedding, k_nearest_neighbors=50, fields="embedding")]
            results = search_client.search(
                search_text = "",
                vector_queries = vector_query,
                search_fields = ["description","acceptance_criteria", "summary"],
                query_type = "semantic",
                semantic_configuration_name = "default",
                top = 3
            )
            result_list = list(results)

            documents = []
            for result in result_list:
                if result['@search.score'] >= 0.85:
                    description = result.get('description', '')
                    summary = result.get('summary', '')
                    acceptance_criteria = result.get('acceptance_criteria', '')
                    #score = result.get('@search.score', '')
                    documents.append(
                        {
                            "description":description,
                            "summary": summary,
                            "acceptance_criteria": acceptance_criteria
                            #"score": score
                        }
                    )
            print("Documents: ", documents)
            
            assistant_input = {
            "user_query": query,
            "retrieved_context": documents
            }

            print(json.dumps(assistant_input, indent=2))
            
            return assistant_input
        except Exception as e:
            msg = f"Error performing semantic search: {e}"
            return msg

    def semantic_search_tcg_manual(self, query: str):

       

        search_client = SearchClient(
            endpoint="https://sdlc-test-ai-search.search.windows.net",
            credential=self.credential,
            index_name="tcg-manual-index-v1"
        )

        embedding = self.get_embeddings(query, self.token)
        vector_query = [VectorizedQuery(vector=embedding, k_nearest_neighbors=50, fields="embedding")]

        results = search_client.search(
            search_text="",
            vector_queries=vector_query,
            search_fields=["description", "manual_test_steps", "summary"],
            query_type="semantic",
            semantic_configuration_name="default",
            top=1
        )
        results = list(results)
        documents = []
        for result in results:
            if result['@search.score'] >= 0.85:
                description = result.get('description', '')
                summary = result.get('summary', '')
                priority = result.get('priority', '')
                manual_test_steps = result.get('manual_test_steps', '')
                documents.append(
                    {
                        "description": description,
                        "summary": summary,
                        "priority": priority,
                        "manual_test_steps": manual_test_steps
                    }
                )
        return documents

    def semantic_search_tcg_cucumber(self, query: str):

       

        search_client = SearchClient(
        ##AI Search endpoint address##
            endpoint="https://sdlc-test-ai-search.search.windows.net",  
            credential=self.credential,
            index_name="tcg-cucumber-index-v1"
        )

        embedding = self.get_embeddings(query, self.token)
        vector_query = [VectorizedQuery(vector=embedding, k_nearest_neighbors=50, fields="embedding")]

        results = search_client.search(
            search_text="",
            vector_queries=vector_query,
            search_fields=["description", "cucumber_scenario", "summary"],
            query_type="semantic",
            semantic_configuration_name="default",
            top=1
        )
        results = list(results)
        documents = []
        for result in results:
            if result['@search.score'] >= 0.85:
                description = result.get('description', '')
                summary = result.get('summary', '')
                priority = result.get('priority', '')
                cucumber_scenario = result.get('cucumber_scenario', '')
                documents.append(
                    {
                        "description": description,
                        "summary": summary,
                        "priority": priority,
                        "cucumber_scenario": cucumber_scenario
                    }
                )
        return documents

