import logging

from common.jira.jira_client import JiraClient


class JiraTestCaseProcessor:

    def __init__(self, auth_data, base_url):
        """
        Initializes the processor with authentication data and base URL.

        :param auth_data: Either session cookies (dict) or OAuth data (dict with oauth_token and cloud_id)
        :param base_url: Base URL of the Jira instance
        """
        self.auth_data = auth_data
        self.base_url = base_url

        # Determine if using OAuth or session cookies
        if isinstance(auth_data, dict) and 'oauth_token' in auth_data:
            self.use_oauth = True
            self.oauth_token = auth_data['oauth_token']
            self.cloud_id = auth_data['cloud_id']
        else:
            self.use_oauth = False
            self.cookies = auth_data

    def processing_data_to_jira_manual(self, test_issues, proj_key, parent_key):
        """
        Processes a list of test case data and creates manual test cases in JIRA,
        including their steps, and links each new issue to a specified parent issue.

        Args:
            test_issues (list): A list of dictionaries, each representing a test case.
                         Each dictionary should contain 'Summary', 'Description', 'Priority', and 'ManualSteps'.
            proj_key (str): The JIRA project key where the issues will be created.
            parent_key (str): The key of the parent JIRA issue to which new issues will be linked.

        Returns:

        """
        response_msg = ''
        status_codes = []
        for i in test_issues:
            # Extract test case fields
            summary = i['Summary']
            desc = i['Description'] + "\n\n *Note: This is AI generated content*"
            priority = i['Priority']
            manual_steps = i['ManualSteps']

            # Create a new manual test case issue in JIRA
            if self.use_oauth:
                jira_client = JiraClient(self.base_url, oauth_token=self.oauth_token, cloud_id=self.cloud_id)
            else:
                jira_client = JiraClient(self.base_url, None, None, cookies=self.cookies)

            new_issue = jira_client.create_issue(proj_key, summary, desc, priority, "Manual")
            status_codes.append(new_issue.status_code)
            new_issue = new_issue.json()
            new_key = new_issue['key']
            insert_msg = f'{new_key} Test Issue Created.'
            response_msg += insert_msg + '\n'
            logging.info(insert_msg)

            # Add each manual test step to the newly created issue
            for step in manual_steps:
                action = step['Action']
                data = step['Data']
                exp_result = step['ExpectedResult'] if 'ExpectedResult' in step else step['Expected Result']

                # Add the test step to the issue
                step_added = jira_client.add_test_step_manual(new_key, action, data, exp_result)
                status_codes.append(step_added.status_code)
                if step_added.status_code == 200:
                    steps_msg = f'{new_key} Manual test steps added.'
                    response_msg += steps_msg + '\n'
                    logging.info(steps_msg)

            # Link the new issue to the parent issue
            response = jira_client.link_issues(parent_key, new_key)
            status_codes.append(response.status_code)
            link_msg = f'{new_key} test issue linked to {parent_key}'
            response_msg += link_msg + '\n'
            logging.info(link_msg)

        # TO DO Proper response return
        status_list = [i for i in status_codes if i > 299]
        return status_list[0] if status_list else 200, response_msg

    def processing_data_to_jira_cucumber(self, test_issues, proj_key, parent_key):
        """
        Processes a list of test case data and creates corresponding 'Cucumber' test issues in Jira.

        Args:
            test_issues (list): A list of dictionaries, each containing test case information with keys:
                         'Summary', 'Description', 'Priority', and 'cucumber_steps'.
            proj_key (str): The Jira project key where the issues should be created.
            parent_key (str): The Jira key of the parent issue to which new issues should be linked.

        Returns:
            None. The function performs operations via Jira API and prints results of each step.
        """
        response_msg = ''
        status_codes = []
        for i in test_issues:
            # Extract individual fields from the input dictionary
            summary = i['Summary']
            desc = i['Description'] + "\n\n *Note: This is AI generated content*"
            priority = i['Priority']

            # Combine cucumber steps into a newline-separated string
            steps = '\n'.join(i['cucumber_steps'])

            # Create a new issue in Jira of type 'Cucumber'
            if self.use_oauth:
                jira_client = JiraClient(self.base_url, oauth_token=self.oauth_token, cloud_id=self.cloud_id)
            else:
                jira_client = JiraClient(self.base_url, None, None, cookies=self.cookies)

            new_issue = jira_client.create_issue(proj_key, summary, desc, priority, "Cucumber")

            status_codes.append(new_issue.status_code)
            new_issue = new_issue.json()
            new_key = new_issue['key']
            insert_msg = f'{new_key} Test Issue Created.'
            response_msg += insert_msg + '\n'
            logging.info(insert_msg)

            # Add cucumber test steps to the newly created issue
            cucumber_resp = jira_client.add_test_cucumber(new_key, steps)
            status_codes.append(cucumber_resp.status_code)
            if cucumber_resp.status_code == 200:
                steps_msg = f'{new_key} Manual test steps added.'
                response_msg += steps_msg + '\n'
                logging.info(steps_msg)

            # Link the new issue to the specified parent issue
            link_isses = jira_client.link_issues(parent_key, new_key)
            status_codes.append(link_isses.status_code)
            link_msg = f'{new_key} test issue linked to {parent_key}'
            response_msg += link_msg + '\n'
            logging.info(link_msg)

        # TO DO Proper response return
        status_list = [i for i in status_codes if i > 299]
        return status_list[0] if status_list else 200, response_msg
