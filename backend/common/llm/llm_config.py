from autogen_ext.models.openai import AzureOpenAIChatCompletionClient
from azure.identity import DefaultAzureCredential
import os
from dotenv import load_dotenv
load_dotenv()

class LLMConfig:
    def __init__(self):
        self.model = os.getenv("AZURE_CHAT_MODEL_NAME")
        self.api_version = os.getenv("AZURE_OPENAI_API_VERSION")
        self.azure_deployment = os.getenv("AZURE_MODEL_DEPLOYMENT_NAME")
        self.azure_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
        self.temperature = os.getenv("AZURE_OPENAI_TEMPERATURE")
        
        credential = DefaultAzureCredential()
        try:
            self.access_token = credential.get_token("https://cognitiveservices.azure.com/.default").token
        except Exception as e:
            raise RuntimeError(f"Failed to acquire AZ token: {e}")


    def get_model_client(self) -> AzureOpenAIChatCompletionClient:
        return AzureOpenAIChatCompletionClient(
            azure_deployment=self.azure_deployment,
            model=self.model,
            api_version=self.api_version,
            azure_endpoint=self.azure_endpoint,
            azure_ad_token=self.access_token,
            temperature=float(self.temperature)
        )