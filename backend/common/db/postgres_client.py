import os
import psycopg2
from psycopg2.extras import RealDictCursor
from azure.identity import DefaultAzureCredential
from dotenv import load_dotenv
load_dotenv()

#from common.secrets.key_vault_client import KeyVaultClient

class PostgresClient:
    def __init__(self):
        #self.vault = KeyVaultClient()

        credential = DefaultAzureCredential()
        token = credential.get_token("https://ossrdbms-aad.database.windows.net").token

        self.conn = psycopg2.connect(
            host=os.getenv("POSTGRES_HOST"),
            port=os.getenv("POSTGRES_PORT"),
            database=os.getenv("POSTGRES_DB"),
            user=os.getenv("POSTGRES_USER"),
            password = token,
            sslmode='require'
        )
        self.conn.autocommit = True

    def execute_query(self, query, params=None):
        with self.conn.cursor() as cur:
            cur.execute(query, params)
            self.conn.commit()

    def fetch_one(self, query, params=None):
        with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute(query, params)
            return cur.fetchone()
        
    def fetch_all(self, query, params=None):
        with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute(query, params)
            return cur.fetchall()

    def close(self):
        if self.conn:
            self.conn.close()


    