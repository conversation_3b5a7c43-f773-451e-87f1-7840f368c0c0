#!/bin/bash

# Test script for Azure Functions using curl
# Make sure to run this from the backend directory

echo "🚀 Testing Azure Functions Deployment with curl"
echo "=================================================="

BASE_URL="https://sdlctest3.azurewebsites.net/api"

# Test 1: RAS Function
echo ""
echo "🧪 Test 1: Testing RAS Function"
echo "--------------------------------"

curl -X POST "${BASE_URL}/DurableFunctionsOrchestrator" \
  -H "Content-Type: application/json" \
  -d '{
    "helper_name": "RAS",
    "activity": "analyze",
    "project_key": "TEST",
    "issue_key": "TEST-001"
  }' \
  -w "\nHTTP Status: %{http_code}\n" \
  -s

echo ""
echo "Note: Save the statusQueryGetUri from the response above to check status"

echo ""
echo "📋 Additional Test Payloads:"
echo ""

echo "For TCG (Test Case Generation):"
echo "curl -X POST \"${BASE_URL}/DurableFunctionsOrchestrator\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{"
echo "    \"helper_name\": \"TCG\","
echo "    \"activity\": \"generate\","
echo "    \"project_key\": \"TEST\","
echo "    \"issue_key\": \"TEST-002\""
echo "  }'"

echo ""
echo "For Document Ingestion:"
echo "curl -X POST \"${BASE_URL}/DurableFunctionsOrchestrator\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{"
echo "    \"helper_name\": \"DocIngestionFunction\","
echo "    \"document_path\": \"test/document.txt\","
echo "    \"project_key\": \"TEST\""
echo "  }'"

echo ""
echo "For Push to Jira:"
echo "curl -X POST \"${BASE_URL}/DurableFunctionsOrchestrator\" \\"
echo "  -H \"Content-Type: application/json\" \\"
echo "  -d '{"
echo "    \"helper_name\": \"RAS\","
echo "    \"activity\": \"PushtoJira\","
echo "    \"project_key\": \"TEST\","
echo "    \"issue_key\": \"TEST-001\","
echo "    \"data\": \"Test data to push\""
echo "  }'"

echo ""
echo "💡 To check orchestration status, use the statusQueryGetUri from the response:"
echo "curl -X GET \"<statusQueryGetUri>\""
