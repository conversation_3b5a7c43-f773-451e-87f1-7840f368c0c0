import asyncio
import logging
from azure.identity import DefaultAzureCredential
from common.utils.TestCaseFormatter import TestCaseParser
from common.jira.jira_request_processor import JiraTestCaseProcessor
from common.jira.jira_client import JiraClient


    
# def update_jira( title, description, acceptance_criteria, jira_id, jira_session, base_url, proj_key):
#     try:
#         client = JiraClient(base_url, None, None, cookies=jira_session)
#         #client.update_jira_issue(jira_id, title, description, acceptance_criteria)
#         response = client.update_jira_issue(jira_id, title, description, acceptance_criteria)
#         return response
#     except Exception as e:
#         error_msg = "Error pushing to Jira : " + str(e)
#         return error_msg

def create_test_issues(issue_data, parent_key, auth_data, base_url, proj_key, issue_type):
    """
    Create test issues in Jira based on the issue type.

    This function processes the provided issue data and creates corresponding
    Jira issues either as manual test cases or cucumber-style test cases,
    depending on the format of the input.

    :param issue_data: Either a string "Manual" to indicate manual test cases,
                       or data representing cucumber test cases.
    :param parent_key: The Jira issue key under which the test cases will be created.
    :param auth_data: Authentication data (OAuth token or session cookies).
    :param base_url: The base URL of the Jira instance.
    :param proj_key: The key of the Jira project where the issues will be created.
    :param issue_type: Type of test case (Manual or Cucumber).
    """
    test_parser = TestCaseParser()
    if issue_type == "Manual":
        # Convert the manual test issue data from string to JSON format
        json_data = test_parser.convert_sring_to_json_manual(issue_data)
        # Processing the manual test issues to Jira for creating , updating and linking Jira issues
        status_code, success_msg = JiraTestCaseProcessor(auth_data, base_url).processing_data_to_jira_manual(json_data, proj_key,
                                                                                           parent_key)
    else:
        # Convert the automatic test issue data from string to JSON format
        json_data = test_parser.convert_sring_to_json_cucumber(issue_data)
        # Processing the automatic test issues to Jira for creating , updating and linking Jira issues
        status_code, success_msg = JiraTestCaseProcessor(auth_data, base_url).processing_data_to_jira_cucumber(json_data,
                                                                                             proj_key, parent_key)
    return status_code, success_msg


def main(inputData) -> any:

    try:
        helper_name = inputData.get("helper_name", None)
        issue_type = inputData.get("issue_type", None)
        base_url = inputData.get("base_url", None)
        jira_session = inputData.get("jira_session", None)
        oauth_token = inputData.get("oauth_token", None)
        cloud_id = inputData.get("cloud_id", None)
        request_data = inputData.get("request_data", None)
        jira_id = inputData.get("jira_id", None)
        proj_key = inputData.get("proj_key", None)

        # Determine authentication method
        if oauth_token and cloud_id:
            auth_data = {"oauth_token": oauth_token, "cloud_id": cloud_id}
        else:
            auth_data = jira_session  # Fallback to session cookies

        status_code, success_msg = create_test_issues(request_data, jira_id, auth_data, base_url, proj_key,
                                                      issue_type)

        return status_code, success_msg

    except Exception as e:
        error_msg = "Error in PG Service access: " + str(e)
        logging.exception(error_msg)
        return error_msg