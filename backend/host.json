{"version": "2.0", "functionTimeout": "00:10:00", "extensionBundle": {"id": "Microsoft.Azure.Functions.ExtensionBundle", "version": "[4.*, 5.0.0)"}, "extensions": {"durableTask": {"hubName": "DurableFunctionsHub", "storageProvider": {"connectionStringName": "AzureWebJobsStorage"}, "maxConcurrentActivityFunctions": 10, "maxConcurrentOrchestratorFunctions": 10}, "http": {"routePrefix": "api"}}, "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": true}}, "logLevel": {"default": "Information"}}, "retry": {"strategy": "fixedDelay", "maxRetryCount": 3, "delayInterval": "00:00:05"}}