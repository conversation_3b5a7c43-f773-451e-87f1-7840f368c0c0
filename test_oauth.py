#!/usr/bin/env python3
"""
Test script for OAuth 2.0 implementation with <PERSON><PERSON>.
This script tests the OAuth helper functionality without Streamlit.
"""

import os
import sys
import logging
from dotenv import load_dotenv

# Add the frontend src directory to the path
sys.path.append('frontend/src')

# Load environment variables
load_dotenv('frontend/.env')

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_oauth_configuration():
    """Test if OAuth configuration is properly loaded."""
    print("🔧 Testing OAuth Configuration...")
    
    required_vars = [
        'JIRA_OAUTH_CLIENT_ID',
        'JIRA_OAUTH_CLIENT_SECRET', 
        'JIRA_OAUTH_REDIRECT_URI',
        'JIRA_OAUTH_SCOPES'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            missing_vars.append(var)
        else:
            print(f"✅ {var}: {'*' * (len(value) - 4) + value[-4:] if len(value) > 4 else '****'}")
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    
    print("✅ OAuth configuration loaded successfully!")
    return True

def test_oauth_helper():
    """Test the OAuth helper class."""
    print("\n🔧 Testing OAuth Helper...")
    
    try:
        from utils.oauth_helper import JiraOAuthHelper
        
        oauth_helper = JiraOAuthHelper()
        print("✅ OAuth helper initialized successfully!")
        
        # Test state generation
        state = oauth_helper.generate_state()
        print(f"✅ Generated state: {state[:10]}...")
        
        # Test authorization URL generation
        auth_url = oauth_helper.get_authorization_url(state)
        print(f"✅ Generated authorization URL: {auth_url[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ OAuth helper test failed: {str(e)}")
        return False

def test_jira_client():
    """Test the Jira client OAuth functionality."""
    print("\n🔧 Testing Jira Client...")
    
    try:
        from services.jira_client import JiraClient
        
        jira_endpoint = os.getenv('JIRA_ENDPOINT', 'https://aiordinate.atlassian.net/')
        client = JiraClient(jira_endpoint)
        print("✅ Jira client initialized successfully!")
        
        # Test OAuth URL construction
        if hasattr(client, 'get_oauth_api_url'):
            try:
                client.cloud_id = "test-cloud-id"
                oauth_url = client.get_oauth_api_url("/rest/api/2/myself")
                print(f"✅ OAuth API URL construction: {oauth_url}")
            except Exception as e:
                print(f"⚠️  OAuth URL construction test: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Jira client test failed: {str(e)}")
        return False

def test_backend_client():
    """Test the backend Jira client OAuth functionality."""
    print("\n🔧 Testing Backend Jira Client...")
    
    try:
        # Add backend path
        sys.path.append('backend')
        from common.jira.jira_client import JiraClient as BackendJiraClient
        
        jira_endpoint = os.getenv('JIRA_ENDPOINT', 'https://aiordinate.atlassian.net/')
        client = BackendJiraClient(jira_endpoint, oauth_token="test-token", cloud_id="test-cloud-id")
        print("✅ Backend Jira client initialized with OAuth!")
        
        # Test OAuth URL construction
        if hasattr(client, 'get_oauth_api_url'):
            oauth_url = client.get_oauth_api_url("/rest/api/2/myself")
            print(f"✅ Backend OAuth API URL: {oauth_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ Backend Jira client test failed: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting OAuth Implementation Tests...\n")
    
    tests = [
        ("OAuth Configuration", test_oauth_configuration),
        ("OAuth Helper", test_oauth_helper),
        ("Frontend Jira Client", test_jira_client),
        ("Backend Jira Client", test_backend_client)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))
    
    print("\n" + "="*50)
    print("📊 TEST RESULTS SUMMARY")
    print("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Tests passed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("🎉 All tests passed! OAuth implementation is ready.")
        print("\n📋 Next steps:")
        print("1. Start the Streamlit app: streamlit run frontend/src/app.py")
        print("2. Click 'Authenticate with Jira' to test the OAuth flow")
        print("3. Verify that you can access Jira issues after authentication")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
