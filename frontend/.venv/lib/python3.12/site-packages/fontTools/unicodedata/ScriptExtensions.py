# -*- coding: utf-8 -*-
#
# NOTE: This file was auto-generated with MetaTools/buildUCD.py.
# Source: https://unicode.org/Public/UNIDATA/ScriptExtensions.txt
# License: http://unicode.org/copyright.html#License
#
# ScriptExtensions-16.0.0.txt
# Date: 2024-07-30, 19:38:00 GMT
# © 2024 Unicode®, Inc.
# Unicode and the Unicode Logo are registered trademarks of Unicode, Inc. in the U.S. and other countries.
# For terms of use and license, see https://www.unicode.org/terms_of_use.html
#
# Unicode Character Database
#   For documentation, see https://www.unicode.org/reports/tr44/
#
# The Script_Extensions property indicates which characters are commonly used
# with more than one script, but with a limited number of scripts.
# For each code point, there is one or more property values.  Each such value is a Script property value.
# For more information, see:
#   UAX #24, Unicode Script Property: https://www.unicode.org/reports/tr24/
#     Especially the sections:
#       https://www.unicode.org/reports/tr24/#Assignment_Script_Values
#       https://www.unicode.org/reports/tr24/#Assignment_ScriptX_Values
#
# Each Script_Extensions value in this file consists of a set
# of one or more abbreviated Script property values. The ordering of the
# values in that set is not material, but for stability in presentation
# it is given here as alphabetical.
#
# All code points not explicitly listed for Script_Extensions
# have as their value the corresponding Script property value.
#
# @missing: 0000..10FFFF; <script>

RANGES = [
    0x0000,  # .. 0x00B6 ; None
    0x00B7,  # .. 0x00B7 ; {'Avst', 'Cari', 'Copt', 'Dupl', 'Elba', 'Geor', 'Glag', 'Gong', 'Goth', 'Grek', 'Hani', 'Latn', 'Lydi', 'Mahj', 'Perm', 'Shaw'}
    0x00B8,  # .. 0x02BB ; None
    0x02BC,  # .. 0x02BC ; {'Beng', 'Cyrl', 'Deva', 'Latn', 'Lisu', 'Thai', 'Toto'}
    0x02BD,  # .. 0x02C6 ; None
    0x02C7,  # .. 0x02C7 ; {'Bopo', 'Latn'}
    0x02C8,  # .. 0x02C8 ; None
    0x02C9,  # .. 0x02CB ; {'Bopo', 'Latn'}
    0x02CC,  # .. 0x02CC ; None
    0x02CD,  # .. 0x02CD ; {'Latn', 'Lisu'}
    0x02CE,  # .. 0x02D6 ; None
    0x02D7,  # .. 0x02D7 ; {'Latn', 'Thai'}
    0x02D8,  # .. 0x02D8 ; None
    0x02D9,  # .. 0x02D9 ; {'Bopo', 'Latn'}
    0x02DA,  # .. 0x02FF ; None
    0x0300,  # .. 0x0300 ; {'Cher', 'Copt', 'Cyrl', 'Grek', 'Latn', 'Perm', 'Sunu', 'Tale'}
    0x0301,  # .. 0x0301 ; {'Cher', 'Cyrl', 'Grek', 'Latn', 'Osge', 'Sunu', 'Tale', 'Todr'}
    0x0302,  # .. 0x0302 ; {'Cher', 'Cyrl', 'Latn', 'Tfng'}
    0x0303,  # .. 0x0303 ; {'Glag', 'Latn', 'Sunu', 'Syrc', 'Thai'}
    0x0304,  # .. 0x0304 ; {'Aghb', 'Cher', 'Copt', 'Cyrl', 'Goth', 'Grek', 'Latn', 'Osge', 'Syrc', 'Tfng', 'Todr'}
    0x0305,  # .. 0x0305 ; {'Copt', 'Elba', 'Glag', 'Goth', 'Kana', 'Latn'}
    0x0306,  # .. 0x0306 ; {'Cyrl', 'Grek', 'Latn', 'Perm'}
    0x0307,  # .. 0x0307 ; {'Copt', 'Dupl', 'Hebr', 'Latn', 'Perm', 'Syrc', 'Tale', 'Tfng', 'Todr'}
    0x0308,  # .. 0x0308 ; {'Armn', 'Cyrl', 'Dupl', 'Goth', 'Grek', 'Hebr', 'Latn', 'Perm', 'Syrc', 'Tale'}
    0x0309,  # .. 0x0309 ; {'Latn', 'Tfng'}
    0x030A,  # .. 0x030A ; {'Dupl', 'Latn', 'Syrc'}
    0x030B,  # .. 0x030B ; {'Cher', 'Cyrl', 'Latn', 'Osge'}
    0x030C,  # .. 0x030C ; {'Cher', 'Latn', 'Tale'}
    0x030D,  # .. 0x030D ; {'Latn', 'Sunu'}
    0x030E,  # .. 0x030E ; {'Ethi', 'Latn'}
    0x030F,  # .. 0x030F ; None
    0x0310,  # .. 0x0310 ; {'Latn', 'Sunu'}
    0x0311,  # .. 0x0311 ; {'Cyrl', 'Latn', 'Todr'}
    0x0312,  # .. 0x0312 ; None
    0x0313,  # .. 0x0313 ; {'Grek', 'Latn', 'Perm', 'Todr'}
    0x0314,  # .. 0x031F ; None
    0x0320,  # .. 0x0320 ; {'Latn', 'Syrc'}
    0x0321,  # .. 0x0322 ; None
    0x0323,  # .. 0x0323 ; {'Cher', 'Dupl', 'Kana', 'Latn', 'Syrc'}
    0x0324,  # .. 0x0324 ; {'Cher', 'Dupl', 'Latn', 'Syrc'}
    0x0325,  # .. 0x0325 ; {'Latn', 'Syrc'}
    0x0326,  # .. 0x032C ; None
    0x032D,  # .. 0x032D ; {'Latn', 'Sunu', 'Syrc'}
    0x032E,  # .. 0x032E ; {'Latn', 'Syrc'}
    0x032F,  # .. 0x032F ; None
    0x0330,  # .. 0x0330 ; {'Cher', 'Latn', 'Syrc'}
    0x0331,  # .. 0x0331 ; {'Aghb', 'Cher', 'Goth', 'Latn', 'Sunu', 'Thai'}
    0x0332,  # .. 0x0341 ; None
    0x0342,  # .. 0x0342 ; {'Grek'}
    0x0343,  # .. 0x0344 ; None
    0x0345,  # .. 0x0345 ; {'Grek'}
    0x0346,  # .. 0x0357 ; None
    0x0358,  # .. 0x0358 ; {'Latn', 'Osge'}
    0x0359,  # .. 0x035D ; None
    0x035E,  # .. 0x035E ; {'Aghb', 'Latn', 'Todr'}
    0x035F,  # .. 0x0362 ; None
    0x0363,  # .. 0x036F ; {'Latn'}
    0x0370,  # .. 0x0373 ; None
    0x0374,  # .. 0x0375 ; {'Copt', 'Grek'}
    0x0376,  # .. 0x0482 ; None
    0x0483,  # .. 0x0483 ; {'Cyrl', 'Perm'}
    0x0484,  # .. 0x0484 ; {'Cyrl', 'Glag'}
    0x0485,  # .. 0x0486 ; {'Cyrl', 'Latn'}
    0x0487,  # .. 0x0487 ; {'Cyrl', 'Glag'}
    0x0488,  # .. 0x0588 ; None
    0x0589,  # .. 0x0589 ; {'Armn', 'Geor', 'Glag'}
    0x058A,  # .. 0x060B ; None
    0x060C,  # .. 0x060C ; {'Arab', 'Gara', 'Nkoo', 'Rohg', 'Syrc', 'Thaa', 'Yezi'}
    0x060D,  # .. 0x061A ; None
    0x061B,  # .. 0x061B ; {'Arab', 'Gara', 'Nkoo', 'Rohg', 'Syrc', 'Thaa', 'Yezi'}
    0x061C,  # .. 0x061C ; {'Arab', 'Syrc', 'Thaa'}
    0x061D,  # .. 0x061E ; None
    0x061F,  # .. 0x061F ; {'Adlm', 'Arab', 'Gara', 'Nkoo', 'Rohg', 'Syrc', 'Thaa', 'Yezi'}
    0x0620,  # .. 0x063F ; None
    0x0640,  # .. 0x0640 ; {'Adlm', 'Arab', 'Mand', 'Mani', 'Ougr', 'Phlp', 'Rohg', 'Sogd', 'Syrc'}
    0x0641,  # .. 0x064A ; None
    0x064B,  # .. 0x0655 ; {'Arab', 'Syrc'}
    0x0656,  # .. 0x065F ; None
    0x0660,  # .. 0x0669 ; {'Arab', 'Thaa', 'Yezi'}
    0x066A,  # .. 0x066F ; None
    0x0670,  # .. 0x0670 ; {'Arab', 'Syrc'}
    0x0671,  # .. 0x06D3 ; None
    0x06D4,  # .. 0x06D4 ; {'Arab', 'Rohg'}
    0x06D5,  # .. 0x0950 ; None
    0x0951,  # .. 0x0951 ; {'Beng', 'Deva', 'Gran', 'Gujr', 'Guru', 'Knda', 'Latn', 'Mlym', 'Orya', 'Shrd', 'Taml', 'Telu', 'Tirh'}
    0x0952,  # .. 0x0952 ; {'Beng', 'Deva', 'Gran', 'Gujr', 'Guru', 'Knda', 'Latn', 'Mlym', 'Orya', 'Taml', 'Telu', 'Tirh'}
    0x0953,  # .. 0x0963 ; None
    0x0964,  # .. 0x0964 ; {'Beng', 'Deva', 'Dogr', 'Gong', 'Gonm', 'Gran', 'Gujr', 'Guru', 'Knda', 'Mahj', 'Mlym', 'Nand', 'Onao', 'Orya', 'Sind', 'Sinh', 'Sylo', 'Takr', 'Taml', 'Telu', 'Tirh'}
    0x0965,  # .. 0x0965 ; {'Beng', 'Deva', 'Dogr', 'Gong', 'Gonm', 'Gran', 'Gujr', 'Gukh', 'Guru', 'Knda', 'Limb', 'Mahj', 'Mlym', 'Nand', 'Onao', 'Orya', 'Sind', 'Sinh', 'Sylo', 'Takr', 'Taml', 'Telu', 'Tirh'}
    0x0966,  # .. 0x096F ; {'Deva', 'Dogr', 'Kthi', 'Mahj'}
    0x0970,  # .. 0x09E5 ; None
    0x09E6,  # .. 0x09EF ; {'Beng', 'Cakm', 'Sylo'}
    0x09F0,  # .. 0x0A65 ; None
    0x0A66,  # .. 0x0A6F ; {'Guru', 'Mult'}
    0x0A70,  # .. 0x0AE5 ; None
    0x0AE6,  # .. 0x0AEF ; {'Gujr', 'Khoj'}
    0x0AF0,  # .. 0x0BE5 ; None
    0x0BE6,  # .. 0x0BF3 ; {'Gran', 'Taml'}
    0x0BF4,  # .. 0x0CE5 ; None
    0x0CE6,  # .. 0x0CEF ; {'Knda', 'Nand', 'Tutg'}
    0x0CF0,  # .. 0x103F ; None
    0x1040,  # .. 0x1049 ; {'Cakm', 'Mymr', 'Tale'}
    0x104A,  # .. 0x10FA ; None
    0x10FB,  # .. 0x10FB ; {'Geor', 'Glag', 'Latn'}
    0x10FC,  # .. 0x16EA ; None
    0x16EB,  # .. 0x16ED ; {'Runr'}
    0x16EE,  # .. 0x1734 ; None
    0x1735,  # .. 0x1736 ; {'Buhd', 'Hano', 'Tagb', 'Tglg'}
    0x1737,  # .. 0x1801 ; None
    0x1802,  # .. 0x1803 ; {'Mong', 'Phag'}
    0x1804,  # .. 0x1804 ; None
    0x1805,  # .. 0x1805 ; {'Mong', 'Phag'}
    0x1806,  # .. 0x1CCF ; None
    0x1CD0,  # .. 0x1CD0 ; {'Beng', 'Deva', 'Gran', 'Knda'}
    0x1CD1,  # .. 0x1CD1 ; {'Deva'}
    0x1CD2,  # .. 0x1CD2 ; {'Beng', 'Deva', 'Gran', 'Knda'}
    0x1CD3,  # .. 0x1CD3 ; {'Deva', 'Gran', 'Knda'}
    0x1CD4,  # .. 0x1CD4 ; {'Deva'}
    0x1CD5,  # .. 0x1CD6 ; {'Beng', 'Deva'}
    0x1CD7,  # .. 0x1CD7 ; {'Deva', 'Shrd'}
    0x1CD8,  # .. 0x1CD8 ; {'Beng', 'Deva'}
    0x1CD9,  # .. 0x1CD9 ; {'Deva', 'Shrd'}
    0x1CDA,  # .. 0x1CDA ; {'Deva', 'Knda', 'Mlym', 'Orya', 'Taml', 'Telu'}
    0x1CDB,  # .. 0x1CDB ; {'Deva'}
    0x1CDC,  # .. 0x1CDD ; {'Deva', 'Shrd'}
    0x1CDE,  # .. 0x1CDF ; {'Deva'}
    0x1CE0,  # .. 0x1CE0 ; {'Deva', 'Shrd'}
    0x1CE1,  # .. 0x1CE1 ; {'Beng', 'Deva'}
    0x1CE2,  # .. 0x1CE8 ; {'Deva'}
    0x1CE9,  # .. 0x1CE9 ; {'Deva', 'Nand'}
    0x1CEA,  # .. 0x1CEA ; {'Beng', 'Deva'}
    0x1CEB,  # .. 0x1CEC ; {'Deva'}
    0x1CED,  # .. 0x1CED ; {'Beng', 'Deva'}
    0x1CEE,  # .. 0x1CF1 ; {'Deva'}
    0x1CF2,  # .. 0x1CF2 ; {'Beng', 'Deva', 'Gran', 'Knda', 'Mlym', 'Nand', 'Orya', 'Sinh', 'Telu', 'Tirh', 'Tutg'}
    0x1CF3,  # .. 0x1CF3 ; {'Deva', 'Gran'}
    0x1CF4,  # .. 0x1CF4 ; {'Deva', 'Gran', 'Knda', 'Tutg'}
    0x1CF5,  # .. 0x1CF6 ; {'Beng', 'Deva'}
    0x1CF7,  # .. 0x1CF7 ; {'Beng'}
    0x1CF8,  # .. 0x1CF9 ; {'Deva', 'Gran'}
    0x1CFA,  # .. 0x1CFA ; {'Nand'}
    0x1CFB,  # .. 0x1DBF ; None
    0x1DC0,  # .. 0x1DC1 ; {'Grek'}
    0x1DC2,  # .. 0x1DF7 ; None
    0x1DF8,  # .. 0x1DF8 ; {'Cyrl', 'Latn', 'Syrc'}
    0x1DF9,  # .. 0x1DF9 ; None
    0x1DFA,  # .. 0x1DFA ; {'Syrc'}
    0x1DFB,  # .. 0x202E ; None
    0x202F,  # .. 0x202F ; {'Latn', 'Mong', 'Phag'}
    0x2030,  # .. 0x204E ; None
    0x204F,  # .. 0x204F ; {'Adlm', 'Arab'}
    0x2050,  # .. 0x2059 ; None
    0x205A,  # .. 0x205A ; {'Cari', 'Geor', 'Glag', 'Hung', 'Lyci', 'Orkh'}
    0x205B,  # .. 0x205C ; None
    0x205D,  # .. 0x205D ; {'Cari', 'Grek', 'Hung', 'Mero'}
    0x205E,  # .. 0x20EF ; None
    0x20F0,  # .. 0x20F0 ; {'Deva', 'Gran', 'Latn'}
    0x20F1,  # .. 0x2E16 ; None
    0x2E17,  # .. 0x2E17 ; {'Copt', 'Latn'}
    0x2E18,  # .. 0x2E2F ; None
    0x2E30,  # .. 0x2E30 ; {'Avst', 'Orkh'}
    0x2E31,  # .. 0x2E31 ; {'Avst', 'Cari', 'Geor', 'Hung', 'Kthi', 'Lydi', 'Samr'}
    0x2E32,  # .. 0x2E3B ; None
    0x2E3C,  # .. 0x2E3C ; {'Dupl'}
    0x2E3D,  # .. 0x2E40 ; None
    0x2E41,  # .. 0x2E41 ; {'Adlm', 'Arab', 'Hung'}
    0x2E42,  # .. 0x2E42 ; None
    0x2E43,  # .. 0x2E43 ; {'Cyrl', 'Glag'}
    0x2E44,  # .. 0x2FEF ; None
    0x2FF0,  # .. 0x2FFF ; {'Hani', 'Tang'}
    0x3000,  # .. 0x3000 ; None
    0x3001,  # .. 0x3001 ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana', 'Mong', 'Yiii'}
    0x3002,  # .. 0x3002 ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana', 'Mong', 'Phag', 'Yiii'}
    0x3003,  # .. 0x3003 ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana'}
    0x3004,  # .. 0x3005 ; None
    0x3006,  # .. 0x3006 ; {'Hani'}
    0x3007,  # .. 0x3007 ; None
    0x3008,  # .. 0x3009 ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana', 'Mong', 'Tibt', 'Yiii'}
    0x300A,  # .. 0x300B ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana', 'Lisu', 'Mong', 'Tibt', 'Yiii'}
    0x300C,  # .. 0x3011 ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana', 'Yiii'}
    0x3012,  # .. 0x3012 ; None
    0x3013,  # .. 0x3013 ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana'}
    0x3014,  # .. 0x301B ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana', 'Yiii'}
    0x301C,  # .. 0x301F ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana'}
    0x3020,  # .. 0x3029 ; None
    0x302A,  # .. 0x302D ; {'Bopo', 'Hani'}
    0x302E,  # .. 0x302F ; None
    0x3030,  # .. 0x3030 ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana'}
    0x3031,  # .. 0x3035 ; {'Hira', 'Kana'}
    0x3036,  # .. 0x3036 ; None
    0x3037,  # .. 0x3037 ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana'}
    0x3038,  # .. 0x303B ; None
    0x303C,  # .. 0x303D ; {'Hani', 'Hira', 'Kana'}
    0x303E,  # .. 0x303F ; {'Hani'}
    0x3040,  # .. 0x3098 ; None
    0x3099,  # .. 0x309C ; {'Hira', 'Kana'}
    0x309D,  # .. 0x309F ; None
    0x30A0,  # .. 0x30A0 ; {'Hira', 'Kana'}
    0x30A1,  # .. 0x30FA ; None
    0x30FB,  # .. 0x30FB ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana', 'Yiii'}
    0x30FC,  # .. 0x30FC ; {'Hira', 'Kana'}
    0x30FD,  # .. 0x318F ; None
    0x3190,  # .. 0x319F ; {'Hani'}
    0x31A0,  # .. 0x31BF ; None
    0x31C0,  # .. 0x31E5 ; {'Hani'}
    0x31E6,  # .. 0x31EE ; None
    0x31EF,  # .. 0x31EF ; {'Hani', 'Tang'}
    0x31F0,  # .. 0x321F ; None
    0x3220,  # .. 0x3247 ; {'Hani'}
    0x3248,  # .. 0x327F ; None
    0x3280,  # .. 0x32B0 ; {'Hani'}
    0x32B1,  # .. 0x32BF ; None
    0x32C0,  # .. 0x32CB ; {'Hani'}
    0x32CC,  # .. 0x32FE ; None
    0x32FF,  # .. 0x32FF ; {'Hani'}
    0x3300,  # .. 0x3357 ; None
    0x3358,  # .. 0x3370 ; {'Hani'}
    0x3371,  # .. 0x337A ; None
    0x337B,  # .. 0x337F ; {'Hani'}
    0x3380,  # .. 0x33DF ; None
    0x33E0,  # .. 0x33FE ; {'Hani'}
    0x33FF,  # .. 0xA66E ; None
    0xA66F,  # .. 0xA66F ; {'Cyrl', 'Glag'}
    0xA670,  # .. 0xA6FF ; None
    0xA700,  # .. 0xA707 ; {'Hani', 'Latn'}
    0xA708,  # .. 0xA82F ; None
    0xA830,  # .. 0xA832 ; {'Deva', 'Dogr', 'Gujr', 'Guru', 'Khoj', 'Knda', 'Kthi', 'Mahj', 'Mlym', 'Modi', 'Nand', 'Shrd', 'Sind', 'Takr', 'Tirh', 'Tutg'}
    0xA833,  # .. 0xA835 ; {'Deva', 'Dogr', 'Gujr', 'Guru', 'Khoj', 'Knda', 'Kthi', 'Mahj', 'Modi', 'Nand', 'Shrd', 'Sind', 'Takr', 'Tirh', 'Tutg'}
    0xA836,  # .. 0xA837 ; {'Deva', 'Dogr', 'Gujr', 'Guru', 'Khoj', 'Kthi', 'Mahj', 'Modi', 'Sind', 'Takr', 'Tirh'}
    0xA838,  # .. 0xA838 ; {'Deva', 'Dogr', 'Gujr', 'Guru', 'Khoj', 'Kthi', 'Mahj', 'Modi', 'Shrd', 'Sind', 'Takr', 'Tirh'}
    0xA839,  # .. 0xA839 ; {'Deva', 'Dogr', 'Gujr', 'Guru', 'Khoj', 'Kthi', 'Mahj', 'Modi', 'Sind', 'Takr', 'Tirh'}
    0xA83A,  # .. 0xA8F0 ; None
    0xA8F1,  # .. 0xA8F1 ; {'Beng', 'Deva', 'Tutg'}
    0xA8F2,  # .. 0xA8F2 ; None
    0xA8F3,  # .. 0xA8F3 ; {'Deva', 'Taml'}
    0xA8F4,  # .. 0xA92D ; None
    0xA92E,  # .. 0xA92E ; {'Kali', 'Latn', 'Mymr'}
    0xA92F,  # .. 0xA9CE ; None
    0xA9CF,  # .. 0xA9CF ; {'Bugi', 'Java'}
    0xA9D0,  # .. 0xFD3D ; None
    0xFD3E,  # .. 0xFD3F ; {'Arab', 'Nkoo'}
    0xFD40,  # .. 0xFDF1 ; None
    0xFDF2,  # .. 0xFDF2 ; {'Arab', 'Thaa'}
    0xFDF3,  # .. 0xFDFC ; None
    0xFDFD,  # .. 0xFDFD ; {'Arab', 'Thaa'}
    0xFDFE,  # .. 0xFE44 ; None
    0xFE45,  # .. 0xFE46 ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana'}
    0xFE47,  # .. 0xFF60 ; None
    0xFF61,  # .. 0xFF65 ; {'Bopo', 'Hang', 'Hani', 'Hira', 'Kana', 'Yiii'}
    0xFF66,  # .. 0xFF6F ; None
    0xFF70,  # .. 0xFF70 ; {'Hira', 'Kana'}
    0xFF71,  # .. 0xFF9D ; None
    0xFF9E,  # .. 0xFF9F ; {'Hira', 'Kana'}
    0xFFA0,  # .. 0x100FF ; None
    0x10100,  # .. 0x10101 ; {'Cpmn', 'Cprt', 'Linb'}
    0x10102,  # .. 0x10102 ; {'Cprt', 'Linb'}
    0x10103,  # .. 0x10106 ; None
    0x10107,  # .. 0x10133 ; {'Cprt', 'Lina', 'Linb'}
    0x10134,  # .. 0x10136 ; None
    0x10137,  # .. 0x1013F ; {'Cprt', 'Linb'}
    0x10140,  # .. 0x102DF ; None
    0x102E0,  # .. 0x102FB ; {'Arab', 'Copt'}
    0x102FC,  # .. 0x10AF1 ; None
    0x10AF2,  # .. 0x10AF2 ; {'Mani', 'Ougr'}
    0x10AF3,  # .. 0x11300 ; None
    0x11301,  # .. 0x11301 ; {'Gran', 'Taml'}
    0x11302,  # .. 0x11302 ; None
    0x11303,  # .. 0x11303 ; {'Gran', 'Taml'}
    0x11304,  # .. 0x1133A ; None
    0x1133B,  # .. 0x1133C ; {'Gran', 'Taml'}
    0x1133D,  # .. 0x11FCF ; None
    0x11FD0,  # .. 0x11FD1 ; {'Gran', 'Taml'}
    0x11FD2,  # .. 0x11FD2 ; None
    0x11FD3,  # .. 0x11FD3 ; {'Gran', 'Taml'}
    0x11FD4,  # .. 0x1BC9F ; None
    0x1BCA0,  # .. 0x1BCA3 ; {'Dupl'}
    0x1BCA4,  # .. 0x1D35F ; None
    0x1D360,  # .. 0x1D371 ; {'Hani'}
    0x1D372,  # .. 0x1F24F ; None
    0x1F250,  # .. 0x1F251 ; {'Hani'}
    0x1F252,  # .. 0x10FFFF ; None
]

VALUES = [
    None,  # 0000..00B6
    {
        "Avst",
        "Cari",
        "Copt",
        "Dupl",
        "Elba",
        "Geor",
        "Glag",
        "Gong",
        "Goth",
        "Grek",
        "Hani",
        "Latn",
        "Lydi",
        "Mahj",
        "Perm",
        "Shaw",
    },  # 00B7..00B7
    None,  # 00B8..02BB
    {"Beng", "Cyrl", "Deva", "Latn", "Lisu", "Thai", "Toto"},  # 02BC..02BC
    None,  # 02BD..02C6
    {"Bopo", "Latn"},  # 02C7..02C7
    None,  # 02C8..02C8
    {"Bopo", "Latn"},  # 02C9..02CB
    None,  # 02CC..02CC
    {"Latn", "Lisu"},  # 02CD..02CD
    None,  # 02CE..02D6
    {"Latn", "Thai"},  # 02D7..02D7
    None,  # 02D8..02D8
    {"Bopo", "Latn"},  # 02D9..02D9
    None,  # 02DA..02FF
    {"Cher", "Copt", "Cyrl", "Grek", "Latn", "Perm", "Sunu", "Tale"},  # 0300..0300
    {"Cher", "Cyrl", "Grek", "Latn", "Osge", "Sunu", "Tale", "Todr"},  # 0301..0301
    {"Cher", "Cyrl", "Latn", "Tfng"},  # 0302..0302
    {"Glag", "Latn", "Sunu", "Syrc", "Thai"},  # 0303..0303
    {
        "Aghb",
        "Cher",
        "Copt",
        "Cyrl",
        "Goth",
        "Grek",
        "Latn",
        "Osge",
        "Syrc",
        "Tfng",
        "Todr",
    },  # 0304..0304
    {"Copt", "Elba", "Glag", "Goth", "Kana", "Latn"},  # 0305..0305
    {"Cyrl", "Grek", "Latn", "Perm"},  # 0306..0306
    {
        "Copt",
        "Dupl",
        "Hebr",
        "Latn",
        "Perm",
        "Syrc",
        "Tale",
        "Tfng",
        "Todr",
    },  # 0307..0307
    {
        "Armn",
        "Cyrl",
        "Dupl",
        "Goth",
        "Grek",
        "Hebr",
        "Latn",
        "Perm",
        "Syrc",
        "Tale",
    },  # 0308..0308
    {"Latn", "Tfng"},  # 0309..0309
    {"Dupl", "Latn", "Syrc"},  # 030A..030A
    {"Cher", "Cyrl", "Latn", "Osge"},  # 030B..030B
    {"Cher", "Latn", "Tale"},  # 030C..030C
    {"Latn", "Sunu"},  # 030D..030D
    {"Ethi", "Latn"},  # 030E..030E
    None,  # 030F..030F
    {"Latn", "Sunu"},  # 0310..0310
    {"Cyrl", "Latn", "Todr"},  # 0311..0311
    None,  # 0312..0312
    {"Grek", "Latn", "Perm", "Todr"},  # 0313..0313
    None,  # 0314..031F
    {"Latn", "Syrc"},  # 0320..0320
    None,  # 0321..0322
    {"Cher", "Dupl", "Kana", "Latn", "Syrc"},  # 0323..0323
    {"Cher", "Dupl", "Latn", "Syrc"},  # 0324..0324
    {"Latn", "Syrc"},  # 0325..0325
    None,  # 0326..032C
    {"Latn", "Sunu", "Syrc"},  # 032D..032D
    {"Latn", "Syrc"},  # 032E..032E
    None,  # 032F..032F
    {"Cher", "Latn", "Syrc"},  # 0330..0330
    {"Aghb", "Cher", "Goth", "Latn", "Sunu", "Thai"},  # 0331..0331
    None,  # 0332..0341
    {"Grek"},  # 0342..0342
    None,  # 0343..0344
    {"Grek"},  # 0345..0345
    None,  # 0346..0357
    {"Latn", "Osge"},  # 0358..0358
    None,  # 0359..035D
    {"Aghb", "Latn", "Todr"},  # 035E..035E
    None,  # 035F..0362
    {"Latn"},  # 0363..036F
    None,  # 0370..0373
    {"Copt", "Grek"},  # 0374..0375
    None,  # 0376..0482
    {"Cyrl", "Perm"},  # 0483..0483
    {"Cyrl", "Glag"},  # 0484..0484
    {"Cyrl", "Latn"},  # 0485..0486
    {"Cyrl", "Glag"},  # 0487..0487
    None,  # 0488..0588
    {"Armn", "Geor", "Glag"},  # 0589..0589
    None,  # 058A..060B
    {"Arab", "Gara", "Nkoo", "Rohg", "Syrc", "Thaa", "Yezi"},  # 060C..060C
    None,  # 060D..061A
    {"Arab", "Gara", "Nkoo", "Rohg", "Syrc", "Thaa", "Yezi"},  # 061B..061B
    {"Arab", "Syrc", "Thaa"},  # 061C..061C
    None,  # 061D..061E
    {"Adlm", "Arab", "Gara", "Nkoo", "Rohg", "Syrc", "Thaa", "Yezi"},  # 061F..061F
    None,  # 0620..063F
    {
        "Adlm",
        "Arab",
        "Mand",
        "Mani",
        "Ougr",
        "Phlp",
        "Rohg",
        "Sogd",
        "Syrc",
    },  # 0640..0640
    None,  # 0641..064A
    {"Arab", "Syrc"},  # 064B..0655
    None,  # 0656..065F
    {"Arab", "Thaa", "Yezi"},  # 0660..0669
    None,  # 066A..066F
    {"Arab", "Syrc"},  # 0670..0670
    None,  # 0671..06D3
    {"Arab", "Rohg"},  # 06D4..06D4
    None,  # 06D5..0950
    {
        "Beng",
        "Deva",
        "Gran",
        "Gujr",
        "Guru",
        "Knda",
        "Latn",
        "Mlym",
        "Orya",
        "Shrd",
        "Taml",
        "Telu",
        "Tirh",
    },  # 0951..0951
    {
        "Beng",
        "Deva",
        "Gran",
        "Gujr",
        "Guru",
        "Knda",
        "Latn",
        "Mlym",
        "Orya",
        "Taml",
        "Telu",
        "Tirh",
    },  # 0952..0952
    None,  # 0953..0963
    {
        "Beng",
        "Deva",
        "Dogr",
        "Gong",
        "Gonm",
        "Gran",
        "Gujr",
        "Guru",
        "Knda",
        "Mahj",
        "Mlym",
        "Nand",
        "Onao",
        "Orya",
        "Sind",
        "Sinh",
        "Sylo",
        "Takr",
        "Taml",
        "Telu",
        "Tirh",
    },  # 0964..0964
    {
        "Beng",
        "Deva",
        "Dogr",
        "Gong",
        "Gonm",
        "Gran",
        "Gujr",
        "Gukh",
        "Guru",
        "Knda",
        "Limb",
        "Mahj",
        "Mlym",
        "Nand",
        "Onao",
        "Orya",
        "Sind",
        "Sinh",
        "Sylo",
        "Takr",
        "Taml",
        "Telu",
        "Tirh",
    },  # 0965..0965
    {"Deva", "Dogr", "Kthi", "Mahj"},  # 0966..096F
    None,  # 0970..09E5
    {"Beng", "Cakm", "Sylo"},  # 09E6..09EF
    None,  # 09F0..0A65
    {"Guru", "Mult"},  # 0A66..0A6F
    None,  # 0A70..0AE5
    {"Gujr", "Khoj"},  # 0AE6..0AEF
    None,  # 0AF0..0BE5
    {"Gran", "Taml"},  # 0BE6..0BF3
    None,  # 0BF4..0CE5
    {"Knda", "Nand", "Tutg"},  # 0CE6..0CEF
    None,  # 0CF0..103F
    {"Cakm", "Mymr", "Tale"},  # 1040..1049
    None,  # 104A..10FA
    {"Geor", "Glag", "Latn"},  # 10FB..10FB
    None,  # 10FC..16EA
    {"Runr"},  # 16EB..16ED
    None,  # 16EE..1734
    {"Buhd", "Hano", "Tagb", "Tglg"},  # 1735..1736
    None,  # 1737..1801
    {"Mong", "Phag"},  # 1802..1803
    None,  # 1804..1804
    {"Mong", "Phag"},  # 1805..1805
    None,  # 1806..1CCF
    {"Beng", "Deva", "Gran", "Knda"},  # 1CD0..1CD0
    {"Deva"},  # 1CD1..1CD1
    {"Beng", "Deva", "Gran", "Knda"},  # 1CD2..1CD2
    {"Deva", "Gran", "Knda"},  # 1CD3..1CD3
    {"Deva"},  # 1CD4..1CD4
    {"Beng", "Deva"},  # 1CD5..1CD6
    {"Deva", "Shrd"},  # 1CD7..1CD7
    {"Beng", "Deva"},  # 1CD8..1CD8
    {"Deva", "Shrd"},  # 1CD9..1CD9
    {"Deva", "Knda", "Mlym", "Orya", "Taml", "Telu"},  # 1CDA..1CDA
    {"Deva"},  # 1CDB..1CDB
    {"Deva", "Shrd"},  # 1CDC..1CDD
    {"Deva"},  # 1CDE..1CDF
    {"Deva", "Shrd"},  # 1CE0..1CE0
    {"Beng", "Deva"},  # 1CE1..1CE1
    {"Deva"},  # 1CE2..1CE8
    {"Deva", "Nand"},  # 1CE9..1CE9
    {"Beng", "Deva"},  # 1CEA..1CEA
    {"Deva"},  # 1CEB..1CEC
    {"Beng", "Deva"},  # 1CED..1CED
    {"Deva"},  # 1CEE..1CF1
    {
        "Beng",
        "Deva",
        "Gran",
        "Knda",
        "Mlym",
        "Nand",
        "Orya",
        "Sinh",
        "Telu",
        "Tirh",
        "Tutg",
    },  # 1CF2..1CF2
    {"Deva", "Gran"},  # 1CF3..1CF3
    {"Deva", "Gran", "Knda", "Tutg"},  # 1CF4..1CF4
    {"Beng", "Deva"},  # 1CF5..1CF6
    {"Beng"},  # 1CF7..1CF7
    {"Deva", "Gran"},  # 1CF8..1CF9
    {"Nand"},  # 1CFA..1CFA
    None,  # 1CFB..1DBF
    {"Grek"},  # 1DC0..1DC1
    None,  # 1DC2..1DF7
    {"Cyrl", "Latn", "Syrc"},  # 1DF8..1DF8
    None,  # 1DF9..1DF9
    {"Syrc"},  # 1DFA..1DFA
    None,  # 1DFB..202E
    {"Latn", "Mong", "Phag"},  # 202F..202F
    None,  # 2030..204E
    {"Adlm", "Arab"},  # 204F..204F
    None,  # 2050..2059
    {"Cari", "Geor", "Glag", "Hung", "Lyci", "Orkh"},  # 205A..205A
    None,  # 205B..205C
    {"Cari", "Grek", "Hung", "Mero"},  # 205D..205D
    None,  # 205E..20EF
    {"Deva", "Gran", "Latn"},  # 20F0..20F0
    None,  # 20F1..2E16
    {"Copt", "Latn"},  # 2E17..2E17
    None,  # 2E18..2E2F
    {"Avst", "Orkh"},  # 2E30..2E30
    {"Avst", "Cari", "Geor", "Hung", "Kthi", "Lydi", "Samr"},  # 2E31..2E31
    None,  # 2E32..2E3B
    {"Dupl"},  # 2E3C..2E3C
    None,  # 2E3D..2E40
    {"Adlm", "Arab", "Hung"},  # 2E41..2E41
    None,  # 2E42..2E42
    {"Cyrl", "Glag"},  # 2E43..2E43
    None,  # 2E44..2FEF
    {"Hani", "Tang"},  # 2FF0..2FFF
    None,  # 3000..3000
    {"Bopo", "Hang", "Hani", "Hira", "Kana", "Mong", "Yiii"},  # 3001..3001
    {"Bopo", "Hang", "Hani", "Hira", "Kana", "Mong", "Phag", "Yiii"},  # 3002..3002
    {"Bopo", "Hang", "Hani", "Hira", "Kana"},  # 3003..3003
    None,  # 3004..3005
    {"Hani"},  # 3006..3006
    None,  # 3007..3007
    {"Bopo", "Hang", "Hani", "Hira", "Kana", "Mong", "Tibt", "Yiii"},  # 3008..3009
    {
        "Bopo",
        "Hang",
        "Hani",
        "Hira",
        "Kana",
        "Lisu",
        "Mong",
        "Tibt",
        "Yiii",
    },  # 300A..300B
    {"Bopo", "Hang", "Hani", "Hira", "Kana", "Yiii"},  # 300C..3011
    None,  # 3012..3012
    {"Bopo", "Hang", "Hani", "Hira", "Kana"},  # 3013..3013
    {"Bopo", "Hang", "Hani", "Hira", "Kana", "Yiii"},  # 3014..301B
    {"Bopo", "Hang", "Hani", "Hira", "Kana"},  # 301C..301F
    None,  # 3020..3029
    {"Bopo", "Hani"},  # 302A..302D
    None,  # 302E..302F
    {"Bopo", "Hang", "Hani", "Hira", "Kana"},  # 3030..3030
    {"Hira", "Kana"},  # 3031..3035
    None,  # 3036..3036
    {"Bopo", "Hang", "Hani", "Hira", "Kana"},  # 3037..3037
    None,  # 3038..303B
    {"Hani", "Hira", "Kana"},  # 303C..303D
    {"Hani"},  # 303E..303F
    None,  # 3040..3098
    {"Hira", "Kana"},  # 3099..309C
    None,  # 309D..309F
    {"Hira", "Kana"},  # 30A0..30A0
    None,  # 30A1..30FA
    {"Bopo", "Hang", "Hani", "Hira", "Kana", "Yiii"},  # 30FB..30FB
    {"Hira", "Kana"},  # 30FC..30FC
    None,  # 30FD..318F
    {"Hani"},  # 3190..319F
    None,  # 31A0..31BF
    {"Hani"},  # 31C0..31E5
    None,  # 31E6..31EE
    {"Hani", "Tang"},  # 31EF..31EF
    None,  # 31F0..321F
    {"Hani"},  # 3220..3247
    None,  # 3248..327F
    {"Hani"},  # 3280..32B0
    None,  # 32B1..32BF
    {"Hani"},  # 32C0..32CB
    None,  # 32CC..32FE
    {"Hani"},  # 32FF..32FF
    None,  # 3300..3357
    {"Hani"},  # 3358..3370
    None,  # 3371..337A
    {"Hani"},  # 337B..337F
    None,  # 3380..33DF
    {"Hani"},  # 33E0..33FE
    None,  # 33FF..A66E
    {"Cyrl", "Glag"},  # A66F..A66F
    None,  # A670..A6FF
    {"Hani", "Latn"},  # A700..A707
    None,  # A708..A82F
    {
        "Deva",
        "Dogr",
        "Gujr",
        "Guru",
        "Khoj",
        "Knda",
        "Kthi",
        "Mahj",
        "Mlym",
        "Modi",
        "Nand",
        "Shrd",
        "Sind",
        "Takr",
        "Tirh",
        "Tutg",
    },  # A830..A832
    {
        "Deva",
        "Dogr",
        "Gujr",
        "Guru",
        "Khoj",
        "Knda",
        "Kthi",
        "Mahj",
        "Modi",
        "Nand",
        "Shrd",
        "Sind",
        "Takr",
        "Tirh",
        "Tutg",
    },  # A833..A835
    {
        "Deva",
        "Dogr",
        "Gujr",
        "Guru",
        "Khoj",
        "Kthi",
        "Mahj",
        "Modi",
        "Sind",
        "Takr",
        "Tirh",
    },  # A836..A837
    {
        "Deva",
        "Dogr",
        "Gujr",
        "Guru",
        "Khoj",
        "Kthi",
        "Mahj",
        "Modi",
        "Shrd",
        "Sind",
        "Takr",
        "Tirh",
    },  # A838..A838
    {
        "Deva",
        "Dogr",
        "Gujr",
        "Guru",
        "Khoj",
        "Kthi",
        "Mahj",
        "Modi",
        "Sind",
        "Takr",
        "Tirh",
    },  # A839..A839
    None,  # A83A..A8F0
    {"Beng", "Deva", "Tutg"},  # A8F1..A8F1
    None,  # A8F2..A8F2
    {"Deva", "Taml"},  # A8F3..A8F3
    None,  # A8F4..A92D
    {"Kali", "Latn", "Mymr"},  # A92E..A92E
    None,  # A92F..A9CE
    {"Bugi", "Java"},  # A9CF..A9CF
    None,  # A9D0..FD3D
    {"Arab", "Nkoo"},  # FD3E..FD3F
    None,  # FD40..FDF1
    {"Arab", "Thaa"},  # FDF2..FDF2
    None,  # FDF3..FDFC
    {"Arab", "Thaa"},  # FDFD..FDFD
    None,  # FDFE..FE44
    {"Bopo", "Hang", "Hani", "Hira", "Kana"},  # FE45..FE46
    None,  # FE47..FF60
    {"Bopo", "Hang", "Hani", "Hira", "Kana", "Yiii"},  # FF61..FF65
    None,  # FF66..FF6F
    {"Hira", "Kana"},  # FF70..FF70
    None,  # FF71..FF9D
    {"Hira", "Kana"},  # FF9E..FF9F
    None,  # FFA0..100FF
    {"Cpmn", "Cprt", "Linb"},  # 10100..10101
    {"Cprt", "Linb"},  # 10102..10102
    None,  # 10103..10106
    {"Cprt", "Lina", "Linb"},  # 10107..10133
    None,  # 10134..10136
    {"Cprt", "Linb"},  # 10137..1013F
    None,  # 10140..102DF
    {"Arab", "Copt"},  # 102E0..102FB
    None,  # 102FC..10AF1
    {"Mani", "Ougr"},  # 10AF2..10AF2
    None,  # 10AF3..11300
    {"Gran", "Taml"},  # 11301..11301
    None,  # 11302..11302
    {"Gran", "Taml"},  # 11303..11303
    None,  # 11304..1133A
    {"Gran", "Taml"},  # 1133B..1133C
    None,  # 1133D..11FCF
    {"Gran", "Taml"},  # 11FD0..11FD1
    None,  # 11FD2..11FD2
    {"Gran", "Taml"},  # 11FD3..11FD3
    None,  # 11FD4..1BC9F
    {"Dupl"},  # 1BCA0..1BCA3
    None,  # 1BCA4..1D35F
    {"Hani"},  # 1D360..1D371
    None,  # 1D372..1F24F
    {"Hani"},  # 1F250..1F251
    None,  # 1F252..10FFFF
]
