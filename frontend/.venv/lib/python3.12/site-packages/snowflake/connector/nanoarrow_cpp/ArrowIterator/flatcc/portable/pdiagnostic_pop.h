#if defined(PDIAGNOSTIC_PUSHED_MSVC)
#if PDIAGNOSTIC_PUSHED_MSVC
#pragma warning( pop )
#endif // PDIAGNOSTIC_PUSHED_MSVC
#undef PDIAGNOSTIC_PUSHED_MSVC
#endif // defined(PDIAGNOSTIC_PUSHED_MSVC)

#if defined(PDIAGNOSTIC_PUSHED_CLANG)
#if PDIAGNOSTIC_PUSHED_CLANG
#pragma clang diagnostic pop
#endif // PDIAGNOSTIC_PUSHED_CLANG
#undef PDIAGNOSTIC_PUSHED_CLANG
#endif // defined(PDIAGNOSTIC_PUSHED_CLANG)

#if defined(PDIAGNOSTIC_PUSHED_GCC)
#if PDIAGNOSTIC_PUSHED_GCC
#pragma GCC diagnostic pop
#endif // PDIAGNOSTIC_PUSHED_GCC
#undef PDIAGNOSTIC_PUSHED_GCC
#endif // defined(PDIAGNOSTIC_PUSHED_GCC)
