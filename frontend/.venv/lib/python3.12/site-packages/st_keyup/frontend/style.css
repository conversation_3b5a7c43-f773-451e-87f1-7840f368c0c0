body {
  -webkit-tap-highlight-color: var(--text-color);
  background-color: var(--background-color);
  color: var(--text-color);
  font-family: var(--font);
}
label {
  font-family: var(--font);
  font-weight: 400;
  line-height: 1.6;
  text-size-adjust: 100%;
  -webkit-tap-highlight-color: var(--text-color);
  -webkit-font-smoothing: auto;
  box-sizing: border-box;
  font-size: 13px;
  color: var(--text-color);
  margin-bottom: 0.5rem;
  height: auto;
  min-height: 1.5rem;
  vertical-align: middle;
  display: flex;
  flex-direction: row;
  -webkit-box-align: center;
  align-items: center;
  position: absolute;
  left: 0;
  top: 2px;
}
.input {
  text-size-adjust: 100%;
  -webkit-tap-highlight-color: var(--text-color);
  -webkit-font-smoothing: auto;
  color: var(--text-color);
  font-family: var(--font);
  font-size: 13px;
  font-weight: normal;
  line-height: 1.6;
  border-radius: 0.45rem;
  transition-duration: 200ms;
  display: flex;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  border-width: 1px;
  border-style: solid;
  transition-property: border;
  transition-timing-function: cubic-bezier(0.2, 0.8, 0.4, 1);
  border-color: var(--background-color);
  background-color: var(--secondary-background-color);
  position: absolute;
  left: 0;
  right: 0;
  bottom: 2px;
  top: 28px;
}
input {
  text-size-adjust: 100%;
  -webkit-tap-highlight-color: var(--text-color);
  -webkit-font-smoothing: auto;
  overflow: visible;
  font-family: var(--font);
  font-size: 15px;
  font-weight: normal;
  line-height: 1.6;
  width: 100%;
  box-sizing: border-box;
  color: var(--text-color);
  background-color: transparent;
  border-width: 0px;
  border-style: none;
  outline: none;
  max-width: 100%;
  cursor: text;
  margin: 0px;
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 14px;
  padding-right: 14px;
  caret-color: var(--text-color);
  min-width: 0px;
}
.input:focus-within {
  text-size-adjust: 100%;
  -webkit-tap-highlight-color: var(--background-color);
  -webkit-font-smoothing: auto;
  color: var(--text-color);
  font-family: var(--font);
  font-size: 1rem;
  font-weight: normal;
  line-height: 1.6;
  border-radius: 0.45rem;
  transition-duration: 200ms;
  display: flex;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  border-width: 1px;
  border-style: solid;
  transition-property: border;
  transition-timing-function: cubic-bezier(0.2, 0.8, 0.4, 1);
  background-color: var(--secondary-background-color);
  padding-left: 0px;
  padding-right: 0px;
  border-color: var(--primary-color);
}
.disabled label {
  color: var(--secondary-background-color) !important;
}

.disabled input {
  cursor: not-allowed;
  color: var(--secondary-background-color) !important;
}

.label-hidden label {
  display: none !important;
}

.label-collapsed label {
  display: none !important;
}

.label-collapsed .input {
  top: 0 !important;
}
