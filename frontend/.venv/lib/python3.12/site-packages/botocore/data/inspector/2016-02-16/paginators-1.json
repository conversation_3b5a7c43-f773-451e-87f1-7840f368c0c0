{"pagination": {"ListFindings": {"result_key": "findingArns", "output_token": "nextToken", "input_token": "nextToken", "limit_key": "maxResults"}, "ListAssessmentTemplates": {"result_key": "assessmentTemplateArns", "output_token": "nextToken", "input_token": "nextToken", "limit_key": "maxResults"}, "PreviewAgents": {"result_key": "agentPreviews", "output_token": "nextToken", "input_token": "nextToken", "limit_key": "maxResults"}, "ListEventSubscriptions": {"result_key": "subscriptions", "output_token": "nextToken", "input_token": "nextToken", "limit_key": "maxResults"}, "ListRulesPackages": {"result_key": "rulesPackageArns", "output_token": "nextToken", "input_token": "nextToken", "limit_key": "maxResults"}, "ListAssessmentRunAgents": {"result_key": "assessmentRunAgents", "output_token": "nextToken", "input_token": "nextToken", "limit_key": "maxResults"}, "ListAssessmentRuns": {"result_key": "assessmentRunArns", "output_token": "nextToken", "input_token": "nextToken", "limit_key": "maxResults"}, "ListAssessmentTargets": {"result_key": "assessmentTargetArns", "output_token": "nextToken", "input_token": "nextToken", "limit_key": "maxResults"}, "ListExclusions": {"input_token": "nextToken", "limit_key": "maxResults", "output_token": "nextToken", "result_key": "exclusionArns"}}}