{"version": "1.0", "examples": {"AllocateAddress": [{"input": {"Domain": "vpc"}, "output": {"AllocationId": "eipalloc-64d5890a", "Domain": "vpc", "PublicIp": "***********"}, "comments": {"input": {}, "output": {}}, "description": "This example allocates an Elastic IP address to use with an instance in a VPC.", "id": "ec2-allocate-address-1", "title": "To allocate an Elastic IP address for EC2-VPC"}, {"output": {"Domain": "standard", "PublicIp": "************"}, "comments": {"input": {}, "output": {}}, "description": "This example allocates an Elastic IP address to use with an instance in EC2-Classic.", "id": "ec2-allocate-address-2", "title": "To allocate an Elastic IP address for EC2-Classic"}], "AssignPrivateIpAddresses": [{"input": {"NetworkInterfaceId": "eni-e5aa89a3", "PrivateIpAddresses": ["*********"]}, "comments": {"input": {}, "output": {}}, "description": "This example assigns the specified secondary private IP address to the specified network interface.", "id": "ec2-assign-private-ip-addresses-1", "title": "To assign a specific secondary private IP address to an interface"}, {"input": {"NetworkInterfaceId": "eni-e5aa89a3", "SecondaryPrivateIpAddressCount": 2}, "comments": {"input": {}, "output": {}}, "description": "This example assigns two secondary private IP addresses to the specified network interface. Amazon EC2 automatically assigns these IP addresses from the available IP addresses in the CIDR block range of the subnet the network interface is associated with.", "id": "ec2-assign-private-ip-addresses-2", "title": "To assign secondary private IP addresses that Amazon EC2 selects to an interface"}], "AssociateAddress": [{"input": {"AllocationId": "eipalloc-64d5890a", "InstanceId": "i-0b263919b6498b123"}, "output": {"AssociationId": "eipassoc-2bebb745"}, "comments": {"input": {}, "output": {}}, "description": "This example associates the specified Elastic IP address with the specified instance in a VPC.", "id": "ec2-associate-address-1", "title": "To associate an Elastic IP address in EC2-VPC"}, {"input": {"AllocationId": "eipalloc-64d5890a", "NetworkInterfaceId": "eni-1a2b3c4d"}, "output": {"AssociationId": "eipassoc-2bebb745"}, "comments": {"input": {}, "output": {}}, "description": "This example associates the specified Elastic IP address with the specified network interface.", "id": "ec2-associate-address-2", "title": "To associate an Elastic IP address with a network interface"}, {"input": {"InstanceId": "i-07ffe74c7330ebf53", "PublicIp": "************"}, "comments": {"input": {}, "output": {}}, "description": "This example associates an Elastic IP address with an instance in EC2-Classic.", "id": "ec2-associate-address-3", "title": "To associate an Elastic IP address in EC2-Classic"}], "AssociateDhcpOptions": [{"input": {"DhcpOptionsId": "dopt-d9070ebb", "VpcId": "vpc-a01106c2"}, "comments": {"input": {}, "output": {}}, "description": "This example associates the specified DHCP options set with the specified VPC.", "id": "ec2-associate-dhcp-options-1", "title": "To associate a DHCP options set with a VPC"}, {"input": {"DhcpOptionsId": "default", "VpcId": "vpc-a01106c2"}, "comments": {"input": {}, "output": {}}, "description": "This example associates the default DHCP options set with the specified VPC.", "id": "ec2-associate-dhcp-options-2", "title": "To associate the default DHCP options set with a VPC"}], "AssociateRouteTable": [{"input": {"RouteTableId": "rtb-22574640", "SubnetId": "subnet-9d4a7b6"}, "output": {"AssociationId": "rtbassoc-781d0d1a"}, "comments": {"input": {}, "output": {}}, "description": "This example associates the specified route table with the specified subnet.", "id": "ec2-associate-route-table-1", "title": "To associate a route table with a subnet"}], "AttachInternetGateway": [{"input": {"InternetGatewayId": "igw-c0a643a9", "VpcId": "vpc-a01106c2"}, "comments": {"input": {}, "output": {}}, "description": "This example attaches the specified Internet gateway to the specified VPC.", "id": "ec2-attach-internet-gateway-1", "title": "To attach an Internet gateway to a VPC"}], "AttachNetworkInterface": [{"input": {"DeviceIndex": 1, "InstanceId": "i-********90abcdef0", "NetworkInterfaceId": "eni-e5aa89a3"}, "output": {"AttachmentId": "eni-attach-66c4350a"}, "comments": {"input": {}, "output": {}}, "description": "This example attaches the specified network interface to the specified instance.", "id": "ec2-attach-network-interface-1", "title": "To attach a network interface to an instance"}], "AttachVolume": [{"input": {"Device": "/dev/sdf", "InstanceId": "i-01474ef662b89480", "VolumeId": "vol-********90abcdef0"}, "output": {"AttachTime": "2016-08-29T18:52:32.724Z", "Device": "/dev/sdf", "InstanceId": "i-01474ef662b89480", "State": "attaching", "VolumeId": "vol-********90abcdef0"}, "comments": {"input": {}, "output": {}}, "description": "This example attaches a volume (``vol-********90abcdef0``) to an instance (``i-01474ef662b89480``) as ``/dev/sdf``.", "id": "to-attach-a-volume-to-an-instance-1472499213109", "title": "To attach a volume to an instance"}], "CancelSpotFleetRequests": [{"input": {"SpotFleetRequestIds": ["sfr-73fbd2ce-aa30-494c-8788-1cee4EXAMPLE"], "TerminateInstances": true}, "output": {"SuccessfulFleetRequests": [{"CurrentSpotFleetRequestState": "cancelled_running", "PreviousSpotFleetRequestState": "active", "SpotFleetRequestId": "sfr-73fbd2ce-aa30-494c-8788-1cee4EXAMPLE"}]}, "comments": {"input": {}, "output": {}}, "description": "This example cancels the specified Spot fleet request and terminates its associated Spot Instances.", "id": "ec2-cancel-spot-fleet-requests-1", "title": "To cancel a Spot fleet request"}, {"input": {"SpotFleetRequestIds": ["sfr-73fbd2ce-aa30-494c-8788-1cee4EXAMPLE"], "TerminateInstances": false}, "output": {"SuccessfulFleetRequests": [{"CurrentSpotFleetRequestState": "cancelled_terminating", "PreviousSpotFleetRequestState": "active", "SpotFleetRequestId": "sfr-73fbd2ce-aa30-494c-8788-1cee4EXAMPLE"}]}, "comments": {"input": {}, "output": {}}, "description": "This example cancels the specified Spot fleet request without terminating its associated Spot Instances.", "id": "ec2-cancel-spot-fleet-requests-2", "title": "To cancel a Spot fleet request without terminating its Spot Instances"}], "CancelSpotInstanceRequests": [{"input": {"SpotInstanceRequestIds": ["sir-08b93456"]}, "output": {"CancelledSpotInstanceRequests": [{"SpotInstanceRequestId": "sir-08b93456", "State": "cancelled"}]}, "comments": {"input": {}, "output": {}}, "description": "This example cancels a Spot Instance request.", "id": "ec2-cancel-spot-instance-requests-1", "title": "To cancel Spot Instance requests"}], "ConfirmProductInstance": [{"input": {"InstanceId": "i-********90abcdef0", "ProductCode": "774F4FF8"}, "output": {"OwnerId": "********9012"}, "comments": {"input": {}, "output": {}}, "description": "This example determines whether the specified product code is associated with the specified instance.", "id": "to-confirm-the-product-instance-1472712108494", "title": "To confirm the product instance"}], "CopySnapshot": [{"input": {"Description": "This is my copied snapshot.", "DestinationRegion": "us-east-1", "SourceRegion": "us-west-2", "SourceSnapshotId": "snap-066877671789bd71b"}, "output": {"SnapshotId": "snap-066877671789bd71b"}, "comments": {"input": {}, "output": {}}, "description": "This example copies a snapshot with the snapshot ID of ``snap-066877671789bd71b`` from the ``us-west-2`` region to the ``us-east-1`` region and adds a short description to identify the snapshot.", "id": "to-copy-a-snapshot-1472502259774", "title": "To copy a snapshot"}], "CreateCustomerGateway": [{"input": {"BgpAsn": 65534, "PublicIp": "********", "Type": "ipsec.1"}, "output": {"CustomerGateway": {"BgpAsn": "65534", "CustomerGatewayId": "cgw-0e11f167", "IpAddress": "********", "State": "available", "Type": "ipsec.1"}}, "comments": {"input": {}, "output": {}}, "description": "This example creates a customer gateway with the specified IP address for its outside interface.", "id": "ec2-create-customer-gateway-1", "title": "To create a customer gateway"}], "CreateDhcpOptions": [{"input": {"DhcpConfigurations": [{"Key": "domain-name-servers", "Values": ["********", "********"]}]}, "output": {"DhcpOptions": {"DhcpConfigurations": [{"Key": "domain-name-servers", "Values": ["********", "********"]}], "DhcpOptionsId": "dopt-d9070ebb"}}, "comments": {"input": {}, "output": {}}, "description": "This example creates a DHCP options set.", "id": "ec2-create-dhcp-options-1", "title": "To create a DHCP options set"}], "CreateInternetGateway": [{"output": {"InternetGateway": {"Attachments": [], "InternetGatewayId": "igw-c0a643a9", "Tags": []}}, "comments": {"input": {}, "output": {}}, "description": "This example creates an Internet gateway.", "id": "ec2-create-internet-gateway-1", "title": "To create an Internet gateway"}], "CreateKeyPair": [{"input": {"KeyName": "my-key-pair"}, "comments": {"input": {}, "output": {}}, "description": "This example creates a key pair named my-key-pair.", "id": "ec2-create-key-pair-1", "title": "To create a key pair"}], "CreateNatGateway": [{"input": {"AllocationId": "eipalloc-37fc1a52", "SubnetId": "subnet-1a2b3c4d"}, "output": {"NatGateway": {"CreateTime": "2015-12-17T12:45:26.732Z", "NatGatewayAddresses": [{"AllocationId": "eipalloc-37fc1a52"}], "NatGatewayId": "nat-08d48af2a8e83edfd", "State": "pending", "SubnetId": "subnet-1a2b3c4d", "VpcId": "vpc-1122aabb"}}, "comments": {"input": {}, "output": {}}, "description": "This example creates a NAT gateway in subnet subnet-1a2b3c4d and associates an Elastic IP address with the allocation ID eipalloc-37fc1a52 with the NAT gateway.", "id": "ec2-create-nat-gateway-1", "title": "To create a NAT gateway"}], "CreateNetworkAcl": [{"input": {"VpcId": "vpc-a01106c2"}, "output": {"NetworkAcl": {"Associations": [], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": false, "NetworkAclId": "acl-5fb85d36", "Tags": [], "VpcId": "vpc-a01106c2"}}, "comments": {"input": {}, "output": {}}, "description": "This example creates a network ACL for the specified VPC.", "id": "ec2-create-network-acl-1", "title": "To create a network ACL"}], "CreateNetworkAclEntry": [{"input": {"CidrBlock": "0.0.0.0/0", "Egress": false, "NetworkAclId": "acl-5fb85d36", "PortRange": {"From": 53, "To": 53}, "Protocol": "udp", "RuleAction": "allow", "RuleNumber": 100}, "comments": {"input": {}, "output": {}}, "description": "This example creates an entry for the specified network ACL. The rule allows ingress traffic from anywhere (0.0.0.0/0) on UDP port 53 (DNS) into any associated subnet.", "id": "ec2-create-network-acl-entry-1", "title": "To create a network ACL entry"}], "CreateNetworkInterface": [{"input": {"Description": "my network interface", "Groups": ["sg-903004f8"], "PrivateIpAddress": "*********", "SubnetId": "subnet-9d4a7b6c"}, "output": {"NetworkInterface": {"AvailabilityZone": "us-east-1d", "Description": "my network interface", "Groups": [{"GroupId": "sg-903004f8", "GroupName": "default"}], "MacAddress": "02:1a:80:41:52:9c", "NetworkInterfaceId": "eni-e5aa89a3", "OwnerId": "********9012", "PrivateIpAddress": "*********", "PrivateIpAddresses": [{"Primary": true, "PrivateIpAddress": "*********"}], "RequesterManaged": false, "SourceDestCheck": true, "Status": "pending", "SubnetId": "subnet-9d4a7b6c", "TagSet": [], "VpcId": "vpc-a01106c2"}}, "comments": {"input": {}, "output": {}}, "description": "This example creates a network interface for the specified subnet.", "id": "ec2-create-network-interface-1", "title": "To create a network interface"}], "CreatePlacementGroup": [{"input": {"GroupName": "my-cluster", "Strategy": "cluster"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example creates a placement group with the specified name.", "id": "to-create-a-placement-group-1472712245768", "title": "To create a placement group"}], "CreateRoute": [{"input": {"DestinationCidrBlock": "0.0.0.0/0", "GatewayId": "igw-c0a643a9", "RouteTableId": "rtb-22574640"}, "comments": {"input": {}, "output": {}}, "description": "This example creates a route for the specified route table. The route matches all traffic (0.0.0.0/0) and routes it to the specified Internet gateway.", "id": "ec2-create-route-1", "title": "To create a route"}], "CreateRouteTable": [{"input": {"VpcId": "vpc-a01106c2"}, "output": {"RouteTable": {"Associations": [], "PropagatingVgws": [], "RouteTableId": "rtb-22574640", "Routes": [{"DestinationCidrBlock": "10.0.0.0/16", "GatewayId": "local", "State": "active"}], "Tags": [], "VpcId": "vpc-a01106c2"}}, "comments": {"input": {}, "output": {}}, "description": "This example creates a route table for the specified VPC.", "id": "ec2-create-route-table-1", "title": "To create a route table"}], "CreateSnapshot": [{"input": {"Description": "This is my root volume snapshot.", "VolumeId": "vol-********90abcdef0"}, "output": {"Description": "This is my root volume snapshot.", "OwnerId": "0********910", "SnapshotId": "snap-066877671789bd71b", "StartTime": "2014-02-28T21:06:01.000Z", "State": "pending", "Tags": [], "VolumeId": "vol-********90abcdef0", "VolumeSize": 8}, "comments": {"input": {}, "output": {}}, "description": "This example creates a snapshot of the volume with a volume ID of ``vol-********90abcdef0`` and a short description to identify the snapshot.", "id": "to-create-a-snapshot-*************", "title": "To create a snapshot"}], "CreateSpotDatafeedSubscription": [{"input": {"Bucket": "my-s3-bucket", "Prefix": "spotdata"}, "output": {"SpotDatafeedSubscription": {"Bucket": "my-s3-bucket", "OwnerId": "********9012", "Prefix": "spotdata", "State": "Active"}}, "comments": {"input": {}, "output": {}}, "description": "This example creates a Spot Instance data feed for your AWS account.", "id": "ec2-create-spot-datafeed-subscription-1", "title": "To create a Spot Instance datafeed"}], "CreateSubnet": [{"input": {"CidrBlock": "********/24", "VpcId": "vpc-a01106c2"}, "output": {"Subnet": {"AvailabilityZone": "us-west-2c", "AvailableIpAddressCount": 251, "CidrBlock": "********/24", "State": "pending", "SubnetId": "subnet-9d4a7b6c", "VpcId": "vpc-a01106c2"}}, "comments": {"input": {}, "output": {}}, "description": "This example creates a subnet in the specified VPC with the specified CIDR block. We recommend that you let us select an Availability Zone for you.", "id": "ec2-create-subnet-1", "title": "To create a subnet"}], "CreateTags": [{"input": {"Resources": ["ami-78a54011"], "Tags": [{"Key": "<PERSON><PERSON>", "Value": "production"}]}, "comments": {"input": {}, "output": {}}, "description": "This example adds the tag Stack=production to the specified image, or overwrites an existing tag for the AMI where the tag key is Stack.", "id": "ec2-create-tags-1", "title": "To add a tag to a resource"}], "CreateVolume": [{"input": {"AvailabilityZone": "us-east-1a", "Size": 80, "VolumeType": "gp2"}, "output": {"AvailabilityZone": "us-east-1a", "CreateTime": "2016-08-29T18:52:32.724Z", "Encrypted": false, "Iops": 240, "Size": 80, "SnapshotId": "", "State": "creating", "VolumeId": "vol-6b60b7c7", "VolumeType": "gp2"}, "comments": {"input": {}, "output": {}}, "description": "This example creates an 80 GiB General Purpose (SSD) volume in the Availability Zone ``us-east-1a``.", "id": "to-create-a-new-volume-1472496724296", "title": "To create a new volume"}, {"input": {"AvailabilityZone": "us-east-1a", "Iops": 1000, "SnapshotId": "snap-066877671789bd71b", "VolumeType": "io1"}, "output": {"Attachments": [], "AvailabilityZone": "us-east-1a", "CreateTime": "2016-08-29T18:52:32.724Z", "Iops": 1000, "Size": 500, "SnapshotId": "snap-066877671789bd71b", "State": "creating", "Tags": [], "VolumeId": "vol-********90abcdef0", "VolumeType": "io1"}, "comments": {"input": {}, "output": {}}, "description": "This example creates a new Provisioned IOPS (SSD) volume with 1000 provisioned IOPS from a snapshot in the Availability Zone ``us-east-1a``.", "id": "to-create-a-new-provisioned-iops-ssd-volume-from-a-snapshot-1472498975176", "title": "To create a new Provisioned IOPS (SSD) volume from a snapshot"}], "CreateVpc": [{"input": {"CidrBlock": "10.0.0.0/16"}, "output": {"Vpc": {"CidrBlock": "10.0.0.0/16", "DhcpOptionsId": "dopt-7a8b9c2d", "InstanceTenancy": "default", "State": "pending", "VpcId": "vpc-a01106c2"}}, "comments": {"input": {}, "output": {}}, "description": "This example creates a VPC with the specified CIDR block.", "id": "ec2-create-vpc-1", "title": "To create a VPC"}], "DeleteCustomerGateway": [{"input": {"CustomerGatewayId": "cgw-0e11f167"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified customer gateway.", "id": "ec2-delete-customer-gateway-1", "title": "To delete a customer gateway"}], "DeleteDhcpOptions": [{"input": {"DhcpOptionsId": "dopt-d9070ebb"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified DHCP options set.", "id": "ec2-delete-dhcp-options-1", "title": "To delete a DHCP options set"}], "DeleteInternetGateway": [{"input": {"InternetGatewayId": "igw-c0a643a9"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified Internet gateway.", "id": "ec2-delete-internet-gateway-1", "title": "To delete an Internet gateway"}], "DeleteKeyPair": [{"input": {"KeyName": "my-key-pair"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified key pair.", "id": "ec2-delete-key-pair-1", "title": "To delete a key pair"}], "DeleteNatGateway": [{"input": {"NatGatewayId": "nat-04ae55e711cec5680"}, "output": {"NatGatewayId": "nat-04ae55e711cec5680"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified NAT gateway.", "id": "ec2-delete-nat-gateway-1", "title": "To delete a NAT gateway"}], "DeleteNetworkAcl": [{"input": {"NetworkAclId": "acl-5fb85d36"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified network ACL.", "id": "ec2-delete-network-acl-1", "title": "To delete a network ACL"}], "DeleteNetworkAclEntry": [{"input": {"Egress": true, "NetworkAclId": "acl-5fb85d36", "RuleNumber": 100}, "comments": {"input": {}, "output": {}}, "description": "This example deletes ingress rule number 100 from the specified network ACL.", "id": "ec2-delete-network-acl-entry-1", "title": "To delete a network ACL entry"}], "DeleteNetworkInterface": [{"input": {"NetworkInterfaceId": "eni-e5aa89a3"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified network interface.", "id": "ec2-delete-network-interface-1", "title": "To delete a network interface"}], "DeletePlacementGroup": [{"input": {"GroupName": "my-cluster"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified placement group.\n", "id": "to-delete-a-placement-group-1472712349959", "title": "To delete a placement group"}], "DeleteRoute": [{"input": {"DestinationCidrBlock": "0.0.0.0/0", "RouteTableId": "rtb-22574640"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified route from the specified route table.", "id": "ec2-delete-route-1", "title": "To delete a route"}], "DeleteRouteTable": [{"input": {"RouteTableId": "rtb-22574640"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified route table.", "id": "ec2-delete-route-table-1", "title": "To delete a route table"}], "DeleteSnapshot": [{"input": {"SnapshotId": "snap-********90abcdef0"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example deletes a snapshot with the snapshot ID of ``snap-********90abcdef0``. If the command succeeds, no output is returned.", "id": "to-delete-a-snapshot-*************", "title": "To delete a snapshot"}], "DeleteSpotDatafeedSubscription": [{"comments": {"input": {}, "output": {}}, "description": "This example deletes a Spot data feed subscription for the account.", "id": "ec2-delete-spot-datafeed-subscription-1", "title": "To cancel a Spot Instance data feed subscription"}], "DeleteSubnet": [{"input": {"SubnetId": "subnet-9d4a7b6c"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified subnet.", "id": "ec2-delete-subnet-1", "title": "To delete a subnet"}], "DeleteTags": [{"input": {"Resources": ["ami-78a54011"], "Tags": [{"Key": "<PERSON><PERSON>", "Value": "test"}]}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the tag Stack=test from the specified image.", "id": "ec2-delete-tags-1", "title": "To delete a tag from a resource"}], "DeleteVolume": [{"input": {"VolumeId": "vol-049df61146c4d7901"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example deletes an available volume with the volume ID of ``vol-049df61146c4d7901``. If the command succeeds, no output is returned.", "id": "to-delete-a-volume-*************", "title": "To delete a volume"}], "DeleteVpc": [{"input": {"VpcId": "vpc-a01106c2"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified VPC.", "id": "ec2-delete-vpc-1", "title": "To delete a VPC"}], "DescribeAccountAttributes": [{"input": {"AttributeNames": ["supported-platforms"]}, "output": {"AccountAttributes": [{"AttributeName": "supported-platforms", "AttributeValues": [{"AttributeValue": "EC2"}, {"AttributeValue": "VPC"}]}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the supported-platforms attribute for your AWS account.", "id": "ec2-describe-account-attributes-1", "title": "To describe a single attribute for your AWS account"}, {"output": {"AccountAttributes": [{"AttributeName": "supported-platforms", "AttributeValues": [{"AttributeValue": "EC2"}, {"AttributeValue": "VPC"}]}, {"AttributeName": "vpc-max-security-groups-per-interface", "AttributeValues": [{"AttributeValue": "5"}]}, {"AttributeName": "max-elastic-ips", "AttributeValues": [{"AttributeValue": "5"}]}, {"AttributeName": "max-instances", "AttributeValues": [{"AttributeValue": "20"}]}, {"AttributeName": "vpc-max-elastic-ips", "AttributeValues": [{"AttributeValue": "5"}]}, {"AttributeName": "default-vpc", "AttributeValues": [{"AttributeValue": "none"}]}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the attributes for your AWS account.", "id": "ec2-describe-account-attributes-2", "title": "To describe all attributes for your AWS account"}], "DescribeAddresses": [{"output": {"Addresses": [{"Domain": "standard", "InstanceId": "i-********90abcdef0", "PublicIp": "************"}, {"AllocationId": "eipalloc-********", "AssociationId": "eipassoc-********", "Domain": "vpc", "InstanceId": "i-********90abcdef0", "NetworkInterfaceId": "eni-********", "NetworkInterfaceOwnerId": "********9012", "PrivateIpAddress": "**********", "PublicIp": "***********"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes your Elastic IP addresses.", "id": "ec2-describe-addresses-1", "title": "To describe your Elastic IP addresses"}, {"input": {"Filters": [{"Name": "domain", "Values": ["vpc"]}]}, "output": {"Addresses": [{"AllocationId": "eipalloc-********", "AssociationId": "eipassoc-********", "Domain": "vpc", "InstanceId": "i-********90abcdef0", "NetworkInterfaceId": "eni-********", "NetworkInterfaceOwnerId": "********9012", "PrivateIpAddress": "**********", "PublicIp": "***********"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes your Elastic IP addresses for use with instances in a VPC.", "id": "ec2-describe-addresses-2", "title": "To describe your Elastic IP addresses for EC2-VPC"}, {"input": {"Filters": [{"Name": "domain", "Values": ["standard"]}]}, "output": {"Addresses": [{"Domain": "standard", "InstanceId": "i-********90abcdef0", "PublicIp": "************"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes your Elastic IP addresses for use with instances in EC2-Classic.", "id": "ec2-describe-addresses-3", "title": "To describe your Elastic IP addresses for EC2-Classic"}], "DescribeAvailabilityZones": [{"output": {"AvailabilityZones": [{"Messages": [], "RegionName": "us-east-1", "State": "available", "ZoneName": "us-east-1b"}, {"Messages": [], "RegionName": "us-east-1", "State": "available", "ZoneName": "us-east-1c"}, {"Messages": [], "RegionName": "us-east-1", "State": "available", "ZoneName": "us-east-1d"}, {"Messages": [], "RegionName": "us-east-1", "State": "available", "ZoneName": "us-east-1e"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the Availability Zones that are available to you. The response includes Availability Zones only for the current region.", "id": "ec2-describe-availability-zones-1", "title": "To describe your Availability Zones"}], "DescribeCustomerGateways": [{"input": {"CustomerGatewayIds": ["cgw-0e11f167"]}, "output": {"CustomerGateways": [{"BgpAsn": "65534", "CustomerGatewayId": "cgw-0e11f167", "IpAddress": "********", "State": "available", "Type": "ipsec.1"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the specified customer gateway.", "id": "ec2-describe-customer-gateways-1", "title": "To describe a customer gateway"}], "DescribeDhcpOptions": [{"input": {"DhcpOptionsIds": ["dopt-d9070ebb"]}, "output": {"DhcpOptions": [{"DhcpConfigurations": [{"Key": "domain-name-servers", "Values": ["********", "********"]}], "DhcpOptionsId": "dopt-d9070ebb"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the specified DHCP options set.", "id": "ec2-describe-dhcp-options-1", "title": "To describe a DHCP options set"}], "DescribeInstanceAttribute": [{"input": {"Attribute": "instanceType", "InstanceId": "i-********90abcdef0"}, "output": {"InstanceId": "i-********90abcdef0", "InstanceType": {"Value": "t1.micro"}}, "comments": {"input": {}, "output": {}}, "description": "This example describes the instance type of the specified instance.\n", "id": "to-describe-the-instance-type-1472712432132", "title": "To describe the instance type"}, {"input": {"Attribute": "disableApiTermination", "InstanceId": "i-********90abcdef0"}, "output": {"DisableApiTermination": {"Value": "false"}, "InstanceId": "i-********90abcdef0"}, "comments": {"input": {}, "output": {}}, "description": "This example describes the ``disableApiTermination`` attribute of the specified instance.\n", "id": "to-describe-the-disableapitermination-attribute-1472712533466", "title": "To describe the disableApiTermination attribute"}, {"input": {"Attribute": "blockDeviceMapping", "InstanceId": "i-********90abcdef0"}, "output": {"BlockDeviceMappings": [{"DeviceName": "/dev/sda1", "Ebs": {"AttachTime": "2013-05-17T22:42:34.000Z", "DeleteOnTermination": true, "Status": "attached", "VolumeId": "vol-049df61146c4d7901"}}, {"DeviceName": "/dev/sdf", "Ebs": {"AttachTime": "2013-09-10T23:07:00.000Z", "DeleteOnTermination": false, "Status": "attached", "VolumeId": "vol-049df61146c4d7901"}}], "InstanceId": "i-********90abcdef0"}, "comments": {"input": {}, "output": {}}, "description": "This example describes the ``blockDeviceMapping`` attribute of the specified instance.\n", "id": "to-describe-the-block-device-mapping-for-an-instance-1472712645423", "title": "To describe the block device mapping for an instance"}], "DescribeInternetGateways": [{"input": {"Filters": [{"Name": "attachment.vpc-id", "Values": ["vpc-a01106c2"]}]}, "output": {"InternetGateways": [{"Attachments": [{"State": "available", "VpcId": "vpc-a01106c2"}], "InternetGatewayId": "igw-c0a643a9", "Tags": []}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the Internet gateway for the specified VPC.", "id": "ec2-describe-internet-gateways-1", "title": "To describe the Internet gateway for a VPC"}], "DescribeKeyPairs": [{"input": {"KeyNames": ["my-key-pair"]}, "output": {"KeyPairs": [{"KeyFingerprint": "1f:51:ae:28:bf:89:e9:d8:1f:25:5d:37:2d:7d:b8:ca:9f:f5:f1:6f", "KeyName": "my-key-pair"}]}, "comments": {"input": {}, "output": {}}, "description": "This example displays the fingerprint for the specified key.", "id": "ec2-describe-key-pairs-1", "title": "To display a key pair"}], "DescribeMovingAddresses": [{"output": {"MovingAddressStatuses": [{"MoveStatus": "MovingToVpc", "PublicIp": "************"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes all of your moving Elastic IP addresses.", "id": "ec2-describe-moving-addresses-1", "title": "To describe your moving addresses"}], "DescribeNatGateways": [{"input": {"Filters": [{"Name": "vpc-id", "Values": ["vpc-1a2b3c4d"]}]}, "output": {"NatGateways": [{"CreateTime": "2015-12-01T12:26:55.983Z", "NatGatewayAddresses": [{"AllocationId": "eipalloc-89c620ec", "NetworkInterfaceId": "eni-9dec76cd", "PrivateIp": "**********", "PublicIp": "198.11.222.333"}], "NatGatewayId": "nat-05dba92075d71c408", "State": "available", "SubnetId": "subnet-847e4dc2", "VpcId": "vpc-1a2b3c4d"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the NAT gateway for the specified VPC.", "id": "ec2-describe-nat-gateways-1", "title": "To describe a NAT gateway"}], "DescribeNetworkAcls": [{"input": {"NetworkAclIds": ["acl-5fb85d36"]}, "output": {"NetworkAcls": [{"Associations": [{"NetworkAclAssociationId": "aclassoc-66ea5f0b", "NetworkAclId": "acl-9aeb5ef7", "SubnetId": "subnet-65ea5f08"}], "Entries": [{"CidrBlock": "0.0.0.0/0", "Egress": true, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}, {"CidrBlock": "0.0.0.0/0", "Egress": false, "Protocol": "-1", "RuleAction": "deny", "RuleNumber": 32767}], "IsDefault": false, "NetworkAclId": "acl-5fb85d36", "Tags": [], "VpcId": "vpc-a01106c2"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the specified network ACL.", "id": "ec2-", "title": "To describe a network ACL"}], "DescribeNetworkInterfaceAttribute": [{"input": {"Attribute": "attachment", "NetworkInterfaceId": "eni-686ea200"}, "output": {"Attachment": {"AttachTime": "2015-05-21T20:02:20.000Z", "AttachmentId": "eni-attach-43348162", "DeleteOnTermination": true, "DeviceIndex": 0, "InstanceId": "i-********90abcdef0", "InstanceOwnerId": "********9012", "Status": "attached"}, "NetworkInterfaceId": "eni-686ea200"}, "comments": {"input": {}, "output": {}}, "description": "This example describes the attachment attribute of the specified network interface.", "id": "ec2-describe-network-interface-attribute-1", "title": "To describe the attachment attribute of a network interface"}, {"input": {"Attribute": "description", "NetworkInterfaceId": "eni-686ea200"}, "output": {"Description": {"Value": "My description"}, "NetworkInterfaceId": "eni-686ea200"}, "comments": {"input": {}, "output": {}}, "description": "This example describes the description attribute of the specified network interface.", "id": "ec2-describe-network-interface-attribute-2", "title": "To describe the description attribute of a network interface"}, {"input": {"Attribute": "groupSet", "NetworkInterfaceId": "eni-686ea200"}, "output": {"Groups": [{"GroupId": "sg-903004f8", "GroupName": "my-security-group"}], "NetworkInterfaceId": "eni-686ea200"}, "comments": {"input": {}, "output": {}}, "description": "This example describes the groupSet attribute of the specified network interface.", "id": "ec2-describe-network-interface-attribute-3", "title": "To describe the groupSet attribute of a network interface"}, {"input": {"Attribute": "sourceDestCheck", "NetworkInterfaceId": "eni-686ea200"}, "output": {"NetworkInterfaceId": "eni-686ea200", "SourceDestCheck": {"Value": true}}, "comments": {"input": {}, "output": {}}, "description": "This example describes the sourceDestCheck attribute of the specified network interface.", "id": "ec2-describe-network-interface-attribute-4", "title": "To describe the sourceDestCheck attribute of a network interface"}], "DescribeNetworkInterfaces": [{"input": {"NetworkInterfaceIds": ["eni-e5aa89a3"]}, "output": {"NetworkInterfaces": [{"Association": {"AssociationId": "eipassoc-0fbb766a", "IpOwnerId": "********9012", "PublicDnsName": "ec2-203-0-113-12.compute-1.amazonaws.com", "PublicIp": "************"}, "Attachment": {"AttachTime": "2013-11-30T23:36:42.000Z", "AttachmentId": "eni-attach-66c4350a", "DeleteOnTermination": false, "DeviceIndex": 1, "InstanceId": "i-********90abcdef0", "InstanceOwnerId": "********9012", "Status": "attached"}, "AvailabilityZone": "us-east-1d", "Description": "my network interface", "Groups": [{"GroupId": "sg-8637d3e3", "GroupName": "default"}], "MacAddress": "02:2f:8f:b0:cf:75", "NetworkInterfaceId": "eni-e5aa89a3", "OwnerId": "********9012", "PrivateDnsName": "ip-10-0-1-17.ec2.internal", "PrivateIpAddress": "*********", "PrivateIpAddresses": [{"Association": {"AssociationId": "eipassoc-0fbb766a", "IpOwnerId": "********9012", "PublicDnsName": "ec2-203-0-113-12.compute-1.amazonaws.com", "PublicIp": "************"}, "Primary": true, "PrivateDnsName": "ip-10-0-1-17.ec2.internal", "PrivateIpAddress": "*********"}], "RequesterManaged": false, "SourceDestCheck": true, "Status": "in-use", "SubnetId": "subnet-b61f49f0", "TagSet": [], "VpcId": "vpc-a01106c2"}]}, "comments": {"input": {}, "output": {}}, "description": "", "id": "ec2-describe-network-interfaces-1", "title": "To describe a network interface"}], "DescribeRegions": [{"output": {"Regions": [{"Endpoint": "ec2.ap-south-1.amazonaws.com", "RegionName": "ap-south-1"}, {"Endpoint": "ec2.eu-west-1.amazonaws.com", "RegionName": "eu-west-1"}, {"Endpoint": "ec2.ap-southeast-1.amazonaws.com", "RegionName": "ap-southeast-1"}, {"Endpoint": "ec2.ap-southeast-2.amazonaws.com", "RegionName": "ap-southeast-2"}, {"Endpoint": "ec2.eu-central-1.amazonaws.com", "RegionName": "eu-central-1"}, {"Endpoint": "ec2.ap-northeast-2.amazonaws.com", "RegionName": "ap-northeast-2"}, {"Endpoint": "ec2.ap-northeast-1.amazonaws.com", "RegionName": "ap-northeast-1"}, {"Endpoint": "ec2.us-east-1.amazonaws.com", "RegionName": "us-east-1"}, {"Endpoint": "ec2.sa-east-1.amazonaws.com", "RegionName": "sa-east-1"}, {"Endpoint": "ec2.us-west-1.amazonaws.com", "RegionName": "us-west-1"}, {"Endpoint": "ec2.us-west-2.amazonaws.com", "RegionName": "us-west-2"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes all the regions that are available to you.", "id": "ec2-describe-regions-1", "title": "To describe your regions"}], "DescribeRouteTables": [{"input": {"RouteTableIds": ["rtb-1f382e7d"]}, "output": {"RouteTables": [{"Associations": [{"Main": true, "RouteTableAssociationId": "rtbassoc-d8ccddba", "RouteTableId": "rtb-1f382e7d"}], "PropagatingVgws": [], "RouteTableId": "rtb-1f382e7d", "Routes": [{"DestinationCidrBlock": "10.0.0.0/16", "GatewayId": "local", "State": "active"}], "Tags": [], "VpcId": "vpc-a01106c2"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the specified route table.", "id": "ec2-describe-route-tables-1", "title": "To describe a route table"}], "DescribeScheduledInstanceAvailability": [{"input": {"FirstSlotStartTimeRange": {"EarliestTime": "2016-01-31T00:00:00Z", "LatestTime": "2016-01-31T04:00:00Z"}, "Recurrence": {"Frequency": "Weekly", "Interval": 1, "OccurrenceDays": [1]}}, "output": {"ScheduledInstanceAvailabilitySet": [{"AvailabilityZone": "us-west-2b", "AvailableInstanceCount": 20, "FirstSlotStartTime": "2016-01-31T00:00:00Z", "HourlyPrice": "0.095", "InstanceType": "c4.large", "MaxTermDurationInDays": 366, "MinTermDurationInDays": 366, "NetworkPlatform": "EC2-VPC", "Platform": "Linux/UNIX", "PurchaseToken": "eyJ2IjoiMSIsInMiOjEsImMiOi...", "Recurrence": {"Frequency": "Weekly", "Interval": 1, "OccurrenceDaySet": [1], "OccurrenceRelativeToEnd": false}, "SlotDurationInHours": 23, "TotalScheduledInstanceHours": 1219}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes a schedule that occurs every week on Sunday, starting on the specified date. Note that the output contains a single schedule as an example.", "id": "ec2-describe-scheduled-instance-availability-1", "title": "To describe an available schedule"}], "DescribeScheduledInstances": [{"input": {"ScheduledInstanceIds": ["sci-1234-1234-1234-1234-********9012"]}, "output": {"ScheduledInstanceSet": [{"AvailabilityZone": "us-west-2b", "CreateDate": "2016-01-25T21:43:38.612Z", "HourlyPrice": "0.095", "InstanceCount": 1, "InstanceType": "c4.large", "NetworkPlatform": "EC2-VPC", "NextSlotStartTime": "2016-01-31T09:00:00Z", "Platform": "Linux/UNIX", "Recurrence": {"Frequency": "Weekly", "Interval": 1, "OccurrenceDaySet": [1], "OccurrenceRelativeToEnd": false, "OccurrenceUnit": ""}, "ScheduledInstanceId": "sci-1234-1234-1234-1234-********9012", "SlotDurationInHours": 32, "TermEndDate": "2017-01-31T09:00:00Z", "TermStartDate": "2016-01-31T09:00:00Z", "TotalScheduledInstanceHours": 1696}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the specified Scheduled Instance.", "id": "ec2-describe-scheduled-instances-1", "title": "To describe your Scheduled Instances"}], "DescribeSnapshotAttribute": [{"input": {"Attribute": "createVolumePermission", "SnapshotId": "snap-066877671789bd71b"}, "output": {"CreateVolumePermissions": [], "SnapshotId": "snap-066877671789bd71b"}, "comments": {"input": {}, "output": {}}, "description": "This example describes the ``createVolumePermission`` attribute on a snapshot with the snapshot ID of ``snap-066877671789bd71b``.", "id": "to-describe-snapshot-attributes-1472503199736", "title": "To describe snapshot attributes"}], "DescribeSnapshots": [{"input": {"SnapshotIds": ["snap-********90abcdef0"]}, "output": {"NextToken": "", "Snapshots": [{"Description": "This is my snapshot.", "OwnerId": "0********910", "Progress": "100%", "SnapshotId": "snap-********90abcdef0", "StartTime": "2014-02-28T21:28:32.000Z", "State": "completed", "VolumeId": "vol-049df61146c4d7901", "VolumeSize": 8}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes a snapshot with the snapshot ID of ``snap-********90abcdef0``.", "id": "to-describe-a-snapshot-1472503807850", "title": "To describe a snapshot"}, {"input": {"Filters": [{"Name": "status", "Values": ["pending"]}], "OwnerIds": ["0********910"]}, "output": {"NextToken": "", "Snapshots": [{"Description": "This is my copied snapshot.", "OwnerId": "0********910", "Progress": "87%", "SnapshotId": "snap-066877671789bd71b", "StartTime": "2014-02-28T21:37:27.000Z", "State": "pending", "VolumeId": "vol-********90abcdef0", "VolumeSize": 8}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes all snapshots owned by the ID 0********910 that are in the ``pending`` status.", "id": "to-describe-snapshots-using-filters-*************", "title": "To describe snapshots using filters"}], "DescribeSpotDatafeedSubscription": [{"output": {"SpotDatafeedSubscription": {"Bucket": "my-s3-bucket", "OwnerId": "********9012", "Prefix": "spotdata", "State": "Active"}}, "comments": {"input": {}, "output": {}}, "description": "This example describes the Spot Instance datafeed subscription for your AWS account.", "id": "ec2-describe-spot-datafeed-subscription-1", "title": "To describe the datafeed for your AWS account"}], "DescribeSpotFleetInstances": [{"input": {"SpotFleetRequestId": "sfr-73fbd2ce-aa30-494c-8788-1cee4EXAMPLE"}, "output": {"ActiveInstances": [{"InstanceId": "i-********90abcdef0", "InstanceType": "m3.medium", "SpotInstanceRequestId": "sir-08b93456"}], "SpotFleetRequestId": "sfr-73fbd2ce-aa30-494c-8788-1cee4EXAMPLE"}, "comments": {"input": {}, "output": {}}, "description": "This example lists the Spot Instances associated with the specified Spot fleet.", "id": "ec2-describe-spot-fleet-instances-1", "title": "To describe the Spot Instances associated with a Spot fleet"}], "DescribeSpotFleetRequestHistory": [{"input": {"SpotFleetRequestId": "sfr-73fbd2ce-aa30-494c-8788-1cee4EXAMPLE", "StartTime": "2015-05-26T00:00:00Z"}, "output": {"HistoryRecords": [{"EventInformation": {"EventSubType": "submitted"}, "EventType": "fleetRequestChange", "Timestamp": "2015-05-26T23:17:20.697Z"}, {"EventInformation": {"EventSubType": "active"}, "EventType": "fleetRequestChange", "Timestamp": "2015-05-26T23:17:20.873Z"}, {"EventInformation": {"EventSubType": "launched", "InstanceId": "i-********90abcdef0"}, "EventType": "instanceChange", "Timestamp": "2015-05-26T23:21:21.712Z"}, {"EventInformation": {"EventSubType": "launched", "InstanceId": "i-********90abcdef1"}, "EventType": "instanceChange", "Timestamp": "2015-05-26T23:21:21.816Z"}], "NextToken": "CpHNsscimcV5oH7bSbub03CI2Qms5+ypNpNm+53MNlR0YcXAkp0xFlfKf91yVxSExmbtma3awYxMFzNA663ZskT0AHtJ6TCb2Z8bQC2EnZgyELbymtWPfpZ1ZbauVg+P+TfGlWxWWB/Vr5dk5d4LfdgA/DRAHUrYgxzrEXAMPLE=", "SpotFleetRequestId": "sfr-73fbd2ce-aa30-494c-8788-1cee4EXAMPLE", "StartTime": "2015-05-26T00:00:00Z"}, "comments": {"input": {}, "output": {}}, "description": "This example returns the history for the specified Spot fleet starting at the specified time.", "id": "ec2-describe-spot-fleet-request-history-1", "title": "To describe Spot fleet history"}], "DescribeSpotFleetRequests": [{"input": {"SpotFleetRequestIds": ["sfr-73fbd2ce-aa30-494c-8788-1cee4EXAMPLE"]}, "output": {"SpotFleetRequestConfigs": [{"SpotFleetRequestConfig": {"IamFleetRole": "arn:aws:iam::********9012:role/my-spot-fleet-role", "LaunchSpecifications": [{"EbsOptimized": false, "ImageId": "ami-1a2b3c4d", "InstanceType": "cc2.8xlarge", "NetworkInterfaces": [{"AssociatePublicIpAddress": true, "DeleteOnTermination": false, "DeviceIndex": 0, "SecondaryPrivateIpAddressCount": 0, "SubnetId": "subnet-a61dafcf"}]}, {"EbsOptimized": false, "ImageId": "ami-1a2b3c4d", "InstanceType": "r3.8xlarge", "NetworkInterfaces": [{"AssociatePublicIpAddress": true, "DeleteOnTermination": false, "DeviceIndex": 0, "SecondaryPrivateIpAddressCount": 0, "SubnetId": "subnet-a61dafcf"}]}], "SpotPrice": "0.05", "TargetCapacity": 20}, "SpotFleetRequestId": "sfr-73fbd2ce-aa30-494c-8788-1cee4EXAMPLE", "SpotFleetRequestState": "active"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the specified Spot fleet request.", "id": "ec2-describe-spot-fleet-requests-1", "title": "To describe a Spot fleet request"}], "DescribeSpotInstanceRequests": [{"input": {"SpotInstanceRequestIds": ["sir-08b93456"]}, "output": {"SpotInstanceRequests": [{"CreateTime": "2014-04-30T18:14:55.000Z", "InstanceId": "i-********90abcdef0", "LaunchSpecification": {"BlockDeviceMappings": [{"DeviceName": "/dev/sda1", "Ebs": {"DeleteOnTermination": true, "VolumeSize": 8, "VolumeType": "standard"}}], "EbsOptimized": false, "ImageId": "ami-7aba833f", "InstanceType": "m1.small", "KeyName": "my-key-pair", "SecurityGroups": [{"GroupId": "sg-e38f24a7", "GroupName": "my-security-group"}]}, "LaunchedAvailabilityZone": "us-west-1b", "ProductDescription": "Linux/UNIX", "SpotInstanceRequestId": "sir-08b93456", "SpotPrice": "0.010000", "State": "active", "Status": {"Code": "fulfilled", "Message": "Your Spot request is fulfilled.", "UpdateTime": "2014-04-30T18:16:21.000Z"}, "Type": "one-time"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the specified Spot Instance request.", "id": "ec2-describe-spot-instance-requests-1", "title": "To describe a Spot Instance request"}], "DescribeSpotPriceHistory": [{"input": {"EndTime": "2014-01-06T08:09:10", "InstanceTypes": ["m1.xlarge"], "ProductDescriptions": ["Linux/UNIX (Amazon VPC)"], "StartTime": "2014-01-06T07:08:09"}, "output": {"SpotPriceHistory": [{"AvailabilityZone": "us-west-1a", "InstanceType": "m1.xlarge", "ProductDescription": "Linux/UNIX (Amazon VPC)", "SpotPrice": "0.080000", "Timestamp": "2014-01-06T04:32:53.000Z"}, {"AvailabilityZone": "us-west-1c", "InstanceType": "m1.xlarge", "ProductDescription": "Linux/UNIX (Amazon VPC)", "SpotPrice": "0.080000", "Timestamp": "2014-01-05T11:28:26.000Z"}]}, "comments": {"input": {}, "output": {}}, "description": "This example returns the Spot Price history for m1.xlarge, Linux/UNIX (Amazon VPC) instances for a particular day in January.", "id": "ec2-describe-spot-price-history-1", "title": "To describe Spot price history for Linux/UNIX (Amazon VPC)"}], "DescribeSubnets": [{"input": {"Filters": [{"Name": "vpc-id", "Values": ["vpc-a01106c2"]}]}, "output": {"Subnets": [{"AvailabilityZone": "us-east-1c", "AvailableIpAddressCount": 251, "CidrBlock": "********/24", "DefaultForAz": false, "MapPublicIpOnLaunch": false, "State": "available", "SubnetId": "subnet-9d4a7b6c", "VpcId": "vpc-a01106c2"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the subnets for the specified VPC.", "id": "ec2-describe-subnets-1", "title": "To describe the subnets for a VPC"}], "DescribeTags": [{"input": {"Filters": [{"Name": "resource-id", "Values": ["i-********90abcdef8"]}]}, "output": {"Tags": [{"Key": "<PERSON><PERSON>", "ResourceId": "i-********90abcdef8", "ResourceType": "instance", "Value": "test"}, {"Key": "Name", "ResourceId": "i-********90abcdef8", "ResourceType": "instance", "Value": "Beta Server"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the tags for the specified instance.", "id": "ec2-describe-tags-1", "title": "To describe the tags for a single resource"}], "DescribeVolumeAttribute": [{"input": {"Attribute": "autoEnableIO", "VolumeId": "vol-049df61146c4d7901"}, "output": {"AutoEnableIO": {"Value": false}, "VolumeId": "vol-049df61146c4d7901"}, "comments": {"input": {}, "output": {}}, "description": "This example describes the ``autoEnableIo`` attribute of the volume with the ID ``vol-049df61146c4d7901``.", "id": "to-describe-a-volume-attribute-1472505773492", "title": "To describe a volume attribute"}], "DescribeVolumeStatus": [{"input": {"VolumeIds": ["vol-********90abcdef0"]}, "output": {"VolumeStatuses": [{"Actions": [], "AvailabilityZone": "us-east-1a", "Events": [], "VolumeId": "vol-********90abcdef0", "VolumeStatus": {"Details": [{"Name": "io-enabled", "Status": "passed"}, {"Name": "io-performance", "Status": "not-applicable"}], "Status": "ok"}}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the status for the volume ``vol-********90abcdef0``.", "id": "to-describe-the-status-of-a-single-volume-1472507016193", "title": "To describe the status of a single volume"}, {"input": {"Filters": [{"Name": "volume-status.status", "Values": ["impaired"]}]}, "output": {"VolumeStatuses": []}, "comments": {"input": {}, "output": {}}, "description": "This example describes the status for all volumes that are impaired. In this example output, there are no impaired volumes.", "id": "to-describe-the-status-of-impaired-volumes-1472507239821", "title": "To describe the status of impaired volumes"}], "DescribeVolumes": [{"input": {}, "output": {"NextToken": "", "Volumes": [{"Attachments": [{"AttachTime": "2013-12-18T22:35:00.000Z", "DeleteOnTermination": true, "Device": "/dev/sda1", "InstanceId": "i-********90abcdef0", "State": "attached", "VolumeId": "vol-049df61146c4d7901"}], "AvailabilityZone": "us-east-1a", "CreateTime": "2013-12-18T22:35:00.084Z", "Size": 8, "SnapshotId": "snap-********90abcdef0", "State": "in-use", "VolumeId": "vol-049df61146c4d7901", "VolumeType": "standard"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes all of your volumes in the default region.", "id": "to-describe-all-volumes-1472506358883", "title": "To describe all volumes"}, {"input": {"Filters": [{"Name": "attachment.instance-id", "Values": ["i-********90abcdef0"]}, {"Name": "attachment.delete-on-termination", "Values": ["true"]}]}, "output": {"Volumes": [{"Attachments": [{"AttachTime": "2013-12-18T22:35:00.000Z", "DeleteOnTermination": true, "Device": "/dev/sda1", "InstanceId": "i-********90abcdef0", "State": "attached", "VolumeId": "vol-049df61146c4d7901"}], "AvailabilityZone": "us-east-1a", "CreateTime": "2013-12-18T22:35:00.084Z", "Size": 8, "SnapshotId": "snap-********90abcdef0", "State": "in-use", "VolumeId": "vol-049df61146c4d7901", "VolumeType": "standard"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes all volumes that are both attached to the instance with the ID i-********90abcdef0 and set to delete when the instance terminates.", "id": "to-describe-volumes-that-are-attached-to-a-specific-instance-1472506613578", "title": "To describe volumes that are attached to a specific instance"}], "DescribeVpcAttribute": [{"input": {"Attribute": "enableDnsSupport", "VpcId": "vpc-a01106c2"}, "output": {"EnableDnsSupport": {"Value": true}, "VpcId": "vpc-a01106c2"}, "comments": {"input": {}, "output": {}}, "description": "This example describes the enableDnsSupport attribute. This attribute indicates whether DNS resolution is enabled for the VPC. If this attribute is true, the Amazon DNS server resolves DNS hostnames for your instances to their corresponding IP addresses; otherwise, it does not.", "id": "ec2-describe-vpc-attribute-1", "title": "To describe the enableDnsSupport attribute"}, {"input": {"Attribute": "enableDnsHostnames", "VpcId": "vpc-a01106c2"}, "output": {"EnableDnsHostnames": {"Value": true}, "VpcId": "vpc-a01106c2"}, "comments": {"input": {}, "output": {}}, "description": "This example describes the enableDnsHostnames attribute. This attribute indicates whether the instances launched in the VPC get DNS hostnames. If this attribute is true, instances in the VPC get DNS hostnames; otherwise, they do not.", "id": "ec2-describe-vpc-attribute-2", "title": "To describe the enableDnsHostnames attribute"}], "DescribeVpcs": [{"input": {"VpcIds": ["vpc-a01106c2"]}, "output": {"Vpcs": [{"CidrBlock": "10.0.0.0/16", "DhcpOptionsId": "dopt-7a8b9c2d", "InstanceTenancy": "default", "IsDefault": false, "State": "available", "Tags": [{"Key": "Name", "Value": "MyVPC"}], "VpcId": "vpc-a01106c2"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the specified VPC.", "id": "ec2-describe-vpcs-1", "title": "To describe a VPC"}], "DetachInternetGateway": [{"input": {"InternetGatewayId": "igw-c0a643a9", "VpcId": "vpc-a01106c2"}, "comments": {"input": {}, "output": {}}, "description": "This example detaches the specified Internet gateway from the specified VPC.", "id": "ec2-detach-internet-gateway-1", "title": "To detach an Internet gateway from a VPC"}], "DetachNetworkInterface": [{"input": {"AttachmentId": "eni-attach-66c4350a"}, "comments": {"input": {}, "output": {}}, "description": "This example detaches the specified network interface from its attached instance.", "id": "ec2-detach-network-interface-1", "title": "To detach a network interface from an instance"}], "DetachVolume": [{"input": {"VolumeId": "vol-********90abcdef0"}, "output": {"AttachTime": "2014-02-27T19:23:06.000Z", "Device": "/dev/sdb", "InstanceId": "i-********90abcdef0", "State": "detaching", "VolumeId": "vol-049df61146c4d7901"}, "comments": {"input": {}, "output": {}}, "description": "This example detaches the volume (``vol-049df61146c4d7901``) from the instance it is attached to.", "id": "to-detach-a-volume-from-an-instance-1472507977694", "title": "To detach a volume from an instance"}], "DisableVgwRoutePropagation": [{"input": {"GatewayId": "vgw-9a4cacf3", "RouteTableId": "rtb-22574640"}, "comments": {"input": {}, "output": {}}, "description": "This example disables the specified virtual private gateway from propagating static routes to the specified route table.", "id": "ec2-disable-vgw-route-propagation-1", "title": "To disable route propagation"}], "DisassociateAddress": [{"input": {"AssociationId": "eipassoc-2bebb745"}, "comments": {"input": {}, "output": {}}, "description": "This example disassociates an Elastic IP address from an instance in a VPC.", "id": "ec2-disassociate-address-1", "title": "To disassociate an Elastic IP address in EC2-VPC"}, {"input": {"PublicIp": "************"}, "comments": {"input": {}, "output": {}}, "description": "This example disassociates an Elastic IP address from an instance in EC2-Classic.", "id": "ec2-disassociate-address-2", "title": "To disassociate an Elastic IP addresses in EC2-Classic"}], "DisassociateRouteTable": [{"input": {"AssociationId": "rtbassoc-781d0d1a"}, "comments": {"input": {}, "output": {}}, "description": "This example disassociates the specified route table from its associated subnet.", "id": "ec2-disassociate-route-table-1", "title": "To disassociate a route table"}], "EnableVgwRoutePropagation": [{"input": {"GatewayId": "vgw-9a4cacf3", "RouteTableId": "rtb-22574640"}, "comments": {"input": {}, "output": {}}, "description": "This example enables the specified virtual private gateway to propagate static routes to the specified route table.", "id": "ec2-enable-vgw-route-propagation-1", "title": "To enable route propagation"}], "EnableVolumeIO": [{"input": {"VolumeId": "vol-********90abcdef0"}, "output": {"Return": true}, "comments": {"input": {}, "output": {}}, "description": "This example enables I/O on volume ``vol-********90abcdef0``.", "id": "to-enable-io-for-a-volume-1472508114867", "title": "To enable I/O for a volume"}], "ModifyNetworkInterfaceAttribute": [{"input": {"Attachment": {"AttachmentId": "eni-attach-43348162", "DeleteOnTermination": false}, "NetworkInterfaceId": "eni-686ea200"}, "comments": {"input": {}, "output": {}}, "description": "This example modifies the attachment attribute of the specified network interface.", "id": "ec2-modify-network-interface-attribute-1", "title": "To modify the attachment attribute of a network interface"}, {"input": {"Description": "My description", "NetworkInterfaceId": "eni-686ea200"}, "comments": {"input": {}, "output": {}}, "description": "This example modifies the description attribute of the specified network interface.", "id": "ec2-modify-network-interface-attribute-2", "title": "To modify the description attribute of a network interface"}, {"input": {"Groups": ["sg-903004f8", "sg-1a2b3c4d"], "NetworkInterfaceId": "eni-686ea200"}, "comments": {"input": {}, "output": {}}, "description": "This example command modifies the groupSet attribute of the specified network interface.", "id": "ec2-modify-network-interface-attribute-3", "title": "To modify the groupSet attribute of a network interface"}, {"input": {"NetworkInterfaceId": "eni-686ea200", "SourceDestCheck": false}, "comments": {"input": {}, "output": {}}, "description": "This example command modifies the sourceDestCheck attribute of the specified network interface.", "id": "ec2-modify-network-interface-attribute-4", "title": "To modify the sourceDestCheck attribute of a network interface"}], "ModifySnapshotAttribute": [{"input": {"Attribute": "createVolumePermission", "OperationType": "remove", "SnapshotId": "snap-********90abcdef0", "UserIds": ["********9012"]}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example modifies snapshot ``snap-********90abcdef0`` to remove the create volume permission for a user with the account ID ``********9012``. If the command succeeds, no output is returned.", "id": "to-modify-a-snapshot-attribute-*************", "title": "To modify a snapshot attribute"}, {"input": {"Attribute": "createVolumePermission", "GroupNames": ["all"], "OperationType": "add", "SnapshotId": "snap-********90abcdef0"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example makes the snapshot ``snap-********90abcdef0`` public.", "id": "to-make-a-snapshot-public-*************", "title": "To make a snapshot public"}], "ModifySpotFleetRequest": [{"input": {"SpotFleetRequestId": "sfr-73fbd2ce-aa30-494c-8788-1cee4EXAMPLE", "TargetCapacity": 20}, "output": {"Return": true}, "comments": {"input": {}, "output": {}}, "description": "This example increases the target capacity of the specified Spot fleet request.", "id": "ec2-modify-spot-fleet-request-1", "title": "To increase the target capacity of a Spot fleet request"}, {"input": {"ExcessCapacityTerminationPolicy": "NoTermination ", "SpotFleetRequestId": "sfr-73fbd2ce-aa30-494c-8788-1cee4EXAMPLE", "TargetCapacity": 10}, "output": {"Return": true}, "comments": {"input": {}, "output": {}}, "description": "This example decreases the target capacity of the specified Spot fleet request without terminating any Spot Instances as a result.", "id": "ec2-modify-spot-fleet-request-2", "title": "To decrease the target capacity of a Spot fleet request"}], "ModifySubnetAttribute": [{"input": {"MapPublicIpOnLaunch": true, "SubnetId": "subnet-1a2b3c4d"}, "comments": {"input": {}, "output": {}}, "description": "This example modifies the specified subnet so that all instances launched into this subnet are assigned a public IP address.", "id": "ec2-modify-subnet-attribute-1", "title": "To change a subnet's public IP addressing behavior"}], "ModifyVolumeAttribute": [{"input": {"AutoEnableIO": {"Value": true}, "DryRun": true, "VolumeId": "vol-********90abcdef0"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example sets the ``autoEnableIo`` attribute of the volume with the ID ``vol-********90abcdef0`` to ``true``. If the command succeeds, no output is returned.", "id": "to-modify-a-volume-attribute-1472508596749", "title": "To modify a volume attribute"}], "ModifyVpcAttribute": [{"input": {"EnableDnsSupport": {"Value": false}, "VpcId": "vpc-a01106c2"}, "comments": {"input": {}, "output": {}}, "description": "This example modifies the enableDnsSupport attribute. This attribute indicates whether DNS resolution is enabled for the VPC. If this attribute is true, the Amazon DNS server resolves DNS hostnames for instances in the VPC to their corresponding IP addresses; otherwise, it does not.", "id": "ec2-modify-vpc-attribute-1", "title": "To modify the enableDnsSupport attribute"}, {"input": {"EnableDnsHostnames": {"Value": false}, "VpcId": "vpc-a01106c2"}, "comments": {"input": {}, "output": {}}, "description": "This example modifies the enableDnsHostnames attribute. This attribute indicates whether instances launched in the VPC get DNS hostnames. If this attribute is true, instances in the VPC get DNS hostnames; otherwise, they do not.", "id": "ec2-modify-vpc-attribute-2", "title": "To modify the enableDnsHostnames attribute"}], "MoveAddressToVpc": [{"input": {"PublicIp": "***********"}, "output": {"Status": "MoveInProgress"}, "comments": {"input": {}, "output": {}}, "description": "This example moves the specified Elastic IP address to the EC2-VPC platform.", "id": "ec2-move-address-to-vpc-1", "title": "To move an address to EC2-VPC"}], "PurchaseScheduledInstances": [{"input": {"PurchaseRequests": [{"InstanceCount": 1, "PurchaseToken": "eyJ2IjoiMSIsInMiOjEsImMiOi..."}]}, "output": {"ScheduledInstanceSet": [{"AvailabilityZone": "us-west-2b", "CreateDate": "2016-01-25T21:43:38.612Z", "HourlyPrice": "0.095", "InstanceCount": 1, "InstanceType": "c4.large", "NetworkPlatform": "EC2-VPC", "NextSlotStartTime": "2016-01-31T09:00:00Z", "Platform": "Linux/UNIX", "Recurrence": {"Frequency": "Weekly", "Interval": 1, "OccurrenceDaySet": [1], "OccurrenceRelativeToEnd": false, "OccurrenceUnit": ""}, "ScheduledInstanceId": "sci-1234-1234-1234-1234-********9012", "SlotDurationInHours": 32, "TermEndDate": "2017-01-31T09:00:00Z", "TermStartDate": "2016-01-31T09:00:00Z", "TotalScheduledInstanceHours": 1696}]}, "comments": {"input": {}, "output": {}}, "description": "This example purchases a Scheduled Instance.", "id": "ec2-purchase-scheduled-instances-1", "title": "To purchase a Scheduled Instance"}], "ReleaseAddress": [{"input": {"AllocationId": "eipalloc-64d5890a"}, "comments": {"input": {}, "output": {}}, "description": "This example releases an Elastic IP address for use with instances in a VPC.", "id": "ec2-release-address-1", "title": "To release an Elastic IP address for EC2-VPC"}, {"input": {"PublicIp": "************"}, "comments": {"input": {}, "output": {}}, "description": "This example releases an Elastic IP address for use with instances in EC2-Classic.", "id": "ec2-release-address-2", "title": "To release an Elastic IP addresses for EC2-Classic"}], "ReplaceNetworkAclAssociation": [{"input": {"AssociationId": "aclassoc-e5b95c8c", "NetworkAclId": "acl-5fb85d36"}, "output": {"NewAssociationId": "aclassoc-3999875b"}, "comments": {"input": {}, "output": {}}, "description": "This example associates the specified network ACL with the subnet for the specified network ACL association.", "id": "ec2-replace-network-acl-association-1", "title": "To replace the network ACL associated with a subnet"}], "ReplaceNetworkAclEntry": [{"input": {"CidrBlock": "************/24", "Egress": false, "NetworkAclId": "acl-5fb85d36", "PortRange": {"From": 53, "To": 53}, "Protocol": "udp", "RuleAction": "allow", "RuleNumber": 100}, "comments": {"input": {}, "output": {}}, "description": "This example replaces an entry for the specified network ACL. The new rule 100 allows ingress traffic from ************/24 on UDP port 53 (DNS) into any associated subnet.", "id": "ec2-replace-network-acl-entry-1", "title": "To replace a network ACL entry"}], "ReplaceRoute": [{"input": {"DestinationCidrBlock": "10.0.0.0/16", "GatewayId": "vgw-9a4cacf3", "RouteTableId": "rtb-22574640"}, "comments": {"input": {}, "output": {}}, "description": "This example replaces the specified route in the specified table table. The new route matches the specified CIDR and sends the traffic to the specified virtual private gateway.", "id": "ec2-replace-route-1", "title": "To replace a route"}], "ReplaceRouteTableAssociation": [{"input": {"AssociationId": "rtbassoc-781d0d1a", "RouteTableId": "rtb-22574640"}, "output": {"NewAssociationId": "rtbassoc-3a1f0f58"}, "comments": {"input": {}, "output": {}}, "description": "This example associates the specified route table with the subnet for the specified route table association.", "id": "ec2-replace-route-table-association-1", "title": "To replace the route table associated with a subnet"}], "RequestSpotFleet": [{"input": {"SpotFleetRequestConfig": {"IamFleetRole": "arn:aws:iam::********9012:role/my-spot-fleet-role", "LaunchSpecifications": [{"IamInstanceProfile": {"Arn": "arn:aws:iam::********9012:instance-profile/my-iam-role"}, "ImageId": "ami-1a2b3c4d", "InstanceType": "m3.medium", "KeyName": "my-key-pair", "SecurityGroups": [{"GroupId": "sg-1a2b3c4d"}], "SubnetId": "subnet-1a2b3c4d, subnet-3c4d5e6f"}], "SpotPrice": "0.04", "TargetCapacity": 2}}, "output": {"SpotFleetRequestId": "sfr-73fbd2ce-aa30-494c-8788-1cee4EXAMPLE"}, "comments": {"input": {}, "output": {}}, "description": "This example creates a Spot fleet request with two launch specifications that differ only by subnet. The Spot fleet launches the instances in the specified subnet with the lowest price. If the instances are launched in a default VPC, they receive a public IP address by default. If the instances are launched in a nondefault VPC, they do not receive a public IP address by default. Note that you can't specify different subnets from the same Availability Zone in a Spot fleet request.", "id": "ec2-request-spot-fleet-1", "title": "To request a Spot fleet in the subnet with the lowest price"}, {"input": {"SpotFleetRequestConfig": {"IamFleetRole": "arn:aws:iam::********9012:role/my-spot-fleet-role", "LaunchSpecifications": [{"IamInstanceProfile": {"Arn": "arn:aws:iam::********9012:instance-profile/my-iam-role"}, "ImageId": "ami-1a2b3c4d", "InstanceType": "m3.medium", "KeyName": "my-key-pair", "Placement": {"AvailabilityZone": "us-west-2a, us-west-2b"}, "SecurityGroups": [{"GroupId": "sg-1a2b3c4d"}]}], "SpotPrice": "0.04", "TargetCapacity": 2}}, "output": {"SpotFleetRequestId": "sfr-73fbd2ce-aa30-494c-8788-1cee4EXAMPLE"}, "comments": {"input": {}, "output": {}}, "description": "This example creates a Spot fleet request with two launch specifications that differ only by Availability Zone. The Spot fleet launches the instances in the specified Availability Zone with the lowest price. If your account supports EC2-VPC only, Amazon EC2 launches the Spot instances in the default subnet of the Availability Zone. If your account supports EC2-Classic, Amazon EC2 launches the instances in EC2-Classic in the Availability Zone.", "id": "ec2-request-spot-fleet-2", "title": "To request a Spot fleet in the Availability Zone with the lowest price"}, {"input": {"SpotFleetRequestConfig": {"IamFleetRole": "arn:aws:iam::********9012:role/my-spot-fleet-role", "LaunchSpecifications": [{"IamInstanceProfile": {"Arn": "arn:aws:iam::************:instance-profile/my-iam-role"}, "ImageId": "ami-1a2b3c4d", "InstanceType": "m3.medium", "KeyName": "my-key-pair", "NetworkInterfaces": [{"AssociatePublicIpAddress": true, "DeviceIndex": 0, "Groups": ["sg-1a2b3c4d"], "SubnetId": "subnet-1a2b3c4d"}]}], "SpotPrice": "0.04", "TargetCapacity": 2}}, "output": {"SpotFleetRequestId": "sfr-73fbd2ce-aa30-494c-8788-1cee4EXAMPLE"}, "comments": {"input": {}, "output": {}}, "description": "This example assigns public addresses to instances launched in a nondefault VPC. Note that when you specify a network interface, you must include the subnet ID and security group ID using the network interface.", "id": "ec2-request-spot-fleet-3", "title": "To launch Spot instances in a subnet and assign them public IP addresses"}, {"input": {"SpotFleetRequestConfig": {"AllocationStrategy": "diversified", "IamFleetRole": "arn:aws:iam::********9012:role/my-spot-fleet-role", "LaunchSpecifications": [{"ImageId": "ami-1a2b3c4d", "InstanceType": "c4.2xlarge", "SubnetId": "subnet-1a2b3c4d"}, {"ImageId": "ami-1a2b3c4d", "InstanceType": "m3.2xlarge", "SubnetId": "subnet-1a2b3c4d"}, {"ImageId": "ami-1a2b3c4d", "InstanceType": "r3.2xlarge", "SubnetId": "subnet-1a2b3c4d"}], "SpotPrice": "0.70", "TargetCapacity": 30}}, "output": {"SpotFleetRequestId": "sfr-73fbd2ce-aa30-494c-8788-1cee4EXAMPLE"}, "comments": {"input": {}, "output": {}}, "description": "This example creates a Spot fleet request that launches 30 instances using the diversified allocation strategy. The launch specifications differ by instance type. The Spot fleet distributes the instances across the launch specifications such that there are 10 instances of each type.", "id": "ec2-request-spot-fleet-4", "title": "To request a Spot fleet using the diversified allocation strategy"}], "RequestSpotInstances": [{"input": {"InstanceCount": 5, "LaunchSpecification": {"IamInstanceProfile": {"Arn": "arn:aws:iam::********9012:instance-profile/my-iam-role"}, "ImageId": "ami-1a2b3c4d", "InstanceType": "m3.medium", "KeyName": "my-key-pair", "Placement": {"AvailabilityZone": "us-west-2a"}, "SecurityGroupIds": ["sg-1a2b3c4d"]}, "SpotPrice": "0.03", "Type": "one-time"}, "comments": {"input": {}, "output": {}}, "description": "This example creates a one-time Spot Instance request for five instances in the specified Availability Zone. If your account supports EC2-VPC only, Amazon EC2 launches the instances in the default subnet of the specified Availability Zone. If your account supports EC2-Classic, Amazon EC2 launches the instances in EC2-Classic in the specified Availability Zone.", "id": "ec2-request-spot-instances-1", "title": "To create a one-time Spot Instance request"}, {"input": {"InstanceCount": 5, "LaunchSpecification": {"IamInstanceProfile": {"Arn": "arn:aws:iam::********9012:instance-profile/my-iam-role"}, "ImageId": "ami-1a2b3c4d", "InstanceType": "m3.medium", "SecurityGroupIds": ["sg-1a2b3c4d"], "SubnetId": "subnet-1a2b3c4d"}, "SpotPrice": "0.050", "Type": "one-time"}, "comments": {"input": {}, "output": {}}, "description": "This example command creates a one-time Spot Instance request for five instances in the specified subnet. Amazon EC2 launches the instances in the specified subnet. If the VPC is a nondefault VPC, the instances do not receive a public IP address by default.", "id": "ec2-request-spot-instances-2", "title": "To create a one-time Spot Instance request"}], "ResetSnapshotAttribute": [{"input": {"Attribute": "createVolumePermission", "SnapshotId": "snap-********90abcdef0"}, "output": {}, "comments": {"input": {}, "output": {}}, "description": "This example resets the create volume permissions for snapshot ``snap-********90abcdef0``. If the command succeeds, no output is returned.", "id": "to-reset-a-snapshot-attribute-1472508825735", "title": "To reset a snapshot attribute"}], "RestoreAddressToClassic": [{"input": {"PublicIp": "************"}, "output": {"PublicIp": "************", "Status": "MoveInProgress"}, "comments": {"input": {}, "output": {}}, "description": "This example restores the specified Elastic IP address to the EC2-Classic platform.", "id": "ec2-restore-address-to-classic-1", "title": "To restore an address to EC2-Classic"}], "RunScheduledInstances": [{"input": {"InstanceCount": 1, "LaunchSpecification": {"IamInstanceProfile": {"Name": "my-iam-role"}, "ImageId": "ami-********", "InstanceType": "c4.large", "KeyName": "my-key-pair", "NetworkInterfaces": [{"AssociatePublicIpAddress": true, "DeviceIndex": 0, "Groups": ["sg-********"], "SubnetId": "subnet-********"}]}, "ScheduledInstanceId": "sci-1234-1234-1234-1234-********9012"}, "output": {"InstanceIdSet": ["i-********90abcdef0"]}, "comments": {"input": {}, "output": {}}, "description": "This example launches the specified Scheduled Instance in a VPC.", "id": "ec2-run-scheduled-instances-1", "title": "To launch a Scheduled Instance in a VPC"}, {"input": {"InstanceCount": 1, "LaunchSpecification": {"IamInstanceProfile": {"Name": "my-iam-role"}, "ImageId": "ami-********", "InstanceType": "c4.large", "KeyName": "my-key-pair", "Placement": {"AvailabilityZone": "us-west-2b"}, "SecurityGroupIds": ["sg-********"]}, "ScheduledInstanceId": "sci-1234-1234-1234-1234-********9012"}, "output": {"InstanceIdSet": ["i-********90abcdef0"]}, "comments": {"input": {}, "output": {}}, "description": "This example launches the specified Scheduled Instance in EC2-Classic.", "id": "ec2-run-scheduled-instances-2", "title": "To launch a Scheduled Instance in EC2-Classic"}], "UnassignPrivateIpAddresses": [{"input": {"NetworkInterfaceId": "eni-e5aa89a3", "PrivateIpAddresses": ["*********"]}, "comments": {"input": {}, "output": {}}, "description": "This example unassigns the specified private IP address from the specified network interface.", "id": "ec2-unassign-private-ip-addresses-1", "title": "To unassign a secondary private IP address from a network interface"}]}}