{"pagination": {"DescribeBackups": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Backups"}, "DescribeFileSystems": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "FileSystems"}, "ListTagsForResource": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Tags"}, "DescribeStorageVirtualMachines": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "StorageVirtualMachines"}, "DescribeVolumes": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Volumes"}, "DescribeS3AccessPointAttachments": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "S3AccessPointAttachments"}, "DescribeSnapshots": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxResults", "result_key": "Snapshots"}}}