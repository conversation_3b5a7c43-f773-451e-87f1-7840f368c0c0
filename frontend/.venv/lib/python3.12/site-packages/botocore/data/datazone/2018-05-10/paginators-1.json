{"pagination": {"ListAssetRevisions": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListDataSourceRunActivities": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListDataSourceRuns": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListDataSources": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListDomains": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListEnvironmentBlueprintConfigurations": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListEnvironmentBlueprints": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListEnvironmentProfiles": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListEnvironments": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListNotifications": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "notifications"}, "ListProjectMemberships": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "members"}, "ListProjects": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListSubscriptionGrants": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListSubscriptionRequests": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListSubscriptionTargets": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListSubscriptions": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "Search": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "SearchGroupProfiles": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "SearchListings": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "SearchTypes": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "SearchUserProfiles": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListMetadataGenerationRuns": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListTimeSeriesDataPoints": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListEnvironmentActions": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListLineageNodeHistory": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "nodes"}, "ListAssetFilters": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListDataProductRevisions": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListDomainUnitsForParent": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListEntityOwners": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "owners"}, "ListPolicyGrants": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "grantList"}, "ListRules": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListConnections": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListJobRuns": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListLineageEvents": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListProjectProfiles": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}}}