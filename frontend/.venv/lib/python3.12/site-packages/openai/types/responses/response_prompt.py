# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from typing import Dict, <PERSON>, Optional
from typing_extensions import TypeAlias

from ..._models import BaseModel
from .response_input_file import ResponseInputFile
from .response_input_text import ResponseInputText
from .response_input_image import ResponseInputImage

__all__ = ["ResponsePrompt", "Variables"]

Variables: TypeAlias = Union[str, ResponseInputText, ResponseInputImage, ResponseInputFile]


class ResponsePrompt(BaseModel):
    id: str
    """The unique identifier of the prompt template to use."""

    variables: Optional[Dict[str, Variables]] = None
    """Optional map of values to substitute in for variables in your prompt.

    The substitution values can either be strings, or other Response input types
    like images or files.
    """

    version: Optional[str] = None
    """Optional version of the prompt template."""
