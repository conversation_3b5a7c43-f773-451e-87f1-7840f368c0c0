(this.webpackJsonpstreamlit_toggle_switch=this.webpackJsonpstreamlit_toggle_switch||[]).push([[0],{34:function(e,t,a){e.exports=a(41)},41:function(e,t,a){"use strict";a.r(t);var c=a(1),r=a.n(c),n=a(16),o=a.n(n),l=a(4),i=a(7),s=a(5),u=a(24),m=a(30),h=a(60),d=a(61),b=a(62),g=a(59),k=Object(u.b)((function(e){var t=e.args,a=t.default_value,n=t.label_end,o=t.label_start,k=t.justify,p=t.active_color,E=t.inactive_color,f=t.track_color;Object(c.useEffect)((function(){return u.a.setFrameHeight()}));var _=r.a.useState({checkStatus:a}),j=Object(l.a)(_,2),v=j[0],O=j[1],S=Object(m.a)({overrides:{MuiSwitch:{switchBase:{color:E},colorSecondary:{"&$checked":{color:p}},track:{opacity:.1,backgroundColor:f,"$checked$checked + &":{opacity:1,backgroundColor:f}}}}});return r.a.createElement(g.a,{theme:S},r.a.createElement(h.a,{component:"div",variant:"subtitle1",paragraph:!1,gutterBottom:!1},r.a.createElement(d.a,{component:"label",container:!0,justifyContent:k},r.a.createElement(d.a,{item:!0},o),r.a.createElement(d.a,{item:!0},r.a.createElement(b.a,{checked:v.checkStatus,onChange:function(e){O(Object(s.a)(Object(s.a)({},v),{},Object(i.a)({},e.target.name,e.target.checked))),u.a.setComponentValue(e.target.checked)},name:"checkStatus"})),r.a.createElement(d.a,{item:!0},n))))}));o.a.render(r.a.createElement(r.a.StrictMode,null,r.a.createElement(k,null)),document.getElementById("root"))}},[[34,1,2]]]);
//# sourceMappingURL=main.508efdfd.chunk.js.map