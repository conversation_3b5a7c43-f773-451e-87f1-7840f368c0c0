{"version": 3, "sources": ["streamlit_toggle.tsx", "index.tsx"], "names": ["withStreamlitConnection", "props", "args", "default_value", "label_end", "label_start", "justify", "active_color", "inactive_color", "track_color", "useEffect", "Streamlit", "setFrameHeight", "React", "useState", "checkStatus", "state", "setState", "snowflakeTheme", "createTheme", "overrides", "MuiSwitch", "switchBase", "color", "colorSecondary", "track", "opacity", "backgroundColor", "ThemeProvider", "theme", "Typography", "component", "variant", "paragraph", "gutterBottom", "Grid", "container", "justifyContent", "item", "Switch", "checked", "onChange", "event", "target", "name", "setComponentValue", "ReactDOM", "render", "StrictMode", "document", "getElementById"], "mappings": "wRAwEeA,eA5DS,SAACC,GAEvB,MAAmGA,EAAMC,KAAlGC,EAAa,EAAbA,cAAeC,EAAS,EAATA,UAAWC,EAAW,EAAXA,YAAYC,EAAO,EAAPA,QAASC,EAAY,EAAZA,aAAcC,EAAc,EAAdA,eAAgBC,EAAW,EAAXA,YACpFC,qBAAU,kBAAMC,IAAUC,oBAG1B,IAIA,EAA0BC,IAAMC,SAAS,CACrCC,YAAaZ,IACf,mBAFKa,EAAK,KAAEC,EAAQ,KAIhBC,EAAiBC,YAAY,CACjCC,UAAW,CACTC,UAAW,CACTC,WACC,CAECC,MAAOf,GAETgB,eAAgB,CACd,YAAa,CAEXD,MAAOhB,IAGXkB,MAAO,CAELC,QAAS,GACTC,gBAAiBlB,EACjB,uBAAwB,CACtBiB,QAAS,EACTC,gBAAiBlB,QAQ3B,OACE,kBAACmB,EAAA,EAAa,CAACC,MAAOX,GACtB,kBAACY,EAAA,EAAU,CAAEC,UAAU,MAAMC,QAAQ,YAAYC,WAAW,EAAOC,cAAc,GACjF,kBAACC,EAAA,EAAI,CAACJ,UAAU,QAAQK,WAAS,EAACC,eAAgB/B,GAChD,kBAAC6B,EAAA,EAAI,CAACG,MAAI,GAAGjC,GACb,kBAAC8B,EAAA,EAAI,CAACG,MAAI,GACV,kBAACC,EAAA,EAAM,CACHC,QAASxB,EAAMD,YACf0B,SA5Ca,SAACC,GACpBzB,EAAS,2BAAKD,GAAK,kBAAG0B,EAAMC,OAAOC,KAAOF,EAAMC,OAAOH,WACvD7B,IAAUkC,kBAAkBH,EAAMC,OAAOH,UA2CnCI,KAAK,iBAET,kBAACT,EAAA,EAAI,CAACG,MAAI,GAAElC,SC5DlB0C,IAASC,OACP,kBAAC,IAAMC,WAAU,KACf,kBAAC,EAAqB,OAExBC,SAASC,eAAe,W", "file": "static/js/main.508efdfd.chunk.js", "sourcesContent": ["import {\n  ComponentProps,\n  Streamlit,\n  withStreamlitConnection,\n} from \"streamlit-component-lib\"\nimport React, { useEffect } from \"react\"\nimport { createTheme } from '@material-ui/core/styles';\nimport { Typography,Switch, Grid } from \"@material-ui/core\";\nimport { ThemeProvider } from '@material-ui/styles';\n\n\n\nconst StreamlitToogle = (props: ComponentProps) => {\n\n  const {default_value, label_end, label_start,justify, active_color, inactive_color, track_color} = props.args;\n  useEffect(() => Streamlit.setFrameHeight());\n\n\n  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n    setState({ ...state, [event.target.name]: event.target.checked });\n    Streamlit.setComponentValue(event.target.checked );\n  };\n  const [state, setState] = React.useState({\n      checkStatus: default_value\n  });\n\n  const snowflakeTheme = createTheme({\n    overrides: {\n      MuiSwitch: {\n        switchBase:\n         {\n          // Controls default (unchecked) color for the thumb\n          color: inactive_color\n        },\n        colorSecondary: {\n          \"&$checked\": {\n            // Controls checked color for the thumb\n            color: active_color\n          }\n        },\n        track: {\n          // Controls default (unchecked) color for the track\n          opacity: 0.1,\n          backgroundColor: track_color,\n          \"$checked$checked + &\": {\n            opacity: 1,\n            backgroundColor: track_color,\n\n          }\n        }\n      }\n    }\n  });\n\n  return (\n    <ThemeProvider theme={snowflakeTheme}>\n    <Typography  component=\"div\" variant=\"subtitle1\" paragraph={false} gutterBottom={false}>\n    <Grid component=\"label\" container justifyContent={justify}>\n      <Grid item >{label_start}</Grid>\n      <Grid item>\n      <Switch\n          checked={state.checkStatus}\n          onChange={handleChange}\n          name=\"checkStatus\"/>\n      </Grid>\n      <Grid item>{label_end}</Grid> \n    </Grid>\n    </Typography>\n    </ThemeProvider>\n  );\n}\n\nexport default withStreamlitConnection(StreamlitToogle);\n", "import React from \"react\"\nimport <PERSON>actD<PERSON> from \"react-dom\"\nimport StreamlitToggleSwitch from \"./streamlit_toggle\"\n\n\nReactDOM.render(\n  <React.StrictMode>\n    <StreamlitToggleSwitch />\n  </React.StrictMode>,\n  document.getElementById(\"root\")\n)\n"], "sourceRoot": ""}