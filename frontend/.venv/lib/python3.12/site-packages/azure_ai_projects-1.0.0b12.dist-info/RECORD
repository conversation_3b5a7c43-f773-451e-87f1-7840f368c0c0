azure/ai/projects/__init__.py,sha256=C7Ddtjy__BxKkRCydRS7BhtQnM7TFZo179UUVC5krVY,1026
azure/ai/projects/__pycache__/__init__.cpython-312.pyc,,
azure/ai/projects/__pycache__/_client.cpython-312.pyc,,
azure/ai/projects/__pycache__/_configuration.cpython-312.pyc,,
azure/ai/projects/__pycache__/_model_base.cpython-312.pyc,,
azure/ai/projects/__pycache__/_patch.cpython-312.pyc,,
azure/ai/projects/__pycache__/_patch_prompts.cpython-312.pyc,,
azure/ai/projects/__pycache__/_patch_telemetry.cpython-312.pyc,,
azure/ai/projects/__pycache__/_serialization.cpython-312.pyc,,
azure/ai/projects/__pycache__/_validation.cpython-312.pyc,,
azure/ai/projects/__pycache__/_vendor.cpython-312.pyc,,
azure/ai/projects/__pycache__/_version.cpython-312.pyc,,
azure/ai/projects/_client.py,sha256=RHHXxk0EEyPRHisYgavtFbBWXG44a1vG8QJZnyTCMtQ,6237
azure/ai/projects/_configuration.py,sha256=ao740PsapvK0n2N7GkwALo1uo3qPQ58YylupQzT35IU,3623
azure/ai/projects/_model_base.py,sha256=IGMDfa4P4Lb2dN95RkvWUcQUSM58r5dwCRel8byqJvA,45355
azure/ai/projects/_patch.py,sha256=ZKmbvBT7IRHXd0XfIdd-69HQF-DRtH3pHN5AMakyrQY,5999
azure/ai/projects/_patch_prompts.py,sha256=912mPNN8j4_mYhu01oS_kaQy17owqhRCkAiU_YilYi4,6824
azure/ai/projects/_patch_telemetry.py,sha256=2lFaVjW7aTEcX0nqQwdjdfDP7GtJGWTpa-kzZD6Wv4c,10546
azure/ai/projects/_serialization.py,sha256=9eoCiOLbhUdv2aer6GQGxJOVRjxSaZ46yH0wrwS6tTw,81964
azure/ai/projects/_utils/__init__.py,sha256=nh5swTUwCBe3xsb9n60QMcuJ33d89Itwx7q9V-oDOUE,450
azure/ai/projects/_utils/__pycache__/__init__.cpython-312.pyc,,
azure/ai/projects/_utils/__pycache__/model_base.cpython-312.pyc,,
azure/ai/projects/_utils/__pycache__/serialization.cpython-312.pyc,,
azure/ai/projects/_utils/model_base.py,sha256=58kTpYny9nbqBSlo6amAsJYuRyidGpn1LAFb7g-80sg,45355
azure/ai/projects/_utils/serialization.py,sha256=9eoCiOLbhUdv2aer6GQGxJOVRjxSaZ46yH0wrwS6tTw,81964
azure/ai/projects/_validation.py,sha256=A7DcsEblccC-dXuesu2U8XKWqrsqV96lLxn6viuokNs,2095
azure/ai/projects/_vendor.py,sha256=w5TZDhlbW_qjSnFvMH2imPrypbgbcCRzPXcG7SvdC8Q,1924
azure/ai/projects/_version.py,sha256=B7Z92Rg9yJD2LNkg5cp5sJrGwLQGpkZCrfA0nOkYGyE,487
azure/ai/projects/aio/__init__.py,sha256=jUawBX1lQ2ZqWI5d7r2DKj-beeetS-V3mYhhoJM3oPI,973
azure/ai/projects/aio/__pycache__/__init__.cpython-312.pyc,,
azure/ai/projects/aio/__pycache__/_client.cpython-312.pyc,,
azure/ai/projects/aio/__pycache__/_configuration.cpython-312.pyc,,
azure/ai/projects/aio/__pycache__/_patch.cpython-312.pyc,,
azure/ai/projects/aio/__pycache__/_vendor.cpython-312.pyc,,
azure/ai/projects/aio/_client.py,sha256=f0cXi6m_rnYehoce3CfbpgA9x7DuCM6BzHVtc1OFSWM,6406
azure/ai/projects/aio/_configuration.py,sha256=FlyYIZ5E6WyjDedv4UWslLpQ3miUDZbSEtX9WnUyyFA,3666
azure/ai/projects/aio/_patch.py,sha256=IjhFakMda-u-ugqLFrjpZy2JAQh3rtZzf7-TdRNqo08,5380
azure/ai/projects/aio/_vendor.py,sha256=kL9C8fr2_jCfx4Un-VJCjRER3rT3_01eRRjxNUiM4Qg,1513
azure/ai/projects/aio/operations/__init__.py,sha256=rx2p8H7e5pR9EbZSyWO-UhyJ8CFbDm3U4UkhKEi46fM,1375
azure/ai/projects/aio/operations/__pycache__/__init__.cpython-312.pyc,,
azure/ai/projects/aio/operations/__pycache__/_operations.cpython-312.pyc,,
azure/ai/projects/aio/operations/__pycache__/_patch.cpython-312.pyc,,
azure/ai/projects/aio/operations/__pycache__/_patch_connections_async.cpython-312.pyc,,
azure/ai/projects/aio/operations/__pycache__/_patch_datasets_async.cpython-312.pyc,,
azure/ai/projects/aio/operations/__pycache__/_patch_inference_async.cpython-312.pyc,,
azure/ai/projects/aio/operations/__pycache__/_patch_telemetry_async.cpython-312.pyc,,
azure/ai/projects/aio/operations/_operations.py,sha256=yIXGZzzM7pXgUdkxBLee62VCpFvG0uSoEGGywEHdhIk,97707
azure/ai/projects/aio/operations/_patch.py,sha256=LCCzA3O2tYvt4E-HFk239-YhiR9C3ROb31juKKkigGY,1062
azure/ai/projects/aio/operations/_patch_connections_async.py,sha256=BWha49EJqJC-O7T-9_5gC-OxZa79xVfdlGP-cdxqLmE,2932
azure/ai/projects/aio/operations/_patch_datasets_async.py,sha256=zmDWUmKIvEYpNRHFR2EqDIKO1w9Mvv6xK7VYau35ouo,10817
azure/ai/projects/aio/operations/_patch_inference_async.py,sha256=UrbTSEkpTV7MxkWuO4GgGd9Yh7bPg_7cAO7Tjuk6KbQ,7594
azure/ai/projects/aio/operations/_patch_telemetry_async.py,sha256=zTa-SaMqasNOQ1Z20KS98Dhv1fEa9rAhV7GdoVqRi_Y,3097
azure/ai/projects/models/__init__.py,sha256=4sdBwwwNH0-cDlGD-64O975fyY_Zc450lcn8qjPi2Rs,2979
azure/ai/projects/models/__pycache__/__init__.cpython-312.pyc,,
azure/ai/projects/models/__pycache__/_enums.cpython-312.pyc,,
azure/ai/projects/models/__pycache__/_models.cpython-312.pyc,,
azure/ai/projects/models/__pycache__/_patch.cpython-312.pyc,,
azure/ai/projects/models/__pycache__/_patch_evaluations.cpython-312.pyc,,
azure/ai/projects/models/_enums.py,sha256=olKdhvNH25Rg3d617St0FIIh2cNoheY0VYpmVOQ3Y0w,6595
azure/ai/projects/models/_models.py,sha256=yav5ZXaTBU9Bm1kULSnjhz_AyWPrA75wL72c3E8VG7w,65854
azure/ai/projects/models/_patch.py,sha256=FDEnweGeXOzCjwY5jRYxhdyZ_NjJzsB6FakY3XsikS4,740
azure/ai/projects/models/_patch_evaluations.py,sha256=OmCINr3TwdqwDcFsTdFszRhr3u3DPcNzPKy8ZBZZFoo,2424
azure/ai/projects/operations/__init__.py,sha256=rx2p8H7e5pR9EbZSyWO-UhyJ8CFbDm3U4UkhKEi46fM,1375
azure/ai/projects/operations/__pycache__/__init__.cpython-312.pyc,,
azure/ai/projects/operations/__pycache__/_operations.cpython-312.pyc,,
azure/ai/projects/operations/__pycache__/_patch.cpython-312.pyc,,
azure/ai/projects/operations/__pycache__/_patch_connections.cpython-312.pyc,,
azure/ai/projects/operations/__pycache__/_patch_datasets.cpython-312.pyc,,
azure/ai/projects/operations/__pycache__/_patch_inference.cpython-312.pyc,,
azure/ai/projects/operations/__pycache__/_patch_telemetry.cpython-312.pyc,,
azure/ai/projects/operations/_operations.py,sha256=0xOs1d6CqEMwWT1ZmHWUP90XYB8gmoo3Rjh3gOrJn5I,118758
azure/ai/projects/operations/_patch.py,sha256=cC2hR9aqritJUIQrK9UnJCainPWgc2ifLgL5Uwjqtl4,1038
azure/ai/projects/operations/_patch_connections.py,sha256=hjdd2CzUVDVBg1Ee0ac024kOfuXzPYTXNPXW4qxCgVc,2866
azure/ai/projects/operations/_patch_datasets.py,sha256=leyCxz5bfSWHmJC94eni0X4rQ5t160YVmSswbvAb6rk,10614
azure/ai/projects/operations/_patch_inference.py,sha256=5iiuPtyD1YS3iH4lULkh3LdhGRyK_z4RVUuQ2i9Sjag,7954
azure/ai/projects/operations/_patch_telemetry.py,sha256=O20R6EW8sYfiMFjoEM6EQu2gzL5bP9NHROjN_bS44TM,2987
azure/ai/projects/py.typed,sha256=dcrsqJrcYfTX-ckLFJMTaj6mD8aDe2u0tkQG-ZYxnEg,26
azure_ai_projects-1.0.0b12.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
azure_ai_projects-1.0.0b12.dist-info/LICENSE,sha256=_VMkgdgo4ToLE8y1mOAjOKNhd0BnWoYu5r3BVBto6T0,1073
azure_ai_projects-1.0.0b12.dist-info/METADATA,sha256=yCwsWPL5vmNGCCKid2098sQxa5FJjRlptnVF6cAtF8k,22057
azure_ai_projects-1.0.0b12.dist-info/RECORD,,
azure_ai_projects-1.0.0b12.dist-info/WHEEL,sha256=iAkIy5fosb7FzIOwONchHf19Qu7_1wCWyFNR5gu9nU0,91
azure_ai_projects-1.0.0b12.dist-info/top_level.txt,sha256=S7DhWV9m80TBzAhOFjxDUiNbKszzoThbnrSz5MpbHSQ,6
