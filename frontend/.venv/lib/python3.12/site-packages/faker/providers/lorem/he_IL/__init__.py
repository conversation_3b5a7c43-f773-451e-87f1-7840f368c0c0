from typing import Dict

from .. import Provider as LoremProvider


class Provider(LoremProvider):
    """Implement lorem provider for ``he_IL`` locale."""

    word_list = (
        "אאוגו",
        "אגת",
        "אדיפיסינג",
        "אדנדום",
        "אט",
        "איאקוליס",
        "איב<PERSON>",
        "איף",
        "איפסום",
        "אלית",
        "אלמנקום",
        "אמט",
        "אס",
        "אפאח",
        "אקווזמן",
        "ארווס",
        "בגורמי",
        "בורק?",
        "בלובק",
        "בלינדו",
        "בלינך",
        "בליקרה",
        "בעליק",
        "בעריר",
        "בראיט",
        "ברומץ",
        "בריקנה",
        "ברשג",
        "גדדיש",
        "גולר",
        "גק",
        "דול",
        "דולור",
        "דומור",
        "דז",
        "דיאם",
        "דלאמת",
        "דס",
        "הבקיץ",
        "הדש",
        "הו<PERSON><PERSON>י<PERSON>",
        "היו<PERSON><PERSON><PERSON>",
        "הכ<PERSON><PERSON><PERSON>",
        "השמ<PERSON>",
        "התידם",
        "וואל",
        "וולופטה",
        "וחאית",
        "ולחת",
        "ולתיעם",
        "ומעיוט",
        "ומרגשח",
        "וסטיבולום",
        "וסתעד",
        "וק",
        "ותלברו",
        "זותה",
        "חשלו",
        "טידום",
        "יבש",
        "יהול",
        "ישבעס",
        "כאנה",
        "כלרשט",
        "להאמית",
        "לורם",
        "ליאמום",
        "ליבם",
        "ליץ",
        "לכימפו",
        "לכנו",
        "לכנוץ",
        "למטכין",
        "למרקוח",
        "למרקל",
        "לפמעט",
        "לפריקך",
        "לפתיעם",
        "לקטוס",
        "לרטי",
        "לתיג",
        "לתכי",
        "מא",
        "מגמש",
        "מונחף",
        "מונפרד",
        "מונפרר",
        "מוסן",
        "מורגם",
        "מיחוצים",
        "מנורך",
        "מנכם",
        "מנק",
        "מנת",
        "מרגשי",
        "נובש",
        "נולום",
        "נון",
        "נונסטי",
        "ניבאה",
        "ניסי",
        "ניצאחו",
        "נמרגי",
        "נשואי",
        "סאפיאן",
        "סוברט",
        "סולגק",
        "סוליסי",
        "סחטיר",
        "סטום",
        "סיט",
        "סילקוף",
        "סכעיט",
        "סת",
        "סתשם",
        "עמחליף",
        "ערששף",
        "פוסיליס",
        "צוט",
        "צופעט",
        "צורק",
        "קוויז",
        "קוויס",
        "קולהע",
        "קולורס",
        "קונדימנטום",
        "קונסקטורר",
        "קורוס",
        "קלאצי",
        "קלובר",
        "קראס",
        "קרהשק",
        "רוגצה",
        "שבצק",
        "שהכים",
        "שלושע",
        "שמחויט",
        "שנרא",
        "שעותלשך",
        "שערש",
        "תוק",
        "תצטנפל",
        "תצטריק",
        "תרבנך",
    )

    parts_of_speech: Dict[str, tuple] = {}
