from typing import <PERSON><PERSON>

from .. import BaseProvider, ElementsType

localized = True


class Provider(BaseProvider):
    formats: ElementsType[str] = (
        "{{last_name}} {{company_suffix}}",
        "{{last_name}}-{{last_name}}",
        "{{last_name}}, {{last_name}} and {{last_name}}",
    )

    company_suffixes: ElementsType[str] = ("Inc", "and Sons", "LLC", "Group", "PLC", "Ltd")

    catch_phrase_words: Tu<PERSON>[ElementsType[str], ...] = (
        (
            "Adaptive",
            "Advanced",
            "Ameliorated",
            "Assimilated",
            "Automated",
            "Balanced",
            "Business-focused",
            "Centralized",
            "Cloned",
            "Compatible",
            "Configurable",
            "Cross-group",
            "Cross-platform",
            "Customer-focused",
            "Customizable",
            "Decentralized",
            "De-engineered",
            "Devolved",
            "Digitized",
            "Distributed",
            "Diverse",
            "Down-sized",
            "Enhanced",
            "Enterprise-wide",
            "Ergonomic",
            "Exclusive",
            "Expanded",
            "Extended",
            "Face-to-face",
            "Focused",
            "Front-line",
            "Fully-configurable",
            "Function-based",
            "Fundamental",
            "Future-proofed",
            "Grass-roots",
            "Horizontal",
            "Implemented",
            "Innovative",
            "Integrated",
            "Intuitive",
            "Inverse",
            "Managed",
            "Mandatory",
            "Monitored",
            "Multi-channeled",
            "Multi-lateral",
            "Multi-layered",
            "Multi-tiered",
            "Networked",
            "Object-based",
            "Open-architected",
            "Open-source",
            "Operative",
            "Optimized",
            "Optional",
            "Organic",
            "Organized",
            "Persevering",
            "Persistent",
            "Phased",
            "Polarized",
            "Pre-emptive",
            "Proactive",
            "Profit-focused",
            "Profound",
            "Programmable",
            "Progressive",
            "Public-key",
            "Quality-focused",
            "Reactive",
            "Realigned",
            "Re-contextualized",
            "Re-engineered",
            "Reduced",
            "Reverse-engineered",
            "Right-sized",
            "Robust",
            "Seamless",
            "Secured",
            "Self-enabling",
            "Sharable",
            "Stand-alone",
            "Streamlined",
            "Switchable",
            "Synchronized",
            "Synergistic",
            "Synergized",
            "Team-oriented",
            "Total",
            "Triple-buffered",
            "Universal",
            "Up-sized",
            "Upgradable",
            "User-centric",
            "User-friendly",
            "Versatile",
            "Virtual",
            "Visionary",
            "Vision-oriented",
        ),
        (
            "24hour",
            "24/7",
            "3rdgeneration",
            "4thgeneration",
            "5thgeneration",
            "6thgeneration",
            "actuating",
            "analyzing",
            "asymmetric",
            "asynchronous",
            "attitude-oriented",
            "background",
            "bandwidth-monitored",
            "bi-directional",
            "bifurcated",
            "bottom-line",
            "clear-thinking",
            "client-driven",
            "client-server",
            "coherent",
            "cohesive",
            "composite",
            "context-sensitive",
            "contextually-based",
            "content-based",
            "dedicated",
            "demand-driven",
            "didactic",
            "directional",
            "discrete",
            "disintermediate",
            "dynamic",
            "eco-centric",
            "empowering",
            "encompassing",
            "even-keeled",
            "executive",
            "explicit",
            "exuding",
            "fault-tolerant",
            "foreground",
            "fresh-thinking",
            "full-range",
            "global",
            "grid-enabled",
            "heuristic",
            "high-level",
            "holistic",
            "homogeneous",
            "human-resource",
            "hybrid",
            "impactful",
            "incremental",
            "intangible",
            "interactive",
            "intermediate",
            "leadingedge",
            "local",
            "logistical",
            "maximized",
            "methodical",
            "mission-critical",
            "mobile",
            "modular",
            "motivating",
            "multimedia",
            "multi-state",
            "multi-tasking",
            "national",
            "needs-based",
            "neutral",
            "next generation",
            "non-volatile",
            "object-oriented",
            "optimal",
            "optimizing",
            "radical",
            "real-time",
            "reciprocal",
            "regional",
            "responsive",
            "scalable",
            "secondary",
            "solution-oriented",
            "stable",
            "static",
            "systematic",
            "systemic",
            "system-worthy",
            "tangible",
            "tertiary",
            "transitional",
            "uniform",
            "upward-trending",
            "user-facing",
            "value-added",
            "web-enabled",
            "well-modulated",
            "zero administration",
            "zero-defect",
            "zero tolerance",
        ),
        (
            "ability",
            "access",
            "adapter",
            "algorithm",
            "alliance",
            "analyzer",
            "application",
            "approach",
            "architecture",
            "archive",
            "artificial intelligence",
            "array",
            "attitude",
            "benchmark",
            "budgetary management",
            "capability",
            "capacity",
            "challenge",
            "circuit",
            "collaboration",
            "complexity",
            "concept",
            "conglomeration",
            "contingency",
            "core",
            "customer loyalty",
            "database",
            "data-warehouse",
            "definition",
            "emulation",
            "encoding",
            "encryption",
            "extranet",
            "firmware",
            "flexibility",
            "focus group",
            "forecast",
            "frame",
            "framework",
            "function",
            "functionalities",
            "Graphic Interface",
            "groupware",
            "Graphical User Interface",
            "hardware",
            "help-desk",
            "hierarchy",
            "hub",
            "implementation",
            "info-mediaries",
            "infrastructure",
            "initiative",
            "installation",
            "instruction set",
            "interface",
            "Internet solution",
            "intranet",
            "knowledge user",
            "knowledgebase",
            "Local Area Network",
            "leverage",
            "matrices",
            "matrix",
            "methodology",
            "middleware",
            "migration",
            "model",
            "moderator",
            "monitoring",
            "moratorium",
            "neural-net",
            "open architecture",
            "open system",
            "orchestration",
            "paradigm",
            "parallelism",
            "policy",
            "portal",
            "pricing structure",
            "process improvement",
            "product",
            "productivity",
            "project",
            "projection",
            "protocol",
            "secured line",
            "service-desk",
            "software",
            "solution",
            "standardization",
            "strategy",
            "structure",
            "success",
            "superstructure",
            "support",
            "synergy",
            "system engine",
            "task-force",
            "throughput",
            "time-frame",
            "toolset",
            "utilization",
            "website",
            "workforce",
        ),
    )

    bsWords: Tuple[ElementsType[str], ...] = (
        (
            "implement",
            "utilize",
            "integrate",
            "streamline",
            "optimize",
            "evolve",
            "transform",
            "embrace",
            "enable",
            "orchestrate",
            "leverage",
            "reinvent",
            "aggregate",
            "architect",
            "enhance",
            "incentivize",
            "morph",
            "empower",
            "envisioneer",
            "monetize",
            "harness",
            "facilitate",
            "seize",
            "disintermediate",
            "synergize",
            "strategize",
            "deploy",
            "brand",
            "grow",
            "target",
            "syndicate",
            "synthesize",
            "deliver",
            "mesh",
            "incubate",
            "engage",
            "maximize",
            "benchmark",
            "expedite",
            "re-intermediate",
            "whiteboard",
            "visualize",
            "repurpose",
            "innovate",
            "scale",
            "unleash",
            "drive",
            "extend",
            "engineer",
            "revolutionize",
            "generate",
            "exploit",
            "transition",
            "e-enable",
            "iterate",
            "cultivate",
            "matrix",
            "productize",
            "redefine",
            "re-contextualize",
        ),
        (
            "clicks-and-mortar",
            "value-added",
            "vertical",
            "proactive",
            "robust",
            "revolutionary",
            "scalable",
            "leading-edge",
            "innovative",
            "intuitive",
            "strategic",
            "e-business",
            "mission-critical",
            "sticky",
            "one-to-one",
            "24/7",
            "end-to-end",
            "global",
            "B2B",
            "B2C",
            "granular",
            "frictionless",
            "virtual",
            "viral",
            "dynamic",
            "24/365",
            "best-of-breed",
            "killer",
            "magnetic",
            "bleeding-edge",
            "web-enabled",
            "interactive",
            "dot-com",
            "back-end",
            "real-time",
            "efficient",
            "front-end",
            "distributed",
            "seamless",
            "extensible",
            "turn-key",
            "world-class",
            "open-source",
            "cross-platform",
            "cross-media",
            "synergistic",
            "bricks-and-clicks",
            "out-of-the-box",
            "enterprise",
            "integrated",
            "impactful",
            "wireless",
            "transparent",
            "next-generation",
            "cutting-edge",
            "user-centric",
            "visionary",
            "customized",
            "ubiquitous",
            "plug-and-play",
            "collaborative",
            "compelling",
            "holistic",
            "rich",
        ),
        (
            "synergies",
            "web-readiness",
            "paradigms",
            "markets",
            "partnerships",
            "infrastructures",
            "platforms",
            "initiatives",
            "channels",
            "eyeballs",
            "communities",
            "ROI",
            "solutions",
            "e-tailers",
            "e-services",
            "action-items",
            "portals",
            "niches",
            "technologies",
            "content",
            "vortals",
            "supply-chains",
            "convergence",
            "relationships",
            "architectures",
            "interfaces",
            "e-markets",
            "e-commerce",
            "systems",
            "bandwidth",
            "info-mediaries",
            "models",
            "mindshare",
            "deliverables",
            "users",
            "schemas",
            "networks",
            "applications",
            "metrics",
            "e-business",
            "functionalities",
            "experiences",
            "web services",
            "methodologies",
        ),
    )

    def company(self) -> str:
        """
        :example: 'Acme Ltd'
        """
        pattern: str = self.random_element(self.formats)
        return self.generator.parse(pattern)

    def company_suffix(self) -> str:
        """
        :example: 'Ltd'
        """
        return self.random_element(self.company_suffixes)

    def catch_phrase(self) -> str:
        """
        :example: 'Robust full-range hub'
        """
        return " ".join([self.random_element(word_list) for word_list in self.catch_phrase_words])

    def bs(self) -> str:
        """
        :example: 'integrate extensible convergence'
        """
        return " ".join([self.random_element(word_list) for word_list in self.bsWords])
