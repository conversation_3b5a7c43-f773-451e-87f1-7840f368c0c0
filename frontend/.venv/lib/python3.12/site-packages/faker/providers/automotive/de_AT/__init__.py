import string

from .. import Provider as AutomotiveProvider


class Provider(AutomotiveProvider):
    """Implement automotive provider for ``de_AT`` locale.

    Sources:

    - https://de.wikipedia.org/wiki/Kfz-Kennzeichen_(%C3%96sterreich)
    """

    license_plate_prefix = (
        "A",
        "AM",
        "B",
        "BA",
        "BB",
        "BD",
        "BG",
        "BH",
        "BK",
        "BL",
        "BM",
        "BN",
        "BP",
        "BR",
        "BZ",
        "DL",
        "DO",
        "E",
        "EF",
        "EU",
        "FB",
        "FE",
        "FF",
        "FK",
        "FR",
        "FV",
        "FW",
        "G",
        "GB",
        "GD",
        "GF",
        "GK",
        "GM",
        "GR",
        "GS",
        "GU",
        "HA",
        "HB",
        "HE",
        "HF",
        "HL",
        "HO",
        "I",
        "IL",
        "IM",
        "JE",
        "JO",
        "JU",
        "JW",
        "K",
        "KB",
        "KD",
        "KF",
        "KG",
        "KI",
        "KK",
        "KL",
        "KO",
        "KR",
        "KS",
        "KU",
        "L",
        "LA",
        "LB",
        "LD",
        "LE",
        "LF",
        "LI",
        "LK",
        "LL",
        "LN",
        "LZ",
        "MA",
        "MD",
        "ME",
        "MI",
        "MT",
        "MU",
        "MZ",
        "N",
        "ND",
        "NK",
        "O",
        "OP",
        "OW",
        "P",
        "PE",
        "PL",
        "PT",
        "RA",
        "RE",
        "RI",
        "RO",
        "S",
        "SB",
        "SD",
        "SE",
        "SK",
        "SL",
        "SO",
        "SP",
        "SR",
        "ST",
        "SV",
        "SW",
        "SZ",
        "T",
        "TA",
        "TD",
        "TK",
        "TU",
        "UU",
        "V",
        "VB",
        "VD",
        "VI",
        "VK",
        "VL",
        "VO",
        "W",
        "WB",
        "WD",
        "WE",
        "WK",
        "WL",
        "WN",
        "WO",
        "WT",
        "WU",
        "WY",
        "WZ",
        "ZE",
        "ZT",
        "ZW",
    )

    license_plate_suffix_for_one_starting_letter = ("-%# ???", "-%## ???", "-%## ??", "-%### ??", "-%### ?", "-%#### ?")

    license_plate_suffix_for_two_starting_letters = (
        "-% ???",
        "-%# ???",
        "-%# ??",
        "-%## ??",
        "-%## ?",
        "-%### ?",
    )

    def license_plate(self) -> str:
        """Generate a license plate."""
        prefix: str = self.random_element(self.license_plate_prefix)

        if len(prefix) == 1:
            suffix = self.bothify(
                self.random_element(self.license_plate_suffix_for_one_starting_letter),
                letters=string.ascii_uppercase,
            )
        else:
            suffix = self.bothify(
                self.random_element(self.license_plate_suffix_for_two_starting_letters),
                letters=string.ascii_uppercase,
            )

        return prefix + suffix
