from collections import OrderedDict

from .. import Provider as PersonProvider


class Provider(PersonProvider):
    # update: 2025 04 30

    # source:
    # 中華民國(ROC)人口 2025 3月: 23,374,742
    # (As of March 2025, the total population of the Republic of China (Taiwan) is 23,374,742.)
    # https://www.ris.gov.tw/app/portal/346

    # 臺灣原住民人口 2024 12月 612,000
    # (As of December 2024, the indigenous population in Taiwan is approximately 612,000, accounting for 2.7% of the
    # total population.)
    # https://www.moi.gov.tw/News_Content.aspx?n=2905&sms=10305&s=325345

    # Although most Taiwanese people are ethnically Han, their culture has diverged significantly from mainland China
    # over centuries.
    # Taiwan’s Han communities—like Hoklo and Hakka—have developed unique languages, customs, and identities distinct
    # from Chinese people today.
    # *Taiwanese Indigenous peoples traditionally have their own naming systems*,
    # which are different from Han Chinese names—they often reflect tribal identity, family lineage, or personal traits.

    formats_female = OrderedDict(
        (
            ("{{last_name}}{{first_name_female}}", 1),  # 漢人 Han
            # ("{{first_indigenous_name_female}} {{last_indigenous_name}}", 0.027), # 原住民 Taiwanese Indigenous Peoples
        )
    )

    formats_male = OrderedDict(
        (
            ("{{last_name}}{{first_name_male}}", 1),  # 漢人 Han
            # ("{{first_indigenous_name_male}} {{last_indigenous_name}}", 0.027), # 原住民 Taiwanese Indigenous Peoples
        )
    )

    formats = formats_male.copy()
    formats.update(formats_female)

    # =============================================================================

    # source:
    # 中華民國(ROC)全國姓名統計 2023/6月
    # (National Name Statistics of the Republic of China (Taiwan), June 2023)
    # https://www.ris.gov.tw/documents/data/5/2/112namestat.pdf
    # page 267: TOP 100 female first name
    # page 281: The top 10 most common female names by year of birth

    first_names_female = OrderedDict(
        (
            # top 100 names in all ages
            ("淑芬", 0.14),
            ("淑惠", 0.13),
            ("美玲", 0.12),
            ("麗華", 0.11),
            ("美惠", 0.11),
            ("淑貞", 0.1),
            ("雅婷", 0.1),
            ("秀英", 0.1),
            ("淑娟", 0.1),
            ("秀琴", 0.1),
            ("秀美", 0.09),
            ("美華", 0.09),
            ("怡君", 0.09),
            ("淑華", 0.09),
            ("美玉", 0.09),
            ("雅惠", 0.08),
            ("秀蘭", 0.08),
            ("淑美", 0.08),
            ("秀鳳", 0.08),
            ("美珠", 0.07),
            ("麗珠", 0.07),
            ("麗娟", 0.07),
            ("淑玲", 0.07),
            ("美雲", 0.07),
            ("雅雯", 0.07),
            ("雅玲", 0.07),
            ("美麗", 0.06),
            ("玉蘭", 0.06),
            ("月娥", 0.06),
            ("麗卿", 0.06),
            ("惠美", 0.06),
            ("麗美", 0.06),
            ("秀珠", 0.06),
            ("淑珍", 0.05),
            ("欣怡", 0.05),
            ("素貞", 0.05),
            ("秀珍", 0.05),
            ("素珍", 0.05),
            ("惠玲", 0.05),
            ("玉梅", 0.05),
            ("玉英", 0.05),
            ("淑慧", 0.05),
            ("秀玲", 0.05),
            ("明珠", 0.05),
            ("秋香", 0.05),
            ("秀玉", 0.05),
            ("麗雲", 0.05),
            ("秀梅", 0.05),
            ("麗玉", 0.05),
            ("寶珠", 0.05),
            ("怡婷", 0.05),
            ("麗玲", 0.05),
            ("宜蓁", 0.04),
            ("月英", 0.04),
            ("淑芳", 0.04),
            ("玉玲", 0.04),
            ("秀雲", 0.04),
            ("慧玲", 0.04),
            ("春美", 0.04),
            ("碧霞", 0.04),
            ("麗香", 0.04),
            ("美鳳", 0.04),
            ("美珍", 0.04),
            ("美英", 0.04),
            ("碧珠", 0.04),
            ("碧雲", 0.04),
            ("佳蓉", 0.04),
            ("美蘭", 0.04),
            ("秀娟", 0.04),
            ("美娟", 0.04),
            ("淑敏", 0.04),
            ("玉珍", 0.04),
            ("淑卿", 0.04),
            ("美慧", 0.04),
            ("靜宜", 0.04),
            ("素珠", 0.04),
            ("雅慧", 0.04),
            ("靜怡", 0.04),
            ("玉美", 0.04),
            ("雅萍", 0.04),
            ("素卿", 0.04),
            ("素琴", 0.04),
            ("秀枝", 0.04),
            ("金蓮", 0.04),
            ("秋月", 0.04),
            ("麗雪", 0.04),
            ("惠珍", 0.04),
            ("心怡", 0.04),
            ("佳玲", 0.04),
            ("鈺婷", 0.04),
            ("詩涵", 0.04),
            ("秀霞", 0.04),
            ("秀華", 0.03),
            ("麗琴", 0.03),
            ("金鳳", 0.03),
            ("麗珍", 0.03),
            ("玉鳳", 0.03),
            ("玉琴", 0.03),
            ("秀蓮", 0.03),
            ("素蘭", 0.03),
            # top n names in younger generation
            ("婉婷", 0.01),
            ("佩珊", 0.01),
            ("怡萱", 0.01),
            ("雅筑", 0.01),
            ("郁婷", 0.01),
            ("宜庭", 0.01),
            ("欣妤", 0.01),
            ("思妤", 0.01),
            ("佳穎", 0.01),
            ("品妤", 0.01),
            ("子涵", 0.01),
            ("品妍", 0.01),
            ("子晴", 0.01),
            ("詠晴", 0.01),
            ("禹彤", 0.01),
            ("羽彤", 0.01),
            ("芯語", 0.01),
            ("宥蓁", 0.01),
            ("語彤", 0.01),
            ("苡晴", 0.01),
            ("苡菲", 0.01),
            ("雨霏", 0.01),
            ("芸菲", 0.01),
            ("苡安", 0.01),
            ("玥彤", 0.01),
        )
    )

    # source:
    # 中華民國(ROC)全國姓名統計 2023/6月
    # (National Name Statistics of the Republic of China (Taiwan), June 2023)
    # https://www.ris.gov.tw/documents/data/5/2/112namestat.pdf
    # page 266: TOP 100 male first name
    # page 280: The top 10 most common male names by year of birth

    first_names_male = OrderedDict(
        (
            # top 100 names in all ages
            ("家豪", 0.06),
            ("志明", 0.05),
            ("建宏", 0.05),
            ("俊傑", 0.05),
            ("俊宏", 0.05),
            ("志豪", 0.05),
            ("志偉", 0.05),
            ("承翰", 0.04),
            ("冠宇", 0.04),
            ("志強", 0.04),
            ("宗翰", 0.04),
            ("志宏", 0.04),
            ("冠廷", 0.04),
            ("志成", 0.04),
            ("文雄", 0.04),
            ("承恩", 0.04),
            ("金龍", 0.04),
            ("文彬", 0.03),
            ("正雄", 0.03),
            ("明輝", 0.03),
            ("柏翰", 0.03),
            ("彥廷", 0.03),
            ("明德", 0.03),
            ("文龍", 0.03),
            ("俊賢", 0.03),
            ("志忠", 0.03),
            ("國華", 0.03),
            ("信宏", 0.03),
            ("家銘", 0.03),
            ("俊雄", 0.03),
            ("宇翔", 0.03),
            ("建成", 0.03),
            ("冠霖", 0.03),
            ("志銘", 0.02),
            ("志雄", 0.02),
            ("進財", 0.02),
            ("明哲", 0.02),
            ("榮華", 0.02),
            ("柏宇", 0.02),
            ("志鴻", 0.02),
            ("志賢", 0.02),
            ("俊良", 0.02),
            ("建華", 0.02),
            ("家瑋", 0.02),
            ("家榮", 0.02),
            ("文祥", 0.02),
            ("建志", 0.02),
            ("文正", 0.02),
            ("文忠", 0.02),
            ("凱翔", 0.02),
            ("家宏", 0.02),
            ("國雄", 0.02),
            ("明宏", 0.02),
            ("文賢", 0.02),
            ("世昌", 0.02),
            ("哲瑋", 0.02),
            ("文傑", 0.02),
            ("正義", 0.02),
            ("武雄", 0.02),
            ("建興", 0.02),
            ("志文", 0.02),
            ("嘉宏", 0.02),
            ("文章", 0.02),
            ("明宗", 0.02),
            ("宇軒", 0.02),
            ("進興", 0.02),
            ("俊豪", 0.02),
            ("俊廷", 0.02),
            ("冠宏", 0.02),
            ("仁傑", 0.02),
            ("威廷", 0.02),
            ("哲維", 0.02),
            ("宗霖", 0.02),
            ("文欽", 0.02),
            ("博文", 0.02),
            ("俊男", 0.02),
            ("宗憲", 0.02),
            ("子豪", 0.02),
            ("俊宇", 0.02),
            ("勝雄", 0.02),
            ("柏諺", 0.02),
            ("建良", 0.02),
            ("俊明", 0.02),
            ("俊銘", 0.02),
            ("世明", 0.02),
            ("義雄", 0.02),
            ("建銘", 0.02),
            ("永昌", 0.02),
            ("文華", 0.02),
            ("子翔", 0.02),
            ("柏宏", 0.02),
            ("政宏", 0.02),
            ("進發", 0.02),
            ("柏霖", 0.02),
            ("建中", 0.02),
            ("國榮", 0.02),
            ("志誠", 0.02),
            ("聰明", 0.02),
            ("俊佑", 0.02),
            ("志遠", 0.02),
            # top n names in younger generation
            ("宥廷", 0.01),
            ("品睿", 0.01),
            ("宸睿", 0.01),
            ("宇恩", 0.01),
            ("宥辰", 0.01),
            ("柏睿", 0.01),
            ("睿恩", 0.01),
            ("恩碩", 0.01),
            ("子睿", 0.01),
            ("子宸", 0.01),
            ("子恩", 0.01),
        )
    )

    # source:
    # 中華民國(ROC)全國姓名統計 2023/6月
    # (National Name Statistics of the Republic of China (Taiwan), June 2023)
    # https://www.ris.gov.tw/documents/data/5/2/112namestat.pdf
    # page 282, 283, 284: TOP 200 last name

    last_names = OrderedDict(
        (
            ("陳", 11.2),
            ("林", 8.33),
            ("黃", 6),
            ("張", 5.3),
            ("李", 5.13),
            ("王", 4.09),
            ("吳", 4),
            ("劉", 3.16),
            ("蔡", 2.93),
            ("楊", 2.64),
            ("許", 2.31),
            ("鄭", 1.89),
            ("謝", 1.77),
            ("洪", 1.51),
            ("郭", 1.5),
            ("邱", 1.47),
            ("曾", 1.45),
            ("廖", 1.35),
            ("賴", 1.33),
            ("徐", 1.26),
            ("周", 1.21),
            ("葉", 1.18),
            ("蘇", 1.14),
            ("莊", 0.95),
            ("江", 0.92),
            ("呂", 0.91),
            ("何", 0.85),
            ("蕭", 0.83),
            ("羅", 0.83),
            ("高", 0.77),
            ("潘", 0.69),
            ("簡", 0.68),
            ("朱", 0.66),
            ("鍾", 0.65),
            ("游", 0.59),
            ("彭", 0.59),
            ("詹", 0.58),
            ("施", 0.54),
            ("胡", 0.54),
            ("沈", 0.51),
            ("余", 0.51),
            ("盧", 0.48),
            ("梁", 0.46),
            ("趙", 0.44),
            ("顏", 0.44),
            ("柯", 0.44),
            ("翁", 0.4),
            ("魏", 0.38),
            ("孫", 0.36),
            ("戴", 0.35),
            ("范", 0.34),
            ("方", 0.33),
            ("宋", 0.32),
            ("鄧", 0.27),
            ("杜", 0.23),
            ("侯", 0.23),
            ("傅", 0.22),
            ("曹", 0.22),
            ("薛", 0.21),
            ("阮", 0.21),
            ("丁", 0.21),
            ("卓", 0.19),
            ("馬", 0.18),
            ("温", 0.18),
            ("董", 0.18),
            ("藍", 0.18),
            ("古", 0.18),
            ("石", 0.18),
            ("紀", 0.17),
            ("唐", 0.17),
            ("蔣", 0.17),
            ("姚", 0.17),
            ("連", 0.17),
            ("歐", 0.16),
            ("馮", 0.16),
            ("程", 0.16),
            ("湯", 0.15),
            ("田", 0.15),
            ("康", 0.15),
            ("黄", 0.15),
            ("姜", 0.15),
            ("白", 0.14),
            ("汪", 0.14),
            ("尤", 0.14),
            ("鄒", 0.14),
            ("黎", 0.13),
            ("巫", 0.12),
            ("鐘", 0.12),
            ("涂", 0.12),
            ("龔", 0.11),
            ("嚴", 0.09),
            ("韓", 0.09),
            ("袁", 0.09),
            ("金", 0.08),
            ("童", 0.08),
            ("陸", 0.07),
            ("柳", 0.07),
            ("凃", 0.07),
            ("夏", 0.07),
            ("邵", 0.07),
            ("錢", 0.06),
            ("伍", 0.06),
            ("倪", 0.06),
            ("溫", 0.06),
            ("駱", 0.05),
            ("譚", 0.05),
            ("于", 0.05),
            ("甘", 0.05),
            ("熊", 0.05),
            ("任", 0.05),
            ("秦", 0.05),
            ("章", 0.05),
            ("毛", 0.05),
            ("官", 0.05),
            ("顧", 0.05),
            ("史", 0.05),
            ("萬", 0.05),
            ("俞", 0.05),
            ("粘", 0.04),
            ("雷", 0.04),
            ("饒", 0.04),
            ("張簡", 0.04),
            ("闕", 0.04),
            ("凌", 0.04),
            ("武", 0.03),
            ("孔", 0.03),
            ("尹", 0.03),
            ("崔", 0.03),
            ("辛", 0.03),
            ("歐陽", 0.03),
            ("辜", 0.03),
            ("陶", 0.03),
            ("段", 0.03),
            ("易", 0.03),
            ("龍", 0.03),
            ("韋", 0.03),
            ("池", 0.03),
            ("葛", 0.03),
            ("褚", 0.03),
            ("孟", 0.02),
            ("麥", 0.02),
            ("殷", 0.02),
            ("莫", 0.02),
            ("文", 0.02),
            ("賀", 0.02),
            ("賈", 0.02),
            ("管", 0.02),
            ("關", 0.02),
            ("包", 0.02),
            ("向", 0.02),
            ("丘", 0.02),
            ("范姜", 0.02),
            ("梅", 0.02),
            ("華", 0.02),
            ("裴", 0.02),
            ("利", 0.02),
            ("全", 0.02),
            ("樊", 0.02),
            ("房", 0.02),
            ("佘", 0.02),
            ("花", 0.01),
            ("安", 0.01),
            ("左", 0.01),
            ("魯", 0.01),
            ("塗", 0.01),
            ("穆", 0.01),
            ("鮑", 0.01),
            ("蒲", 0.01),
            ("郝", 0.01),
            ("谷", 0.01),
            ("成", 0.01),
            ("邢", 0.01),
            ("練", 0.01),
            ("閻", 0.01),
            ("鄔", 0.01),
            ("陽", 0.01),
            ("盛", 0.01),
            ("常", 0.01),
            ("符", 0.01),
            ("耿", 0.01),
            ("解", 0.01),
            ("繆", 0.01),
            ("申", 0.01),
            ("聶", 0.01),
            ("祝", 0.01),
            ("岳", 0.01),
            ("曲", 0.01),
            ("籃", 0.01),
            ("齊", 0.01),
            ("應", 0.01),
            ("舒", 0.01),
            ("單", 0.01),
            ("喬", 0.01),
            ("畢", 0.01),
            ("留", 0.01),
            ("鄞", 0.01),
            ("翟", 0.01),
            ("牛", 0.01),
            ("龎", 0.01),
            ("覃", 0.01),
        )
    )

    first_names = first_names_male.copy()
    first_names.update(first_names_female)

    # =============================================================================

    # From https://en.wikipedia.org/wiki/Chinese_given_name#Common_Chinese_names
    # The above information is slightly incorrect.

    # 使用 pypinyin 進行姓名翻譯：https://github.com/mozillazg/python-pinyin
    # Using pypinyin for name translation: https://github.com/mozillazg/python-pinyin

    # print(lazy_pinyin("許", style=Style.WADEGILES, v_to_u=True)[0].replace("'","").upper().replace("Ü","U"))
    # 轉換過程有部分姓氏拼音剛好是重複的或是複姓的
    # 因為重建過程字典的特性無法重複所以就被忽略了 目前懶得修ouo
    # Some surnames result in duplicate transliterations during the conversion process.
    # Due to the nature of dictionaries (no duplicate keys), duplicates are ignored during reconstruction.

    # 使用威妥瑪拼音，而不是漢語拼音
    # Using Wade–Giles romanization instead of Hanyu Pinyin

    last_romanized_names = OrderedDict(
        (
            ("CHEN", 11.2),
            ("LIN", 8.33),
            ("HUANG", 0.15),
            ("CHANG", 0.01),
            ("LI", 0.02),
            ("WANG", 0.14),
            ("WU", 0.01),
            ("LIU", 0.01),
            ("TSAI", 2.93),
            ("YANG", 0.01),
            ("HSU", 1.26),
            ("CHENG", 0.01),
            ("HSIEH", 1.77),
            ("HUNG", 1.51),
            ("KUO", 1.5),
            ("CHIU", 0.02),
            ("TSENG", 1.45),
            ("LIAO", 1.35),
            ("LEI", 0.04),
            ("CHOU", 1.21),
            ("YEH", 1.18),
            ("SU", 1.14),
            ("CHUANG", 0.95),
            ("CHIANG", 0.15),
            ("LU", 0.01),
            ("HO", 0.02),
            ("HSIAO", 0.83),
            ("LO", 0.05),
            ("KAO", 0.77),
            ("PAN", 0.69),
            ("CHIEN", 0.06),
            ("CHU", 0.01),
            ("CHUNG", 0.12),
            ("YU", 0.05),
            ("PENG", 0.59),
            ("CHAN", 0.04),
            ("SHIH", 0.05),
            ("HU", 0.54),
            ("SHEN", 0.01),
            ("LIANG", 0.46),
            ("CHAO", 0.44),
            ("YEN", 0.01),
            ("KO", 0.03),
            ("WENG", 0.4),
            ("WEI", 0.03),
            ("SUN", 0.36),
            ("TAI", 0.35),
            ("FAN", 0.02),
            ("FANG", 0.02),
            ("SUNG", 0.32),
            ("TENG", 0.27),
            ("TU", 0.01),
            ("HOU", 0.23),
            ("FU", 0.01),
            ("TSAO", 0.22),
            ("HSUEH", 0.21),
            ("JUAN", 0.21),
            ("TING", 0.21),
            ("CHO", 0.19),
            ("MA", 0.18),
            ("WEN", 0.02),
            ("TUNG", 0.08),
            ("LAN", 0.01),
            ("KU", 0.01),
            ("CHI", 0.01),
            ("TANG", 0.15),
            ("YAO", 0.17),
            ("LIEN", 0.01),
            ("OU", 0.03),
            ("FENG", 0.16),
            ("TIEN", 0.15),
            ("KANG", 0.15),
            ("PAI", 0.14),
            ("TSOU", 0.14),
            ("KUNG", 0.03),
            ("HAN", 0.09),
            ("YUAN", 0.09),
            ("CHIN", 0.05),
            ("HSIA", 0.07),
            ("SHAO", 0.07),
            ("NI", 0.06),
            ("TAN", 0.01),
            ("KAN", 0.05),
            ("HSIUNG", 0.05),
            ("JEN", 0.05),
            ("MAO", 0.05),
            ("KUAN", 0.02),
            ("WAN", 0.05),
            ("JAO", 0.04),
            ("CHUEH", 0.04),
            ("LING", 0.04),
            ("YIN", 0.01),
            ("TSUI", 0.03),
            ("HSIN", 0.03),
            ("TAO", 0.03),
            ("TUAN", 0.03),
            ("I", 0.03),
            ("LUNG", 0.03),
            ("CHIH", 0.03),
            ("MENG", 0.02),
            ("MEI", 0.02),
            ("MO", 0.02),
            ("CHIA", 0.02),
            ("PAO", 0.01),
            ("HSIANG", 0.02),
            ("HUA", 0.01),
            ("PEI", 0.02),
            ("CHUAN", 0.02),
            ("SHE", 0.02),
            ("AN", 0.01),
            ("TSO", 0.01),
            ("MU", 0.01),
            ("PU", 0.01),
            ("HAO", 0.01),
            ("HSING", 0.01),
            ("SHENG", 0.01),
            ("KENG", 0.01),
            ("CHIEH", 0.01),
            ("MOU", 0.01),
            ("NIEH", 0.01),
            ("YUEH", 0.01),
            ("YING", 0.01),
            ("SHU", 0.01),
            ("CHIAO", 0.01),
            ("PI", 0.01),
            ("TI", 0.01),
            ("NIU", 0.01),
            ("PANG", 0.01),
        )
    )

    first_romanized_names_male = OrderedDict(
        (
            ("CHIA-HAO", 0.06),
            ("CHIH-MING", 0.02),
            ("CHIEN-HUNG", 0.05),
            ("CHUN-CHIEH", 0.05),
            ("CHUN-HUNG", 0.05),
            ("CHIH-HAO", 0.05),
            ("CHIH-WEI", 0.05),
            ("CHENG-HAN", 0.04),
            ("KUAN-YU", 0.04),
            ("CHIH-CHIANG", 0.04),
            ("TSUNG-HAN", 0.04),
            ("CHIH-HUNG", 0.02),
            ("KUAN-TING", 0.04),
            ("CHIH-CHENG", 0.02),
            ("WEN-HSIUNG", 0.04),
            ("CHENG-EN", 0.04),
            ("CHIN-LUNG", 0.04),
            ("WEN-PIN", 0.03),
            ("CHENG-HSIUNG", 0.03),
            ("MING-HUI", 0.03),
            ("PAI-HAN", 0.03),
            ("YEN-TING", 0.03),
            ("MING-TE", 0.03),
            ("WEN-LUNG", 0.03),
            ("CHUN-HSIEN", 0.03),
            ("CHIH-CHUNG", 0.03),
            ("KUO-HUA", 0.03),
            ("HSIN-HUNG", 0.03),
            ("CHIA-MING", 0.03),
            ("CHUN-HSIUNG", 0.03),
            ("YU-HSIANG", 0.03),
            ("CHIEN-CHENG", 0.03),
            ("KUAN-LIN", 0.03),
            ("CHIH-HSIUNG", 0.02),
            ("CHIN-TSAI", 0.02),
            ("MING-CHE", 0.02),
            ("JUNG-HUA", 0.02),
            ("PAI-YU", 0.02),
            ("CHIH-HSIEN", 0.02),
            ("CHUN-LIANG", 0.02),
            ("CHIEN-HUA", 0.02),
            ("CHIA-WEI", 0.02),
            ("CHIA-JUNG", 0.02),
            ("WEN-HSIANG", 0.02),
            ("CHIEN-CHIH", 0.02),
            ("WEN-CHENG", 0.02),
            ("WEN-CHUNG", 0.02),
            ("KAI-HSIANG", 0.02),
            ("CHIA-HUNG", 0.02),
            ("KUO-HSIUNG", 0.02),
            ("MING-HUNG", 0.02),
            ("WEN-HSIEN", 0.02),
            ("SHIH-CHANG", 0.02),
            ("CHE-WEI", 0.02),
            ("WEN-CHIEH", 0.02),
            ("CHENG-I", 0.02),
            ("WU-HSIUNG", 0.02),
            ("CHIEN-HSING", 0.02),
            ("CHIH-WEN", 0.02),
            ("WEN-CHANG", 0.02),
            ("MING-TSUNG", 0.02),
            ("YU-HSUAN", 0.02),
            ("CHIN-HSING", 0.02),
            ("CHUN-HAO", 0.02),
            ("CHUN-TING", 0.02),
            ("KUAN-HUNG", 0.02),
            ("JEN-CHIEH", 0.02),
            ("WEI-TING", 0.02),
            ("TSUNG-LIN", 0.02),
            ("WEN-CHIN", 0.02),
            ("PO-WEN", 0.02),
            ("CHUN-NAN", 0.02),
            ("TSUNG-HSIEN", 0.02),
            ("TZU-HAO", 0.02),
            ("CHUN-YU", 0.02),
            ("SHENG-HSIUNG", 0.02),
            ("PAI-YEN", 0.02),
            ("CHIEN-LIANG", 0.02),
            ("CHUN-MING", 0.02),
            ("SHIH-MING", 0.02),
            ("I-HSIUNG", 0.02),
            ("CHIEN-MING", 0.02),
            ("YUNG-CHANG", 0.02),
            ("WEN-HUA", 0.02),
            ("TZU-HSIANG", 0.02),
            ("PAI-HUNG", 0.02),
            ("CHENG-HUNG", 0.02),
            ("CHIN-FA", 0.02),
            ("PAI-LIN", 0.02),
            ("CHIEN-CHUNG", 0.02),
            ("KUO-JUNG", 0.02),
            ("TSUNG-MING", 0.02),
            ("CHIH-YUAN", 0.02),
            ("YU-TING", 0.01),
            ("PIN-JUI", 0.01),
            ("CHEN-JUI", 0.01),
            ("YU-EN", 0.01),
            ("YU-CHEN", 0.01),
            ("PAI-JUI", 0.01),
            ("JUI-EN", 0.01),
            ("EN-SHO", 0.01),
            ("TZU-JUI", 0.01),
            ("TZU-CHEN", 0.01),
            ("TZU-EN", 0.01),
        )
    )

    first_romanized_names_female = OrderedDict(
        (
            ("SHU-FEN", 0.14),
            ("SHU-HUI", 0.05),
            ("MEI-LING", 0.12),
            ("LI-HUA", 0.11),
            ("MEI-HUI", 0.04),
            ("SHU-CHEN", 0.05),
            ("YA-TING", 0.1),
            ("HSIU-YING", 0.1),
            ("SHU-CHUAN", 0.1),
            ("HSIU-CHIN", 0.1),
            ("HSIU-MEI", 0.05),
            ("MEI-HUA", 0.09),
            ("I-CHUN", 0.09),
            ("SHU-HUA", 0.09),
            ("MEI-YU", 0.09),
            ("YA-HUI", 0.04),
            ("HSIU-LAN", 0.08),
            ("SHU-MEI", 0.08),
            ("HSIU-FENG", 0.08),
            ("MEI-CHU", 0.07),
            ("LI-CHU", 0.07),
            ("LI-CHUAN", 0.07),
            ("SHU-LING", 0.07),
            ("MEI-YUN", 0.07),
            ("YA-WEN", 0.07),
            ("YA-LING", 0.07),
            ("MEI-LI", 0.06),
            ("YU-LAN", 0.06),
            ("YUEH-O", 0.06),
            ("LI-CHING", 0.06),
            ("HUI-MEI", 0.06),
            ("LI-MEI", 0.06),
            ("HSIU-CHU", 0.06),
            ("HSIN-I", 0.04),
            ("SU-CHEN", 0.05),
            ("HSIU-CHEN", 0.05),
            ("HUI-LING", 0.04),
            ("YU-MEI", 0.04),
            ("YU-YING", 0.05),
            ("HSIU-LING", 0.05),
            ("MING-CHU", 0.05),
            ("CHIU-HSIANG", 0.05),
            ("HSIU-YU", 0.05),
            ("LI-YUN", 0.05),
            ("LI-YU", 0.05),
            ("PAO-CHU", 0.05),
            ("I-TING", 0.01),
            ("LI-LING", 0.05),
            ("I-CHEN", 0.04),
            ("YUEH-YING", 0.04),
            ("SHU-FANG", 0.04),
            ("YU-LING", 0.04),
            ("HSIU-YUN", 0.04),
            ("CHUN-MEI", 0.04),
            ("PI-HSIA", 0.04),
            ("LI-HSIANG", 0.04),
            ("MEI-FENG", 0.04),
            ("MEI-CHEN", 0.04),
            ("MEI-YING", 0.04),
            ("PI-CHU", 0.04),
            ("PI-YUN", 0.04),
            ("CHIA-JUNG", 0.04),
            ("MEI-LAN", 0.04),
            ("HSIU-CHUAN", 0.04),
            ("MEI-CHUAN", 0.04),
            ("SHU-MIN", 0.04),
            ("YU-CHEN", 0.01),
            ("SHU-CHING", 0.04),
            ("CHING-I", 0.04),
            ("SU-CHU", 0.04),
            ("YA-PING", 0.04),
            ("SU-CHING", 0.04),
            ("SU-CHIN", 0.04),
            ("HSIU-CHIH", 0.04),
            ("CHIN-LIEN", 0.04),
            ("CHIU-YUEH", 0.04),
            ("LI-HSUEH", 0.04),
            ("HUI-CHEN", 0.04),
            ("CHIA-LING", 0.04),
            ("YU-TING", 0.01),
            ("SHIH-HAN", 0.04),
            ("HSIU-HSIA", 0.04),
            ("HSIU-HUA", 0.03),
            ("LI-CHIN", 0.03),
            ("CHIN-FENG", 0.03),
            ("LI-CHEN", 0.03),
            ("YU-FENG", 0.03),
            ("YU-CHIN", 0.03),
            ("HSIU-LIEN", 0.03),
            ("SU-LAN", 0.03),
            ("WAN-TING", 0.01),
            ("PEI-SHAN", 0.01),
            ("I-HSUAN", 0.01),
            ("YA-CHU", 0.01),
            ("HSIN-YU", 0.01),
            ("SSU-YU", 0.01),
            ("CHIA-YING", 0.01),
            ("PIN-YU", 0.01),
            ("TZU-HAN", 0.01),
            ("PIN-YEN", 0.01),
            ("TZU-CHING", 0.01),
            ("YUNG-CHING", 0.01),
            ("YU-TUNG", 0.01),
            ("I-CHING", 0.01),
            ("I-FEI", 0.01),
            ("YU-FEI", 0.01),
            ("YUN-FEI", 0.01),
            ("I-AN", 0.01),
            ("YUEH-TUNG", 0.01),
        )
    )

    first_romanized_names = first_romanized_names_male.copy()
    first_romanized_names.update(first_romanized_names_female)

    romanized_formats_female = OrderedDict(
        (("{{last_romanized_name}} {{first_romanized_name_female}}", 1),)  # 漢人 Han
    )

    romanized_formats_male = OrderedDict((("{{last_romanized_name}} {{first_romanized_name_male}}", 1),))  # 漢人 Han

    romanized_formats = romanized_formats_male.copy()
    romanized_formats.update(romanized_formats_female)

    def first_romanized_name_male(self) -> str:  # 只有jp有實作
        """
        :example: 'CHIA-HAO'
        """
        return self.random_element(self.first_romanized_names_male)

    def first_romanized_name_female(self) -> str:  # 只有jp有實作
        """
        :example: 'SHU-FEN'
        """
        return self.random_element(self.first_romanized_names_female)

    def romanized_name(self) -> str:  # 姓名
        """
        :example: 'WANG SHU-FEN'
        """
        pattern: str = self.random_element(self.romanized_formats)
        return self.generator.parse(pattern)

    def first_romanized_name(self) -> str:  # 只有姓
        """
        :example: 'WANG'
        """
        return self.random_element(self.first_romanized_names)

    def last_romanized_name(self) -> str:  # 只有名
        """
        :example: 'SHU-FEN'
        """
        return self.random_element(self.last_romanized_names)

    def romanized_name_male(self) -> str:  # 男生姓名
        """
        :example: 'WANG CHIH-MING'
        """
        pattern: str = self.random_element(self.romanized_formats_male)
        return self.generator.parse(pattern)

    def romanized_name_female(self) -> str:  # 女生姓名
        """
        :example: 'WANG SHU-FEN'
        """
        pattern: str = self.random_element(self.romanized_formats_female)
        return self.generator.parse(pattern)
