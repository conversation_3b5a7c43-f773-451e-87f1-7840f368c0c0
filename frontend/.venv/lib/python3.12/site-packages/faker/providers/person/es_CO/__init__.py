from collections import OrderedDict

from ..es import Provider as PersonProvider


class Provider(PersonProvider):
    formats_female = [
        "{{first_name_female}} {{last_name}}",
        "{{first_name_female}} {{first_name_female}} {{last_name}}",
        "{{first_name_female}} {{last_name}} {{last_name}}",
        "{{first_name_female}} {{first_name_female}} {{last_name}} {{last_name}}",
    ]

    formats_male = [
        "{{first_name_male}} {{last_name}}",
        "{{first_name_male}} {{first_name_male}} {{last_name}}",
        "{{first_name_male}} {{last_name}} {{last_name}}",
        "{{first_name_male}} {{first_name_male}} {{last_name}} {{last_name}}",
    ]

    formats = formats_female + formats_male

    # 300 female first names
    # Source: Most frequent female first names from the dataset "Ciudadanía Digital"
    # <https://www.datos.gov.co/Ciencia-Tecnolog-a-e-Innovaci-n/Ciudadan-a-Digital/g4cd-bvpd>
    # Weightings derived from total number on each name
    first_names_female = OrderedDict(
        [
            ("<PERSON>", 0.091410602),
            ("<PERSON><PERSON>", 0.034645603),
            ("<PERSON>", 0.023351346),
            ("Ana", 0.020151805),
            ("<PERSON>", 0.018010166),
            ("<PERSON>", 0.016136401),
            ("<PERSON>", 0.015622408),
            ("<PERSON>", 0.014981956),
            ("Paola", 0.014810625),
            ("<PERSON>", 0.013346151),
            ("Marcela", 0.013283602),
            ("Isabel", 0.013113631),
            ("Milena", 0.012810402),
            ("Lucía", 0.012599637),
            ("Cecilia", 0.012194425),
            ("Claudia", 0.011997259),
            ("Carolina", 0.011933349),
            ("Gloria", 0.011810970),
            ("Rosa", 0.011619242),
            ("Liliana", 0.011109328),
            ("Elena", 0.010636128),
            ("Alejandra", 0.010181965),
            ("Adriana", 0.009594544),
            ("Laura", 0.009359303),
            ("Fernanda", 0.008134150),
            ("Marina", 0.008090637),
            ("Inés", 0.007652791),
            ("Lorena", 0.007152395),
            ("Ángela", 0.007043613),
            ("Cristina", 0.006926673),
            ("Leidy", 0.006914435),
            ("Daniela", 0.006910356),
            ("Olga", 0.006802934),
            ("Esther", 0.006773019),
            ("Tatiana", 0.006690073),
            ("Johana", 0.006411320),
            ("Rocío", 0.006339252),
            ("Beatriz", 0.006067298),
            ("Lina", 0.006034663),
            ("Mónica", 0.006007468),
            ("Alba", 0.006002029),
            ("Luisa", 0.005946278),
            ("Esperanza", 0.005904125),
            ("Yaneth", 0.005904125),
            ("Blanca", 0.005881009),
            ("Amparo", 0.005864692),
            ("Alexandra", 0.005845655),
            ("Nancy", 0.005670245),
            ("Margarita", 0.005626732),
            ("Elizabeth", 0.005584579),
            ("Stella", 0.005570981),
            ("Karen", 0.005569622),
            ("Angie", 0.005380613),
            ("Viviana", 0.005365656),
            ("Natalia", 0.005086903),
            ("Mercedes", 0.005077385),
            ("Eugenia", 0.004825827),
            ("Valentina", 0.004602825),
            ("Yolanda", 0.004540275),
            ("Angélica", 0.004498122),
            ("Paula", 0.004454609),
            ("Camila", 0.004389340),
            ("Teresa", 0.004377103),
            ("Sofía", 0.004315913),
            ("Vanessa", 0.004121466),
            ("Nelly", 0.004039879),
            ("Mary", 0.004038520),
            ("Gladys", 0.003903902),
            ("Ruth", 0.003796480),
            ("Flor", 0.003716254),
            ("Nubia", 0.003708095),
            ("Johanna", 0.003701296),
            ("Erika", 0.003684979),
            ("Doris", 0.003572118),
            ("Judith", 0.003490532),
            ("Dayana", 0.003472855),
            ("Sonia", 0.003355915),
            ("Maritza", 0.003334158),
            ("Edith", 0.003267529),
            ("Clara", 0.003244413),
            ("Consuelo", 0.003215858),
            ("Julieth", 0.003123394),
            ("Juliana", 0.003107077),
            ("Dora", 0.003092119),
            ("Victoria", 0.002935745),
            ("Aura", 0.002856879),
            ("Eliana", 0.002855519),
            ("Astrid", 0.002844641),
            ("Alicia", 0.002841921),
            ("Katherine", 0.002833763),
            ("Janeth", 0.002782091),
            ("Leonor", 0.002742658),
            ("Myriam", 0.002676029),
            ("Lizeth", 0.002648834),
            ("Mery", 0.002616199),
            ("Nidia", 0.002609400),
            ("Jenny", 0.002511497),
            ("Kelly", 0.002495180),
            ("Constanza", 0.002423112),
            ("Ximena", 0.002387758),
            ("Yulieth", 0.002376880),
            ("Ligia", 0.002375520),
            ("Sara", 0.002361922),
            ("Socorro", 0.002338806),
            ("Ingrid", 0.002322489),
            ("Helena", 0.002319769),
            ("Dary", 0.002318409),
            ("Rosalba", 0.002280336),
            ("Catalina", 0.002254500),
            ("Fanny", 0.002216427),
            ("Pilar", 0.002137560),
            ("Yenny", 0.002126682),
            ("Mariela", 0.002080449),
            ("Yamile", 0.002062772),
            ("Lilia", 0.002038297),
            ("Karina", 0.002011101),
            ("Mariana", 0.001977107),
            ("Silvia", 0.001953991),
            ("Julia", 0.001939033),
            ("Maribel", 0.001898240),
            ("Amanda", 0.001891441),
            ("Lucy", 0.001888722),
            ("Elsa", 0.001884642),
            ("Jessica", 0.001869685),
            ("Rosario", 0.001865606),
            ("Fabiola", 0.001847929),
            ("Marleny", 0.001808495),
            ("Marta", 0.001805776),
            ("Elvira", 0.001788099),
            ("Omaira", 0.001769062),
            ("Yuliana", 0.001756824),
            ("Mireya", 0.001752745),
            ("Marisol", 0.001695634),
            ("Piedad", 0.001673878),
            ("Rubiela", 0.001668439),
            ("Deisy", 0.001665719),
            ("Estela", 0.001595011),
            ("Miriam", 0.001552858),
            ("Manuela", 0.001537901),
            ("Jennifer", 0.001536541),
            ("Graciela", 0.001497108),
            ("Yadira", 0.001494388),
            ("Elisa", 0.001488949),
            ("Yolima", 0.001472632),
            ("Ruby", 0.001450876),
            ("Gabriela", 0.001448156),
            ("Libia", 0.001445436),
            ("Yohana", 0.001430479),
            ("Karol", 0.001422320),
            ("Bibiana", 0.001397844),
            ("Katerine", 0.001396485),
            ("Edilma", 0.001389686),
            ("Nohora", 0.001378808),
            ("Shirley", 0.001365210),
            ("Xiomara", 0.001321697),
            ("Francy", 0.001316258),
            ("Betty", 0.001305380),
            ("Melissa", 0.001297221),
            ("Estella", 0.001272746),
            ("Carmenza", 0.001271386),
            ("Edna", 0.001261867),
            ("Mayra", 0.001260508),
            ("Vanesa", 0.001259148),
            ("Lida", 0.001245550),
            ("Raquel", 0.001244190),
            ("Estefanía", 0.001231952),
            ("Hilda", 0.001230593),
            ("Mabel", 0.001222434),
            ("Cindy", 0.001212916),
            ("Liseth", 0.001208836),
            ("Wendy", 0.001199318),
            ("Lady", 0.001185720),
            ("Yésica", 0.001180281),
            ("Mayerly", 0.001173482),
            ("Verónica", 0.001173482),
            ("Norma", 0.001157165),
            ("Gina", 0.001150366),
            ("Susana", 0.001150366),
            ("Yesenia", 0.001144927),
            ("Maira", 0.001142208),
            ("Nora", 0.001134049),
            ("Marlene", 0.001128610),
            ("Valeria", 0.001124531),
            ("Elvia", 0.001116372),
            ("Yasmín", 0.001116372),
            ("Bertha", 0.001113652),
            ("Aida", 0.001112293),
            ("Tania", 0.001102774),
            ("Ester", 0.001071499),
            ("Yineth", 0.001067420),
            ("Dolores", 0.001045664),
            ("Irma", 0.001041585),
            ("Matilde", 0.001036145),
            ("Aurora", 0.001029347),
            ("Magda", 0.001022548),
            ("Miryam", 0.001022548),
            ("Esmeralda", 0.001007590),
            ("Lucero", 0.001006230),
            ("Lucila", 0.001003511),
            ("Gladis", 0.001000791),
            ("Juana", 0.000984474),
            ("Jimena", 0.000983114),
            ("Margoth", 0.000965437),
            ("Antonia", 0.000961358),
            ("Yuri", 0.000953199),
            ("Josefina", 0.000950480),
            ("Liceth", 0.000939602),
            ("Yuli", 0.000932803),
            ("Nury", 0.000930083),
            ("Nataly", 0.000924644),
            ("Vilma", 0.000921925),
            ("Yéssica", 0.000921925),
            ("Yudy", 0.000921925),
            ("Yuly", 0.000916486),
            ("Heidy", 0.000913766),
            ("Nelcy", 0.000890650),
            ("Ofelia", 0.000889290),
            ("Jhoana", 0.000887930),
            ("Gilma", 0.000875692),
            ("Zoraida", 0.000868894),
            ("Enith", 0.000856656),
            ("Elsy", 0.000853936),
            ("Clemencia", 0.000828100),
            ("Danna", 0.000824021),
            ("Emilia", 0.000818582),
            ("Cielo", 0.000817222),
            ("Linda", 0.000817222),
            ("Damaris", 0.000815863),
            ("Delia", 0.000811783),
            ("Irene", 0.000810423),
            ("Virginia", 0.000803625),
            ("Paulina", 0.000796826),
            ("Alcira", 0.000790027),
            ("Magaly", 0.000785948),
            ("Ivonne", 0.000779149),
            ("Oliva", 0.000768271),
            ("Yina", 0.000753313),
            ("Carol", 0.000745154),
            ("Geraldine", 0.000745154),
            ("Yeimy", 0.000738356),
            ("Magdalena", 0.000732917),
            ("Yanet", 0.000731557),
            ("Yazmín", 0.000730197),
            ("Sindy", 0.000728837),
            ("Dahiana", 0.000726118),
            ("Alix", 0.000724758),
            ("Rita", 0.000724758),
            ("Lidia", 0.000719319),
            ("Marlén", 0.000716599),
            ("Michel", 0.000715239),
            ("Yeny", 0.000708441),
            ("Marly", 0.000703002),
            ("Dolly", 0.000698922),
            ("Leydi", 0.000694843),
            ("Nayibe", 0.000694843),
            ("Yury", 0.000693483),
            ("Denis", 0.000690764),
            ("Derly", 0.000689404),
            ("Yurany", 0.000688044),
            ("Emilce", 0.000685325),
            ("Tulia", 0.000683965),
            ("Yenifer", 0.000681245),
            ("Anyi", 0.000677166),
            ("Francia", 0.000673087),
            ("Jazmín", 0.000671727),
            ("Josefa", 0.000671727),
            ("Janneth", 0.000669007),
            ("Emilse", 0.000662208),
            ("Jackeline", 0.000649970),
            ("Concepción", 0.000645891),
            ("Dina", 0.000644531),
            ("Lisbeth", 0.000640452),
            ("Nathalia", 0.000640452),
            ("Amelia", 0.000639092),
            ("Emma", 0.000637733),
            ("Jacqueline", 0.000637733),
            ("Zulma", 0.000637733),
            ("Maricela", 0.000632293),
            ("Adela", 0.000629574),
            ("Ibeth", 0.000629574),
            ("Candelaria", 0.000615976),
            ("Mirian", 0.000613257),
            ("Magnolia", 0.000611897),
            ("Elcy", 0.000606458),
            ("Aleida", 0.000603738),
            ("Eva", 0.000598299),
            ("Gisela", 0.000598299),
            ("Yurani", 0.000596939),
            ("Azucena", 0.000595580),
            ("Amalia", 0.000591500),
            ("Deicy", 0.000590141),
            ("Nelsy", 0.000588781),
            ("Iris", 0.000584701),
            ("Katherin", 0.000584701),
            ("Lilian", 0.000584701),
            ("Leticia", 0.000575183),
            ("Deyanira", 0.000573823),
            ("Melba", 0.000573823),
            ("Adiela", 0.000567024),
            ("Catherine", 0.000557506),
        ]
    )

    # 300 male first names
    # Source: Most frequent male first names from the dataset "Ciudadanía Digital"
    # <https://www.datos.gov.co/Ciencia-Tecnolog-a-e-Innovaci-n/Ciudadan-a-Digital/g4cd-bvpd>
    # Weightings derived from total number on each name
    first_names_male = OrderedDict(
        [
            ("José", 0.048691700),
            ("Andrés", 0.039716926),
            ("Luis", 0.038832502),
            ("Carlos", 0.037462599),
            ("Juan", 0.032670871),
            ("Alberto", 0.021566442),
            ("David", 0.020957759),
            ("Antonio", 0.019540921),
            ("Fernando", 0.019087709),
            ("Jorge", 0.016852449),
            ("Jesús", 0.016303901),
            ("Alexander", 0.015906424),
            ("Enrique", 0.015438545),
            ("Javier", 0.014926665),
            ("Manuel", 0.013744500),
            ("Eduardo", 0.013292754),
            ("Jhon", 0.012868877),
            ("Diego", 0.012004987),
            ("Camilo", 0.011381637),
            ("Alejandro", 0.011356703),
            ("Óscar", 0.010821355),
            ("Daniel", 0.010819889),
            ("Miguel", 0.010749487),
            ("Felipe", 0.010291874),
            ("Jairo", 0.010011734),
            ("Mauricio", 0.009859196),
            ("César", 0.009266647),
            ("Alfonso", 0.008726899),
            ("Rafael", 0.008559695),
            ("Cristian", 0.008083016),
            ("Sebastián", 0.007534468),
            ("Ángel", 0.007062188),
            ("Iván", 0.007059255),
            ("Jaime", 0.007024054),
            ("Julio", 0.006956586),
            ("Julián", 0.006823115),
            ("Fabián", 0.006730713),
            ("Dario", 0.006658844),
            ("William", 0.006591376),
            ("Orlando", 0.006400704),
            ("Francisco", 0.006356703),
            ("Ricardo", 0.006339102),
            ("Mario", 0.006192432),
            ("Edwin", 0.006070695),
            ("John", 0.006014960),
            ("Víctor", 0.005956292),
            ("Leonardo", 0.005865356),
            ("Armando", 0.005843356),
            ("Augusto", 0.005786154),
            ("Pablo", 0.005552948),
            ("Álvaro", 0.005506013),
            ("Hernán", 0.005488413),
            ("Fredy", 0.005476679),
            ("Pedro", 0.005412144),
            ("Héctor", 0.005325609),
            ("Santiago", 0.005315342),
            ("Edgar", 0.005305075),
            ("Gustavo", 0.005247873),
            ("Hernando", 0.005161338),
            ("Guillermo", 0.005145204),
            ("Esteban", 0.005055735),
            ("Humberto", 0.004832796),
            ("Nelson", 0.004813728),
            ("Wilson", 0.004730126),
            ("Arturo", 0.004656791),
            ("Gabriel", 0.004545321),
            ("Alfredo", 0.004297448),
            ("Omar", 0.004282781),
            ("Germán", 0.004224113),
            ("Henry", 0.003967439),
            ("Fabio", 0.003961572),
            ("Alonso", 0.003751833),
            ("Brayan", 0.003709299),
            ("Hugo", 0.003684365),
            ("Sergio", 0.003650631),
            ("Alexis", 0.003374890),
            ("Adolfo", 0.003335289),
            ("Stiven", 0.003276621),
            ("Kevin", 0.003134350),
            ("Johan", 0.003075682),
            ("Yesid", 0.003056615),
            ("Ernesto", 0.002924611),
            ("Raúl", 0.002763274),
            ("Rodrigo", 0.002694339),
            ("Roberto", 0.002585802),
            ("Rubén", 0.002560868),
            ("Anderson", 0.002525667),
            ("Eliecer", 0.002511000),
            ("Nicolás", 0.002471399),
            ("Ferney", 0.002395131),
            ("Steven", 0.002390730),
            ("Emilio", 0.002324729),
            ("Yeison", 0.002311528),
            ("Arley", 0.002222059),
            ("Néstor", 0.002200059),
            ("Albeiro", 0.002125257),
            ("Wilmer", 0.002101789),
            ("Gerardo", 0.002095923),
            ("Jair", 0.002091522),
            ("Jonathan", 0.002032854),
            ("Marco", 0.001984453),
            ("Elkin", 0.001971253),
            ("Harold", 0.001940452),
            ("Martín", 0.001915518),
            ("Elías", 0.001912584),
            ("Robinson", 0.001908184),
            ("Alirio", 0.001884717),
            ("Edison", 0.001871517),
            ("Adrián", 0.001861250),
            ("Edinson", 0.001840716),
            ("Ramiro", 0.001831916),
            ("Jhonatan", 0.001828982),
            ("León", 0.001820182),
            ("Milton", 0.001770314),
            ("Oswaldo", 0.001726313),
            ("Ignacio", 0.001714579),
            ("Freddy", 0.001692578),
            ("Segundo", 0.001663244),
            ("Ramón", 0.001651511),
            ("Duván", 0.001632444),
            ("Samuel", 0.001554708),
            ("Gilberto", 0.001535641),
            ("Walter", 0.001453505),
            ("Alex", 0.001428571),
            ("Libardo", 0.001422705),
            ("James", 0.001421238),
            ("Vicente", 0.001394837),
            ("Ariel", 0.001378703),
            ("Danilo", 0.001377237),
            ("Giovanny", 0.001353769),
            ("Gregorio", 0.001334702),
            ("Gonzalo", 0.001325902),
            ("Michael", 0.001267234),
            ("Marlon", 0.001251100),
            ("Efrain", 0.001186565),
            ("Mateo", 0.001180698),
            ("Bernardo", 0.001167498),
            ("Leandro", 0.001164564),
            ("Jhoan", 0.001158698),
            ("Rodolfo", 0.001155764),
            ("Joaquín", 0.001127897),
            ("Felix", 0.001089762),
            ("Jeison", 0.001080962),
            ("Leonel", 0.001078029),
            ("Estiven", 0.001070695),
            ("Wilmar", 0.001063362),
            ("Edward", 0.001057495),
            ("Christian", 0.001054561),
            ("Jefferson", 0.001054561),
            ("Ronald", 0.000992960),
            ("René", 0.000990026),
            ("Aníbal", 0.000972426),
            ("Richard", 0.000966559),
            ("Andrey", 0.000959226),
            ("Jean", 0.000956292),
            ("Jaider", 0.000954825),
            ("Darwin", 0.000951892),
            ("Ever", 0.000951892),
            ("Arnulfo", 0.000950425),
            ("Giovanni", 0.000940158),
            ("Emiro", 0.000934292),
            ("Uriel", 0.000929891),
            ("Franklin", 0.000924025),
            ("Edilberto", 0.000912291),
            ("Smith", 0.000897624),
            ("Octavio", 0.000890290),
            ("Cristhian", 0.000875623),
            ("Tulio", 0.000875623),
            ("Eduar", 0.000862423),
            ("Junior", 0.000859490),
            ("Didier", 0.000855089),
            ("Reinaldo", 0.000847756),
            ("Fidel", 0.000830155),
            ("Willian", 0.000819889),
            ("Jimmy", 0.000815488),
            ("Eder", 0.000758287),
            ("Isaac", 0.000758287),
            ("Saúl", 0.000746553),
            ("Danny", 0.000745087),
            ("Marcos", 0.000740686),
            ("Yair", 0.000730419),
            ("Moisés", 0.000724553),
            ("Edwar", 0.000723086),
            ("Jhonny", 0.000718686),
            ("Miller", 0.000717219),
            ("Santos", 0.000717219),
            ("Esneider", 0.000715752),
            ("Franco", 0.000714286),
            ("Abel", 0.000705485),
            ("Dairo", 0.000701085),
            ("Roger", 0.000701085),
            ("Aldemar", 0.000695219),
            ("Rolando", 0.000693752),
            ("Hermes", 0.000689352),
            ("Jeferson", 0.000684952),
            ("Efrén", 0.000679085),
            ("Jeisson", 0.000679085),
            ("Ismael", 0.000674685),
            ("Edgardo", 0.000673218),
            ("Maicol", 0.000661484),
            ("Brandon", 0.000654151),
            ("Bryan", 0.000654151),
            ("Robert", 0.000649751),
            ("Eduard", 0.000636550),
            ("Nilson", 0.000635084),
            ("Agustín", 0.000632150),
            ("Tomás", 0.000630683),
            ("Edilson", 0.000621883),
            ("Aurelio", 0.000618950),
            ("Domingo", 0.000618950),
            ("Arbey", 0.000616016),
            ("Joan", 0.000614550),
            ("Yeferson", 0.000607216),
            ("Samir", 0.000602816),
            ("Wilder", 0.000602816),
            ("Ciro", 0.000599883),
            ("Josué", 0.000598416),
            ("Joel", 0.000596949),
            ("Horacio", 0.000591082),
            ("Jader", 0.000591082),
            ("Gerson", 0.000583749),
            ("Marino", 0.000579349),
            ("Erick", 0.000572015),
            ("Eugenio", 0.000561748),
            ("Benjamín", 0.000558815),
            ("Norberto", 0.000557348),
            ("Alcides", 0.000555881),
            ("Israel", 0.000539748),
            ("Yamid", 0.000535348),
            ("Emerson", 0.000525081),
            ("Frank", 0.000504547),
            ("Geovanny", 0.000504547),
            ("Gildardo", 0.000495747),
            ("Vladimir", 0.000495747),
            ("Silvio", 0.000485480),
            ("Dagoberto", 0.000479613),
            ("Misael", 0.000472279),
            ("Adalberto", 0.000464946),
            ("Elmer", 0.000464946),
            ("Campo", 0.000460546),
            ("Herney", 0.000456145),
            ("Eider", 0.000454679),
            ("Farid", 0.000451745),
            ("Edisson", 0.000447345),
            ("Evelio", 0.000442945),
            ("Bladimir", 0.000429745),
            ("Heriberto", 0.000423878),
            ("Sneider", 0.000422411),
            ("Nel", 0.000419478),
            ("Rigoberto", 0.000419478),
            ("Jhony", 0.000416544),
            ("Salvador", 0.000415078),
            ("Argemiro", 0.000410678),
            ("Brian", 0.000407744),
            ("Abelardo", 0.000404811),
            ("Federico", 0.000401877),
            ("Jonatan", 0.000398944),
            ("Wilfredo", 0.000397477),
            ("Faber", 0.000396011),
            ("Osvaldo", 0.000394544),
            ("Simón", 0.000394544),
            ("Elver", 0.000390144),
            ("Alveiro", 0.000388677),
            ("Jerson", 0.000385744),
            ("Ovidio", 0.000381344),
            ("Elvis", 0.000375477),
            ("Norbey", 0.000375477),
            ("Wilman", 0.000374010),
            ("Johnny", 0.000372543),
            ("Cristobal", 0.000368143),
            ("Harvey", 0.000366676),
            ("Ancizar", 0.000363743),
            ("Yerson", 0.000363743),
            ("Román", 0.000362276),
            ("Ronal", 0.000362276),
            ("Reinel", 0.000360810),
            ("Albert", 0.000359343),
            ("Darío", 0.000359343),
            ("Edier", 0.000357876),
            ("Neider", 0.000353476),
            ("Harol", 0.000352009),
            ("Paulo", 0.000352009),
            ("Deiby", 0.000347609),
            ("Dany", 0.000346143),
            ("Leider", 0.000341742),
            ("Damián", 0.000340276),
            ("Aldair", 0.000335876),
            ("Gallego", 0.000335876),
            ("Abraham", 0.000332942),
            ("Yecid", 0.000331476),
            ("Ocampo", 0.000324142),
            ("Wilfrido", 0.000324142),
            ("Lorenzo", 0.000318275),
            ("Paul", 0.000318275),
            ("Wilber", 0.000316808),
            ("Bayron", 0.000315342),
            ("Dubán", 0.000312408),
            ("Jhan", 0.000312408),
            ("Isaías", 0.000310942),
            ("Isidro", 0.000310942),
        ]
    )

    first_names = first_names_female.copy()
    first_names.update(first_names_male)

    # 300 last names
    # Source: Most frequent last names from the dataset "Ciudadanía Digital"
    # <https://www.datos.gov.co/Ciencia-Tecnolog-a-e-Innovaci-n/Ciudadan-a-Digital/g4cd-bvpd>
    # Weightings derived from total number on each name
    last_names = OrderedDict(
        [
            ("Rodríguez", 0.027384697),
            ("Gómez", 0.020422368),
            ("Martínez", 0.020115369),
            ("García", 0.019433148),
            ("López", 0.019162104),
            ("González", 0.018265076),
            ("Hernández", 0.01699467),
            ("Sánchez", 0.016893259),
            ("Pérez", 0.016406486),
            ("Díaz", 0.015069702),
            ("Ramírez", 0.014970134),
            ("Rojas", 0.012601722),
            ("Torres", 0.012484639),
            ("Moreno", 0.01134238),
            ("Vargas", 0.010733913),
            ("Muñoz", 0.010541231),
            ("Ortiz", 0.01009871),
            ("Castro", 0.009097505),
            ("Gutiérrez", 0.008656827),
            ("Jiménez", 0.008560948),
            ("Suárez", 0.008066799),
            ("Álvarez", 0.008056658),
            ("Ruiz", 0.007958934),
            ("Valencia", 0.007941418),
            ("Quintero", 0.00766392),
            ("Herrera", 0.007485989),
            ("Romero", 0.00748138),
            ("Mosquera", 0.007114455),
            ("Morales", 0.007082188),
            ("Arias", 0.006243241),
            ("Rivera", 0.006023824),
            ("Flórez", 0.005914116),
            ("Giraldo", 0.005782281),
            ("Medina", 0.005736185),
            ("Castillo", 0.005722356),
            ("Parra", 0.005665197),
            ("Peña", 0.005635696),
            ("Guerrero", 0.005407982),
            ("Salazar", 0.005365573),
            ("Osorio", 0.005327775),
            ("Mejía", 0.005317634),
            ("Mendoza", 0.005201472),
            ("Marín", 0.005053043),
            ("Cardona", 0.00496546),
            ("Cárdenas", 0.004892629),
            ("Cruz", 0.004795827),
            ("Restrepo", 0.004729449),
            ("Correa", 0.004724839),
            ("Ortega", 0.004712854),
            ("Acosta", 0.004640023),
            ("Ramos", 0.004636335),
            ("Reyes", 0.004593005),
            ("Rincón", 0.004554284),
            ("Zapata", 0.004487906),
            ("Sierra", 0.004380963),
            ("Mora", 0.004333945),
            ("Palacios", 0.004313663),
            ("Molina", 0.004285083),
            ("Delgado", 0.004150483),
            ("Guzmán", 0.004148639),
            ("Silva", 0.00413942),
            ("Contreras", 0.004136654),
            ("Lozano", 0.004089636),
            ("Montoya", 0.004063823),
            ("Ríos", 0.003995601),
            ("Vásquez", 0.003978084),
            ("Caicedo", 0.003936598),
            ("Cortés", 0.003899721),
            ("Velásquez", 0.003888658),
            ("Londoño", 0.003881283),
            ("Ospina", 0.003877595),
            ("Jaramillo", 0.003845328),
            ("Córdoba", 0.003807529),
            ("Escobar", 0.003759589),
            ("Murillo", 0.003740229),
            ("Orozco", 0.00373101),
            ("Fernández", 0.003705196),
            ("Vega", 0.003632364),
            ("Hurtado", 0.003556767),
            ("Carvajal", 0.003517124),
            ("Agudelo", 0.00351528),
            ("Calderón", 0.003481169),
            ("León", 0.003475638),
            ("Ávila", 0.003279269),
            ("Garzón", 0.003224876),
            ("Beltrán", 0.0032175),
            ("Trujillo", 0.00320275),
            ("Pineda", 0.003086588),
            ("Méndez", 0.003059852),
            ("Barrera", 0.003041414),
            ("Acevedo", 0.002976879),
            ("Henao", 0.002974113),
            ("Bedoya", 0.002971348),
            ("Franco", 0.002971348),
            ("Jaimes", 0.002916954),
            ("Bernal", 0.002892063),
            ("Arango", 0.002830294),
            ("Hoyos", 0.002818309),
            ("Navarro", 0.002813699),
            ("Durán", 0.002772213),
            ("Vergara", 0.002759306),
            ("Soto", 0.002739024),
            ("Camacho", 0.002672646),
            ("Sandoval", 0.002652363),
            ("Gil", 0.002644988),
            ("Buitrago", 0.002634847),
            ("Duarte", 0.002609033),
            ("Carrillo", 0.002527904),
            ("Duque", 0.002526982),
            ("Pacheco", 0.002519607),
            ("Barrios", 0.002500247),
            ("Aguirre", 0.002496559),
            ("Vélez", 0.002459682),
            ("Benavides", 0.002455994),
            ("Bermúdez", 0.002447697),
            ("Narváez", 0.002442166),
            ("Rueda", 0.002432025),
            ("Toro", 0.002431103),
            ("Blanco", 0.002385007),
            ("Amaya", 0.002381319),
            ("Forero", 0.002380397),
            ("Becerra", 0.002371178),
            ("Pinzón", 0.002364724),
            ("Camargo", 0.002363802),
            ("Vanegas", 0.002347208),
            ("Bonilla", 0.002326004),
            ("Padilla", 0.002326004),
            ("Ariza", 0.00231955),
            ("Ardila", 0.002313097),
            ("Galvis", 0.0023048),
            ("Daza", 0.002289127),
            ("Mena", 0.002284517),
            ("Villamizar", 0.002254094),
            ("Sarmiento", 0.002245797),
            ("Cano", 0.002234734),
            ("Zambrano", 0.00223289),
            ("Espinosa", 0.00222828),
            ("Gallego", 0.00222828),
            ("Tovar", 0.002224593),
            ("Uribe", 0.002219061),
            ("Ochoa", 0.002190482),
            ("Castellanos", 0.002181262),
            ("Cabrera", 0.002177575),
            ("Castañeda", 0.002145307),
            ("Solano", 0.002143464),
            ("Fuentes", 0.002104743),
            ("Perdomo", 0.002103821),
            ("Guevara", 0.002101977),
            ("Castaño", 0.002077085),
            ("Patiño", 0.002046662),
            ("Ocampo", 0.002029146),
            ("Páez", 0.002020848),
            ("Serna", 0.002020848),
            ("Mesa", 0.002005176),
            ("Angulo", 0.001987659),
            ("Mercado", 0.001982128),
            ("Alzate", 0.001969221),
            ("Rosero", 0.001952626),
            ("Bautista", 0.001948939),
            ("Vera", 0.001932344),
            ("Meneses", 0.0019305),
            ("Arenas", 0.001922203),
            ("Cifuentes", 0.001902843),
            ("Arévalo", 0.001896389),
            ("Montes", 0.001878873),
            ("Arrieta", 0.001876107),
            ("Guerra", 0.001869653),
            ("Aguilar", 0.001855825),
            ("Ayala", 0.001849371),
            ("Figueroa", 0.001845684),
            ("Fonseca", 0.001840152),
            ("Pinto", 0.001832777),
            ("Bravo", 0.001805119),
            ("Luna", 0.001801431),
            ("Niño", 0.001798666),
            ("Salcedo", 0.00179129),
            ("Serrano", 0.001790368),
            ("Roa", 0.001773774),
            ("Palacio", 0.001770086),
            ("Perea", 0.001765476),
            ("Velasco", 0.001761789),
            ("Villa", 0.001760867),
            ("Sepúlveda", 0.001755335),
            ("Benítez", 0.001744272),
            ("Meza", 0.001741507),
            ("Sanabria", 0.001737819),
            ("Miranda", 0.001722146),
            ("Gaitán", 0.001714771),
            ("Melo", 0.00170463),
            ("Márquez", 0.001678816),
            ("Ordóñez", 0.001675128),
            ("Zuluaga", 0.001673285),
            ("Andrade", 0.001663143),
            ("Estrada", 0.00165669),
            ("Prieto", 0.00162811),
            ("Alvarado", 0.001624423),
            ("Leal", 0.001623501),
            ("Gaviria", 0.001616125),
            ("Salas", 0.001603219),
            ("Polo", 0.001597687),
            ("Bohórquez", 0.001585702),
            ("Arboleda", 0.001580171),
            ("Pulido", 0.001580171),
            ("Pardo", 0.001579249),
            ("Monsalve", 0.001575561),
            ("Cuéllar", 0.001573717),
            ("Rangel", 0.001571873),
            ("Nieto", 0.001570029),
            ("Loaiza", 0.00156542),
            ("Rivas", 0.001562654),
            ("Murcia", 0.001561732),
            ("Campo", 0.001555279),
            ("Naranjo", 0.001555279),
            ("Galindo", 0.001538684),
            ("Santos", 0.001537762),
            ("Lara", 0.001532231),
            ("Triana", 0.001510105),
            ("Burbano", 0.001485213),
            ("Maldonado", 0.001485213),
            ("Galeano", 0.001476916),
            ("Pabón", 0.001464931),
            ("Rentería", 0.001462165),
            ("Espitia", 0.001458477),
            ("Fajardo", 0.001457555),
            ("Gamboa", 0.001455711),
            ("Chávez", 0.001436351),
            ("Vallejo", 0.001435429),
            ("Barreto", 0.001431742),
            ("Caro", 0.001415147),
            ("Ceballos", 0.001407772),
            ("Alarcón", 0.001405006),
            ("Prada", 0.00140224),
            ("Villegas", 0.001384724),
            ("Cáceres", 0.001381958),
            ("Caballero", 0.001380114),
            ("Salgado", 0.001380114),
            ("Velandia", 0.001373661),
            ("Carmona", 0.001365363),
            ("Chaparro", 0.001364441),
            ("Oviedo", 0.001360754),
            ("Granados", 0.001348769),
            ("Montenegro", 0.001348769),
            ("Saavedra", 0.00133955),
            ("Betancur", 0.001338628),
            ("Rubio", 0.001335862),
            ("Cuesta", 0.001312814),
            ("Rico", 0.001300829),
            ("Ballesteros", 0.001299907),
            ("Ibarra", 0.001298985),
            ("Valderrama", 0.001283312),
            ("Barbosa", 0.001277781),
            ("Garcés", 0.001269484),
            ("Monroy", 0.001253811),
            ("Erazo", 0.001251045),
            ("Núñez", 0.001245514),
            ("Quiroga", 0.001231685),
            ("Angarita", 0.001230763),
            ("Cantillo", 0.001227997),
            ("Posada", 0.001214168),
            ("Pedraza", 0.001210481),
            ("Arteaga", 0.001204027),
            ("Yepes", 0.001204027),
            ("Bustos", 0.001198496),
            ("Olaya", 0.001196652),
            ("Salamanca", 0.001189277),
            ("Burgos", 0.001186511),
            ("Corredor", 0.001180979),
            ("Alfonso", 0.001173604),
            ("Paz", 0.001168072),
            ("Parada", 0.001161619),
            ("Bolaños", 0.001150556),
            ("Tamayo", 0.001149634),
            ("Manrique", 0.001144103),
            ("Domínguez", 0.001138571),
            ("Cardozo", 0.001134883),
            ("Quiroz", 0.001134883),
            ("Bastidas", 0.001127508),
            ("Obando", 0.001112757),
            ("Rendón", 0.001112757),
            ("Mantilla", 0.001109991),
            ("Gonzáles", 0.001107226),
            ("Puentes", 0.00110446),
            ("Bejarano", 0.001088787),
            ("Riascos", 0.001086943),
            ("Castrillón", 0.001086022),
            ("Bustamante", 0.0010851),
            ("Rengifo", 0.0010851),
            ("Ospino", 0.001083256),
            ("Ojeda", 0.001081412),
            ("Villamil", 0.001073115),
            ("Cerón", 0.00105652),
            ("Arroyo", 0.001055598),
            ("Ángel", 0.001053754),
            ("Chacón", 0.001050067),
            ("Portilla", 0.001042691),
            ("Barragán", 0.001041769),
            ("Orjuela", 0.001039926),
            ("Bolívar", 0.001024253),
            ("Molano", 0.001021487),
            ("Anaya", 0.001016878),
        ]
    )

    prefixes_female = OrderedDict(
        [
            ("Sra.", 0.5),
            ("Srta.", 0.2),
            ("Dra.", 0.2),
            ("Doña", 0.05),
            ("Dña.", 0.05),
        ]
    )

    prefixes_male = OrderedDict(
        [
            ("Sr.", 0.7),
            ("Dr.", 0.2),
            ("Don", 0.05),
            ("D.", 0.05),
        ]
    )
