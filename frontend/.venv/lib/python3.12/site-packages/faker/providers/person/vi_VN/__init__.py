from .. import Provider as <PERSON><PERSON><PERSON>ider


class Provider(PersonProvider):
    formats_female = (
        "{{first_name_female}} {{last_name}}",
        "{{first_name_unisex}} {{last_name}}",
        "{{prefix_female}} {{first_name_unisex}} {{last_name}}",
        "{{prefix_female}} {{first_name_female}} {{last_name}}",
    )
    formats_male = (
        "{{first_name_male}} {{last_name}}",
        "{{first_name_male}} {{middle_name}} {{last_name}}",
        "{{first_name_unisex}} {{middle_name}} {{last_name}}",
        "{{prefix_male}} {{first_name_male}} {{last_name}}",
    )
    formats = formats_female + formats_male

    # Name from : https://en.wikipedia.org/wiki/Vietnamese_name
    # and https://vinpearl.com/en/vietnamese-names-top-200-popular-names-for-boys-and-girls

    first_names_female = (
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
    )

    first_names_unisex = (
        "An",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "L<PERSON>m",
        "H<PERSON>nh",
        "Thành",
        "<PERSON>",
        "Nh<PERSON>t",
        "Ph<PERSON><PERSON>ng",
        "<PERSON>hoa",
        "H<PERSON>i",
        "Nhật",
    )

    first_names_male = (
        "<PERSON>",
        "Hưng",
        "V<PERSON>",
        "T<PERSON>",
        "Ho<PERSON>ng",
        "<PERSON><PERSON>c",
        "Trung",
        "Quang",
        "Anh",
        "Khoa",
        "Dũng",
        "Quang",
        "Thành",
        "Huy",
        "Bảo",
        "Châu",
        "Minh",
        "Tùng",
        "Nhiên",
        "Trọng",
    )

    middle_names = (
        "Văn",
        "Thị",
        "Quang",
        "Đức",
        "Trí",
        "Xuân",
        "Hoàng",
        "Hải",
        "Đức",
        "Thế",
        "Tấn",
        "Phú",
        "Hữu",
        "Bảo",
        "Mai",
        "Mai Bảo",
    )

    last_names = ("Nguyễn", "Trần", "Lê", "Phạm", "Vũ", "Đặng", "Bùi", "Dương", "Mai", "Hoàng")

    # Typically, Vietnamese will be addressed with their given name and a prefix
    # https://en.wikipedia.org/wiki/Vietnamese_name#Given_name

    prefixes_female = ("Cô", "Chị", "Bà", "Quý cô")

    prefixes_male = ("Ông", "Anh", "Bác", "Quý ông")

    def first_name_unisex(self) -> str:
        return self.random_element(self.first_names_unisex)

    def middle_name(self) -> str:
        return self.random_element(self.middle_names)
