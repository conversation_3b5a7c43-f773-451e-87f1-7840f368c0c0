(window.webpackJsonp=window.webpackJsonp||[]).push([[2],[function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}n.d(t,"a",function(){return r})},function(e,t,n){"use strict";function r(e){return(r=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}n.d(t,"a",function(){return r})},function(e,t,n){"use strict";function r(e){return(r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e){return(i="function"===typeof Symbol&&"symbol"===r(Symbol.iterator)?function(e){return r(e)}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":r(e)})(e)}var a=n(9);function o(e,t){return!t||"object"!==i(t)&&"function"!==typeof t?Object(a.a)(e):t}n.d(t,"a",function(){return o})},function(e,t,n){"use strict";n.d(t,"a",function(){return i});var r=n(12);function i(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&Object(r.a)(e,t)}},function(e,t,n){"use strict";function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function i(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),e}n.d(t,"a",function(){return i})},function(e,t,n){e.exports=n(24)},function(e,t,n){"use strict";e.exports=n(18)},function(e,t,n){"use strict";n.d(t,"a",function(){return i});var r=n(8);function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},i=Object.keys(n);"function"===typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),i.forEach(function(t){Object(r.a)(e,t,n[t])})}return e}},function(e,t,n){"use strict";function r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}n.d(t,"a",function(){return r})},function(e,t,n){"use strict";function r(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}n.d(t,"a",function(){return r})},function(e,t,n){"use strict";var r={};n.r(r),n.d(r,"memcpy",function(){return Fe}),n.d(r,"joinUint8Arrays",function(){return Me}),n.d(r,"toArrayBufferView",function(){return Ne}),n.d(r,"toInt8Array",function(){return Ue}),n.d(r,"toInt16Array",function(){return Pe}),n.d(r,"toInt32Array",function(){return Re}),n.d(r,"toBigInt64Array",function(){return ze}),n.d(r,"toUint8Array",function(){return Ve}),n.d(r,"toUint16Array",function(){return We}),n.d(r,"toUint32Array",function(){return $e}),n.d(r,"toBigUint64Array",function(){return He}),n.d(r,"toFloat32Array",function(){return Ye}),n.d(r,"toFloat64Array",function(){return Ke}),n.d(r,"toUint8ClampedArray",function(){return Qe}),n.d(r,"toArrayBufferViewIterator",function(){return Ge}),n.d(r,"toInt8ArrayIterator",function(){return Je}),n.d(r,"toInt16ArrayIterator",function(){return Xe}),n.d(r,"toInt32ArrayIterator",function(){return Ze}),n.d(r,"toUint8ArrayIterator",function(){return et}),n.d(r,"toUint16ArrayIterator",function(){return tt}),n.d(r,"toUint32ArrayIterator",function(){return nt}),n.d(r,"toFloat32ArrayIterator",function(){return rt}),n.d(r,"toFloat64ArrayIterator",function(){return it}),n.d(r,"toUint8ClampedArrayIterator",function(){return at}),n.d(r,"toArrayBufferViewAsyncIterator",function(){return ot}),n.d(r,"toInt8ArrayAsyncIterator",function(){return st}),n.d(r,"toInt16ArrayAsyncIterator",function(){return ct}),n.d(r,"toInt32ArrayAsyncIterator",function(){return lt}),n.d(r,"toUint8ArrayAsyncIterator",function(){return ft}),n.d(r,"toUint16ArrayAsyncIterator",function(){return ht}),n.d(r,"toUint32ArrayAsyncIterator",function(){return dt}),n.d(r,"toFloat32ArrayAsyncIterator",function(){return pt}),n.d(r,"toFloat64ArrayAsyncIterator",function(){return yt}),n.d(r,"toUint8ClampedArrayAsyncIterator",function(){return vt}),n.d(r,"rebaseValueOffsets",function(){return bt}),n.d(r,"compareArrayLike",function(){return mt});var i={};n.r(i),n.d(i,"getBool",function(){return Zt}),n.d(i,"getBit",function(){return en}),n.d(i,"setBool",function(){return tn}),n.d(i,"truncateBitmap",function(){return nn}),n.d(i,"packBools",function(){return rn}),n.d(i,"iterateBits",function(){return an}),n.d(i,"popcnt_bit_range",function(){return on}),n.d(i,"popcnt_array",function(){return un}),n.d(i,"popcnt_uint32",function(){return sn});var a={};n.r(a),n.d(a,"uint16ToFloat64",function(){return Dr}),n.d(a,"float64ToUint16",function(){return Cr});var o={};n.r(o),n.d(o,"isArrowBigNumSymbol",function(){return Vr}),n.d(o,"bignumToString",function(){return Rr}),n.d(o,"bignumToBigInt",function(){return zr}),n.d(o,"BN",function(){return Gr});var u={};n.r(u),n.d(u,"clampIndex",function(){return Ri}),n.d(u,"clampRange",function(){return zi}),n.d(u,"createElementComparator",function(){return $i});var s={};n.r(s),n.d(s,"BaseInt64",function(){return co}),n.d(s,"Uint64",function(){return lo}),n.d(s,"Int64",function(){return fo}),n.d(s,"Int128",function(){return ho});var c=n(13),l=n.n(c),f=n(6),h=n.n(f),d=new WeakMap,p=new WeakMap;function y(e){var t=d.get(e);return console.assert(null!=t,"'this' is expected an Event object, but got",e),t}function v(e){null==e.passiveListener?e.event.cancelable&&(e.canceled=!0,"function"===typeof e.event.preventDefault&&e.event.preventDefault()):"undefined"!==typeof console&&"function"===typeof console.error&&console.error("Unable to preventDefault inside passive event listener invocation.",e.passiveListener)}function b(e,t){d.set(this,{eventTarget:e,event:t,eventPhase:2,currentTarget:e,canceled:!1,stopped:!1,immediateStopped:!1,passiveListener:null,timeStamp:t.timeStamp||Date.now()}),Object.defineProperty(this,"isTrusted",{value:!1,enumerable:!0});for(var n=Object.keys(t),r=0;r<n.length;++r){var i=n[r];i in this||Object.defineProperty(this,i,m(i))}}function m(e){return{get:function(){return y(this).event[e]},set:function(t){y(this).event[e]=t},configurable:!0,enumerable:!0}}function g(e){return{value:function(){var t=y(this).event;return t[e].apply(t,arguments)},configurable:!0,enumerable:!0}}function k(e){if(null==e||e===Object.prototype)return b;var t=p.get(e);return null==t&&(t=function(e,t){var n=Object.keys(t);if(0===n.length)return e;function r(t,n){e.call(this,t,n)}r.prototype=Object.create(e.prototype,{constructor:{value:r,configurable:!0,writable:!0}});for(var i=0;i<n.length;++i){var a=n[i];if(!(a in e.prototype)){var o="function"===typeof Object.getOwnPropertyDescriptor(t,a).value;Object.defineProperty(r.prototype,a,o?g(a):m(a))}}return r}(k(Object.getPrototypeOf(e)),e),p.set(e,t)),t}function w(e){return y(e).immediateStopped}function _(e,t){y(e).passiveListener=t}b.prototype={get type(){return y(this).event.type},get target(){return y(this).eventTarget},get currentTarget(){return y(this).currentTarget},composedPath:function(){var e=y(this).currentTarget;return null==e?[]:[e]},get NONE(){return 0},get CAPTURING_PHASE(){return 1},get AT_TARGET(){return 2},get BUBBLING_PHASE(){return 3},get eventPhase(){return y(this).eventPhase},stopPropagation:function(){var e=y(this);e.stopped=!0,"function"===typeof e.event.stopPropagation&&e.event.stopPropagation()},stopImmediatePropagation:function(){var e=y(this);e.stopped=!0,e.immediateStopped=!0,"function"===typeof e.event.stopImmediatePropagation&&e.event.stopImmediatePropagation()},get bubbles(){return Boolean(y(this).event.bubbles)},get cancelable(){return Boolean(y(this).event.cancelable)},preventDefault:function(){v(y(this))},get defaultPrevented(){return y(this).canceled},get composed(){return Boolean(y(this).event.composed)},get timeStamp(){return y(this).timeStamp},get srcElement(){return y(this).eventTarget},get cancelBubble(){return y(this).stopped},set cancelBubble(e){if(e){var t=y(this);t.stopped=!0,"boolean"===typeof t.event.cancelBubble&&(t.event.cancelBubble=!0)}},get returnValue(){return!y(this).canceled},set returnValue(e){e||v(y(this))},initEvent:function(){}},Object.defineProperty(b.prototype,"constructor",{value:b,configurable:!0,writable:!0}),"undefined"!==typeof window&&"undefined"!==typeof window.Event&&(Object.setPrototypeOf(b.prototype,window.Event.prototype),p.set(window.Event.prototype,b));var O=new WeakMap,j=3;function x(e){return null!==e&&"object"===typeof e}function S(e){var t=O.get(e);if(null==t)throw new TypeError("'this' is expected an EventTarget object, but got another value.");return t}function T(e,t){Object.defineProperty(e,"on".concat(t),function(e){return{get:function(){for(var t=S(this).get(e);null!=t;){if(t.listenerType===j)return t.listener;t=t.next}return null},set:function(t){"function"===typeof t||x(t)||(t=null);for(var n=S(this),r=null,i=n.get(e);null!=i;)i.listenerType===j?null!==r?r.next=i.next:null!==i.next?n.set(e,i.next):n.delete(e):r=i,i=i.next;if(null!==t){var a={listener:t,listenerType:j,passive:!1,once:!1,next:null};null===r?n.set(e,a):r.next=a}},configurable:!0,enumerable:!0}}(t))}function I(e){function t(){E.call(this)}t.prototype=Object.create(E.prototype,{constructor:{value:t,configurable:!0,writable:!0}});for(var n=0;n<e.length;++n)T(t.prototype,e[n]);return t}function E(){if(!(this instanceof E)){if(1===arguments.length&&Array.isArray(arguments[0]))return I(arguments[0]);if(arguments.length>0){for(var e=new Array(arguments.length),t=0;t<arguments.length;++t)e[t]=arguments[t];return I(e)}throw new TypeError("Cannot call a class as a function")}O.set(this,new Map)}E.prototype={addEventListener:function(e,t,n){if(null!=t){if("function"!==typeof t&&!x(t))throw new TypeError("'listener' should be a function or an object.");var r=S(this),i=x(n),a=(i?Boolean(n.capture):Boolean(n))?1:2,o={listener:t,listenerType:a,passive:i&&Boolean(n.passive),once:i&&Boolean(n.once),next:null},u=r.get(e);if(void 0!==u){for(var s=null;null!=u;){if(u.listener===t&&u.listenerType===a)return;s=u,u=u.next}s.next=o}else r.set(e,o)}},removeEventListener:function(e,t,n){if(null!=t)for(var r=S(this),i=(x(n)?Boolean(n.capture):Boolean(n))?1:2,a=null,o=r.get(e);null!=o;){if(o.listener===t&&o.listenerType===i)return void(null!==a?a.next=o.next:null!==o.next?r.set(e,o.next):r.delete(e));a=o,o=o.next}},dispatchEvent:function(e){if(null==e||"string"!==typeof e.type)throw new TypeError('"event.type" should be a string.');var t=S(this),n=e.type,r=t.get(n);if(null==r)return!0;for(var i=function(e,t){return new(k(Object.getPrototypeOf(t)))(e,t)}(this,e),a=null;null!=r;){if(r.once?null!==a?a.next=r.next:null!==r.next?t.set(n,r.next):t.delete(n):a=r,_(i,r.passive?r.listener:null),"function"===typeof r.listener)try{r.listener.call(this,i)}catch(o){"undefined"!==typeof console&&"function"===typeof console.error&&console.error(o)}else r.listenerType!==j&&"function"===typeof r.listener.handleEvent&&r.listener.handleEvent(i);if(w(i))break;r=r.next}return _(i,null),function(e,t){y(e).eventPhase=t}(i,0),function(e,t){y(e).currentTarget=t}(i,null),!i.defaultPrevented}},Object.defineProperty(E.prototype,"constructor",{value:E,configurable:!0,writable:!0}),"undefined"!==typeof window&&"undefined"!==typeof window.EventTarget&&Object.setPrototypeOf(E.prototype,window.EventTarget.prototype);function A(e,t,n,r,i,a,o){try{var u=e[a](o),s=u.value}catch(c){return void n(c)}u.done?t(s):Promise.resolve(s).then(r,i)}function B(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){A(a,r,i,o,u,"next",e)}function u(e){A(a,r,i,o,u,"throw",e)}o(void 0)})}}var D=n(0),C=n(4),L=n(5);function F(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=[],r=!0,i=!1,a=void 0;try{for(var o,u=e[Symbol.iterator]();!(r=(o=u.next()).done)&&(n.push(o.value),!t||n.length!==t);r=!0);}catch(s){i=!0,a=s}finally{try{r||null==u.return||u.return()}finally{if(i)throw a}}return n}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function M(e){this.wrapped=e}function N(e){return new M(e)}function U(e){var t,n;function r(t,n){try{var a=e[t](n),o=a.value,u=o instanceof M;Promise.resolve(u?o.wrapped:o).then(function(e){u?r("next",e):i(a.done?"return":"normal",e)},function(e){r("throw",e)})}catch(s){i("throw",s)}}function i(e,i){switch(e){case"return":t.resolve({value:i,done:!0});break;case"throw":t.reject(i);break;default:t.resolve({value:i,done:!1})}(t=t.next)?r(t.key,t.arg):n=null}this._invoke=function(e,i){return new Promise(function(a,o){var u={key:e,arg:i,resolve:a,reject:o,next:null};n?n=n.next=u:(t=n=u,r(e,i))})},"function"!==typeof e.return&&(this.return=void 0)}function P(e){return function(){return new U(e.apply(this,arguments))}}function R(e,t){var n={},r=!1;function i(n,i){return r=!0,i=new Promise(function(t){t(e[n](i))}),{done:!1,value:t(i)}}return"function"===typeof Symbol&&Symbol.iterator&&(n[Symbol.iterator]=function(){return this}),n.next=function(e){return r?(r=!1,e):i("next",e)},"function"===typeof e.throw&&(n.throw=function(e){if(r)throw r=!1,e;return i("throw",e)}),"function"===typeof e.return&&(n.return=function(e){return i("return",e)}),n}"function"===typeof Symbol&&Symbol.asyncIterator&&(U.prototype[Symbol.asyncIterator]=function(){return this}),U.prototype.next=function(e){return this._invoke("next",e)},U.prototype.throw=function(e){return this._invoke("throw",e)},U.prototype.return=function(e){return this._invoke("return",e)};var z={};function V(e,t,n){return t<=e&&e<=n}function W(e){if(void 0===e)return{};if(e===Object(e))return e;throw TypeError("Could not convert argument to dictionary")}z.Offset,z.Table,z.SIZEOF_SHORT=2,z.SIZEOF_INT=4,z.FILE_IDENTIFIER_LENGTH=4,z.Encoding={UTF8_BYTES:1,UTF16_STRING:2},z.int32=new Int32Array(2),z.float32=new Float32Array(z.int32.buffer),z.float64=new Float64Array(z.int32.buffer),z.isLittleEndian=1===new Uint16Array(new Uint8Array([1,0]).buffer)[0],z.Long=function(e,t){this.low=0|e,this.high=0|t},z.Long.create=function(e,t){return 0==e&&0==t?z.Long.ZERO:new z.Long(e,t)},z.Long.prototype.toFloat64=function(){return(this.low>>>0)+4294967296*this.high},z.Long.prototype.equals=function(e){return this.low==e.low&&this.high==e.high},z.Long.ZERO=new z.Long(0,0),z.Builder=function(e){if(e)t=e;else var t=1024;this.bb=z.ByteBuffer.allocate(t),this.space=t,this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1},z.Builder.prototype.clear=function(){this.bb.clear(),this.space=this.bb.capacity(),this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1},z.Builder.prototype.forceDefaults=function(e){this.force_defaults=e},z.Builder.prototype.dataBuffer=function(){return this.bb},z.Builder.prototype.asUint8Array=function(){return this.bb.bytes().subarray(this.bb.position(),this.bb.position()+this.offset())},z.Builder.prototype.prep=function(e,t){e>this.minalign&&(this.minalign=e);for(var n=1+~(this.bb.capacity()-this.space+t)&e-1;this.space<n+e+t;){var r=this.bb.capacity();this.bb=z.Builder.growByteBuffer(this.bb),this.space+=this.bb.capacity()-r}this.pad(n)},z.Builder.prototype.pad=function(e){for(var t=0;t<e;t++)this.bb.writeInt8(--this.space,0)},z.Builder.prototype.writeInt8=function(e){this.bb.writeInt8(this.space-=1,e)},z.Builder.prototype.writeInt16=function(e){this.bb.writeInt16(this.space-=2,e)},z.Builder.prototype.writeInt32=function(e){this.bb.writeInt32(this.space-=4,e)},z.Builder.prototype.writeInt64=function(e){this.bb.writeInt64(this.space-=8,e)},z.Builder.prototype.writeFloat32=function(e){this.bb.writeFloat32(this.space-=4,e)},z.Builder.prototype.writeFloat64=function(e){this.bb.writeFloat64(this.space-=8,e)},z.Builder.prototype.addInt8=function(e){this.prep(1,0),this.writeInt8(e)},z.Builder.prototype.addInt16=function(e){this.prep(2,0),this.writeInt16(e)},z.Builder.prototype.addInt32=function(e){this.prep(4,0),this.writeInt32(e)},z.Builder.prototype.addInt64=function(e){this.prep(8,0),this.writeInt64(e)},z.Builder.prototype.addFloat32=function(e){this.prep(4,0),this.writeFloat32(e)},z.Builder.prototype.addFloat64=function(e){this.prep(8,0),this.writeFloat64(e)},z.Builder.prototype.addFieldInt8=function(e,t,n){(this.force_defaults||t!=n)&&(this.addInt8(t),this.slot(e))},z.Builder.prototype.addFieldInt16=function(e,t,n){(this.force_defaults||t!=n)&&(this.addInt16(t),this.slot(e))},z.Builder.prototype.addFieldInt32=function(e,t,n){(this.force_defaults||t!=n)&&(this.addInt32(t),this.slot(e))},z.Builder.prototype.addFieldInt64=function(e,t,n){!this.force_defaults&&t.equals(n)||(this.addInt64(t),this.slot(e))},z.Builder.prototype.addFieldFloat32=function(e,t,n){(this.force_defaults||t!=n)&&(this.addFloat32(t),this.slot(e))},z.Builder.prototype.addFieldFloat64=function(e,t,n){(this.force_defaults||t!=n)&&(this.addFloat64(t),this.slot(e))},z.Builder.prototype.addFieldOffset=function(e,t,n){(this.force_defaults||t!=n)&&(this.addOffset(t),this.slot(e))},z.Builder.prototype.addFieldStruct=function(e,t,n){t!=n&&(this.nested(t),this.slot(e))},z.Builder.prototype.nested=function(e){if(e!=this.offset())throw new Error("FlatBuffers: struct must be serialized inline.")},z.Builder.prototype.notNested=function(){if(this.isNested)throw new Error("FlatBuffers: object serialization must not be nested.")},z.Builder.prototype.slot=function(e){this.vtable[e]=this.offset()},z.Builder.prototype.offset=function(){return this.bb.capacity()-this.space},z.Builder.growByteBuffer=function(e){var t=e.capacity();if(3221225472&t)throw new Error("FlatBuffers: cannot grow buffer beyond 2 gigabytes.");var n=t<<1,r=z.ByteBuffer.allocate(n);return r.setPosition(n-t),r.bytes().set(e.bytes(),n-t),r},z.Builder.prototype.addOffset=function(e){this.prep(z.SIZEOF_INT,0),this.writeInt32(this.offset()-e+z.SIZEOF_INT)},z.Builder.prototype.startObject=function(e){this.notNested(),null==this.vtable&&(this.vtable=[]),this.vtable_in_use=e;for(var t=0;t<e;t++)this.vtable[t]=0;this.isNested=!0,this.object_start=this.offset()},z.Builder.prototype.endObject=function(){if(null==this.vtable||!this.isNested)throw new Error("FlatBuffers: endObject called without startObject");this.addInt32(0);for(var e=this.offset(),t=this.vtable_in_use-1;t>=0&&0==this.vtable[t];t--);for(var n=t+1;t>=0;t--)this.addInt16(0!=this.vtable[t]?e-this.vtable[t]:0);this.addInt16(e-this.object_start);var r=(n+2)*z.SIZEOF_SHORT;this.addInt16(r);var i=0,a=this.space;e:for(t=0;t<this.vtables.length;t++){var o=this.bb.capacity()-this.vtables[t];if(r==this.bb.readInt16(o)){for(var u=z.SIZEOF_SHORT;u<r;u+=z.SIZEOF_SHORT)if(this.bb.readInt16(a+u)!=this.bb.readInt16(o+u))continue e;i=this.vtables[t];break}}return i?(this.space=this.bb.capacity()-e,this.bb.writeInt32(this.space,i-e)):(this.vtables.push(this.offset()),this.bb.writeInt32(this.bb.capacity()-e,this.offset()-e)),this.isNested=!1,e},z.Builder.prototype.finish=function(e,t){if(t){var n=t;if(this.prep(this.minalign,z.SIZEOF_INT+z.FILE_IDENTIFIER_LENGTH),n.length!=z.FILE_IDENTIFIER_LENGTH)throw new Error("FlatBuffers: file identifier must be length "+z.FILE_IDENTIFIER_LENGTH);for(var r=z.FILE_IDENTIFIER_LENGTH-1;r>=0;r--)this.writeInt8(n.charCodeAt(r))}this.prep(this.minalign,z.SIZEOF_INT),this.addOffset(e),this.bb.setPosition(this.space)},z.Builder.prototype.requiredField=function(e,t){var n=this.bb.capacity()-e,r=n-this.bb.readInt32(n);if(!(0!=this.bb.readInt16(r+t)))throw new Error("FlatBuffers: field "+t+" must be set")},z.Builder.prototype.startVector=function(e,t,n){this.notNested(),this.vector_num_elems=t,this.prep(z.SIZEOF_INT,e*t),this.prep(n,e*t)},z.Builder.prototype.endVector=function(){return this.writeInt32(this.vector_num_elems),this.offset()},z.Builder.prototype.createString=function(e){if(e instanceof Uint8Array)var t=e;else{t=[];for(var n=0;n<e.length;){var r,i=e.charCodeAt(n++);if(i<55296||i>=56320)r=i;else r=(i<<10)+e.charCodeAt(n++)+-56613888;r<128?t.push(r):(r<2048?t.push(r>>6&31|192):(r<65536?t.push(r>>12&15|224):t.push(r>>18&7|240,r>>12&63|128),t.push(r>>6&63|128)),t.push(63&r|128))}}this.addInt8(0),this.startVector(1,t.length,1),this.bb.setPosition(this.space-=t.length);n=0;for(var a=this.space,o=this.bb.bytes();n<t.length;n++)o[a++]=t[n];return this.endVector()},z.Builder.prototype.createLong=function(e,t){return z.Long.create(e,t)},z.ByteBuffer=function(e){this.bytes_=e,this.position_=0},z.ByteBuffer.allocate=function(e){return new z.ByteBuffer(new Uint8Array(e))},z.ByteBuffer.prototype.clear=function(){this.position_=0},z.ByteBuffer.prototype.bytes=function(){return this.bytes_},z.ByteBuffer.prototype.position=function(){return this.position_},z.ByteBuffer.prototype.setPosition=function(e){this.position_=e},z.ByteBuffer.prototype.capacity=function(){return this.bytes_.length},z.ByteBuffer.prototype.readInt8=function(e){return this.readUint8(e)<<24>>24},z.ByteBuffer.prototype.readUint8=function(e){return this.bytes_[e]},z.ByteBuffer.prototype.readInt16=function(e){return this.readUint16(e)<<16>>16},z.ByteBuffer.prototype.readUint16=function(e){return this.bytes_[e]|this.bytes_[e+1]<<8},z.ByteBuffer.prototype.readInt32=function(e){return this.bytes_[e]|this.bytes_[e+1]<<8|this.bytes_[e+2]<<16|this.bytes_[e+3]<<24},z.ByteBuffer.prototype.readUint32=function(e){return this.readInt32(e)>>>0},z.ByteBuffer.prototype.readInt64=function(e){return new z.Long(this.readInt32(e),this.readInt32(e+4))},z.ByteBuffer.prototype.readUint64=function(e){return new z.Long(this.readUint32(e),this.readUint32(e+4))},z.ByteBuffer.prototype.readFloat32=function(e){return z.int32[0]=this.readInt32(e),z.float32[0]},z.ByteBuffer.prototype.readFloat64=function(e){return z.int32[z.isLittleEndian?0:1]=this.readInt32(e),z.int32[z.isLittleEndian?1:0]=this.readInt32(e+4),z.float64[0]},z.ByteBuffer.prototype.writeInt8=function(e,t){this.bytes_[e]=t},z.ByteBuffer.prototype.writeUint8=function(e,t){this.bytes_[e]=t},z.ByteBuffer.prototype.writeInt16=function(e,t){this.bytes_[e]=t,this.bytes_[e+1]=t>>8},z.ByteBuffer.prototype.writeUint16=function(e,t){this.bytes_[e]=t,this.bytes_[e+1]=t>>8},z.ByteBuffer.prototype.writeInt32=function(e,t){this.bytes_[e]=t,this.bytes_[e+1]=t>>8,this.bytes_[e+2]=t>>16,this.bytes_[e+3]=t>>24},z.ByteBuffer.prototype.writeUint32=function(e,t){this.bytes_[e]=t,this.bytes_[e+1]=t>>8,this.bytes_[e+2]=t>>16,this.bytes_[e+3]=t>>24},z.ByteBuffer.prototype.writeInt64=function(e,t){this.writeInt32(e,t.low),this.writeInt32(e+4,t.high)},z.ByteBuffer.prototype.writeUint64=function(e,t){this.writeUint32(e,t.low),this.writeUint32(e+4,t.high)},z.ByteBuffer.prototype.writeFloat32=function(e,t){z.float32[0]=t,this.writeInt32(e,z.int32[0])},z.ByteBuffer.prototype.writeFloat64=function(e,t){z.float64[0]=t,this.writeInt32(e,z.int32[z.isLittleEndian?0:1]),this.writeInt32(e+4,z.int32[z.isLittleEndian?1:0])},z.ByteBuffer.prototype.getBufferIdentifier=function(){if(this.bytes_.length<this.position_+z.SIZEOF_INT+z.FILE_IDENTIFIER_LENGTH)throw new Error("FlatBuffers: ByteBuffer is too short to contain an identifier.");for(var e="",t=0;t<z.FILE_IDENTIFIER_LENGTH;t++)e+=String.fromCharCode(this.readInt8(this.position_+z.SIZEOF_INT+t));return e},z.ByteBuffer.prototype.__offset=function(e,t){var n=e-this.readInt32(e);return t<this.readInt16(n)?this.readInt16(n+t):0},z.ByteBuffer.prototype.__union=function(e,t){return e.bb_pos=t+this.readInt32(t),e.bb=this,e},z.ByteBuffer.prototype.__string=function(e,t){e+=this.readInt32(e);var n=this.readInt32(e),r="",i=0;if(e+=z.SIZEOF_INT,t===z.Encoding.UTF8_BYTES)return this.bytes_.subarray(e,e+n);for(;i<n;){var a,o=this.readUint8(e+i++);if(o<192)a=o;else{var u=this.readUint8(e+i++);if(o<224)a=(31&o)<<6|63&u;else{var s=this.readUint8(e+i++);if(o<240)a=(15&o)<<12|(63&u)<<6|63&s;else a=(7&o)<<18|(63&u)<<12|(63&s)<<6|63&this.readUint8(e+i++)}}a<65536?r+=String.fromCharCode(a):(a-=65536,r+=String.fromCharCode(55296+(a>>10),56320+(1023&a)))}return r},z.ByteBuffer.prototype.__indirect=function(e){return e+this.readInt32(e)},z.ByteBuffer.prototype.__vector=function(e){return e+this.readInt32(e)+z.SIZEOF_INT},z.ByteBuffer.prototype.__vector_len=function(e){return this.readInt32(e+this.readInt32(e))},z.ByteBuffer.prototype.__has_identifier=function(e){if(e.length!=z.FILE_IDENTIFIER_LENGTH)throw new Error("FlatBuffers: file identifier must be length "+z.FILE_IDENTIFIER_LENGTH);for(var t=0;t<z.FILE_IDENTIFIER_LENGTH;t++)if(e.charCodeAt(t)!=this.readInt8(this.position_+z.SIZEOF_INT+t))return!1;return!0},z.ByteBuffer.prototype.createLong=function(e,t){return z.Long.create(e,t)};var $=-1;function H(e){this.tokens=[].slice.call(e)}H.prototype={endOfStream:function(){return!this.tokens.length},read:function(){return this.tokens.length?this.tokens.shift():$},prepend:function(e){if(Array.isArray(e))for(var t=e;t.length;)this.tokens.unshift(t.pop());else this.tokens.unshift(e)},push:function(e){if(Array.isArray(e))for(var t=e;t.length;)this.tokens.push(t.shift());else this.tokens.push(e)}};var Y=-1;function K(e,t){if(e)throw TypeError("Decoder error");return t||65533}var Q="utf-8";function q(e,t){if(!(this instanceof q))return new q(e,t);if((e=void 0!==e?String(e).toLowerCase():Q)!==Q)throw new Error("Encoding not supported. Only utf-8 is supported");t=W(t),this._streaming=!1,this._BOMseen=!1,this._decoder=null,this._fatal=Boolean(t.fatal),this._ignoreBOM=Boolean(t.ignoreBOM),Object.defineProperty(this,"encoding",{value:"utf-8"}),Object.defineProperty(this,"fatal",{value:this._fatal}),Object.defineProperty(this,"ignoreBOM",{value:this._ignoreBOM})}function G(e,t){if(!(this instanceof G))return new G(e,t);if((e=void 0!==e?String(e).toLowerCase():Q)!==Q)throw new Error("Encoding not supported. Only utf-8 is supported");t=W(t),this._streaming=!1,this._encoder=null,this._options={fatal:Boolean(t.fatal)},Object.defineProperty(this,"encoding",{value:"utf-8"})}function J(e){var t=e.fatal,n=0,r=0,i=0,a=128,o=191;this.handler=function(e,u){if(u===$&&0!==i)return i=0,K(t);if(u===$)return Y;if(0===i){if(V(u,0,127))return u;if(V(u,194,223))i=1,n=u-192;else if(V(u,224,239))224===u&&(a=160),237===u&&(o=159),i=2,n=u-224;else{if(!V(u,240,244))return K(t);240===u&&(a=144),244===u&&(o=143),i=3,n=u-240}return n<<=6*i,null}if(!V(u,a,o))return n=i=r=0,a=128,o=191,e.prepend(u),K(t);if(a=128,o=191,n+=u-128<<6*(i-(r+=1)),r!==i)return null;var s=n;return n=i=r=0,s}}function X(e){e.fatal;this.handler=function(e,t){if(t===$)return Y;if(V(t,0,127))return t;var n,r;V(t,128,2047)?(n=1,r=192):V(t,2048,65535)?(n=2,r=224):V(t,65536,1114111)&&(n=3,r=240);for(var i=[(t>>6*n)+r];n>0;){var a=t>>6*(n-1);i.push(128|63&a),n-=1}return i}}q.prototype={decode:function(e,t){var n;n="object"===typeof e&&e instanceof ArrayBuffer?new Uint8Array(e):"object"===typeof e&&"buffer"in e&&e.buffer instanceof ArrayBuffer?new Uint8Array(e.buffer,e.byteOffset,e.byteLength):new Uint8Array(0),t=W(t),this._streaming||(this._decoder=new J({fatal:this._fatal}),this._BOMseen=!1),this._streaming=Boolean(t.stream);for(var r,i=new H(n),a=[];!i.endOfStream()&&(r=this._decoder.handler(i,i.read()))!==Y;)null!==r&&(Array.isArray(r)?a.push.apply(a,r):a.push(r));if(!this._streaming){do{if((r=this._decoder.handler(i,i.read()))===Y)break;null!==r&&(Array.isArray(r)?a.push.apply(a,r):a.push(r))}while(!i.endOfStream());this._decoder=null}return a.length&&(-1===["utf-8"].indexOf(this.encoding)||this._ignoreBOM||this._BOMseen||(65279===a[0]?(this._BOMseen=!0,a.shift()):this._BOMseen=!0)),function(e){for(var t="",n=0;n<e.length;++n){var r=e[n];r<=65535?t+=String.fromCharCode(r):(r-=65536,t+=String.fromCharCode(55296+(r>>10),56320+(1023&r)))}return t}(a)}},G.prototype={encode:function(e,t){e=e?String(e):"",t=W(t),this._streaming||(this._encoder=new X(this._options)),this._streaming=Boolean(t.stream);for(var n,r=[],i=new H(function(e){for(var t=String(e),n=t.length,r=0,i=[];r<n;){var a=t.charCodeAt(r);if(a<55296||a>57343)i.push(a);else if(56320<=a&&a<=57343)i.push(65533);else if(55296<=a&&a<=56319)if(r===n-1)i.push(65533);else{var o=e.charCodeAt(r+1);if(56320<=o&&o<=57343){var u=1023&a,s=1023&o;i.push(65536+(u<<10)+s),r+=1}else i.push(65533)}r+=1}return i}(e));!i.endOfStream()&&(n=this._encoder.handler(i,i.read()))!==Y;)Array.isArray(n)?r.push.apply(r,n):r.push(n);if(!this._streaming){for(;(n=this._encoder.handler(i,i.read()))!==Y;)Array.isArray(n)?r.push.apply(r,n):r.push(n);this._encoder=null}return new Uint8Array(r)}};var Z="function"===typeof Buffer?Buffer:null,ee="function"===typeof TextDecoder&&"function"===typeof TextEncoder,te=function(e){if(ee||!Z){var t=new e("utf-8");return function(e){return t.decode(e)}}return function(e){var t=Ve(e),n=t.buffer,r=t.byteOffset,i=t.length;return Z.from(n,r,i).toString()}}("undefined"!==typeof TextDecoder?TextDecoder:q),ne=function(e){if(ee||!Z){var t=new e;return function(e){return t.encode(e)}}return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return Ve(Z.from(e,"utf8"))}}("undefined"!==typeof TextEncoder?TextEncoder:G),re=n(2),ie=n(1),ae=n(3),oe=Object.freeze({done:!0,value:void 0}),ue=function(){function e(t){Object(D.a)(this,e),this._json=t}return Object(C.a)(e,[{key:"schema",get:function(){return this._json.schema}},{key:"batches",get:function(){return this._json.batches||[]}},{key:"dictionaries",get:function(){return this._json.dictionaries||[]}}]),e}(),se=function(){function e(){Object(D.a)(this,e)}return Object(C.a)(e,[{key:"tee",value:function(){return this._getDOMStream().tee()}},{key:"pipe",value:function(e,t){return this._getNodeStream().pipe(e,t)}},{key:"pipeTo",value:function(e,t){return this._getDOMStream().pipeTo(e,t)}},{key:"pipeThrough",value:function(e,t){return this._getDOMStream().pipeThrough(e,t)}},{key:"_getDOMStream",value:function(){return this._DOMStream||(this._DOMStream=this.toDOMStream())}},{key:"_getNodeStream",value:function(){return this._nodeStream||(this._nodeStream=this.toNodeStream())}}]),e}(),ce=function(e){function t(){var e;return Object(D.a)(this,t),(e=Object(re.a)(this,Object(ie.a)(t).call(this)))._values=[],e.resolvers=[],e._closedPromise=new Promise(function(t){return e._closedPromiseResolve=t}),e}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"cancel",value:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.return(t);case 2:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"write",value:function(e){this._ensureOpen()&&(this.resolvers.length<=0?this._values.push(e):this.resolvers.shift().resolve({done:!1,value:e}))}},{key:"abort",value:function(e){this._closedPromiseResolve&&(this.resolvers.length<=0?this._error={error:e}:this.resolvers.shift().reject({done:!0,value:e}))}},{key:"close",value:function(){if(this._closedPromiseResolve){for(var e=this.resolvers;e.length>0;)e.shift().resolve(oe);this._closedPromiseResolve(),this._closedPromiseResolve=void 0}}},{key:Symbol.asyncIterator,value:function(){return this}},{key:"toDOMStream",value:function(e){return _t.toDOMStream(this._closedPromiseResolve||this._error?this:this._values,e)}},{key:"toNodeStream",value:function(e){return _t.toNodeStream(this._closedPromiseResolve||this._error?this:this._values,e)}},{key:"throw",value:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.abort(t);case 2:return e.abrupt("return",oe);case 3:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"return",value:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.close();case 2:return e.abrupt("return",oe);case 3:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"read",value:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.next(t,"read");case 2:return e.abrupt("return",e.sent.value);case 3:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"peek",value:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.next(t,"peek");case 2:return e.abrupt("return",e.sent.value);case 3:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"next",value:function(){var e=this;return this._values.length>0?Promise.resolve({done:!1,value:this._values.shift()}):this._error?Promise.reject({done:!0,value:this._error.error}):this._closedPromiseResolve?new Promise(function(t,n){e.resolvers.push({resolve:t,reject:n})}):Promise.resolve(oe)}},{key:"_ensureOpen",value:function(){if(this._closedPromiseResolve)return!0;throw new Error("".concat(this," is closed"))}},{key:"closed",get:function(){return this._closedPromise}}]),t}(se),le=F(function(){var e=function(){throw new Error("BigInt is not available in this environment")};function t(){throw e()}return t.asIntN=function(){throw e()},t.asUintN=function(){throw e()},"undefined"!==typeof BigInt?[BigInt,!0]:[t,!1]}(),2),fe=le[0],he=le[1],de=F(function(){var e=function(){throw new Error("BigInt64Array is not available in this environment")};return"undefined"!==typeof BigInt64Array?[BigInt64Array,!0]:[function(){function t(){throw Object(D.a)(this,t),e()}return Object(C.a)(t,null,[{key:"of",value:function(){throw e()}},{key:"from",value:function(){throw e()}},{key:"BYTES_PER_ELEMENT",get:function(){return 8}}]),t}(),!1]}(),2),pe=de[0],ye=(de[1],F(function(){var e=function(){throw new Error("BigUint64Array is not available in this environment")};return"undefined"!==typeof BigUint64Array?[BigUint64Array,!0]:[function(){function t(){throw Object(D.a)(this,t),e()}return Object(C.a)(t,null,[{key:"of",value:function(){throw e()}},{key:"from",value:function(){throw e()}},{key:"BYTES_PER_ELEMENT",get:function(){return 8}}]),t}(),!1]}(),2)),ve=ye[0],be=(ye[1],function(e){return"number"===typeof e}),me=function(e){return"boolean"===typeof e},ge=function(e){return"function"===typeof e},ke=function(e){return null!=e&&Object(e)===e},we=function(e){return ke(e)&&ge(e.then)},_e=function(e){return ke(e)&&ge(e[Symbol.iterator])},Oe=function(e){return ke(e)&&ge(e[Symbol.asyncIterator])},je=function(e){return ke(e)&&ke(e.schema)},xe=function(e){return ke(e)&&"done"in e&&"value"in e},Se=function(e){return ke(e)&&ge(e.stat)&&be(e.fd)},Te=function(e){return ke(e)&&Ie(e.body)},Ie=function(e){return ke(e)&&ge(e.cancel)&&ge(e.getReader)&&!(e instanceof se)},Ee=function(e){return ke(e)&&ge(e.read)&&ge(e.pipe)&&me(e.readable)&&!(e instanceof se)},Ae=L.mark(Ge);function Be(e){var t,n,r,i=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);i--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new De(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function De(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:t}})}return(De=function(e){this.s=e,this.n=e.next}).prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new De(e)}var Ce=z.ByteBuffer,Le="undefined"!==typeof SharedArrayBuffer?SharedArrayBuffer:ArrayBuffer;function Fe(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:t.byteLength,i=e.byteLength,a=new Uint8Array(e.buffer,e.byteOffset,i),o=new Uint8Array(t.buffer,t.byteOffset,Math.min(r,i));return a.set(o,n),e}function Me(e,t){for(var n,r,i,a=function(e){for(var t,n,r,i,a,o,u=e[0]?[e[0]]:[],s=0,c=0,l=e.length;++s<l;)if(a=u[c],o=e[s],!a||!o||a.buffer!==o.buffer||o.byteOffset<a.byteOffset)o&&(u[++c]=o);else{var f=a;t=f.byteOffset,r=f.byteLength;var h=o;n=h.byteOffset,i=h.byteLength,t+r<n||n+i<t?o&&(u[++c]=o):u[c]=new Uint8Array(a.buffer,t,n-t+i)}return u}(e),o=a.reduce(function(e,t){return e+t.byteLength},0),u=0,s=-1,c=Math.min(t||1/0,o),l=a.length;++s<l;){if(c<=u+(r=(n=a[s]).subarray(0,Math.min(n.length,c-u))).length){r.length<n.length?a[s]=n.subarray(r.length):r.length===n.length&&s++,i?Fe(i,r,u):i=r;break}Fe(i||(i=new Uint8Array(c)),r,u),u+=r.length}return[i||new Uint8Array(0),a.slice(s),o-(i?i.byteLength:0)]}function Ne(e,t){var n=xe(t)?t.value:t;return n instanceof e?e===Uint8Array?new e(n.buffer,n.byteOffset,n.byteLength):n:n?("string"===typeof n&&(n=ne(n)),n instanceof ArrayBuffer?new e(n):n instanceof Le?new e(n):n instanceof Ce?Ne(e,n.bytes()):ArrayBuffer.isView(n)?n.byteLength<=0?new e(0):new e(n.buffer,n.byteOffset,n.byteLength/e.BYTES_PER_ELEMENT):e.from(n)):new e(0)}var Ue=function(e){return Ne(Int8Array,e)},Pe=function(e){return Ne(Int16Array,e)},Re=function(e){return Ne(Int32Array,e)},ze=function(e){return Ne(pe,e)},Ve=function(e){return Ne(Uint8Array,e)},We=function(e){return Ne(Uint16Array,e)},$e=function(e){return Ne(Uint32Array,e)},He=function(e){return Ne(ve,e)},Ye=function(e){return Ne(Float32Array,e)},Ke=function(e){return Ne(Float64Array,e)},Qe=function(e){return Ne(Uint8ClampedArray,e)},qe=function(e){return e.next(),e};function Ge(e,t){var n,r;return L.wrap(function(i){for(;;)switch(i.prev=i.next){case 0:return n=L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t;case 2:case"end":return e.stop()}},e)}),r="string"===typeof t?n(t):ArrayBuffer.isView(t)?n(t):t instanceof ArrayBuffer?n(t):t instanceof Le?n(t):_e(t)?t:n(t),i.delegateYield(qe(L.mark(function t(n){var r;return L.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:r=null;case 1:return t.t0=n,t.next=4,Ne(e,r);case 4:t.t1=t.sent,r=t.t0.next.call(t.t0,t.t1);case 6:if(!r.done){t.next=1;break}case 7:case"end":return t.stop()}},t)})(r[Symbol.iterator]())),"t0",3);case 3:case"end":return i.stop()}},Ae)}var Je=function(e){return Ge(Int8Array,e)},Xe=function(e){return Ge(Int16Array,e)},Ze=function(e){return Ge(Int32Array,e)},et=function(e){return Ge(Uint8Array,e)},tt=function(e){return Ge(Uint16Array,e)},nt=function(e){return Ge(Uint32Array,e)},rt=function(e){return Ge(Float32Array,e)},it=function(e){return Ge(Float64Array,e)},at=function(e){return Ge(Uint8ClampedArray,e)};function ot(e,t){return ut.apply(this,arguments)}function ut(){return(ut=P(L.mark(function e(t,n){var r,i,a;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!we(n)){e.next=13;break}return e.t0=R,e.t1=Be,e.t2=ot,e.t3=t,e.next=7,N(n);case 7:return e.t4=e.sent,e.t5=(0,e.t2)(e.t3,e.t4),e.t6=(0,e.t1)(e.t5),e.t7=N,e.delegateYield((0,e.t0)(e.t6,e.t7),"t8",12);case 12:return e.abrupt("return",e.t8);case 13:return r=function(){var e=P(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,N(t);case 2:return e.next=4,e.sent;case 4:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}(),i=function(){var e=P(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.delegateYield(R(Be(qe(L.mark(function e(t){var n;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:n=null;case 1:return e.t0=t,e.next=4,n&&n.value;case 4:e.t1=e.sent,n=e.t0.next.call(e.t0,e.t1);case 6:if(!n.done){e.next=1;break}case 7:case"end":return e.stop()}},e)})(t[Symbol.iterator]()))),N),"t0",1);case 1:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}(),a="string"===typeof n?r(n):ArrayBuffer.isView(n)?r(n):n instanceof ArrayBuffer?r(n):n instanceof Le?r(n):_e(n)?i(n):Oe(n)?n:r(n),e.delegateYield(R(Be(qe(function(){var e=P(L.mark(function e(n){var r;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:r=null;case 1:return e.t0=N,e.t1=n,e.next=5,Ne(t,r);case 5:return e.t2=e.sent,e.t3=e.t1.next.call(e.t1,e.t2),e.next=9,(0,e.t0)(e.t3);case 9:r=e.sent;case 10:if(!r.done){e.next=1;break}case 11:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}()(a[Symbol.asyncIterator]()))),N),"t9",17);case 17:case"end":return e.stop()}},e)}))).apply(this,arguments)}var st=function(e){return ot(Int8Array,e)},ct=function(e){return ot(Int16Array,e)},lt=function(e){return ot(Int32Array,e)},ft=function(e){return ot(Uint8Array,e)},ht=function(e){return ot(Uint16Array,e)},dt=function(e){return ot(Uint32Array,e)},pt=function(e){return ot(Float32Array,e)},yt=function(e){return ot(Float64Array,e)},vt=function(e){return ot(Uint8ClampedArray,e)};function bt(e,t,n){if(0!==e){n=n.slice(0,t+1);for(var r=-1;++r<=t;)n[r]+=e}return n}function mt(e,t){var n=0,r=e.length;if(r!==t.length)return!1;if(r>0)do{if(e[n]!==t[n])return!1}while(++n<r);return!0}function gt(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"===typeof e)return kt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return kt(e,t)}(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){u=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(u)throw a}}}}function kt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var wt=L.mark(jt),_t={fromIterable:function(e){return Ot(jt(e))},fromAsyncIterable:function(e){return Ot(function(e){return xt.apply(this,arguments)}(e))},fromDOMStream:function(e){return Ot(function(e){return St.apply(this,arguments)}(e))},fromNodeStream:function(e){return Ot(function(e){return Bt.apply(this,arguments)}(e))},toDOMStream:function(e,t){throw new Error('"toDOMStream" not available in this environment')},toNodeStream:function(e,t){throw new Error('"toNodeStream" not available in this environment')}},Ot=function(e){return e.next(),e};function jt(e){var t,n,r,i,a,o,u,s,c,l,f,h;return L.wrap(function(d){for(;;)switch(d.prev=d.next){case 0:return s=function(){if("peek"===a)return Me(r,o)[0];var e=F(Me(r,o),3);return i=e[0],r=e[1],u=e[2],i},n=!1,r=[],u=0,d.next=6,null;case 6:c=d.sent,a=c.cmd,o=c.size,l=et(e)[Symbol.iterator](),d.prev=10;case 11:if(f=isNaN(o-u)?l.next(void 0):l.next(o-u),t=f.done,i=f.value,!t&&i.byteLength>0&&(r.push(i),u+=i.byteLength),!(t||o<=u)){d.next=22;break}case 16:return d.next=18,s();case 18:h=d.sent,a=h.cmd,o=h.size;case 21:if(o<u){d.next=16;break}case 22:if(!t){d.next=11;break}case 23:d.next=28;break;case 25:d.prev=25,d.t0=d.catch(10),(n=!0)&&"function"===typeof l.throw&&l.throw(d.t0);case 28:return d.prev=28,!1===n&&"function"===typeof l.return&&l.return(),d.finish(28);case 31:case"end":return d.stop()}},wt,null,[[10,25,28,31]])}function xt(){return(xt=P(L.mark(function e(t){var n,r,i,a,o,u,s,c,l,f,h,d;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return c=function(){if("peek"===o)return Me(i,u)[0];var e=F(Me(i,u),3);return a=e[0],i=e[1],s=e[2],a},r=!1,i=[],s=0,e.next=6,null;case 6:l=e.sent,o=l.cmd,u=l.size,f=ft(t)[Symbol.asyncIterator](),e.prev=10;case 11:if(!isNaN(u-s)){e.next=17;break}return e.next=14,N(f.next(void 0));case 14:e.t0=e.sent,e.next=20;break;case 17:return e.next=19,N(f.next(u-s));case 19:e.t0=e.sent;case 20:if(h=e.t0,n=h.done,a=h.value,!n&&a.byteLength>0&&(i.push(a),s+=a.byteLength),!(n||u<=s)){e.next=31;break}case 25:return e.next=27,c();case 27:d=e.sent,o=d.cmd,u=d.size;case 30:if(u<s){e.next=25;break}case 31:if(!n){e.next=11;break}case 32:e.next=40;break;case 34:if(e.prev=34,e.t1=e.catch(10),e.t2=(r=!0)&&"function"===typeof f.throw,!e.t2){e.next=40;break}return e.next=40,N(f.throw(e.t1));case 40:if(e.prev=40,e.t3=!1===r&&"function"===typeof f.return,!e.t3){e.next=45;break}return e.next=45,N(f.return());case 45:return e.finish(40);case 46:case"end":return e.stop()}},e,null,[[10,34,40,46]])}))).apply(this,arguments)}function St(){return(St=P(L.mark(function e(t){var n,r,i,a,o,u,s,c,l,f,h,d;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return c=function(){if("peek"===o)return Me(i,u)[0];var e=F(Me(i,u),3);return a=e[0],i=e[1],s=e[2],a},n=!1,r=!1,i=[],s=0,e.next=6,null;case 6:l=e.sent,o=l.cmd,u=l.size,f=new Tt(t),e.prev=10;case 11:if(!isNaN(u-s)){e.next=17;break}return e.next=14,N(f.read(void 0));case 14:e.t0=e.sent,e.next=20;break;case 17:return e.next=19,N(f.read(u-s));case 19:e.t0=e.sent;case 20:if(h=e.t0,n=h.done,a=h.value,!n&&a.byteLength>0&&(i.push(Ve(a)),s+=a.byteLength),!(n||u<=s)){e.next=31;break}case 25:return e.next=27,c();case 27:d=e.sent,o=d.cmd,u=d.size;case 30:if(u<s){e.next=25;break}case 31:if(!n){e.next=11;break}case 32:e.next=40;break;case 34:if(e.prev=34,e.t1=e.catch(10),e.t2=r=!0,!e.t2){e.next=40;break}return e.next=40,N(f.cancel(e.t1));case 40:if(e.prev=40,!1!==r){e.next=46;break}return e.next=44,N(f.cancel());case 44:e.next=47;break;case 46:t.locked&&f.releaseLock();case 47:return e.finish(40);case 48:case"end":return e.stop()}},e,null,[[10,34,40,48]])}))).apply(this,arguments)}var Tt=function(){function e(t){Object(D.a)(this,e),this.source=t,this.byobReader=null,this.defaultReader=null;try{this.supportsBYOB=!!(this.reader=this.getBYOBReader())}catch(n){this.supportsBYOB=!(this.reader=this.getDefaultReader())}}return Object(C.a)(e,[{key:"releaseLock",value:function(){this.reader&&this.reader.releaseLock(),this.reader=this.byobReader=this.defaultReader=null}},{key:"cancel",value:function(){var e=B(L.mark(function e(t){var n,r;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=this.reader,r=this.source,e.t0=n,!e.t0){e.next=5;break}return e.next=5,n.cancel(t).catch(function(){});case 5:r&&r.locked&&this.releaseLock();case 6:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"read",value:function(){var e=B(L.mark(function e(t){var n;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(0!==t){e.next=2;break}return e.abrupt("return",{done:null==this.reader,value:new Uint8Array(0)});case 2:if(this.supportsBYOB&&"number"===typeof t){e.next=8;break}return e.next=5,this.getDefaultReader().read();case 5:e.t0=e.sent,e.next=11;break;case 8:return e.next=10,this.readFromBYOBReader(t);case 10:e.t0=e.sent;case 11:return!(n=e.t0).done&&(n.value=Ve(n)),e.abrupt("return",n);case 14:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"getDefaultReader",value:function(){return this.byobReader&&this.releaseLock(),this.defaultReader||(this.defaultReader=this.source.getReader(),this.defaultReader.closed.catch(function(){})),this.reader=this.defaultReader}},{key:"getBYOBReader",value:function(){return this.defaultReader&&this.releaseLock(),this.byobReader||(this.byobReader=this.source.getReader({mode:"byob"}),this.byobReader.closed.catch(function(){})),this.reader=this.byobReader}},{key:"readFromBYOBReader",value:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,It(this.getBYOBReader(),new ArrayBuffer(t),0,t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"closed",get:function(){return this.reader?this.reader.closed.catch(function(){}):Promise.resolve()}}]),e}();function It(e,t,n,r){return Et.apply(this,arguments)}function Et(){return(Et=B(L.mark(function e(t,n,r,i){var a,o,u;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!(r>=i)){e.next=2;break}return e.abrupt("return",{done:!1,value:new Uint8Array(n,0,i)});case 2:return e.next=4,t.read(new Uint8Array(n,r,i-r));case 4:if(a=e.sent,o=a.done,u=a.value,!((r+=u.byteLength)<i)||o){e.next=11;break}return e.next=10,It(t,u.buffer,r,i);case 10:return e.abrupt("return",e.sent);case 11:return e.abrupt("return",{done:o,value:new Uint8Array(u.buffer,0,r)});case 12:case"end":return e.stop()}},e)}))).apply(this,arguments)}var At=function(e,t){var n,r=function(e){return n([t,e])};return[t,r,new Promise(function(i){return(n=i)&&e.once(t,r)})]};function Bt(){return(Bt=P(L.mark(function e(t){var n,r,i,a,o,u,s,c,l,f,h,d,p,y,v;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return v=function(e,n){return l=c=null,new Promise(function(){var r=B(L.mark(function r(i,a){var o,u,s,c,l,f;return L.wrap(function(r){for(;;)switch(r.prev=r.next){case 0:o=gt(e);try{for(o.s();!(u=o.n()).done;)s=F(u.value,2),c=s[0],l=s[1],t.off(c,l)}catch(n){o.e(n)}finally{o.f()}try{(f=t.destroy)&&f.call(t,n),n=void 0}catch(h){n=h||n}finally{null!=n?a(n):i()}case 3:case"end":return r.stop()}},r)}));return function(e,t){return r.apply(this,arguments)}}())},f=function(){if("peek"===o)return Me(c,u)[0];var e=F(Me(c,u),3);return l=e[0],c=e[1],s=e[2],l},n=[],r="error",i=!1,a=null,s=0,c=[],e.next=9,null;case 9:if(h=e.sent,o=h.cmd,u=h.size,!t.isTTY){e.next=16;break}return e.next=15,new Uint8Array(0);case 15:return e.abrupt("return",e.sent);case 16:e.prev=16,n[0]=At(t,"end"),n[1]=At(t,"error");case 19:return n[2]=At(t,"readable"),e.next=22,N(Promise.race(n.map(function(e){return e[2]})));case 22:if(d=e.sent,p=F(d,2),r=p[0],a=p[1],"error"!==r){e.next=28;break}return e.abrupt("break",37);case 28:if((i="end"===r)||(isFinite(u-s)?(l=Ve(t.read(u-s))).byteLength<u-s&&(l=Ve(t.read(void 0))):l=Ve(t.read(void 0)),l.byteLength>0&&(c.push(l),s+=l.byteLength)),!(i||u<=s)){e.next=36;break}case 30:return e.next=32,f();case 32:y=e.sent,o=y.cmd,u=y.size;case 35:if(u<s){e.next=30;break}case 36:if(!i){e.next=19;break}case 37:return e.prev=37,e.next=40,N(v(n,"error"===r?a:null));case 40:return e.finish(37);case 41:case"end":return e.stop()}},e,null,[[16,,37,41]])}))).apply(this,arguments)}var Dt=n(7);function Ct(e,t,n){return(Ct="undefined"!==typeof Reflect&&Reflect.get?Reflect.get:function(e,t,n){var r=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=Object(ie.a)(e)););return e}(e,t);if(r){var i=Object.getOwnPropertyDescriptor(r,t);return i.get?i.get.call(n):i.value}})(e,t,n||e)}var Lt,Ft,Mt,Nt,Ut,Pt,Rt=function e(){Object(D.a)(this,e)};Ut=Lt||(Lt={}),Nt=Ut.apache||(Ut.apache={}),Mt=Nt.arrow||(Nt.arrow={}),function(e){e[e.V1=0]="V1",e[e.V2=1]="V2",e[e.V3=2]="V3",e[e.V4=3]="V4"}((Ft=Mt.flatbuf||(Mt.flatbuf={})).MetadataVersion||(Ft.MetadataVersion={})),function(e){!function(e){!function(e){!function(e){!function(e){e[e.Sparse=0]="Sparse",e[e.Dense=1]="Dense"}(e.UnionMode||(e.UnionMode={}))}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){!function(e){e[e.HALF=0]="HALF",e[e.SINGLE=1]="SINGLE",e[e.DOUBLE=2]="DOUBLE"}(e.Precision||(e.Precision={}))}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){!function(e){e[e.DAY=0]="DAY",e[e.MILLISECOND=1]="MILLISECOND"}(e.DateUnit||(e.DateUnit={}))}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){!function(e){e[e.SECOND=0]="SECOND",e[e.MILLISECOND=1]="MILLISECOND",e[e.MICROSECOND=2]="MICROSECOND",e[e.NANOSECOND=3]="NANOSECOND"}(e.TimeUnit||(e.TimeUnit={}))}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){!function(e){e[e.YEAR_MONTH=0]="YEAR_MONTH",e[e.DAY_TIME=1]="DAY_TIME"}(e.IntervalUnit||(e.IntervalUnit={}))}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){!function(e){e[e.NONE=0]="NONE",e[e.Null=1]="Null",e[e.Int=2]="Int",e[e.FloatingPoint=3]="FloatingPoint",e[e.Binary=4]="Binary",e[e.Utf8=5]="Utf8",e[e.Bool=6]="Bool",e[e.Decimal=7]="Decimal",e[e.Date=8]="Date",e[e.Time=9]="Time",e[e.Timestamp=10]="Timestamp",e[e.Interval=11]="Interval",e[e.List=12]="List",e[e.Struct_=13]="Struct_",e[e.Union=14]="Union",e[e.FixedSizeBinary=15]="FixedSizeBinary",e[e.FixedSizeList=16]="FixedSizeList",e[e.Map=17]="Map",e[e.Duration=18]="Duration",e[e.LargeBinary=19]="LargeBinary",e[e.LargeUtf8=20]="LargeUtf8",e[e.LargeList=21]="LargeList"}(e.Type||(e.Type={}))}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){!function(e){e[e.Little=0]="Little",e[e.Big=1]="Big"}(e.Endianness||(e.Endianness={}))}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){Object(D.a)(this,e),this.bb=null,this.bb_pos=0}return Object(C.a)(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}}],[{key:"getRootAsNull",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startNull",value:function(e){e.startObject(0)}},{key:"endNull",value:function(e){return e.endObject()}},{key:"createNull",value:function(t){return e.startNull(t),e.endNull(t)}}]),e}();e.Null=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){Object(D.a)(this,e),this.bb=null,this.bb_pos=0}return Object(C.a)(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}}],[{key:"getRootAsStruct_",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startStruct_",value:function(e){e.startObject(0)}},{key:"endStruct_",value:function(e){return e.endObject()}},{key:"createStruct_",value:function(t){return e.startStruct_(t),e.endStruct_(t)}}]),e}();e.Struct_=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){Object(D.a)(this,e),this.bb=null,this.bb_pos=0}return Object(C.a)(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}}],[{key:"getRootAsList",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startList",value:function(e){e.startObject(0)}},{key:"endList",value:function(e){return e.endObject()}},{key:"createList",value:function(t){return e.startList(t),e.endList(t)}}]),e}();e.List=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){Object(D.a)(this,e),this.bb=null,this.bb_pos=0}return Object(C.a)(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}}],[{key:"getRootAsLargeList",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startLargeList",value:function(e){e.startObject(0)}},{key:"endLargeList",value:function(e){return e.endObject()}},{key:"createLargeList",value:function(t){return e.startLargeList(t),e.endLargeList(t)}}]),e}();e.LargeList=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){Object(D.a)(this,e),this.bb=null,this.bb_pos=0}return Object(C.a)(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"listSize",value:function(){var e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt32(this.bb_pos+e):0}}],[{key:"getRootAsFixedSizeList",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startFixedSizeList",value:function(e){e.startObject(1)}},{key:"addListSize",value:function(e,t){e.addFieldInt32(0,t,0)}},{key:"endFixedSizeList",value:function(e){return e.endObject()}},{key:"createFixedSizeList",value:function(t,n){return e.startFixedSizeList(t),e.addListSize(t,n),e.endFixedSizeList(t)}}]),e}();e.FixedSizeList=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){Object(D.a)(this,e),this.bb=null,this.bb_pos=0}return Object(C.a)(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"keysSorted",value:function(){var e=this.bb.__offset(this.bb_pos,4);return!!e&&!!this.bb.readInt8(this.bb_pos+e)}}],[{key:"getRootAsMap",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startMap",value:function(e){e.startObject(1)}},{key:"addKeysSorted",value:function(e,t){e.addFieldInt8(0,+t,0)}},{key:"endMap",value:function(e){return e.endObject()}},{key:"createMap",value:function(t,n){return e.startMap(t),e.addKeysSorted(t,n),e.endMap(t)}}]),e}();e.Map=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){Object(D.a)(this,t),this.bb=null,this.bb_pos=0}return Object(C.a)(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"mode",value:function(){var t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):e.apache.arrow.flatbuf.UnionMode.Sparse}},{key:"typeIds",value:function(e){var t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb.__vector(this.bb_pos+t)+4*e):0}},{key:"typeIdsLength",value:function(){var e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__vector_len(this.bb_pos+e):0}},{key:"typeIdsArray",value:function(){var e=this.bb.__offset(this.bb_pos,6);return e?new Int32Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+e),this.bb.__vector_len(this.bb_pos+e)):null}}],[{key:"getRootAsUnion",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startUnion",value:function(e){e.startObject(2)}},{key:"addMode",value:function(t,n){t.addFieldInt16(0,n,e.apache.arrow.flatbuf.UnionMode.Sparse)}},{key:"addTypeIds",value:function(e,t){e.addFieldOffset(1,t,0)}},{key:"createTypeIdsVector",value:function(e,t){e.startVector(4,t.length,4);for(var n=t.length-1;n>=0;n--)e.addInt32(t[n]);return e.endVector()}},{key:"startTypeIdsVector",value:function(e,t){e.startVector(4,t,4)}},{key:"endUnion",value:function(e){return e.endObject()}},{key:"createUnion",value:function(e,n,r){return t.startUnion(e),t.addMode(e,n),t.addTypeIds(e,r),t.endUnion(e)}}]),t}();t.Union=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){Object(D.a)(this,e),this.bb=null,this.bb_pos=0}return Object(C.a)(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"bitWidth",value:function(){var e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt32(this.bb_pos+e):0}},{key:"isSigned",value:function(){var e=this.bb.__offset(this.bb_pos,6);return!!e&&!!this.bb.readInt8(this.bb_pos+e)}}],[{key:"getRootAsInt",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startInt",value:function(e){e.startObject(2)}},{key:"addBitWidth",value:function(e,t){e.addFieldInt32(0,t,0)}},{key:"addIsSigned",value:function(e,t){e.addFieldInt8(1,+t,0)}},{key:"endInt",value:function(e){return e.endObject()}},{key:"createInt",value:function(t,n,r){return e.startInt(t),e.addBitWidth(t,n),e.addIsSigned(t,r),e.endInt(t)}}]),e}();e.Int=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){Object(D.a)(this,t),this.bb=null,this.bb_pos=0}return Object(C.a)(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"precision",value:function(){var t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):e.apache.arrow.flatbuf.Precision.HALF}}],[{key:"getRootAsFloatingPoint",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startFloatingPoint",value:function(e){e.startObject(1)}},{key:"addPrecision",value:function(t,n){t.addFieldInt16(0,n,e.apache.arrow.flatbuf.Precision.HALF)}},{key:"endFloatingPoint",value:function(e){return e.endObject()}},{key:"createFloatingPoint",value:function(e,n){return t.startFloatingPoint(e),t.addPrecision(e,n),t.endFloatingPoint(e)}}]),t}();t.FloatingPoint=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){Object(D.a)(this,e),this.bb=null,this.bb_pos=0}return Object(C.a)(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}}],[{key:"getRootAsUtf8",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startUtf8",value:function(e){e.startObject(0)}},{key:"endUtf8",value:function(e){return e.endObject()}},{key:"createUtf8",value:function(t){return e.startUtf8(t),e.endUtf8(t)}}]),e}();e.Utf8=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){Object(D.a)(this,e),this.bb=null,this.bb_pos=0}return Object(C.a)(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}}],[{key:"getRootAsBinary",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startBinary",value:function(e){e.startObject(0)}},{key:"endBinary",value:function(e){return e.endObject()}},{key:"createBinary",value:function(t){return e.startBinary(t),e.endBinary(t)}}]),e}();e.Binary=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){Object(D.a)(this,e),this.bb=null,this.bb_pos=0}return Object(C.a)(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}}],[{key:"getRootAsLargeUtf8",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startLargeUtf8",value:function(e){e.startObject(0)}},{key:"endLargeUtf8",value:function(e){return e.endObject()}},{key:"createLargeUtf8",value:function(t){return e.startLargeUtf8(t),e.endLargeUtf8(t)}}]),e}();e.LargeUtf8=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){Object(D.a)(this,e),this.bb=null,this.bb_pos=0}return Object(C.a)(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}}],[{key:"getRootAsLargeBinary",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startLargeBinary",value:function(e){e.startObject(0)}},{key:"endLargeBinary",value:function(e){return e.endObject()}},{key:"createLargeBinary",value:function(t){return e.startLargeBinary(t),e.endLargeBinary(t)}}]),e}();e.LargeBinary=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){Object(D.a)(this,e),this.bb=null,this.bb_pos=0}return Object(C.a)(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"byteWidth",value:function(){var e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt32(this.bb_pos+e):0}}],[{key:"getRootAsFixedSizeBinary",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startFixedSizeBinary",value:function(e){e.startObject(1)}},{key:"addByteWidth",value:function(e,t){e.addFieldInt32(0,t,0)}},{key:"endFixedSizeBinary",value:function(e){return e.endObject()}},{key:"createFixedSizeBinary",value:function(t,n){return e.startFixedSizeBinary(t),e.addByteWidth(t,n),e.endFixedSizeBinary(t)}}]),e}();e.FixedSizeBinary=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){Object(D.a)(this,e),this.bb=null,this.bb_pos=0}return Object(C.a)(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}}],[{key:"getRootAsBool",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startBool",value:function(e){e.startObject(0)}},{key:"endBool",value:function(e){return e.endObject()}},{key:"createBool",value:function(t){return e.startBool(t),e.endBool(t)}}]),e}();e.Bool=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){Object(D.a)(this,e),this.bb=null,this.bb_pos=0}return Object(C.a)(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"precision",value:function(){var e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt32(this.bb_pos+e):0}},{key:"scale",value:function(){var e=this.bb.__offset(this.bb_pos,6);return e?this.bb.readInt32(this.bb_pos+e):0}}],[{key:"getRootAsDecimal",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startDecimal",value:function(e){e.startObject(2)}},{key:"addPrecision",value:function(e,t){e.addFieldInt32(0,t,0)}},{key:"addScale",value:function(e,t){e.addFieldInt32(1,t,0)}},{key:"endDecimal",value:function(e){return e.endObject()}},{key:"createDecimal",value:function(t,n,r){return e.startDecimal(t),e.addPrecision(t,n),e.addScale(t,r),e.endDecimal(t)}}]),e}();e.Decimal=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){Object(D.a)(this,t),this.bb=null,this.bb_pos=0}return Object(C.a)(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"unit",value:function(){var t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):e.apache.arrow.flatbuf.DateUnit.MILLISECOND}}],[{key:"getRootAsDate",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startDate",value:function(e){e.startObject(1)}},{key:"addUnit",value:function(t,n){t.addFieldInt16(0,n,e.apache.arrow.flatbuf.DateUnit.MILLISECOND)}},{key:"endDate",value:function(e){return e.endObject()}},{key:"createDate",value:function(e,n){return t.startDate(e),t.addUnit(e,n),t.endDate(e)}}]),t}();t.Date=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){Object(D.a)(this,t),this.bb=null,this.bb_pos=0}return Object(C.a)(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"unit",value:function(){var t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):e.apache.arrow.flatbuf.TimeUnit.MILLISECOND}},{key:"bitWidth",value:function(){var e=this.bb.__offset(this.bb_pos,6);return e?this.bb.readInt32(this.bb_pos+e):32}}],[{key:"getRootAsTime",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startTime",value:function(e){e.startObject(2)}},{key:"addUnit",value:function(t,n){t.addFieldInt16(0,n,e.apache.arrow.flatbuf.TimeUnit.MILLISECOND)}},{key:"addBitWidth",value:function(e,t){e.addFieldInt32(1,t,32)}},{key:"endTime",value:function(e){return e.endObject()}},{key:"createTime",value:function(e,n,r){return t.startTime(e),t.addUnit(e,n),t.addBitWidth(e,r),t.endTime(e)}}]),t}();t.Time=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){Object(D.a)(this,t),this.bb=null,this.bb_pos=0}return Object(C.a)(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"unit",value:function(){var t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):e.apache.arrow.flatbuf.TimeUnit.SECOND}},{key:"timezone",value:function(e){var t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__string(this.bb_pos+t,e):null}}],[{key:"getRootAsTimestamp",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startTimestamp",value:function(e){e.startObject(2)}},{key:"addUnit",value:function(t,n){t.addFieldInt16(0,n,e.apache.arrow.flatbuf.TimeUnit.SECOND)}},{key:"addTimezone",value:function(e,t){e.addFieldOffset(1,t,0)}},{key:"endTimestamp",value:function(e){return e.endObject()}},{key:"createTimestamp",value:function(e,n,r){return t.startTimestamp(e),t.addUnit(e,n),t.addTimezone(e,r),t.endTimestamp(e)}}]),t}();t.Timestamp=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){Object(D.a)(this,t),this.bb=null,this.bb_pos=0}return Object(C.a)(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"unit",value:function(){var t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):e.apache.arrow.flatbuf.IntervalUnit.YEAR_MONTH}}],[{key:"getRootAsInterval",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startInterval",value:function(e){e.startObject(1)}},{key:"addUnit",value:function(t,n){t.addFieldInt16(0,n,e.apache.arrow.flatbuf.IntervalUnit.YEAR_MONTH)}},{key:"endInterval",value:function(e){return e.endObject()}},{key:"createInterval",value:function(e,n){return t.startInterval(e),t.addUnit(e,n),t.endInterval(e)}}]),t}();t.Interval=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){Object(D.a)(this,t),this.bb=null,this.bb_pos=0}return Object(C.a)(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"unit",value:function(){var t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):e.apache.arrow.flatbuf.TimeUnit.MILLISECOND}}],[{key:"getRootAsDuration",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startDuration",value:function(e){e.startObject(1)}},{key:"addUnit",value:function(t,n){t.addFieldInt16(0,n,e.apache.arrow.flatbuf.TimeUnit.MILLISECOND)}},{key:"endDuration",value:function(e){return e.endObject()}},{key:"createDuration",value:function(e,n){return t.startDuration(e),t.addUnit(e,n),t.endDuration(e)}}]),t}();t.Duration=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){Object(D.a)(this,e),this.bb=null,this.bb_pos=0}return Object(C.a)(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"key",value:function(e){var t=this.bb.__offset(this.bb_pos,4);return t?this.bb.__string(this.bb_pos+t,e):null}},{key:"value",value:function(e){var t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__string(this.bb_pos+t,e):null}}],[{key:"getRootAsKeyValue",value:function(t,n){return(n||new e).__init(t.readInt32(t.position())+t.position(),t)}},{key:"startKeyValue",value:function(e){e.startObject(2)}},{key:"addKey",value:function(e,t){e.addFieldOffset(0,t,0)}},{key:"addValue",value:function(e,t){e.addFieldOffset(1,t,0)}},{key:"endKeyValue",value:function(e){return e.endObject()}},{key:"createKeyValue",value:function(t,n,r){return e.startKeyValue(t),e.addKey(t,n),e.addValue(t,r),e.endKeyValue(t)}}]),e}();e.KeyValue=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){Object(D.a)(this,t),this.bb=null,this.bb_pos=0}return Object(C.a)(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"id",value:function(){var e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt64(this.bb_pos+e):this.bb.createLong(0,0)}},{key:"indexType",value:function(t){var n=this.bb.__offset(this.bb_pos,6);return n?(t||new e.apache.arrow.flatbuf.Int).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}},{key:"isOrdered",value:function(){var e=this.bb.__offset(this.bb_pos,8);return!!e&&!!this.bb.readInt8(this.bb_pos+e)}}],[{key:"getRootAsDictionaryEncoding",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startDictionaryEncoding",value:function(e){e.startObject(3)}},{key:"addId",value:function(e,t){e.addFieldInt64(0,t,e.createLong(0,0))}},{key:"addIndexType",value:function(e,t){e.addFieldOffset(1,t,0)}},{key:"addIsOrdered",value:function(e,t){e.addFieldInt8(2,+t,0)}},{key:"endDictionaryEncoding",value:function(e){return e.endObject()}},{key:"createDictionaryEncoding",value:function(e,n,r,i){return t.startDictionaryEncoding(e),t.addId(e,n),t.addIndexType(e,r),t.addIsOrdered(e,i),t.endDictionaryEncoding(e)}}]),t}();t.DictionaryEncoding=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){Object(D.a)(this,t),this.bb=null,this.bb_pos=0}return Object(C.a)(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"name",value:function(e){var t=this.bb.__offset(this.bb_pos,4);return t?this.bb.__string(this.bb_pos+t,e):null}},{key:"nullable",value:function(){var e=this.bb.__offset(this.bb_pos,6);return!!e&&!!this.bb.readInt8(this.bb_pos+e)}},{key:"typeType",value:function(){var t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readUint8(this.bb_pos+t):e.apache.arrow.flatbuf.Type.NONE}},{key:"type",value:function(e){var t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__union(e,this.bb_pos+t):null}},{key:"dictionary",value:function(t){var n=this.bb.__offset(this.bb_pos,12);return n?(t||new e.apache.arrow.flatbuf.DictionaryEncoding).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}},{key:"children",value:function(t,n){var r=this.bb.__offset(this.bb_pos,14);return r?(n||new e.apache.arrow.flatbuf.Field).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*t),this.bb):null}},{key:"childrenLength",value:function(){var e=this.bb.__offset(this.bb_pos,14);return e?this.bb.__vector_len(this.bb_pos+e):0}},{key:"customMetadata",value:function(t,n){var r=this.bb.__offset(this.bb_pos,16);return r?(n||new e.apache.arrow.flatbuf.KeyValue).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*t),this.bb):null}},{key:"customMetadataLength",value:function(){var e=this.bb.__offset(this.bb_pos,16);return e?this.bb.__vector_len(this.bb_pos+e):0}}],[{key:"getRootAsField",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startField",value:function(e){e.startObject(7)}},{key:"addName",value:function(e,t){e.addFieldOffset(0,t,0)}},{key:"addNullable",value:function(e,t){e.addFieldInt8(1,+t,0)}},{key:"addTypeType",value:function(t,n){t.addFieldInt8(2,n,e.apache.arrow.flatbuf.Type.NONE)}},{key:"addType",value:function(e,t){e.addFieldOffset(3,t,0)}},{key:"addDictionary",value:function(e,t){e.addFieldOffset(4,t,0)}},{key:"addChildren",value:function(e,t){e.addFieldOffset(5,t,0)}},{key:"createChildrenVector",value:function(e,t){e.startVector(4,t.length,4);for(var n=t.length-1;n>=0;n--)e.addOffset(t[n]);return e.endVector()}},{key:"startChildrenVector",value:function(e,t){e.startVector(4,t,4)}},{key:"addCustomMetadata",value:function(e,t){e.addFieldOffset(6,t,0)}},{key:"createCustomMetadataVector",value:function(e,t){e.startVector(4,t.length,4);for(var n=t.length-1;n>=0;n--)e.addOffset(t[n]);return e.endVector()}},{key:"startCustomMetadataVector",value:function(e,t){e.startVector(4,t,4)}},{key:"endField",value:function(e){return e.endObject()}},{key:"createField",value:function(e,n,r,i,a,o,u,s){return t.startField(e),t.addName(e,n),t.addNullable(e,r),t.addTypeType(e,i),t.addType(e,a),t.addDictionary(e,o),t.addChildren(e,u),t.addCustomMetadata(e,s),t.endField(e)}}]),t}();t.Field=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){Object(D.a)(this,e),this.bb=null,this.bb_pos=0}return Object(C.a)(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"offset",value:function(){return this.bb.readInt64(this.bb_pos)}},{key:"length",value:function(){return this.bb.readInt64(this.bb_pos+8)}}],[{key:"createBuffer",value:function(e,t,n){return e.prep(8,16),e.writeInt64(n),e.writeInt64(t),e.offset()}}]),e}();e.Buffer=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){Object(D.a)(this,t),this.bb=null,this.bb_pos=0}return Object(C.a)(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"endianness",value:function(){var t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):e.apache.arrow.flatbuf.Endianness.Little}},{key:"fields",value:function(t,n){var r=this.bb.__offset(this.bb_pos,6);return r?(n||new e.apache.arrow.flatbuf.Field).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*t),this.bb):null}},{key:"fieldsLength",value:function(){var e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__vector_len(this.bb_pos+e):0}},{key:"customMetadata",value:function(t,n){var r=this.bb.__offset(this.bb_pos,8);return r?(n||new e.apache.arrow.flatbuf.KeyValue).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+r)+4*t),this.bb):null}},{key:"customMetadataLength",value:function(){var e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__vector_len(this.bb_pos+e):0}}],[{key:"getRootAsSchema",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startSchema",value:function(e){e.startObject(3)}},{key:"addEndianness",value:function(t,n){t.addFieldInt16(0,n,e.apache.arrow.flatbuf.Endianness.Little)}},{key:"addFields",value:function(e,t){e.addFieldOffset(1,t,0)}},{key:"createFieldsVector",value:function(e,t){e.startVector(4,t.length,4);for(var n=t.length-1;n>=0;n--)e.addOffset(t[n]);return e.endVector()}},{key:"startFieldsVector",value:function(e,t){e.startVector(4,t,4)}},{key:"addCustomMetadata",value:function(e,t){e.addFieldOffset(2,t,0)}},{key:"createCustomMetadataVector",value:function(e,t){e.startVector(4,t.length,4);for(var n=t.length-1;n>=0;n--)e.addOffset(t[n]);return e.endVector()}},{key:"startCustomMetadataVector",value:function(e,t){e.startVector(4,t,4)}},{key:"endSchema",value:function(e){return e.endObject()}},{key:"finishSchemaBuffer",value:function(e,t){e.finish(t)}},{key:"createSchema",value:function(e,n,r,i){return t.startSchema(e),t.addEndianness(e,n),t.addFields(e,r),t.addCustomMetadata(e,i),t.endSchema(e)}}]),t}();t.Schema=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Lt||(Lt={})),function(e){!function(e){!function(e){!function(e){e.Schema=Lt.apache.arrow.flatbuf.Schema}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Pt||(Pt={})),function(e){!function(e){!function(e){!function(e){!function(e){e[e.NONE=0]="NONE",e[e.Schema=1]="Schema",e[e.DictionaryBatch=2]="DictionaryBatch",e[e.RecordBatch=3]="RecordBatch",e[e.Tensor=4]="Tensor",e[e.SparseTensor=5]="SparseTensor"}(e.MessageHeader||(e.MessageHeader={}))}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Pt||(Pt={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){Object(D.a)(this,e),this.bb=null,this.bb_pos=0}return Object(C.a)(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"length",value:function(){return this.bb.readInt64(this.bb_pos)}},{key:"nullCount",value:function(){return this.bb.readInt64(this.bb_pos+8)}}],[{key:"createFieldNode",value:function(e,t,n){return e.prep(8,16),e.writeInt64(n),e.writeInt64(t),e.offset()}}]),e}();e.FieldNode=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(Pt||(Pt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){Object(D.a)(this,t),this.bb=null,this.bb_pos=0}return Object(C.a)(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"length",value:function(){var e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt64(this.bb_pos+e):this.bb.createLong(0,0)}},{key:"nodes",value:function(t,n){var r=this.bb.__offset(this.bb_pos,6);return r?(n||new e.apache.arrow.flatbuf.FieldNode).__init(this.bb.__vector(this.bb_pos+r)+16*t,this.bb):null}},{key:"nodesLength",value:function(){var e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__vector_len(this.bb_pos+e):0}},{key:"buffers",value:function(e,t){var n=this.bb.__offset(this.bb_pos,8);return n?(t||new Lt.apache.arrow.flatbuf.Buffer).__init(this.bb.__vector(this.bb_pos+n)+16*e,this.bb):null}},{key:"buffersLength",value:function(){var e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__vector_len(this.bb_pos+e):0}}],[{key:"getRootAsRecordBatch",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startRecordBatch",value:function(e){e.startObject(3)}},{key:"addLength",value:function(e,t){e.addFieldInt64(0,t,e.createLong(0,0))}},{key:"addNodes",value:function(e,t){e.addFieldOffset(1,t,0)}},{key:"startNodesVector",value:function(e,t){e.startVector(16,t,8)}},{key:"addBuffers",value:function(e,t){e.addFieldOffset(2,t,0)}},{key:"startBuffersVector",value:function(e,t){e.startVector(16,t,8)}},{key:"endRecordBatch",value:function(e){return e.endObject()}},{key:"createRecordBatch",value:function(e,n,r,i){return t.startRecordBatch(e),t.addLength(e,n),t.addNodes(e,r),t.addBuffers(e,i),t.endRecordBatch(e)}}]),t}();t.RecordBatch=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Pt||(Pt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){Object(D.a)(this,t),this.bb=null,this.bb_pos=0}return Object(C.a)(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"id",value:function(){var e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt64(this.bb_pos+e):this.bb.createLong(0,0)}},{key:"data",value:function(t){var n=this.bb.__offset(this.bb_pos,6);return n?(t||new e.apache.arrow.flatbuf.RecordBatch).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}},{key:"isDelta",value:function(){var e=this.bb.__offset(this.bb_pos,8);return!!e&&!!this.bb.readInt8(this.bb_pos+e)}}],[{key:"getRootAsDictionaryBatch",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startDictionaryBatch",value:function(e){e.startObject(3)}},{key:"addId",value:function(e,t){e.addFieldInt64(0,t,e.createLong(0,0))}},{key:"addData",value:function(e,t){e.addFieldOffset(1,t,0)}},{key:"addIsDelta",value:function(e,t){e.addFieldInt8(2,+t,0)}},{key:"endDictionaryBatch",value:function(e){return e.endObject()}},{key:"createDictionaryBatch",value:function(e,n,r,i){return t.startDictionaryBatch(e),t.addId(e,n),t.addData(e,r),t.addIsDelta(e,i),t.endDictionaryBatch(e)}}]),t}();t.DictionaryBatch=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Pt||(Pt={})),function(e){!function(t){!function(t){!function(t){var n=function(){function t(){Object(D.a)(this,t),this.bb=null,this.bb_pos=0}return Object(C.a)(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"version",value:function(){var e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt16(this.bb_pos+e):Lt.apache.arrow.flatbuf.MetadataVersion.V1}},{key:"headerType",value:function(){var t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readUint8(this.bb_pos+t):e.apache.arrow.flatbuf.MessageHeader.NONE}},{key:"header",value:function(e){var t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__union(e,this.bb_pos+t):null}},{key:"bodyLength",value:function(){var e=this.bb.__offset(this.bb_pos,10);return e?this.bb.readInt64(this.bb_pos+e):this.bb.createLong(0,0)}},{key:"customMetadata",value:function(e,t){var n=this.bb.__offset(this.bb_pos,12);return n?(t||new Lt.apache.arrow.flatbuf.KeyValue).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*e),this.bb):null}},{key:"customMetadataLength",value:function(){var e=this.bb.__offset(this.bb_pos,12);return e?this.bb.__vector_len(this.bb_pos+e):0}}],[{key:"getRootAsMessage",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startMessage",value:function(e){e.startObject(5)}},{key:"addVersion",value:function(e,t){e.addFieldInt16(0,t,Lt.apache.arrow.flatbuf.MetadataVersion.V1)}},{key:"addHeaderType",value:function(t,n){t.addFieldInt8(1,n,e.apache.arrow.flatbuf.MessageHeader.NONE)}},{key:"addHeader",value:function(e,t){e.addFieldOffset(2,t,0)}},{key:"addBodyLength",value:function(e,t){e.addFieldInt64(3,t,e.createLong(0,0))}},{key:"addCustomMetadata",value:function(e,t){e.addFieldOffset(4,t,0)}},{key:"createCustomMetadataVector",value:function(e,t){e.startVector(4,t.length,4);for(var n=t.length-1;n>=0;n--)e.addOffset(t[n]);return e.endVector()}},{key:"startCustomMetadataVector",value:function(e,t){e.startVector(4,t,4)}},{key:"endMessage",value:function(e){return e.endObject()}},{key:"finishMessageBuffer",value:function(e,t){e.finish(t)}},{key:"createMessage",value:function(e,n,r,i,a,o){return t.startMessage(e),t.addVersion(e,n),t.addHeaderType(e,r),t.addHeader(e,i),t.addBodyLength(e,a),t.addCustomMetadata(e,o),t.endMessage(e)}}]),t}();t.Message=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(Pt||(Pt={}));Lt.apache.arrow.flatbuf.Type;var zt,Vt,Wt=Lt.apache.arrow.flatbuf.DateUnit,$t=Lt.apache.arrow.flatbuf.TimeUnit,Ht=Lt.apache.arrow.flatbuf.Precision,Yt=Lt.apache.arrow.flatbuf.UnionMode,Kt=Lt.apache.arrow.flatbuf.IntervalUnit,Qt=Pt.apache.arrow.flatbuf.MessageHeader,qt=Lt.apache.arrow.flatbuf.MetadataVersion;!function(e){e[e.NONE=0]="NONE",e[e.Null=1]="Null",e[e.Int=2]="Int",e[e.Float=3]="Float",e[e.Binary=4]="Binary",e[e.Utf8=5]="Utf8",e[e.Bool=6]="Bool",e[e.Decimal=7]="Decimal",e[e.Date=8]="Date",e[e.Time=9]="Time",e[e.Timestamp=10]="Timestamp",e[e.Interval=11]="Interval",e[e.List=12]="List",e[e.Struct=13]="Struct",e[e.Union=14]="Union",e[e.FixedSizeBinary=15]="FixedSizeBinary",e[e.FixedSizeList=16]="FixedSizeList",e[e.Map=17]="Map",e[e.Dictionary=-1]="Dictionary",e[e.Int8=-2]="Int8",e[e.Int16=-3]="Int16",e[e.Int32=-4]="Int32",e[e.Int64=-5]="Int64",e[e.Uint8=-6]="Uint8",e[e.Uint16=-7]="Uint16",e[e.Uint32=-8]="Uint32",e[e.Uint64=-9]="Uint64",e[e.Float16=-10]="Float16",e[e.Float32=-11]="Float32",e[e.Float64=-12]="Float64",e[e.DateDay=-13]="DateDay",e[e.DateMillisecond=-14]="DateMillisecond",e[e.TimestampSecond=-15]="TimestampSecond",e[e.TimestampMillisecond=-16]="TimestampMillisecond",e[e.TimestampMicrosecond=-17]="TimestampMicrosecond",e[e.TimestampNanosecond=-18]="TimestampNanosecond",e[e.TimeSecond=-19]="TimeSecond",e[e.TimeMillisecond=-20]="TimeMillisecond",e[e.TimeMicrosecond=-21]="TimeMicrosecond",e[e.TimeNanosecond=-22]="TimeNanosecond",e[e.DenseUnion=-23]="DenseUnion",e[e.SparseUnion=-24]="SparseUnion",e[e.IntervalDayTime=-25]="IntervalDayTime",e[e.IntervalYearMonth=-26]="IntervalYearMonth"}(zt||(zt={})),function(e){e[e.OFFSET=0]="OFFSET",e[e.DATA=1]="DATA",e[e.VALIDITY=2]="VALIDITY",e[e.TYPE=3]="TYPE"}(Vt||(Vt={}));var Gt=L.mark(an);function Jt(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"===typeof e)return Xt(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Xt(e,t)}(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){u=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(u)throw a}}}}function Xt(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Zt(e,t,n,r){return 0!==(n&1<<r)}function en(e,t,n,r){return(n&1<<r)>>r}function tn(e,t,n){return n?!!(e[t>>3]|=1<<t%8)||!0:!(e[t>>3]&=~(1<<t%8))&&!1}function nn(e,t,n){var r=n.byteLength+7&-8;if(e>0||n.byteLength<r){var i=new Uint8Array(r);return i.set(e%8===0?n.subarray(e>>3):rn(an(n,e,t,null,Zt)).subarray(0,r)),i}return n}function rn(e){var t,n=[],r=0,i=0,a=0,o=Jt(e);try{for(o.s();!(t=o.n()).done;){t.value&&(a|=1<<i),8===++i&&(n[r++]=a,a=i=0)}}catch(s){o.e(s)}finally{o.f()}(0===r||i>0)&&(n[r++]=a);var u=new Uint8Array(n.length+7&-8);return u.set(n),u}function an(e,t,n,r,i){var a,o,u,s,c;return L.wrap(function(l){for(;;)switch(l.prev=l.next){case 0:a=t%8,o=t>>3,u=0,s=n;case 3:if(!(s>0)){l.next=11;break}c=e[o++];case 5:return l.next=7,i(r,u++,c,a);case 7:if(--s>0&&++a<8){l.next=5;break}case 8:a=0,l.next=3;break;case 11:case"end":return l.stop()}},Gt)}function on(e,t,n){if(n-t<=0)return 0;if(n-t<8){var r,i=0,a=Jt(an(e,t,n-t,e,en));try{for(a.s();!(r=a.n()).done;){i+=r.value}}catch(s){a.e(s)}finally{a.f()}return i}var o=n>>3<<3,u=t+(t%8===0?0:8-t%8);return on(e,t,u)+on(e,o,n)+un(e,u>>3,o-u>>3)}function un(e,t,n){for(var r=0,i=0|t,a=new DataView(e.buffer,e.byteOffset,e.byteLength),o=void 0===n?e.byteLength:i+n;o-i>=4;)r+=sn(a.getUint32(i)),i+=4;for(;o-i>=2;)r+=sn(a.getUint16(i)),i+=2;for(;o-i>=1;)r+=sn(a.getUint8(i)),i+=1;return r}function sn(e){var t=0|e;return 16843009*((t=(858993459&(t-=t>>>1&1431655765))+(t>>>2&858993459))+(t>>>4)&252645135)>>>24}function cn(e){return function(e){if(Array.isArray(e)){for(var t=0,n=new Array(e.length);t<e.length;t++)n[t]=e[t];return n}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}var ln=function(){function e(){Object(D.a)(this,e)}return Object(C.a)(e,[{key:"visitMany",value:function(e){for(var t=this,n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];return e.map(function(e,n){return t.visit.apply(t,[e].concat(cn(r.map(function(e){return e[n]}))))})}},{key:"visit",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.getVisitFn(t[0],!1).apply(this,t)}},{key:"getVisitFn",value:function(e){return function(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=null,i=zt.NONE;t instanceof ar?i=fn(t.type):t instanceof Rt?i=fn(t.type):t instanceof jn?i=fn(t):"number"!==typeof(i=t)&&(i=zt[t]);switch(i){case zt.Null:r=e.visitNull;break;case zt.Bool:r=e.visitBool;break;case zt.Int:r=e.visitInt;break;case zt.Int8:r=e.visitInt8||e.visitInt;break;case zt.Int16:r=e.visitInt16||e.visitInt;break;case zt.Int32:r=e.visitInt32||e.visitInt;break;case zt.Int64:r=e.visitInt64||e.visitInt;break;case zt.Uint8:r=e.visitUint8||e.visitInt;break;case zt.Uint16:r=e.visitUint16||e.visitInt;break;case zt.Uint32:r=e.visitUint32||e.visitInt;break;case zt.Uint64:r=e.visitUint64||e.visitInt;break;case zt.Float:r=e.visitFloat;break;case zt.Float16:r=e.visitFloat16||e.visitFloat;break;case zt.Float32:r=e.visitFloat32||e.visitFloat;break;case zt.Float64:r=e.visitFloat64||e.visitFloat;break;case zt.Utf8:r=e.visitUtf8;break;case zt.Binary:r=e.visitBinary;break;case zt.FixedSizeBinary:r=e.visitFixedSizeBinary;break;case zt.Date:r=e.visitDate;break;case zt.DateDay:r=e.visitDateDay||e.visitDate;break;case zt.DateMillisecond:r=e.visitDateMillisecond||e.visitDate;break;case zt.Timestamp:r=e.visitTimestamp;break;case zt.TimestampSecond:r=e.visitTimestampSecond||e.visitTimestamp;break;case zt.TimestampMillisecond:r=e.visitTimestampMillisecond||e.visitTimestamp;break;case zt.TimestampMicrosecond:r=e.visitTimestampMicrosecond||e.visitTimestamp;break;case zt.TimestampNanosecond:r=e.visitTimestampNanosecond||e.visitTimestamp;break;case zt.Time:r=e.visitTime;break;case zt.TimeSecond:r=e.visitTimeSecond||e.visitTime;break;case zt.TimeMillisecond:r=e.visitTimeMillisecond||e.visitTime;break;case zt.TimeMicrosecond:r=e.visitTimeMicrosecond||e.visitTime;break;case zt.TimeNanosecond:r=e.visitTimeNanosecond||e.visitTime;break;case zt.Decimal:r=e.visitDecimal;break;case zt.List:r=e.visitList;break;case zt.Struct:r=e.visitStruct;break;case zt.Union:r=e.visitUnion;break;case zt.DenseUnion:r=e.visitDenseUnion||e.visitUnion;break;case zt.SparseUnion:r=e.visitSparseUnion||e.visitUnion;break;case zt.Dictionary:r=e.visitDictionary;break;case zt.Interval:r=e.visitInterval;break;case zt.IntervalDayTime:r=e.visitIntervalDayTime||e.visitInterval;break;case zt.IntervalYearMonth:r=e.visitIntervalYearMonth||e.visitInterval;break;case zt.FixedSizeList:r=e.visitFixedSizeList;break;case zt.Map:r=e.visitMap}if("function"===typeof r)return r;if(!n)return function(){return null};throw new Error("Unrecognized type '".concat(zt[i],"'"))}(this,e,!(arguments.length>1&&void 0!==arguments[1])||arguments[1])}},{key:"visitNull",value:function(e){return null}},{key:"visitBool",value:function(e){return null}},{key:"visitInt",value:function(e){return null}},{key:"visitFloat",value:function(e){return null}},{key:"visitUtf8",value:function(e){return null}},{key:"visitBinary",value:function(e){return null}},{key:"visitFixedSizeBinary",value:function(e){return null}},{key:"visitDate",value:function(e){return null}},{key:"visitTimestamp",value:function(e){return null}},{key:"visitTime",value:function(e){return null}},{key:"visitDecimal",value:function(e){return null}},{key:"visitList",value:function(e){return null}},{key:"visitStruct",value:function(e){return null}},{key:"visitUnion",value:function(e){return null}},{key:"visitDictionary",value:function(e){return null}},{key:"visitInterval",value:function(e){return null}},{key:"visitFixedSizeList",value:function(e){return null}},{key:"visitMap",value:function(e){return null}}]),e}();function fn(e){switch(e.typeId){case zt.Null:return zt.Null;case zt.Int:var t=e.bitWidth,n=e.isSigned;switch(t){case 8:return n?zt.Int8:zt.Uint8;case 16:return n?zt.Int16:zt.Uint16;case 32:return n?zt.Int32:zt.Uint32;case 64:return n?zt.Int64:zt.Uint64}return zt.Int;case zt.Float:switch(e.precision){case Ht.HALF:return zt.Float16;case Ht.SINGLE:return zt.Float32;case Ht.DOUBLE:return zt.Float64}return zt.Float;case zt.Binary:return zt.Binary;case zt.Utf8:return zt.Utf8;case zt.Bool:return zt.Bool;case zt.Decimal:return zt.Decimal;case zt.Time:switch(e.unit){case $t.SECOND:return zt.TimeSecond;case $t.MILLISECOND:return zt.TimeMillisecond;case $t.MICROSECOND:return zt.TimeMicrosecond;case $t.NANOSECOND:return zt.TimeNanosecond}return zt.Time;case zt.Timestamp:switch(e.unit){case $t.SECOND:return zt.TimestampSecond;case $t.MILLISECOND:return zt.TimestampMillisecond;case $t.MICROSECOND:return zt.TimestampMicrosecond;case $t.NANOSECOND:return zt.TimestampNanosecond}return zt.Timestamp;case zt.Date:switch(e.unit){case Wt.DAY:return zt.DateDay;case Wt.MILLISECOND:return zt.DateMillisecond}return zt.Date;case zt.Interval:switch(e.unit){case Kt.DAY_TIME:return zt.IntervalDayTime;case Kt.YEAR_MONTH:return zt.IntervalYearMonth}return zt.Interval;case zt.Map:return zt.Map;case zt.List:return zt.List;case zt.Struct:return zt.Struct;case zt.Union:switch(e.mode){case Yt.Dense:return zt.DenseUnion;case Yt.Sparse:return zt.SparseUnion}return zt.Union;case zt.FixedSizeBinary:return zt.FixedSizeBinary;case zt.FixedSizeList:return zt.FixedSizeList;case zt.Dictionary:return zt.Dictionary}throw new Error("Unrecognized type '".concat(zt[e.typeId],"'"))}ln.prototype.visitInt8=null,ln.prototype.visitInt16=null,ln.prototype.visitInt32=null,ln.prototype.visitInt64=null,ln.prototype.visitUint8=null,ln.prototype.visitUint16=null,ln.prototype.visitUint32=null,ln.prototype.visitUint64=null,ln.prototype.visitFloat16=null,ln.prototype.visitFloat32=null,ln.prototype.visitFloat64=null,ln.prototype.visitDateDay=null,ln.prototype.visitDateMillisecond=null,ln.prototype.visitTimestampSecond=null,ln.prototype.visitTimestampMillisecond=null,ln.prototype.visitTimestampMicrosecond=null,ln.prototype.visitTimestampNanosecond=null,ln.prototype.visitTimeSecond=null,ln.prototype.visitTimeMillisecond=null,ln.prototype.visitTimeMicrosecond=null,ln.prototype.visitTimeNanosecond=null,ln.prototype.visitDenseUnion=null,ln.prototype.visitSparseUnion=null,ln.prototype.visitIntervalDayTime=null,ln.prototype.visitIntervalYearMonth=null;var hn=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"compareSchemas",value:function(e,t){return e===t||t instanceof e.constructor&&On.compareFields(e.fields,t.fields)}},{key:"compareFields",value:function(e,t){return e===t||Array.isArray(e)&&Array.isArray(t)&&e.length===t.length&&e.every(function(e,n){return On.compareField(e,t[n])})}},{key:"compareField",value:function(e,t){return e===t||t instanceof e.constructor&&e.name===t.name&&e.nullable===t.nullable&&On.visit(e.type,t.type)}}]),t}(ln);function dn(e,t){return t instanceof e.constructor}function pn(e,t){return e===t||dn(e,t)}function yn(e,t){return e===t||dn(e,t)&&e.bitWidth===t.bitWidth&&e.isSigned===t.isSigned}function vn(e,t){return e===t||dn(e,t)&&e.precision===t.precision}function bn(e,t){return e===t||dn(e,t)&&e.unit===t.unit}function mn(e,t){return e===t||dn(e,t)&&e.unit===t.unit&&e.timezone===t.timezone}function gn(e,t){return e===t||dn(e,t)&&e.unit===t.unit&&e.bitWidth===t.bitWidth}function kn(e,t){return e===t||dn(e,t)&&e.mode===t.mode&&e.typeIds.every(function(e,n){return e===t.typeIds[n]})&&On.compareFields(e.children,t.children)}function wn(e,t){return e===t||dn(e,t)&&e.unit===t.unit}hn.prototype.visitNull=pn,hn.prototype.visitBool=pn,hn.prototype.visitInt=yn,hn.prototype.visitInt8=yn,hn.prototype.visitInt16=yn,hn.prototype.visitInt32=yn,hn.prototype.visitInt64=yn,hn.prototype.visitUint8=yn,hn.prototype.visitUint16=yn,hn.prototype.visitUint32=yn,hn.prototype.visitUint64=yn,hn.prototype.visitFloat=vn,hn.prototype.visitFloat16=vn,hn.prototype.visitFloat32=vn,hn.prototype.visitFloat64=vn,hn.prototype.visitUtf8=pn,hn.prototype.visitBinary=pn,hn.prototype.visitFixedSizeBinary=function(e,t){return e===t||dn(e,t)&&e.byteWidth===t.byteWidth},hn.prototype.visitDate=bn,hn.prototype.visitDateDay=bn,hn.prototype.visitDateMillisecond=bn,hn.prototype.visitTimestamp=mn,hn.prototype.visitTimestampSecond=mn,hn.prototype.visitTimestampMillisecond=mn,hn.prototype.visitTimestampMicrosecond=mn,hn.prototype.visitTimestampNanosecond=mn,hn.prototype.visitTime=gn,hn.prototype.visitTimeSecond=gn,hn.prototype.visitTimeMillisecond=gn,hn.prototype.visitTimeMicrosecond=gn,hn.prototype.visitTimeNanosecond=gn,hn.prototype.visitDecimal=pn,hn.prototype.visitList=function(e,t){return e===t||dn(e,t)&&e.children.length===t.children.length&&On.compareFields(e.children,t.children)},hn.prototype.visitStruct=function(e,t){return e===t||dn(e,t)&&e.children.length===t.children.length&&On.compareFields(e.children,t.children)},hn.prototype.visitUnion=kn,hn.prototype.visitDenseUnion=kn,hn.prototype.visitSparseUnion=kn,hn.prototype.visitDictionary=function(e,t){return e===t||dn(e,t)&&e.id===t.id&&e.isOrdered===t.isOrdered&&On.visit(e.indices,t.indices)&&On.visit(e.dictionary,t.dictionary)},hn.prototype.visitInterval=wn,hn.prototype.visitIntervalDayTime=wn,hn.prototype.visitIntervalYearMonth=wn,hn.prototype.visitFixedSizeList=function(e,t){return e===t||dn(e,t)&&e.listSize===t.listSize&&e.children.length===t.children.length&&On.compareFields(e.children,t.children)},hn.prototype.visitMap=function(e,t){return e===t||dn(e,t)&&e.keysSorted===t.keysSorted&&e.children.length===t.children.length&&On.compareFields(e.children,t.children)};var _n,On=new hn,jn=function(){function e(){Object(D.a)(this,e)}return Object(C.a)(e,[{key:"compareTo",value:function(e){return On.visit(this,e)}},{key:"typeId",get:function(){return zt.NONE}}],[{key:"isNull",value:function(e){return e&&e.typeId===zt.Null}},{key:"isInt",value:function(e){return e&&e.typeId===zt.Int}},{key:"isFloat",value:function(e){return e&&e.typeId===zt.Float}},{key:"isBinary",value:function(e){return e&&e.typeId===zt.Binary}},{key:"isUtf8",value:function(e){return e&&e.typeId===zt.Utf8}},{key:"isBool",value:function(e){return e&&e.typeId===zt.Bool}},{key:"isDecimal",value:function(e){return e&&e.typeId===zt.Decimal}},{key:"isDate",value:function(e){return e&&e.typeId===zt.Date}},{key:"isTime",value:function(e){return e&&e.typeId===zt.Time}},{key:"isTimestamp",value:function(e){return e&&e.typeId===zt.Timestamp}},{key:"isInterval",value:function(e){return e&&e.typeId===zt.Interval}},{key:"isList",value:function(e){return e&&e.typeId===zt.List}},{key:"isStruct",value:function(e){return e&&e.typeId===zt.Struct}},{key:"isUnion",value:function(e){return e&&e.typeId===zt.Union}},{key:"isFixedSizeBinary",value:function(e){return e&&e.typeId===zt.FixedSizeBinary}},{key:"isFixedSizeList",value:function(e){return e&&e.typeId===zt.FixedSizeList}},{key:"isMap",value:function(e){return e&&e.typeId===zt.Map}},{key:"isDictionary",value:function(e){return e&&e.typeId===zt.Dictionary}}]),e}();jn[Symbol.toStringTag]=((_n=jn.prototype).children=null,_n.ArrayType=Array,_n[Symbol.toStringTag]="DataType");var xn=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toString",value:function(){return"Null"}},{key:"typeId",get:function(){return zt.Null}}]),t}(jn);xn[Symbol.toStringTag]=xn.prototype[Symbol.toStringTag]="Null";var Sn=function(e){function t(e,n){var r;return Object(D.a)(this,t),(r=Object(re.a)(this,Object(ie.a)(t).call(this))).isSigned=e,r.bitWidth=n,r}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toString",value:function(){return"".concat(this.isSigned?"I":"Ui","nt").concat(this.bitWidth)}},{key:"typeId",get:function(){return zt.Int}},{key:"ArrayType",get:function(){switch(this.bitWidth){case 8:return this.isSigned?Int8Array:Uint8Array;case 16:return this.isSigned?Int16Array:Uint16Array;case 32:case 64:return this.isSigned?Int32Array:Uint32Array}throw new Error("Unrecognized ".concat(this[Symbol.toStringTag]," type"))}}]),t}(jn);Sn[Symbol.toStringTag]=function(e){return e.isSigned=null,e.bitWidth=null,e[Symbol.toStringTag]="Int"}(Sn.prototype);var Tn=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).call(this,!0,8))}return Object(ae.a)(t,e),t}(Sn),In=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).call(this,!0,16))}return Object(ae.a)(t,e),t}(Sn),En=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).call(this,!0,32))}return Object(ae.a)(t,e),t}(Sn),An=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).call(this,!0,64))}return Object(ae.a)(t,e),t}(Sn),Bn=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).call(this,!1,8))}return Object(ae.a)(t,e),t}(Sn),Dn=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).call(this,!1,16))}return Object(ae.a)(t,e),t}(Sn),Cn=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).call(this,!1,32))}return Object(ae.a)(t,e),t}(Sn),Ln=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).call(this,!1,64))}return Object(ae.a)(t,e),t}(Sn);Object.defineProperty(Tn.prototype,"ArrayType",{value:Int8Array}),Object.defineProperty(In.prototype,"ArrayType",{value:Int16Array}),Object.defineProperty(En.prototype,"ArrayType",{value:Int32Array}),Object.defineProperty(An.prototype,"ArrayType",{value:Int32Array}),Object.defineProperty(Bn.prototype,"ArrayType",{value:Uint8Array}),Object.defineProperty(Dn.prototype,"ArrayType",{value:Uint16Array}),Object.defineProperty(Cn.prototype,"ArrayType",{value:Uint32Array}),Object.defineProperty(Ln.prototype,"ArrayType",{value:Uint32Array});var Fn=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this))).precision=e,n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toString",value:function(){return"Float".concat(this.precision<<5||16)}},{key:"typeId",get:function(){return zt.Float}},{key:"ArrayType",get:function(){switch(this.precision){case Ht.HALF:return Uint16Array;case Ht.SINGLE:return Float32Array;case Ht.DOUBLE:return Float64Array}throw new Error("Unrecognized ".concat(this[Symbol.toStringTag]," type"))}}]),t}(jn);Fn[Symbol.toStringTag]=function(e){return e.precision=null,e[Symbol.toStringTag]="Float"}(Fn.prototype);var Mn=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).call(this,Ht.HALF))}return Object(ae.a)(t,e),t}(Fn),Nn=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).call(this,Ht.SINGLE))}return Object(ae.a)(t,e),t}(Fn),Un=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).call(this,Ht.DOUBLE))}return Object(ae.a)(t,e),t}(Fn);Object.defineProperty(Mn.prototype,"ArrayType",{value:Uint16Array}),Object.defineProperty(Nn.prototype,"ArrayType",{value:Float32Array}),Object.defineProperty(Un.prototype,"ArrayType",{value:Float64Array});var Pn=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).call(this))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toString",value:function(){return"Binary"}},{key:"typeId",get:function(){return zt.Binary}}]),t}(jn);Pn[Symbol.toStringTag]=function(e){return e.ArrayType=Uint8Array,e[Symbol.toStringTag]="Binary"}(Pn.prototype);var Rn=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).call(this))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toString",value:function(){return"Utf8"}},{key:"typeId",get:function(){return zt.Utf8}}]),t}(jn);Rn[Symbol.toStringTag]=function(e){return e.ArrayType=Uint8Array,e[Symbol.toStringTag]="Utf8"}(Rn.prototype);var zn=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).call(this))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toString",value:function(){return"Bool"}},{key:"typeId",get:function(){return zt.Bool}}]),t}(jn);zn[Symbol.toStringTag]=function(e){return e.ArrayType=Uint8Array,e[Symbol.toStringTag]="Bool"}(zn.prototype);var Vn=function(e){function t(e,n){var r;return Object(D.a)(this,t),(r=Object(re.a)(this,Object(ie.a)(t).call(this))).scale=e,r.precision=n,r}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toString",value:function(){return"Decimal[".concat(this.precision,"e").concat(this.scale>0?"+":"").concat(this.scale,"]")}},{key:"typeId",get:function(){return zt.Decimal}}]),t}(jn);Vn[Symbol.toStringTag]=function(e){return e.scale=null,e.precision=null,e.ArrayType=Uint32Array,e[Symbol.toStringTag]="Decimal"}(Vn.prototype);var Wn=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this))).unit=e,n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toString",value:function(){return"Date".concat(32*(this.unit+1),"<").concat(Wt[this.unit],">")}},{key:"typeId",get:function(){return zt.Date}}]),t}(jn);Wn[Symbol.toStringTag]=function(e){return e.unit=null,e.ArrayType=Int32Array,e[Symbol.toStringTag]="Date"}(Wn.prototype);var $n=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).call(this,Wt.DAY))}return Object(ae.a)(t,e),t}(Wn),Hn=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).call(this,Wt.MILLISECOND))}return Object(ae.a)(t,e),t}(Wn),Yn=function(e){function t(e,n){var r;return Object(D.a)(this,t),(r=Object(re.a)(this,Object(ie.a)(t).call(this))).unit=e,r.bitWidth=n,r}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toString",value:function(){return"Time".concat(this.bitWidth,"<").concat($t[this.unit],">")}},{key:"typeId",get:function(){return zt.Time}}]),t}(jn);Yn[Symbol.toStringTag]=function(e){return e.unit=null,e.bitWidth=null,e.ArrayType=Int32Array,e[Symbol.toStringTag]="Time"}(Yn.prototype);var Kn=function(e){function t(e,n){var r;return Object(D.a)(this,t),(r=Object(re.a)(this,Object(ie.a)(t).call(this))).unit=e,r.timezone=n,r}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toString",value:function(){return"Timestamp<".concat($t[this.unit]).concat(this.timezone?", ".concat(this.timezone):"",">")}},{key:"typeId",get:function(){return zt.Timestamp}}]),t}(jn);Kn[Symbol.toStringTag]=function(e){return e.unit=null,e.timezone=null,e.ArrayType=Int32Array,e[Symbol.toStringTag]="Timestamp"}(Kn.prototype);var Qn=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this))).unit=e,n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toString",value:function(){return"Interval<".concat(Kt[this.unit],">")}},{key:"typeId",get:function(){return zt.Interval}}]),t}(jn);Qn[Symbol.toStringTag]=function(e){return e.unit=null,e.ArrayType=Int32Array,e[Symbol.toStringTag]="Interval"}(Qn.prototype);var qn=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this))).children=[e],n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toString",value:function(){return"List<".concat(this.valueType,">")}},{key:"typeId",get:function(){return zt.List}},{key:"valueType",get:function(){return this.children[0].type}},{key:"valueField",get:function(){return this.children[0]}},{key:"ArrayType",get:function(){return this.valueType.ArrayType}}]),t}(jn);qn[Symbol.toStringTag]=function(e){return e.children=null,e[Symbol.toStringTag]="List"}(qn.prototype);var Gn=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this))).children=e,n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toString",value:function(){return"Struct<{".concat(this.children.map(function(e){return"".concat(e.name,":").concat(e.type)}).join(", "),"}>")}},{key:"typeId",get:function(){return zt.Struct}}]),t}(jn);Gn[Symbol.toStringTag]=function(e){return e.children=null,e[Symbol.toStringTag]="Struct"}(Gn.prototype);var Jn=function(e){function t(e,n,r){var i;return Object(D.a)(this,t),(i=Object(re.a)(this,Object(ie.a)(t).call(this))).mode=e,i.children=r,i.typeIds=n=Int32Array.from(n),i.typeIdToChildIndex=n.reduce(function(e,t,n){return(e[t]=n)&&e||e},Object.create(null)),i}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toString",value:function(){return"".concat(this[Symbol.toStringTag],"<").concat(this.children.map(function(e){return"".concat(e.type)}).join(" | "),">")}},{key:"typeId",get:function(){return zt.Union}}]),t}(jn);Jn[Symbol.toStringTag]=function(e){return e.mode=null,e.typeIds=null,e.children=null,e.typeIdToChildIndex=null,e.ArrayType=Int8Array,e[Symbol.toStringTag]="Union"}(Jn.prototype);var Xn=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this))).byteWidth=e,n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toString",value:function(){return"FixedSizeBinary[".concat(this.byteWidth,"]")}},{key:"typeId",get:function(){return zt.FixedSizeBinary}}]),t}(jn);Xn[Symbol.toStringTag]=function(e){return e.byteWidth=null,e.ArrayType=Uint8Array,e[Symbol.toStringTag]="FixedSizeBinary"}(Xn.prototype);var Zn=function(e){function t(e,n){var r;return Object(D.a)(this,t),(r=Object(re.a)(this,Object(ie.a)(t).call(this))).listSize=e,r.children=[n],r}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toString",value:function(){return"FixedSizeList[".concat(this.listSize,"]<").concat(this.valueType,">")}},{key:"typeId",get:function(){return zt.FixedSizeList}},{key:"valueType",get:function(){return this.children[0].type}},{key:"valueField",get:function(){return this.children[0]}},{key:"ArrayType",get:function(){return this.valueType.ArrayType}}]),t}(jn);Zn[Symbol.toStringTag]=function(e){return e.children=null,e.listSize=null,e[Symbol.toStringTag]="FixedSizeList"}(Zn.prototype);var er=function(e){function t(e){var n,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this))).children=[e],n.keysSorted=r,n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toString",value:function(){return"Map<{".concat(this.children[0].type.children.map(function(e){return"".concat(e.name,":").concat(e.type)}).join(", "),"}>")}},{key:"typeId",get:function(){return zt.Map}},{key:"keyType",get:function(){return this.children[0].type.children[0].type}},{key:"valueType",get:function(){return this.children[0].type.children[1].type}}]),t}(jn);er[Symbol.toStringTag]=function(e){return e.children=null,e.keysSorted=null,e[Symbol.toStringTag]="Map_"}(er.prototype);var tr,nr=(tr=-1,function(){return++tr}),rr=function(e){function t(e,n,r,i){var a;return Object(D.a)(this,t),(a=Object(re.a)(this,Object(ie.a)(t).call(this))).indices=n,a.dictionary=e,a.isOrdered=i||!1,a.id=null==r?nr():"number"===typeof r?r:r.low,a}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toString",value:function(){return"Dictionary<".concat(this.indices,", ").concat(this.dictionary,">")}},{key:"typeId",get:function(){return zt.Dictionary}},{key:"children",get:function(){return this.dictionary.children}},{key:"valueType",get:function(){return this.dictionary}},{key:"ArrayType",get:function(){return this.dictionary.ArrayType}}]),t}(jn);function ir(e){var t=e;switch(e.typeId){case zt.Decimal:return 4;case zt.Timestamp:return 2;case zt.Date:case zt.Interval:return 1+t.unit;case zt.Int:case zt.Time:return+(t.bitWidth>32)+1;case zt.FixedSizeList:return t.listSize;case zt.FixedSizeBinary:return t.byteWidth;default:return 1}}rr[Symbol.toStringTag]=function(e){return e.id=null,e.indices=null,e.isOrdered=null,e.dictionary=null,e[Symbol.toStringTag]="Dictionary"}(rr.prototype);var ar=function(){function e(t,n,r,i,a,o,u){var s;Object(D.a)(this,e),this.type=t,this.dictionary=u,this.offset=Math.floor(Math.max(n||0,0)),this.length=Math.floor(Math.max(r||0,0)),this._nullCount=Math.floor(Math.max(i||0,-1)),this.childData=(o||[]).map(function(t){return t instanceof e?t:t.data}),a instanceof e?(this.stride=a.stride,this.values=a.values,this.typeIds=a.typeIds,this.nullBitmap=a.nullBitmap,this.valueOffsets=a.valueOffsets):(this.stride=ir(t),a&&((s=a[0])&&(this.valueOffsets=s),(s=a[1])&&(this.values=s),(s=a[2])&&(this.nullBitmap=s),(s=a[3])&&(this.typeIds=s)))}return Object(C.a)(e,[{key:"clone",value:function(t){return new e(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.offset,arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.length,arguments.length>3&&void 0!==arguments[3]?arguments[3]:this._nullCount,arguments.length>4&&void 0!==arguments[4]?arguments[4]:this,arguments.length>5&&void 0!==arguments[5]?arguments[5]:this.childData,this.dictionary)}},{key:"slice",value:function(e,t){var n=this.stride,r=this.typeId,i=this.childData,a=+(0===this._nullCount)-1,o=16===r?n:1,u=this._sliceBuffers(e,t,n,r);return this.clone(this.type,this.offset+e,t,a,u,!i.length||this.valueOffsets?i:this._sliceChildren(i,o*e,o*t))}},{key:"_changeLengthAndBackfillNullBitmap",value:function(e){if(this.typeId===zt.Null)return this.clone(this.type,0,e,0);var t=this.length,n=this.nullCount,r=new Uint8Array((e+63&-64)>>3).fill(255,0,t>>3);r[t>>3]=(1<<t-(-8&t))-1,n>0&&r.set(nn(this.offset,t,this.nullBitmap),0);var i=this.buffers;return i[Vt.VALIDITY]=r,this.clone(this.type,0,e,n+(e-t),i)}},{key:"_sliceBuffers",value:function(e,t,n,r){var i,a=this.buffers;return(i=a[Vt.TYPE])&&(a[Vt.TYPE]=i.subarray(e,e+t)),(i=a[Vt.OFFSET])&&(a[Vt.OFFSET]=i.subarray(e,e+t+1))||(i=a[Vt.DATA])&&(a[Vt.DATA]=6===r?i:i.subarray(n*e,n*(e+t))),a}},{key:"_sliceChildren",value:function(e,t,n){return e.map(function(e){return e.slice(t,n)})}},{key:"typeId",get:function(){return this.type.typeId}},{key:"ArrayType",get:function(){return this.type.ArrayType}},{key:"buffers",get:function(){return[this.valueOffsets,this.values,this.nullBitmap,this.typeIds]}},{key:"byteLength",get:function(){var e=0,t=this.valueOffsets,n=this.values,r=this.nullBitmap,i=this.typeIds;return t&&(e+=t.byteLength),n&&(e+=n.byteLength),r&&(e+=r.byteLength),i&&(e+=i.byteLength),this.childData.reduce(function(e,t){return e+t.byteLength},e)}},{key:"nullCount",get:function(){var e,t=this._nullCount;return t<=-1&&(e=this.nullBitmap)&&(this._nullCount=t=this.length-on(e,this.offset,this.offset+this.length)),t}}],[{key:"new",value:function(t,n,r,i,a,o,u){switch(a instanceof e?a=a.buffers:a||(a=[]),t.typeId){case zt.Null:return e.Null(t,n,r);case zt.Int:return e.Int(t,n,r,i||0,a[Vt.VALIDITY],a[Vt.DATA]||[]);case zt.Dictionary:return e.Dictionary(t,n,r,i||0,a[Vt.VALIDITY],a[Vt.DATA]||[],u);case zt.Float:return e.Float(t,n,r,i||0,a[Vt.VALIDITY],a[Vt.DATA]||[]);case zt.Bool:return e.Bool(t,n,r,i||0,a[Vt.VALIDITY],a[Vt.DATA]||[]);case zt.Decimal:return e.Decimal(t,n,r,i||0,a[Vt.VALIDITY],a[Vt.DATA]||[]);case zt.Date:return e.Date(t,n,r,i||0,a[Vt.VALIDITY],a[Vt.DATA]||[]);case zt.Time:return e.Time(t,n,r,i||0,a[Vt.VALIDITY],a[Vt.DATA]||[]);case zt.Timestamp:return e.Timestamp(t,n,r,i||0,a[Vt.VALIDITY],a[Vt.DATA]||[]);case zt.Interval:return e.Interval(t,n,r,i||0,a[Vt.VALIDITY],a[Vt.DATA]||[]);case zt.FixedSizeBinary:return e.FixedSizeBinary(t,n,r,i||0,a[Vt.VALIDITY],a[Vt.DATA]||[]);case zt.Binary:return e.Binary(t,n,r,i||0,a[Vt.VALIDITY],a[Vt.OFFSET]||[],a[Vt.DATA]||[]);case zt.Utf8:return e.Utf8(t,n,r,i||0,a[Vt.VALIDITY],a[Vt.OFFSET]||[],a[Vt.DATA]||[]);case zt.List:return e.List(t,n,r,i||0,a[Vt.VALIDITY],a[Vt.OFFSET]||[],(o||[])[0]);case zt.FixedSizeList:return e.FixedSizeList(t,n,r,i||0,a[Vt.VALIDITY],(o||[])[0]);case zt.Struct:return e.Struct(t,n,r,i||0,a[Vt.VALIDITY],o||[]);case zt.Map:return e.Map(t,n,r,i||0,a[Vt.VALIDITY],a[Vt.OFFSET]||[],(o||[])[0]);case zt.Union:return e.Union(t,n,r,i||0,a[Vt.VALIDITY],a[Vt.TYPE]||[],a[Vt.OFFSET]||o,o)}throw new Error("Unrecognized typeId ".concat(t.typeId))}},{key:"Null",value:function(t,n,r){return new e(t,n,r,0)}},{key:"Int",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,Ne(t.ArrayType,o),Ve(a)])}},{key:"Dictionary",value:function(t,n,r,i,a,o,u){return new e(t,n,r,i,[void 0,Ne(t.indices.ArrayType,o),Ve(a)],[],u)}},{key:"Float",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,Ne(t.ArrayType,o),Ve(a)])}},{key:"Bool",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,Ne(t.ArrayType,o),Ve(a)])}},{key:"Decimal",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,Ne(t.ArrayType,o),Ve(a)])}},{key:"Date",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,Ne(t.ArrayType,o),Ve(a)])}},{key:"Time",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,Ne(t.ArrayType,o),Ve(a)])}},{key:"Timestamp",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,Ne(t.ArrayType,o),Ve(a)])}},{key:"Interval",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,Ne(t.ArrayType,o),Ve(a)])}},{key:"FixedSizeBinary",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,Ne(t.ArrayType,o),Ve(a)])}},{key:"Binary",value:function(t,n,r,i,a,o,u){return new e(t,n,r,i,[Re(o),Ve(u),Ve(a)])}},{key:"Utf8",value:function(t,n,r,i,a,o,u){return new e(t,n,r,i,[Re(o),Ve(u),Ve(a)])}},{key:"List",value:function(t,n,r,i,a,o,u){return new e(t,n,r,i,[Re(o),void 0,Ve(a)],[u])}},{key:"FixedSizeList",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,void 0,Ve(a)],[o])}},{key:"Struct",value:function(t,n,r,i,a,o){return new e(t,n,r,i,[void 0,void 0,Ve(a)],o)}},{key:"Map",value:function(t,n,r,i,a,o,u){return new e(t,n,r,i,[Re(o),void 0,Ve(a)],[u])}},{key:"Union",value:function(t,n,r,i,a,o,u,s){var c=[void 0,void 0,Ve(a),Ne(t.ArrayType,o)];return t.mode===Yt.Sparse?new e(t,n,r,i,c,u):(c[Vt.OFFSET]=Re(u),new e(t,n,r,i,c,s))}}]),e}();ar.prototype.childData=Object.freeze([]);var or=void 0;function ur(e){if(null===e)return"null";if(e===or)return"undefined";switch(typeof e){case"number":case"bigint":return"".concat(e);case"string":return'"'.concat(e,'"')}return"function"===typeof e[Symbol.toPrimitive]?e[Symbol.toPrimitive]("string"):ArrayBuffer.isView(e)?"[".concat(e,"]"):JSON.stringify(e)}function sr(e){if(!e||e.length<=0)return function(e){return!0};var t="",n=e.filter(function(e){return e===e});return n.length>0&&(t="\n    switch (x) {".concat(n.map(function(e){return"\n        case ".concat(function(e){if("bigint"!==typeof e)return ur(e);if(he)return"".concat(ur(e),"n");return'"'.concat(ur(e),'"')}(e),":")}).join(""),"\n            return false;\n    }")),e.length!==n.length&&(t="if (x !== x) return false;\n".concat(t)),new Function("x","".concat(t,"\nreturn true;"))}var cr=function(e,t){return(e*t+63&-64||64)/t},lr=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;Object(D.a)(this,e),this.buffer=t,this.stride=n,this.BYTES_PER_ELEMENT=t.BYTES_PER_ELEMENT,this.ArrayType=t.constructor,this._resize(this.length=t.length/n|0)}return Object(C.a)(e,[{key:"set",value:function(e,t){return this}},{key:"append",value:function(e){return this.set(this.length,e)}},{key:"reserve",value:function(e){if(e>0){this.length+=e;var t=this.stride,n=this.length*t,r=this.buffer.length;n>=r&&this._resize(cr(0===r?1*n:2*n,this.BYTES_PER_ELEMENT))}return this}},{key:"flush",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.length;e=cr(e*this.stride,this.BYTES_PER_ELEMENT);var t=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e.length>=t?e.subarray(0,t):Fe(new e.constructor(t),e,0)}(this.buffer,e);return this.clear(),t}},{key:"clear",value:function(){return this.length=0,this._resize(0),this}},{key:"_resize",value:function(e){return this.buffer=Fe(new this.ArrayType(e),this.buffer)}},{key:"byteLength",get:function(){return this.length*this.stride*this.BYTES_PER_ELEMENT|0}},{key:"reservedLength",get:function(){return this.buffer.length/this.stride}},{key:"reservedByteLength",get:function(){return this.buffer.byteLength}}]),e}();lr.prototype.offset=0;var fr=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"last",value:function(){return this.get(this.length-1)}},{key:"get",value:function(e){return this.buffer[e]}},{key:"set",value:function(e,t){return this.reserve(e-this.length+1),this.buffer[e*this.stride]=t,this}}]),t}(lr),hr=function(e){function t(){var e,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Uint8Array(0);return Object(D.a)(this,t),(e=Object(re.a)(this,Object(ie.a)(t).call(this,n,1/8))).numValid=0,e}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"get",value:function(e){return this.buffer[e>>3]>>e%8&1}},{key:"set",value:function(e,t){var n=this.reserve(e-this.length+1).buffer,r=e>>3,i=e%8,a=n[r]>>i&1;return t?0===a&&(n[r]|=1<<i,++this.numValid):1===a&&(n[r]&=~(1<<i),--this.numValid),this}},{key:"clear",value:function(){return this.numValid=0,Ct(Object(ie.a)(t.prototype),"clear",this).call(this)}},{key:"numInvalid",get:function(){return this.length-this.numValid}}]),t}(fr),dr=function(e){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Int32Array(1);return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).call(this,e,1))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"append",value:function(e){return this.set(this.length-1,e)}},{key:"set",value:function(e,t){var n=this.length-1,r=this.reserve(e-n+1).buffer;return n<e++&&r.fill(r[n],n,e),r[e]=r[e-1]+t,this}},{key:"flush",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.length-1;return e>this.length&&this.set(e-1,0),Ct(Object(ie.a)(t.prototype),"flush",this).call(this,e+1)}}]),t}(fr),pr=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"set",value:function(e,t){switch(this.reserve(e-this.length+1),typeof t){case"bigint":this.buffer64[e]=t;break;case"number":this.buffer[e*this.stride]=t;break;default:this.buffer.set(t,e*this.stride)}return this}},{key:"_resize",value:function(e){var n=Ct(Object(ie.a)(t.prototype),"_resize",this).call(this,e),r=n.byteLength/(this.BYTES_PER_ELEMENT*this.stride);return he&&(this.buffer64=new this.ArrayType64(n.buffer,n.byteOffset,r)),n}},{key:"ArrayType64",get:function(){return this._ArrayType64||(this._ArrayType64=this.buffer instanceof Int32Array?pe:ve)}}]),t}(lr);function yr(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"===typeof e)return vr(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return vr(e,t)}(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){u=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(u)throw a}}}}function vr(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function br(e){var t,n,r,i=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);i--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new mr(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function mr(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:t}})}return(mr=function(e){this.s=e,this.n=e.next}).prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new mr(e)}var gr=function(){function e(t){var n=t.type,r=t.nullValues;Object(D.a)(this,e),this.length=0,this.finished=!1,this.type=n,this.children=[],this.nullValues=r,this.stride=ir(n),this._nulls=new hr,r&&r.length>0&&(this._isValid=sr(r))}return Object(C.a)(e,[{key:"toVector",value:function(){return Rt.new(this.flush())}},{key:"append",value:function(e){return this.set(this.length,e)}},{key:"isValid",value:function(e){return this._isValid(e)}},{key:"set",value:function(e,t){return this.setValid(e,this.isValid(t))&&this.setValue(e,t),this}},{key:"setValue",value:function(e,t){this._setValue(this,e,t)}},{key:"setValid",value:function(e,t){return this.length=this._nulls.set(e,+t).length,t}},{key:"addChild",value:function(e){arguments.length>1&&void 0!==arguments[1]?arguments[1]:"".concat(this.numChildren);throw new Error('Cannot append children to non-nested type "'.concat(this.type,'"'))}},{key:"getChildAt",value:function(e){return this.children[e]||null}},{key:"flush",value:function(){var e=[],t=this._values,n=this._offsets,r=this._typeIds,i=this.length,a=this.nullCount;r?(e[Vt.TYPE]=r.flush(i),n&&(e[Vt.OFFSET]=n.flush(i))):n?(t&&(e[Vt.DATA]=t.flush(n.last())),e[Vt.OFFSET]=n.flush(i)):t&&(e[Vt.DATA]=t.flush(i)),a>0&&(e[Vt.VALIDITY]=this._nulls.flush(i));var o=ar.new(this.type,0,i,a,e,this.children.map(function(e){return e.flush()}));return this.clear(),o}},{key:"finish",value:function(){return this.finished=!0,this.children.forEach(function(e){return e.finish()}),this}},{key:"clear",value:function(){return this.length=0,this._offsets&&this._offsets.clear(),this._values&&this._values.clear(),this._nulls&&this._nulls.clear(),this._typeIds&&this._typeIds.clear(),this.children.forEach(function(e){return e.clear()}),this}},{key:"ArrayType",get:function(){return this.type.ArrayType}},{key:"nullCount",get:function(){return this._nulls.numInvalid}},{key:"numChildren",get:function(){return this.children.length}},{key:"byteLength",get:function(){var e=0;return this._offsets&&(e+=this._offsets.byteLength),this._values&&(e+=this._values.byteLength),this._nulls&&(e+=this._nulls.byteLength),this._typeIds&&(e+=this._typeIds.byteLength),this.children.reduce(function(e,t){return e+t.byteLength},e)}},{key:"reservedLength",get:function(){return this._nulls.reservedLength}},{key:"reservedByteLength",get:function(){var e=0;return this._offsets&&(e+=this._offsets.reservedByteLength),this._values&&(e+=this._values.reservedByteLength),this._nulls&&(e+=this._nulls.reservedByteLength),this._typeIds&&(e+=this._typeIds.reservedByteLength),this.children.reduce(function(e,t){return e+t.reservedByteLength},e)}},{key:"valueOffsets",get:function(){return this._offsets?this._offsets.buffer:null}},{key:"values",get:function(){return this._values?this._values.buffer:null}},{key:"nullBitmap",get:function(){return this._nulls?this._nulls.buffer:null}},{key:"typeIds",get:function(){return this._typeIds?this._typeIds.buffer:null}}],[{key:"new",value:function(e){}},{key:"throughNode",value:function(e){throw new Error('"throughNode" not available in this environment')}},{key:"throughDOM",value:function(e){throw new Error('"throughDOM" not available in this environment')}},{key:"throughIterable",value:function(e){return function(e){var t=e.queueingStrategy,n=void 0===t?"count":t,r=e.highWaterMark,i=void 0===r?"bytes"!==n?1e3:Math.pow(2,14):r,a="bytes"!==n?"length":"byteLength";return L.mark(function t(n){var r,o,u,s,c;return L.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:r=0,o=gr.new(e),u=yr(n),t.prev=3,u.s();case 5:if((s=u.n()).done){t.next=14;break}if(c=s.value,!(o.append(c)[a]>=i)){t.next=12;break}if(t.t0=++r,!t.t0){t.next=12;break}return t.next=12,o.toVector();case 12:t.next=5;break;case 14:t.next=19;break;case 16:t.prev=16,t.t1=t.catch(3),u.e(t.t1);case 19:return t.prev=19,u.f(),t.finish(19);case 22:if(!(o.finish().length>0||0===r)){t.next=25;break}return t.next=25,o.toVector();case 25:case"end":return t.stop()}},t,null,[[3,16,19,22]])})}(e)}},{key:"throughAsyncIterable",value:function(e){return function(e){var t=e.queueingStrategy,n=void 0===t?"count":t,r=e.highWaterMark,i=void 0===r?"bytes"!==n?1e3:Math.pow(2,14):r,a="bytes"!==n?"length":"byteLength";return function(){var t=P(L.mark(function t(n){var r,o,u,s,c,l,f,h;return L.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:r=0,o=gr.new(e),u=!1,s=!1,t.prev=4,l=br(n);case 6:return t.next=8,N(l.next());case 8:if(!(u=!(f=t.sent).done)){t.next=18;break}if(h=f.value,!(o.append(h)[a]>=i)){t.next=15;break}if(t.t0=++r,!t.t0){t.next=15;break}return t.next=15,o.toVector();case 15:u=!1,t.next=6;break;case 18:t.next=24;break;case 20:t.prev=20,t.t1=t.catch(4),s=!0,c=t.t1;case 24:if(t.prev=24,t.prev=25,!u||null==l.return){t.next=29;break}return t.next=29,N(l.return());case 29:if(t.prev=29,!s){t.next=32;break}throw c;case 32:return t.finish(29);case 33:return t.finish(24);case 34:if(!(o.finish().length>0||0===r)){t.next=37;break}return t.next=37,o.toVector();case 37:case"end":return t.stop()}},t,null,[[4,20,24,34],[25,,29,33]])}));return function(e){return t.apply(this,arguments)}}()}(e)}}]),e}();gr.prototype.length=1,gr.prototype.stride=1,gr.prototype.children=null,gr.prototype.finished=!1,gr.prototype.nullValues=null,gr.prototype._isValid=function(){return!0};var kr=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this,e)))._values=new fr(new n.ArrayType(0),n.stride),n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"setValue",value:function(e,n){var r=this._values;return r.reserve(e-r.length+1),Ct(Object(ie.a)(t.prototype),"setValue",this).call(this,e,n)}}]),t}(gr),wr=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this,e)))._pendingLength=0,n._offsets=new dr,n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"setValue",value:function(e,t){var n=this._pending||(this._pending=new Map),r=n.get(e);r&&(this._pendingLength-=r.length),this._pendingLength+=t.length,n.set(e,t)}},{key:"setValid",value:function(e,n){return!!Ct(Object(ie.a)(t.prototype),"setValid",this).call(this,e,n)||((this._pending||(this._pending=new Map)).set(e,void 0),!1)}},{key:"clear",value:function(){return this._pendingLength=0,this._pending=void 0,Ct(Object(ie.a)(t.prototype),"clear",this).call(this)}},{key:"flush",value:function(){return this._flush(),Ct(Object(ie.a)(t.prototype),"flush",this).call(this)}},{key:"finish",value:function(){return this._flush(),Ct(Object(ie.a)(t.prototype),"finish",this).call(this)}},{key:"_flush",value:function(){var e=this._pending,t=this._pendingLength;return this._pendingLength=0,this._pending=void 0,e&&e.size>0&&this._flushPending(e,t),this}}]),t}(gr);var _r=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this,e)))._values=new hr,n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"setValue",value:function(e,t){this._values.set(e,+t)}}]),t}(gr),Or=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"setValue",value:function(e,t){}},{key:"setValid",value:function(e,t){return this.length=Math.max(e+1,this.length),t}}]),t}(gr),jr=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(kr),xr=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(jr),Sr=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(jr),Tr=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(kr),Ir=function(e){function t(e){var n,r=e.type,i=e.nullValues,a=e.dictionaryHashFunction;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this,{type:new rr(r.dictionary,r.indices,r.id,r.isOrdered)})))._nulls=null,n._dictionaryOffset=0,n._keysToIndices=Object.create(null),n.indices=gr.new({type:n.type.indices,nullValues:i}),n.dictionary=gr.new({type:n.type.dictionary,nullValues:null}),"function"===typeof a&&(n.valueToKey=a),n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"isValid",value:function(e){return this.indices.isValid(e)}},{key:"setValid",value:function(e,t){var n=this.indices;return t=n.setValid(e,t),this.length=n.length,t}},{key:"setValue",value:function(e,t){var n=this._keysToIndices,r=this.valueToKey(t),i=n[r];return void 0===i&&(n[r]=i=this._dictionaryOffset+this.dictionary.append(t).length-1),this.indices.setValue(e,i)}},{key:"flush",value:function(){var e=this.type,t=this._dictionary,n=this.dictionary.toVector(),r=this.indices.flush().clone(e);return r.dictionary=t?t.concat(n):n,this.finished||(this._dictionaryOffset+=n.length),this._dictionary=r.dictionary,this.clear(),r}},{key:"finish",value:function(){return this.indices.finish(),this.dictionary.finish(),this._dictionaryOffset=0,this._keysToIndices=Object.create(null),Ct(Object(ie.a)(t.prototype),"finish",this).call(this)}},{key:"clear",value:function(){return this.indices.clear(),this.dictionary.clear(),Ct(Object(ie.a)(t.prototype),"clear",this).call(this)}},{key:"valueToKey",value:function(e){return"string"===typeof e?e:"".concat(e)}},{key:"values",get:function(){return this.indices.values}},{key:"nullCount",get:function(){return this.indices.nullCount}},{key:"nullBitmap",get:function(){return this.indices.nullBitmap}},{key:"byteLength",get:function(){return this.indices.byteLength+this.dictionary.byteLength}},{key:"reservedLength",get:function(){return this.indices.reservedLength+this.dictionary.reservedLength}},{key:"reservedByteLength",get:function(){return this.indices.reservedByteLength+this.dictionary.reservedByteLength}}]),t}(gr),Er=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(kr),Ar=new Float64Array(1),Br=new Uint32Array(Ar.buffer);function Dr(e){var t=(31744&e)>>10,n=(1023&e)/1024,r=Math.pow(-1,(32768&e)>>15);switch(t){case 31:return r*(n?NaN:1/0);case 0:return r*(n?6103515625e-14*n:0)}return r*Math.pow(2,t-15)*(1+n)}function Cr(e){if(e!==e)return 32256;Ar[0]=e;var t=(2147483648&Br[1])>>16&65535,n=2146435072&Br[1],r=0;return n>=1089470464?Br[0]>0?n=31744:(n=(2080374784&n)>>16,r=(1048575&Br[1])>>10):n<=1056964608?(r=1048576+((r=1048576+(1048575&Br[1]))<<(n>>20)-998)>>21,n=0):(n=n-1056964608>>10,r=512+(1048575&Br[1])>>10),t|n|65535&r}var Lr=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(kr),Fr=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"setValue",value:function(e,t){this._values.set(e,Cr(t))}}]),t}(Lr),Mr=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"setValue",value:function(e,t){this._values.set(e,t)}}]),t}(Lr),Nr=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"setValue",value:function(e,t){this._values.set(e,t)}}]),t}(Lr),Ur=n(12);function Pr(e,t,n){return(Pr=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(e){return!1}}()?Reflect.construct:function(e,t,n){var r=[null];r.push.apply(r,t);var i=new(Function.bind.apply(e,r));return n&&Object(Ur.a)(i,n.prototype),i}).apply(null,arguments)}var Rr,zr,Vr=Symbol.for("isArrowBigNum");function Wr(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return 0===n.length?Object.setPrototypeOf(Ne(this.TypedArray,e),this.constructor.prototype):Object.setPrototypeOf(Pr(this.TypedArray,[e].concat(n)),this.constructor.prototype)}function $r(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Wr.apply(this,t)}function Hr(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Wr.apply(this,t)}function Yr(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Wr.apply(this,t)}function Kr(e){for(var t,n,r=e.buffer,i=e.byteOffset,a=e.length,o=e.signed,u=new Int32Array(r,i,a),s=0,c=0,l=u.length;c<l;)n=u[c++],t=u[c++],o||(t>>>=0),s+=(n>>>0)+t*Math.pow(c,32);return s}function Qr(e){var t="",n=new Uint32Array(2),r=new Uint16Array(e.buffer,e.byteOffset,e.byteLength/2),i=new Uint32Array((r=new Uint16Array(r).reverse()).buffer),a=-1,o=r.length-1;do{for(n[0]=r[a=0];a<o;)r[a++]=n[1]=n[0]/10,n[0]=(n[0]-10*n[1]<<16)+r[a];r[a]=n[1]=n[0]/10,n[0]=n[0]-10*n[1],t="".concat(n[0]).concat(t)}while(i[0]||i[1]||i[2]||i[3]);return t||"0"}Wr.prototype[Vr]=!0,Wr.prototype.toJSON=function(){return'"'.concat(Rr(this),'"')},Wr.prototype.valueOf=function(){return Kr(this)},Wr.prototype.toString=function(){return Rr(this)},Wr.prototype[Symbol.toPrimitive]=function(){switch(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default"){case"number":return Kr(this);case"string":return Rr(this);case"default":return zr(this)}return Rr(this)},Object.setPrototypeOf($r.prototype,Object.create(Int32Array.prototype)),Object.setPrototypeOf(Hr.prototype,Object.create(Uint32Array.prototype)),Object.setPrototypeOf(Yr.prototype,Object.create(Uint32Array.prototype)),Object.assign($r.prototype,Wr.prototype,{constructor:$r,signed:!0,TypedArray:Int32Array,BigIntArray:pe}),Object.assign(Hr.prototype,Wr.prototype,{constructor:Hr,signed:!1,TypedArray:Uint32Array,BigIntArray:ve}),Object.assign(Yr.prototype,Wr.prototype,{constructor:Yr,signed:!0,TypedArray:Uint32Array,BigIntArray:ve}),he?(zr=function(e){return 8===e.byteLength?new e.BigIntArray(e.buffer,e.byteOffset,1)[0]:Qr(e)},Rr=function(e){return 8===e.byteLength?"".concat(new e.BigIntArray(e.buffer,e.byteOffset,1)[0]):Qr(e)}):zr=Rr=Qr;var qr,Gr=function(){function e(t,n){return Object(D.a)(this,e),e.new(t,n)}return Object(C.a)(e,null,[{key:"new",value:function(e,t){switch(t){case!0:return new $r(e);case!1:return new Hr(e)}switch(e.constructor){case Int8Array:case Int16Array:case Int32Array:case pe:return new $r(e)}return 16===e.byteLength?new Yr(e):new Hr(e)}},{key:"signed",value:function(e){return new $r(e)}},{key:"unsigned",value:function(e){return new Hr(e)}},{key:"decimal",value:function(e){return new Yr(e)}}]),e}(),Jr=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"setValue",value:function(e,t){this._values.set(e,t)}}]),t}(kr),Xr=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Jr),Zr=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Jr),ei=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Jr),ti=function(e){function t(e){var n;return Object(D.a)(this,t),e.nullValues&&(e.nullValues=e.nullValues.map(oi)),(n=Object(re.a)(this,Object(ie.a)(t).call(this,e)))._values=new pr(new Int32Array(0),2),n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"isValid",value:function(e){return Ct(Object(ie.a)(t.prototype),"isValid",this).call(this,oi(e))}},{key:"values64",get:function(){return this._values.buffer64}}]),t}(Jr),ni=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Jr),ri=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Jr),ii=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Jr),ai=function(e){function t(e){var n;return Object(D.a)(this,t),e.nullValues&&(e.nullValues=e.nullValues.map(oi)),(n=Object(re.a)(this,Object(ie.a)(t).call(this,e)))._values=new pr(new Uint32Array(0),2),n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"isValid",value:function(e){return Ct(Object(ie.a)(t.prototype),"isValid",this).call(this,oi(e))}},{key:"values64",get:function(){return this._values.buffer64}}]),t}(Jr),oi=(qr={BigIntArray:pe},function(e){return ArrayBuffer.isView(e)&&(qr.buffer=e.buffer,qr.byteOffset=e.byteOffset,qr.byteLength=e.byteLength,e=zr(qr),qr.buffer=null),e}),ui=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(kr),si=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(ui),ci=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(ui),li=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(ui),fi=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(ui),hi=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(kr),di=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(hi),pi=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(hi),yi=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(hi),vi=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(hi),bi=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(kr),mi=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(bi),gi=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(bi);function ki(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"===typeof e)return wi(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return wi(e,t)}(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){u=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(u)throw a}}}}function wi(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var _i=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this,e)))._values=new lr(new Uint8Array(0)),n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"setValue",value:function(e,n){return Ct(Object(ie.a)(t.prototype),"setValue",this).call(this,e,Ve(n))}},{key:"_flushPending",value:function(e,t){var n,r,i=this._offsets,a=this._values.reserve(t).buffer,o=0,u=0,s=0,c=ki(e);try{for(c.s();!(r=c.n()).done;){var l=F(r.value,2);o=l[0],void 0===(n=l[1])?i.set(o,0):(u=n.length,a.set(n,s),i.set(o,u),s+=u)}}catch(f){c.e(f)}finally{c.f()}}},{key:"byteLength",get:function(){var e=this._pendingLength+4*this.length;return this._offsets&&(e+=this._offsets.byteLength),this._values&&(e+=this._values.byteLength),this._nulls&&(e+=this._nulls.byteLength),e}}]),t}(wr),Oi=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this,e)))._values=new lr(new Uint8Array(0)),n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"setValue",value:function(e,n){return Ct(Object(ie.a)(t.prototype),"setValue",this).call(this,e,ne(n))}},{key:"_flushPending",value:function(e,t){}},{key:"byteLength",get:function(){var e=this._pendingLength+4*this.length;return this._offsets&&(e+=this._offsets.byteLength),this._values&&(e+=this._values.byteLength),this._nulls&&(e+=this._nulls.byteLength),e}}]),t}(wr);Oi.prototype._flushPending=_i.prototype._flushPending;var ji=function(){function e(){Object(D.a)(this,e)}return Object(C.a)(e,[{key:"get",value:function(e){return this._values[e]}},{key:"clear",value:function(){return this._values=null,this}},{key:"bind",value:function(e){return e instanceof Rt?e:(this._values=e,this)}},{key:"length",get:function(){return this._values.length}}]),e}(),xi=n(9),Si=n(8);function Ti(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"===typeof e)return Ii(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ii(e,t)}(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){u=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(u)throw a}}}}function Ii(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Ei=Symbol.for("parent"),Ai=Symbol.for("rowIndex"),Bi=Symbol.for("keyToIdx"),Di=Symbol.for("idxToVal"),Ci=Symbol.for("nodejs.util.inspect.custom"),Li=function(){function e(t,n){Object(D.a)(this,e),this[Ei]=t,this.size=n}return Object(C.a)(e,[{key:"entries",value:function(){return this[Symbol.iterator]()}},{key:"has",value:function(e){return void 0!==this.get(e)}},{key:"get",value:function(e){var t=void 0;if(null!==e&&void 0!==e){var n=this[Bi]||(this[Bi]=new Map),r=n.get(e);if(void 0!==r){var i=this[Di]||(this[Di]=new Array(this.size));void 0!==(t=i[r])||(i[r]=t=this.getValue(r))}else if((r=this.getIndex(e))>-1){n.set(e,r);var a=this[Di]||(this[Di]=new Array(this.size));void 0!==(t=a[r])||(a[r]=t=this.getValue(r))}}return t}},{key:"set",value:function(e,t){if(null!==e&&void 0!==e){var n=this[Bi]||(this[Bi]=new Map),r=n.get(e);if(void 0===r&&n.set(e,r=this.getIndex(e)),r>-1)(this[Di]||(this[Di]=new Array(this.size)))[r]=this.setValue(r,t)}return this}},{key:"clear",value:function(){throw new Error("Clearing ".concat(this[Symbol.toStringTag]," not supported."))}},{key:"delete",value:function(e){throw new Error("Deleting ".concat(this[Symbol.toStringTag]," values not supported."))}},{key:Symbol.iterator,value:L.mark(function e(){var t,n,r,i,a,o,u,s,c;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t=this.keys(),n=this.values(),r=this[Bi]||(this[Bi]=new Map),i=this[Di]||(this[Di]=new Array(this.size)),u=0;case 5:if((s=t.next()).done||(c=n.next()).done){e.next=15;break}return a=s.value,o=c.value,i[u]=o,r.has(a)||r.set(a,u),e.next=12,[a,o];case 12:++u,e.next=5;break;case 15:case"end":return e.stop()}},e,this)})},{key:"forEach",value:function(e,t){for(var n,r,i,a,o=this.keys(),u=this.values(),s=void 0===t?e:function(n,r,i){return e.call(t,n,r,i)},c=this[Bi]||(this[Bi]=new Map),l=this[Di]||(this[Di]=new Array(this.size)),f=0;!(i=o.next()).done&&!(a=u.next()).done;++f)n=i.value,r=a.value,l[f]=r,c.has(n)||c.set(n,f),s(r,n,this)}},{key:"toArray",value:function(){return cn(this.values())}},{key:"toJSON",value:function(){var e={};return this.forEach(function(t,n){return e[n]=t}),e}},{key:"inspect",value:function(){return this.toString()}},{key:Ci,value:function(){return this.toString()}},{key:"toString",value:function(){var e=[];return this.forEach(function(t,n){n=ur(n),t=ur(t),e.push("".concat(n,": ").concat(t))}),"{ ".concat(e.join(", ")," }")}}]),e}();Li[Symbol.toStringTag]=function(e){var t;return Object.defineProperties(e,(t={size:{writable:!0,enumerable:!1,configurable:!1,value:0}},Object(Si.a)(t,Ei,{writable:!0,enumerable:!1,configurable:!1,value:null}),Object(Si.a)(t,Ai,{writable:!0,enumerable:!1,configurable:!1,value:-1}),t)),e[Symbol.toStringTag]="Row"}(Li.prototype);var Fi=function(e){function t(e){var n;return Object(D.a)(this,t),n=Object(re.a)(this,Object(ie.a)(t).call(this,e,e.length)),Object(re.a)(n,Pi(Object(xi.a)(Object(xi.a)(n))))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"keys",value:function(){return this[Ei].getChildAt(0)[Symbol.iterator]()}},{key:"values",value:function(){return this[Ei].getChildAt(1)[Symbol.iterator]()}},{key:"getKey",value:function(e){return this[Ei].getChildAt(0).get(e)}},{key:"getIndex",value:function(e){return this[Ei].getChildAt(0).indexOf(e)}},{key:"getValue",value:function(e){return this[Ei].getChildAt(1).get(e)}},{key:"setValue",value:function(e,t){this[Ei].getChildAt(1).set(e,t)}}]),t}(Li),Mi=function(e){function t(e){var n;return Object(D.a)(this,t),n=Object(re.a)(this,Object(ie.a)(t).call(this,e,e.type.children.length)),Object(re.a)(n,Ui(Object(xi.a)(Object(xi.a)(n))))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"keys",value:L.mark(function e(){var t,n,r;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t=Ti(this[Ei].type.children),e.prev=1,t.s();case 3:if((n=t.n()).done){e.next=9;break}return r=n.value,e.next=7,r.name;case 7:e.next=3;break;case 9:e.next=14;break;case 11:e.prev=11,e.t0=e.catch(1),t.e(e.t0);case 14:return e.prev=14,t.f(),e.finish(14);case 17:case"end":return e.stop()}},e,this,[[1,11,14,17]])})},{key:"values",value:L.mark(function e(){var t,n,r;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t=Ti(this[Ei].type.children),e.prev=1,t.s();case 3:if((n=t.n()).done){e.next=9;break}return r=n.value,e.next=7,this[r.name];case 7:e.next=3;break;case 9:e.next=14;break;case 11:e.prev=11,e.t0=e.catch(1),t.e(e.t0);case 14:return e.prev=14,t.f(),e.finish(14);case 17:case"end":return e.stop()}},e,this,[[1,11,14,17]])})},{key:"getKey",value:function(e){return this[Ei].type.children[e].name}},{key:"getIndex",value:function(e){return this[Ei].type.children.findIndex(function(t){return t.name===e})}},{key:"getValue",value:function(e){return this[Ei].getChildAt(e).get(this[Ai])}},{key:"setValue",value:function(e,t){return this[Ei].getChildAt(e).set(this[Ai],t)}}]),t}(Li);Object.setPrototypeOf(Li.prototype,Map.prototype);var Ni,Ui=function(){var e={enumerable:!0,configurable:!1,get:null,set:null};return function(t){var n,r=-1,i=t[Bi]||(t[Bi]=new Map),a=function(e){return function(){return this.get(e)}},o=function(e){return function(t){return this.set(e,t)}},u=Ti(t.keys());try{for(u.s();!(n=u.n()).done;){var s=n.value;i.set(s,++r),e.get=a(s),e.set=o(s),t.hasOwnProperty(s)||(e.enumerable=!0,Object.defineProperty(t,s,e)),t.hasOwnProperty(r)||(e.enumerable=!1,Object.defineProperty(t,r,e))}}catch(c){u.e(c)}finally{u.f()}return e.get=e.set=null,t}}(),Pi=function(){if("undefined"===typeof Proxy)return Ui;var e=Li.prototype.has,t=Li.prototype.get,n=Li.prototype.set,r=Li.prototype.getKey,i={isExtensible:function(){return!1},deleteProperty:function(){return!1},preventExtensions:function(){return!0},ownKeys:function(e){return cn(e.keys()).map(function(e){return"".concat(e)})},has:function(e,t){switch(t){case"getKey":case"getIndex":case"getValue":case"setValue":case"toArray":case"toJSON":case"inspect":case"constructor":case"isPrototypeOf":case"propertyIsEnumerable":case"toString":case"toLocaleString":case"valueOf":case"size":case"has":case"get":case"set":case"clear":case"delete":case"keys":case"values":case"entries":case"forEach":case"__proto__":case"__defineGetter__":case"__defineSetter__":case"hasOwnProperty":case"__lookupGetter__":case"__lookupSetter__":case Symbol.iterator:case Symbol.toStringTag:case Ei:case Ai:case Di:case Bi:case Ci:return!0}return"number"!==typeof t||e.has(t)||(t=e.getKey(t)),e.has(t)},get:function(n,i,a){switch(i){case"getKey":case"getIndex":case"getValue":case"setValue":case"toArray":case"toJSON":case"inspect":case"constructor":case"isPrototypeOf":case"propertyIsEnumerable":case"toString":case"toLocaleString":case"valueOf":case"size":case"has":case"get":case"set":case"clear":case"delete":case"keys":case"values":case"entries":case"forEach":case"__proto__":case"__defineGetter__":case"__defineSetter__":case"hasOwnProperty":case"__lookupGetter__":case"__lookupSetter__":case Symbol.iterator:case Symbol.toStringTag:case Ei:case Ai:case Di:case Bi:case Ci:return Reflect.get(n,i,a)}return"number"!==typeof i||e.call(a,i)||(i=r.call(a,i)),t.call(a,i)},set:function(t,i,a,o){switch(i){case Ei:case Ai:case Di:case Bi:return Reflect.set(t,i,a,o);case"getKey":case"getIndex":case"getValue":case"setValue":case"toArray":case"toJSON":case"inspect":case"constructor":case"isPrototypeOf":case"propertyIsEnumerable":case"toString":case"toLocaleString":case"valueOf":case"size":case"has":case"get":case"set":case"clear":case"delete":case"keys":case"values":case"entries":case"forEach":case"__proto__":case"__defineGetter__":case"__defineSetter__":case"hasOwnProperty":case"__lookupGetter__":case"__lookupSetter__":case Symbol.iterator:case Symbol.toStringTag:return!1}return"number"!==typeof i||e.call(o,i)||(i=r.call(o,i)),!!e.call(o,i)&&!!n.call(o,i,a)}};return function(e){return new Proxy(e,i)}}();function Ri(e,t,n){var r=e.length,i=t>-1?t:r+t%r;return n?n(e,i):i}function zi(e,t,n,r){var i=e.length,a=void 0===i?0:i,o="number"!==typeof t?0:t,u="number"!==typeof n?a:n;return o<0&&(o=(o%a+a)%a),u<0&&(u=(u%a+a)%a),u<o&&(Ni=o,o=u,u=Ni),u>a&&(u=a),r?r(e,o,u):[o,u]}var Vi=he?fe(0):0,Wi=function(e){return e!==e};function $i(e){var t=typeof e;if("object"!==t||null===e)return Wi(e)?Wi:"bigint"!==t?function(t){return t===e}:function(t){return Vi+t===e};if(e instanceof Date){var n=e.valueOf();return function(e){return e instanceof Date&&e.valueOf()===n}}return ArrayBuffer.isView(e)?function(t){return!!t&&mt(e,t)}:e instanceof Map?function(e){var t=-1,n=[];return e.forEach(function(e){return n[++t]=$i(e)}),Hi(n)}(e):Array.isArray(e)?function(e){for(var t=[],n=-1,r=e.length;++n<r;)t[n]=$i(e[n]);return Hi(t)}(e):e instanceof Rt?function(e){for(var t=[],n=-1,r=e.length;++n<r;)t[n]=$i(e.get(n));return Hi(t)}(e):function(e){var t=Object.keys(e);if(0===t.length)return function(){return!1};for(var n=[],r=-1,i=t.length;++r<i;)n[r]=$i(e[t[r]]);return Hi(n,t)}(e)}function Hi(e,t){return function(n){if(!n||"object"!==typeof n)return!1;switch(n.constructor){case Array:return function(e,t){var n=e.length;if(t.length!==n)return!1;for(var r=-1;++r<n;)if(!e[r](t[r]))return!1;return!0}(e,n);case Map:case Fi:case Mi:return Yi(e,n,n.keys());case Object:case void 0:return Yi(e,n,t||Object.keys(n))}return n instanceof Rt&&function(e,t){var n=e.length;if(t.length!==n)return!1;for(var r=-1;++r<n;)if(!e[r](t.get(r)))return!1;return!0}(e,n)}}function Yi(e,t,n){for(var r=n[Symbol.iterator](),i=t instanceof Map?t.keys():Object.keys(t)[Symbol.iterator](),a=t instanceof Map?t.values():Object.values(t)[Symbol.iterator](),o=0,u=e.length,s=a.next(),c=r.next(),l=i.next();o<u&&!c.done&&!l.done&&!s.done&&(c.value===l.value&&e[o](s.value));++o,c=r.next(),l=i.next(),s=a.next());return!!(o===u&&c.done&&l.done&&s.done)||(r.return&&r.return(),i.return&&i.return(),a.return&&a.return(),!1)}function Ki(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"===typeof e)return Qi(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Qi(e,t)}(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){u=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(u)throw a}}}}function Qi(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var qi=function(e){function t(e){var n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(e){for(var t=new Uint32Array((e||[]).length+1),n=t[0]=0,r=t.length,i=0;++i<r;)t[i]=n+=e[i-1].length;return t}(r);return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this)))._nullCount=-1,n._type=e,n._chunks=r,n._chunkOffsets=i,n._length=i[i.length-1],n._numChildren=(n._type.children||[]).length,n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:Symbol.iterator,value:L.mark(function e(){var t,n,r;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t=Ki(this._chunks),e.prev=1,t.s();case 3:if((n=t.n()).done){e.next=8;break}return r=n.value,e.delegateYield(r,"t0",6);case 6:e.next=3;break;case 8:e.next=13;break;case 10:e.prev=10,e.t1=e.catch(1),t.e(e.t1);case 13:return e.prev=13,t.f(),e.finish(13);case 16:case"end":return e.stop()}},e,this,[[1,10,13,16]])})},{key:"clone",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this._chunks;return new t(this._type,e)}},{key:"concat",value:function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return this.clone(t.flatten.apply(t,[this].concat(n)))}},{key:"slice",value:function(e,t){return zi(this,e,t,this._sliceInternal)}},{key:"getChildAt",value:function(e){if(e<0||e>=this._numChildren)return null;var n,r,i,a=this._children||(this._children=[]);return(n=a[e])?n:(r=(this._type.children||[])[e])&&(i=this._chunks.map(function(t){return t.getChildAt(e)}).filter(function(e){return null!=e})).length>0?a[e]=new t(r.type,i):null}},{key:"search",value:function(e,t){var n=e,r=this._chunkOffsets,i=r.length-1;if(n<0)return null;if(n>=r[i])return null;if(i<=1)return t?t(this,0,n):[0,n];var a=0,o=0,u=0;do{if(a+1===i)return t?t(this,a,n-o):[a,n-o];n>=r[u=a+(i-a)/2|0]?a=u:i=u}while(n<r[i]&&n>=(o=r[a]));return null}},{key:"isValid",value:function(e){return!!this.search(e,this.isValidInternal)}},{key:"get",value:function(e){return this.search(e,this.getInternal)}},{key:"set",value:function(e,t){this.search(e,function(e,n,r){return e.chunks[n].set(r,t)})}},{key:"indexOf",value:function(e,t){var n=this;return t&&"number"===typeof t?this.search(t,function(t,r,i){return n.indexOfInternal(t,r,i,e)}):this.indexOfInternal(this,0,Math.max(0,t||0),e)}},{key:"toArray",value:function(){var e=this.chunks,t=e.length,n=this._type.ArrayType;if(t<=0)return new n(0);if(t<=1)return e[0].toArray();for(var r=0,i=new Array(t),a=-1;++a<t;)r+=(i[a]=e[a].toArray()).length;n!==i[0].constructor&&(n=i[0].constructor);for(var o=new n(r),u=n===Array?Ji:Gi,s=-1,c=0;++s<t;)c=u(i[s],o,c);return o}},{key:"getInternal",value:function(e,t,n){return e._chunks[t].get(n)}},{key:"isValidInternal",value:function(e,t,n){return e._chunks[t].isValid(n)}},{key:"indexOfInternal",value:function(e,t,n,r){for(var i=e._chunks,a=t-1,o=i.length,u=n,s=0,c=-1;++a<o;){if(~(c=i[a].indexOf(r,u)))return s+c;u=0,s+=i[a].length}return-1}},{key:"_sliceInternal",value:function(e,t,n){for(var r=[],i=e.chunks,a=e._chunkOffsets,o=-1,u=i.length;++o<u;){var s=i[o],c=s.length,l=a[o];if(l>=n)break;if(!(t>=l+c))if(l>=t&&l+c<=n)r.push(s);else{var f=Math.max(0,t-l),h=Math.min(n-l,c);r.push(s.slice(f,h))}}return e.clone(r)}},{key:"type",get:function(){return this._type}},{key:"length",get:function(){return this._length}},{key:"chunks",get:function(){return this._chunks}},{key:"typeId",get:function(){return this._type.typeId}},{key:"VectorName",get:function(){return"Chunked<".concat(this._type,">")}},{key:"data",get:function(){return this._chunks[0]?this._chunks[0].data:null}},{key:"ArrayType",get:function(){return this._type.ArrayType}},{key:"numChildren",get:function(){return this._numChildren}},{key:"stride",get:function(){return this._chunks[0]?this._chunks[0].stride:1}},{key:"byteLength",get:function(){return this._chunks.reduce(function(e,t){return e+t.byteLength},0)}},{key:"nullCount",get:function(){var e=this._nullCount;return e<0&&(this._nullCount=e=this._chunks.reduce(function(e,t){return e+t.nullCount},0)),e}},{key:"indices",get:function(){if(jn.isDictionary(this._type)){if(!this._indices){var e=this._chunks;this._indices=1===e.length?e[0].indices:t.concat.apply(t,cn(e.map(function(e){return e.indices})))}return this._indices}return null}},{key:"dictionary",get:function(){return jn.isDictionary(this._type)?this._chunks[this._chunks.length-1].data.dictionary:null}}],[{key:"flatten",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return ra(Rt,t)}},{key:"concat",value:function(){var e=t.flatten.apply(t,arguments);return new t(e[0].type,e)}}]),t}(Rt);var Gi=function(e,t,n){return t.set(e,n),n+e.length},Ji=function(e,t,n){for(var r=n,i=-1,a=e.length;++i<a;)t[r++]=e[i];return r},Xi=function(e){function t(e){var n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],i=arguments.length>2?arguments[2]:void 0;return Object(D.a)(this,t),r=qi.flatten.apply(qi,cn(r)),(n=Object(re.a)(this,Object(ie.a)(t).call(this,e.type,r,i)))._field=e,1!==r.length||Object(xi.a)(Object(xi.a)(n))instanceof Zi?n:Object(re.a)(n,new Zi(e,r[0],n._chunkOffsets))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"clone",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this._chunks;return new t(this._field,e)}},{key:"getChildAt",value:function(e){if(e<0||e>=this.numChildren)return null;var n,r,i,a=this._children||(this._children=[]);return(n=a[e])?n:(r=(this.type.children||[])[e])&&(i=this._chunks.map(function(t){return t.getChildAt(e)}).filter(function(e){return null!=e})).length>0?a[e]=new t(r,i):null}},{key:"field",get:function(){return this._field}},{key:"name",get:function(){return this._field.name}},{key:"nullable",get:function(){return this._field.nullable}},{key:"metadata",get:function(){return this._field.metadata}}],[{key:"new",value:function(e,n){for(var r=arguments.length,i=new Array(r>2?r-2:0),a=2;a<r;a++)i[a-2]=arguments[a];var o=qi.flatten(Array.isArray(n)?[].concat(cn(n),i):n instanceof Rt?[n].concat(i):[Rt.new.apply(Rt,[n].concat(i))]);if("string"===typeof e){var u=o[0].data.type;e=new ca(e,u,!0)}else!e.nullable&&o.some(function(e){return e.nullCount>0})&&(e=e.clone({nullable:!0}));return new t(e,o)}}]),t}(qi),Zi=function(e){function t(e,n,r){var i;return Object(D.a)(this,t),(i=Object(re.a)(this,Object(ie.a)(t).call(this,e,[n],r)))._chunk=n,i}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"search",value:function(e,t){return t?t(this,0,e):[0,e]}},{key:"isValid",value:function(e){return this._chunk.isValid(e)}},{key:"get",value:function(e){return this._chunk.get(e)}},{key:"set",value:function(e,t){this._chunk.set(e,t)}},{key:"indexOf",value:function(e,t){return this._chunk.indexOf(e,t)}}]),t}(Xi),ea=Array.isArray,ta=function(e,t){return aa(e,t,[],0)},na=function(e){return ua(e,[[],[]])},ra=function(e,t){return function e(t,n,r,i){var a,o=i;var u=-1,s=n.length;for(;++u<s;)ea(a=n[u])?o=e(t,a,r,o).length:a instanceof qi?o=e(t,a.chunks,r,o).length:a instanceof t&&(r[o++]=a);return r}(e,t,[],0)},ia=function(e,t){return function e(t,n,r,i){var a,o=i;var u=-1,s=n.length;for(;++u<s;)ea(a=n[u])?o=e(t,a,r,o).length:a instanceof t?o=aa(Rt,a.schema.fields.map(function(e,t){return a.getChildAt(t)}),r,o).length:a instanceof Rt&&(r[o++]=a);return r}(e,t,[],0)};function aa(e,t,n,r){for(var i,a=r,o=-1,u=t.length;++o<u;)ea(i=t[o])?a=aa(e,i,n,a).length:i instanceof e&&(n[a++]=i);return n}var oa=function(e,t,n){var r=F(t,2),i=r[0],a=r[1];return e[0][n]=i,e[1][n]=a,e};function ua(e,t){var n,r;switch(r=e.length){case 0:return t;case 1:if(n=t[0],!e[0])return t;if(ea(e[0]))return ua(e[0],t);if(!(e[0]instanceof ar||e[0]instanceof Rt||e[0]instanceof jn)){var i=F(Object.entries(e[0]).reduce(oa,t),2);n=i[0],e=i[1]}break;default:ea(n=e[r-1])?e=ea(e[0])?e[0]:e.slice(0,r-1):(e=ea(e[0])?e[0]:e,n=[])}for(var a,o,u=-1,s=-1,c=-1,l=e.length,f=F(t,2),h=f[0],d=f[1];++c<l;)if((o=e[c])instanceof Xi&&(d[++s]=o))h[++u]=o.field.clone(n[c],o.type,!0);else{var p=n[c];a=void 0===p?c:p,o instanceof jn&&(d[++s]=o)?h[++u]=ca.new(a,o,!0):o&&o.type&&(d[++s]=o)&&(o instanceof ar&&(d[s]=o=Rt.new(o)),h[++u]=ca.new(a,o.type,!0))}return t}var sa=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1?arguments[1]:void 0,r=arguments.length>2?arguments[2]:void 0;Object(D.a)(this,e),this.fields=t||[],this.metadata=n||new Map,r||(r=fa(t)),this.dictionaries=r}return Object(C.a)(e,[{key:"toString",value:function(){return"Schema<{ ".concat(this.fields.map(function(e,t){return"".concat(t,": ").concat(e)}).join(", ")," }>")}},{key:"compareTo",value:function(e){return On.compareSchemas(this,e)}},{key:"select",value:function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];var i=n.reduce(function(e,t){return(e[t]=!0)&&e},Object.create(null));return new e(this.fields.filter(function(e){return i[e.name]}),this.metadata)}},{key:"selectAt",value:function(){for(var t=this,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return new e(r.map(function(e){return t.fields[e]}).filter(Boolean),this.metadata)}},{key:"assign",value:function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];var i=n[0]instanceof e?n[0]:new e(ta(ca,n)),a=cn(this.fields),o=la(la(new Map,this.metadata),i.metadata),u=i.fields.filter(function(e){var t=a.findIndex(function(t){return t.name===e.name});return!~t||(a[t]=e.clone({metadata:la(la(new Map,a[t].metadata),e.metadata)}))&&!1}),s=fa(u,new Map);return new e([].concat(cn(a),cn(u)),o,new Map([].concat(cn(this.dictionaries),cn(s))))}},{key:Symbol.toStringTag,get:function(){return"Schema"}}],[{key:"from",value:function(){return e.new(arguments.length<=0?void 0:arguments[0],arguments.length<=1?void 0:arguments[1])}},{key:"new",value:function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return new e(na(n)[0])}}]),e}(),ca=function(){function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3?arguments[3]:void 0;Object(D.a)(this,e),this.name=t,this.type=n,this.nullable=r,this.metadata=i||new Map}return Object(C.a)(e,[{key:"toString",value:function(){return"".concat(this.name,": ").concat(this.type)}},{key:"compareTo",value:function(e){return On.compareField(this,e)}},{key:"clone",value:function(){for(var t,n,r,i,a,o,u,s,c,l,f=arguments.length,h=new Array(f),d=0;d<f;d++)h[d]=arguments[d];var p=h[0],y=h[1],v=h[2],b=h[3];return h[0]&&"object"===typeof h[0]?(p=void 0===(u=(o=h[0]).name)?this.name:u,y=void 0===(s=o.type)?this.type:s,v=void 0===(c=o.nullable)?this.nullable:c,b=void 0===(l=o.metadata)?this.metadata:l):(p=void 0===(n=(t=F(h,4))[0])?this.name:n,y=void 0===(r=t[1])?this.type:r,v=void 0===(i=t[2])?this.nullable:i,b=void 0===(a=t[3])?this.metadata:a),e.new(p,y,v,b)}},{key:"typeId",get:function(){return this.type.typeId}},{key:Symbol.toStringTag,get:function(){return"Field"}}],[{key:"new",value:function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];var i=n[0],a=n[1],o=n[2],u=n[3];return n[0]&&"object"===typeof n[0]&&(i=n[0].name,void 0===a&&(a=n[0].type),void 0===o&&(o=n[0].nullable),void 0===u&&(u=n[0].metadata)),new e("".concat(i),a,o,u)}}]),e}();function la(e,t){return new Map([].concat(cn(e||new Map),cn(t||new Map)))}function fa(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Map,n=-1,r=e.length;++n<r;){var i=e[n].type;if(jn.isDictionary(i))if(t.has(i.id)){if(t.get(i.id)!==i.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}else t.set(i.id,i.dictionary);i.children&&i.children.length>0&&fa(i.children,t)}return t}function ha(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"===typeof e)return da(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return da(e,t)}(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){u=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(u)throw a}}}}function da(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}sa.prototype.fields=null,sa.prototype.metadata=null,sa.prototype.dictionaries=null,ca.prototype.type=null,ca.prototype.name=null,ca.prototype.nullable=null,ca.prototype.metadata=null;var pa=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this,e)))._run=new ji,n._offsets=new dr,n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"addChild",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"0";if(this.numChildren>0)throw new Error("ListBuilder can only have one child.");return this.children[this.numChildren]=e,this.type=new qn(new ca(t,e.type,!0)),this.numChildren-1}},{key:"clear",value:function(){return this._run.clear(),Ct(Object(ie.a)(t.prototype),"clear",this).call(this)}},{key:"_flushPending",value:function(e){var t,n,r=this._run,i=this._offsets,a=this._setValue,o=0,u=ha(e);try{for(u.s();!(n=u.n()).done;){var s=F(n.value,2);o=s[0],void 0===(t=s[1])?i.set(o,0):(i.set(o,t.length),a(this,o,r.bind(t)))}}catch(c){u.e(c)}finally{u.f()}}}]),t}(wr),ya=function(e){function t(){var e;return Object(D.a)(this,t),(e=Object(re.a)(this,Object(ie.a)(t).apply(this,arguments)))._run=new ji,e}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"setValue",value:function(e,n){Ct(Object(ie.a)(t.prototype),"setValue",this).call(this,e,this._run.bind(n))}},{key:"addChild",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"0";if(this.numChildren>0)throw new Error("FixedSizeListBuilder can only have one child.");var n=this.children.push(e);return this.type=new Zn(this.type.listSize,new ca(t,e.type,!0)),n}},{key:"clear",value:function(){return this._run.clear(),Ct(Object(ie.a)(t.prototype),"clear",this).call(this)}}]),t}(gr),va=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"set",value:function(e,n){return Ct(Object(ie.a)(t.prototype),"set",this).call(this,e,n)}},{key:"setValue",value:function(e,t){t=t instanceof Map?t:new Map(Object.entries(t));var n=this._pending||(this._pending=new Map),r=n.get(e);r&&(this._pendingLength-=r.size),this._pendingLength+=t.size,n.set(e,t)}},{key:"addChild",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"".concat(this.numChildren);if(this.numChildren>0)throw new Error("ListBuilder can only have one child.");return this.children[this.numChildren]=e,this.type=new er(new ca(t,e.type,!0),this.type.keysSorted),this.numChildren-1}},{key:"_flushPending",value:function(e){var t=this,n=this._offsets,r=this._setValue;e.forEach(function(e,i){void 0===e?n.set(i,0):(n.set(i,e.size),r(t,i,e))})}}]),t}(wr),ba=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"addChild",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"".concat(this.numChildren),n=this.children.push(e);return this.type=new Gn([].concat(cn(this.type.children),[new ca(t,e.type,!0)])),n}}]),t}(gr),ma=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this,e)))._typeIds=new fr(new Int8Array(0),1),"function"===typeof e.valueToChildTypeId&&(n._valueToChildTypeId=e.valueToChildTypeId),n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"append",value:function(e,t){return this.set(this.length,e,t)}},{key:"set",value:function(e,t,n){return void 0===n&&(n=this._valueToChildTypeId(this,t,e)),this.setValid(e,this.isValid(t))&&this.setValue(e,t,n),this}},{key:"setValue",value:function(e,n,r){this._typeIds.set(e,r),Ct(Object(ie.a)(t.prototype),"setValue",this).call(this,e,n)}},{key:"addChild",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"".concat(this.children.length),n=this.children.push(e),r=this.type,i=r.children,a=r.mode,o=r.typeIds,u=[].concat(cn(i),[new ca(t,e.type)]);return this.type=new Jn(a,[].concat(cn(o),[n]),u),n}},{key:"_valueToChildTypeId",value:function(e,t,n){throw new Error("Cannot map UnionBuilder value to child typeId. Pass the `childTypeId` as the second argument to unionBuilder.append(), or supply a `valueToChildTypeId` function as part of the UnionBuilder constructor options.")}},{key:"typeIdToChildIndex",get:function(){return this.type.typeIdToChildIndex}}]),t}(gr),ga=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(ma),ka=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this,e)))._offsets=new fr(new Int32Array(0)),n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"setValue",value:function(e,n,r){var i=this.type.typeIdToChildIndex[r];return this._offsets.set(e,this.getChildAt(i).length),Ct(Object(ie.a)(t.prototype),"setValue",this).call(this,e,n,r)}}]),t}(ma),wa=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(ln),_a=function(e,t,n){e[t]=n%4294967296|0,e[t+1]=n/4294967296|0},Oa=function(e,t,n,r){var i=t[n],a=t[n+1];null!=i&&null!=a&&e.set(r.subarray(0,a-i),i)},ja=function(e,t,n){!function(e,t,n){e[t]=n/864e5|0}(e.values,t,n.valueOf())},xa=function(e,t,n){var r=e.values;_a(r,2*t,n.valueOf())},Sa=function(e,t,n){var r=e.stride;e.values[r*t]=n},Ta=function(e,t,n){var r=e.stride;e.values[r*t]=Cr(n)},Ia=function(e,t,n){switch(typeof n){case"bigint":e.values64[t]=n;break;case"number":e.values[t*e.stride]=n;break;default:var r=n,i=e.stride,a=Ne(e.ArrayType,r);e.values.set(a.subarray(0,i),i*t)}},Ea=function(e,t,n){var r=e.values;return _a(r,2*t,n/1e3)},Aa=function(e,t,n){var r=e.values;return _a(r,2*t,n)},Ba=function(e,t,n){return function(e,t,n){e[t]=1e3*n%4294967296|0,e[t+1]=1e3*n/4294967296|0}(e.values,2*t,n)},Da=function(e,t,n){return function(e,t,n){e[t]=1e6*n%4294967296|0,e[t+1]=1e6*n/4294967296|0}(e.values,2*t,n)},Ca=function(e,t,n){e.values[e.stride*t]=n},La=function(e,t,n){e.values[e.stride*t]=n},Fa=function(e,t,n){e.values.set(n.subarray(0,2),2*t)},Ma=function(e,t,n){e.values.set(n.subarray(0,2),2*t)},Na=function(e,t,n){var r=e.typeIdToChildIndex[e.typeIds[t]],i=e.getChildAt(r);i&&i.set(e.valueOffsets[t],n)},Ua=function(e,t,n){var r=e.typeIdToChildIndex[e.typeIds[t]],i=e.getChildAt(r);i&&i.set(t,n)},Pa=function(e,t,n){e.values.set(n.subarray(0,2),2*t)},Ra=function(e,t,n){e.values[t]=12*n[0]+n[1]%12};wa.prototype.visitBool=function(e,t,n){var r=e.offset,i=e.values,a=r+t;n?i[a>>3]|=1<<a%8:i[a>>3]&=~(1<<a%8)},wa.prototype.visitInt=function(e,t,n){e.type.bitWidth<64?Sa(e,t,n):Ia(e,t,n)},wa.prototype.visitInt8=Sa,wa.prototype.visitInt16=Sa,wa.prototype.visitInt32=Sa,wa.prototype.visitInt64=Ia,wa.prototype.visitUint8=Sa,wa.prototype.visitUint16=Sa,wa.prototype.visitUint32=Sa,wa.prototype.visitUint64=Ia,wa.prototype.visitFloat=function(e,t,n){e.type.precision!==Ht.HALF?Sa(e,t,n):Ta(e,t,n)},wa.prototype.visitFloat16=Ta,wa.prototype.visitFloat32=Sa,wa.prototype.visitFloat64=Sa,wa.prototype.visitUtf8=function(e,t,n){var r=e.values,i=e.valueOffsets;Oa(r,i,t,ne(n))},wa.prototype.visitBinary=function(e,t,n){var r=e.values,i=e.valueOffsets;return Oa(r,i,t,n)},wa.prototype.visitFixedSizeBinary=function(e,t,n){var r=e.stride;e.values.set(n.subarray(0,r),r*t)},wa.prototype.visitDate=function(e,t,n){e.type.unit===Wt.DAY?ja(e,t,n):xa(e,t,n)},wa.prototype.visitDateDay=ja,wa.prototype.visitDateMillisecond=xa,wa.prototype.visitTimestamp=function(e,t,n){switch(e.type.unit){case $t.SECOND:return Ea(e,t,n);case $t.MILLISECOND:return Aa(e,t,n);case $t.MICROSECOND:return Ba(e,t,n);case $t.NANOSECOND:return Da(e,t,n)}},wa.prototype.visitTimestampSecond=Ea,wa.prototype.visitTimestampMillisecond=Aa,wa.prototype.visitTimestampMicrosecond=Ba,wa.prototype.visitTimestampNanosecond=Da,wa.prototype.visitTime=function(e,t,n){switch(e.type.unit){case $t.SECOND:return Ca(e,t,n);case $t.MILLISECOND:return La(e,t,n);case $t.MICROSECOND:return Fa(e,t,n);case $t.NANOSECOND:return Ma(e,t,n)}},wa.prototype.visitTimeSecond=Ca,wa.prototype.visitTimeMillisecond=La,wa.prototype.visitTimeMicrosecond=Fa,wa.prototype.visitTimeNanosecond=Ma,wa.prototype.visitDecimal=function(e,t,n){e.values.set(n.subarray(0,4),4*t)},wa.prototype.visitList=function(e,t,n){for(var r=e.getChildAt(0),i=e.valueOffsets,a=-1,o=i[t],u=i[t+1];o<u;)r.set(o++,n.get(++a))},wa.prototype.visitStruct=function(e,t,n){var r,i,a=n instanceof Map?(r=t,i=n,function(e,t,n){return e&&e.set(r,i.get(t.name))}):n instanceof Rt?function(e,t){return function(n,r,i){return n&&n.set(e,t.get(i))}}(t,n):Array.isArray(n)?function(e,t){return function(n,r,i){return n&&n.set(e,t[i])}}(t,n):function(e,t){return function(n,r,i){return n&&n.set(e,t[r.name])}}(t,n);e.type.children.forEach(function(t,n){return a(e.getChildAt(n),t,n)})},wa.prototype.visitUnion=function(e,t,n){e.type.mode===Yt.Dense?Na(e,t,n):Ua(e,t,n)},wa.prototype.visitDenseUnion=Na,wa.prototype.visitSparseUnion=Ua,wa.prototype.visitDictionary=function(e,t,n){var r=e.getKey(t);null!==r&&e.setValue(r,n)},wa.prototype.visitInterval=function(e,t,n){e.type.unit===Kt.DAY_TIME?Pa(e,t,n):Ra(e,t,n)},wa.prototype.visitIntervalDayTime=Pa,wa.prototype.visitIntervalYearMonth=Ra,wa.prototype.visitFixedSizeList=function(e,t,n){for(var r=e.getChildAt(0),i=e.stride,a=-1,o=t*i;++a<i;)r.set(o+a,n.get(a))},wa.prototype.visitMap=function(e,t,n){for(var r=e.getChildAt(0),i=e.valueOffsets,a=n instanceof Map?cn(n):Object.entries(n),o=-1,u=i[t],s=i[t+1];u<s;)r.set(u++,a[++o])};var za,Va=new wa,Wa=new(function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"visitNull",value:function(){return Or}},{key:"visitBool",value:function(){return _r}},{key:"visitInt",value:function(){return Jr}},{key:"visitInt8",value:function(){return Xr}},{key:"visitInt16",value:function(){return Zr}},{key:"visitInt32",value:function(){return ei}},{key:"visitInt64",value:function(){return ti}},{key:"visitUint8",value:function(){return ni}},{key:"visitUint16",value:function(){return ri}},{key:"visitUint32",value:function(){return ii}},{key:"visitUint64",value:function(){return ai}},{key:"visitFloat",value:function(){return Lr}},{key:"visitFloat16",value:function(){return Fr}},{key:"visitFloat32",value:function(){return Mr}},{key:"visitFloat64",value:function(){return Nr}},{key:"visitUtf8",value:function(){return Oi}},{key:"visitBinary",value:function(){return _i}},{key:"visitFixedSizeBinary",value:function(){return Er}},{key:"visitDate",value:function(){return jr}},{key:"visitDateDay",value:function(){return xr}},{key:"visitDateMillisecond",value:function(){return Sr}},{key:"visitTimestamp",value:function(){return hi}},{key:"visitTimestampSecond",value:function(){return di}},{key:"visitTimestampMillisecond",value:function(){return pi}},{key:"visitTimestampMicrosecond",value:function(){return yi}},{key:"visitTimestampNanosecond",value:function(){return vi}},{key:"visitTime",value:function(){return ui}},{key:"visitTimeSecond",value:function(){return si}},{key:"visitTimeMillisecond",value:function(){return ci}},{key:"visitTimeMicrosecond",value:function(){return li}},{key:"visitTimeNanosecond",value:function(){return fi}},{key:"visitDecimal",value:function(){return Tr}},{key:"visitList",value:function(){return pa}},{key:"visitStruct",value:function(){return ba}},{key:"visitUnion",value:function(){return ma}},{key:"visitDenseUnion",value:function(){return ka}},{key:"visitSparseUnion",value:function(){return ga}},{key:"visitDictionary",value:function(){return Ir}},{key:"visitInterval",value:function(){return bi}},{key:"visitIntervalDayTime",value:function(){return mi}},{key:"visitIntervalYearMonth",value:function(){return gi}},{key:"visitFixedSizeList",value:function(){return ya}},{key:"visitMap",value:function(){return va}}]),t}(ln));gr.new=function e(t){var n=t.type;var r=new(Wa.getVisitFn(n)())(t);if(n.children&&n.children.length>0){var i=t.children||[],a={nullValues:t.nullValues},o=Array.isArray(i)?function(e,t){return i[t]||a}:function(e){var t=e.name;return i[t]||a};n.children.forEach(function(t,n){var i=t.type,a=o(t,n);r.children.push(e(Object(Dt.a)({},a,{type:i})))})}return r},Object.keys(zt).map(function(e){return zt[e]}).filter(function(e){return"number"===typeof e&&e!==zt.NONE}).forEach(function(e){Wa.visit(e).prototype._setValue=Va.getVisitFn(e)}),Oi.prototype._setValue=Va.visitBinary,function(e){!function(t){!function(t){!function(t){var n=function(){function t(){Object(D.a)(this,t),this.bb=null,this.bb_pos=0}return Object(C.a)(t,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"version",value:function(){var e=this.bb.__offset(this.bb_pos,4);return e?this.bb.readInt16(this.bb_pos+e):Lt.apache.arrow.flatbuf.MetadataVersion.V1}},{key:"schema",value:function(e){var t=this.bb.__offset(this.bb_pos,6);return t?(e||new Lt.apache.arrow.flatbuf.Schema).__init(this.bb.__indirect(this.bb_pos+t),this.bb):null}},{key:"dictionaries",value:function(t,n){var r=this.bb.__offset(this.bb_pos,8);return r?(n||new e.apache.arrow.flatbuf.Block).__init(this.bb.__vector(this.bb_pos+r)+24*t,this.bb):null}},{key:"dictionariesLength",value:function(){var e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__vector_len(this.bb_pos+e):0}},{key:"recordBatches",value:function(t,n){var r=this.bb.__offset(this.bb_pos,10);return r?(n||new e.apache.arrow.flatbuf.Block).__init(this.bb.__vector(this.bb_pos+r)+24*t,this.bb):null}},{key:"recordBatchesLength",value:function(){var e=this.bb.__offset(this.bb_pos,10);return e?this.bb.__vector_len(this.bb_pos+e):0}}],[{key:"getRootAsFooter",value:function(e,n){return(n||new t).__init(e.readInt32(e.position())+e.position(),e)}},{key:"startFooter",value:function(e){e.startObject(4)}},{key:"addVersion",value:function(e,t){e.addFieldInt16(0,t,Lt.apache.arrow.flatbuf.MetadataVersion.V1)}},{key:"addSchema",value:function(e,t){e.addFieldOffset(1,t,0)}},{key:"addDictionaries",value:function(e,t){e.addFieldOffset(2,t,0)}},{key:"startDictionariesVector",value:function(e,t){e.startVector(24,t,8)}},{key:"addRecordBatches",value:function(e,t){e.addFieldOffset(3,t,0)}},{key:"startRecordBatchesVector",value:function(e,t){e.startVector(24,t,8)}},{key:"endFooter",value:function(e){return e.endObject()}},{key:"finishFooterBuffer",value:function(e,t){e.finish(t)}},{key:"createFooter",value:function(e,n,r,i,a){return t.startFooter(e),t.addVersion(e,n),t.addSchema(e,r),t.addDictionaries(e,i),t.addRecordBatches(e,a),t.endFooter(e)}}]),t}();t.Footer=n}(t.flatbuf||(t.flatbuf={}))}(t.arrow||(t.arrow={}))}(e.apache||(e.apache={}))}(za||(za={})),function(e){!function(e){!function(e){!function(e){var t=function(){function e(){Object(D.a)(this,e),this.bb=null,this.bb_pos=0}return Object(C.a)(e,[{key:"__init",value:function(e,t){return this.bb_pos=e,this.bb=t,this}},{key:"offset",value:function(){return this.bb.readInt64(this.bb_pos)}},{key:"metaDataLength",value:function(){return this.bb.readInt32(this.bb_pos+8)}},{key:"bodyLength",value:function(){return this.bb.readInt64(this.bb_pos+16)}}],[{key:"createBlock",value:function(e,t,n,r){return e.prep(8,24),e.writeInt64(r),e.pad(4),e.writeInt32(n),e.writeInt64(t),e.offset()}}]),e}();e.Block=t}(e.flatbuf||(e.flatbuf={}))}(e.arrow||(e.arrow={}))}(e.apache||(e.apache={}))}(za||(za={}));var $a=z.Long,Ha=z.Builder,Ya=z.ByteBuffer,Ka=za.apache.arrow.flatbuf.Block,Qa=za.apache.arrow.flatbuf.Footer,qa=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:qt.V4,r=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0;Object(D.a)(this,e),this.schema=t,this.version=n,r&&(this._recordBatches=r),i&&(this._dictionaryBatches=i)}return Object(C.a)(e,[{key:"recordBatches",value:L.mark(function e(){var t,n,r;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:n=-1,r=this.numRecordBatches;case 1:if(!(++n<r)){e.next=7;break}if(!(t=this.getRecordBatch(n))){e.next=5;break}return e.next=5,t;case 5:e.next=1;break;case 7:case"end":return e.stop()}},e,this)})},{key:"dictionaryBatches",value:L.mark(function e(){var t,n,r;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:n=-1,r=this.numDictionaries;case 1:if(!(++n<r)){e.next=7;break}if(!(t=this.getDictionaryBatch(n))){e.next=5;break}return e.next=5,t;case 5:e.next=1;break;case 7:case"end":return e.stop()}},e,this)})},{key:"getRecordBatch",value:function(e){return e>=0&&e<this.numRecordBatches&&this._recordBatches[e]||null}},{key:"getDictionaryBatch",value:function(e){return e>=0&&e<this.numDictionaries&&this._dictionaryBatches[e]||null}},{key:"numRecordBatches",get:function(){return this._recordBatches.length}},{key:"numDictionaries",get:function(){return this._dictionaryBatches.length}}],[{key:"decode",value:function(e){e=new Ya(Ve(e));var t=Qa.getRootAsFooter(e),n=sa.decode(t.schema());return new Ga(n,t)}},{key:"encode",value:function(e){var t=new Ha,n=sa.encode(t,e.schema);Qa.startRecordBatchesVector(t,e.numRecordBatches),cn(e.recordBatches()).slice().reverse().forEach(function(e){return Ja.encode(t,e)});var r=t.endVector();Qa.startDictionariesVector(t,e.numDictionaries),cn(e.dictionaryBatches()).slice().reverse().forEach(function(e){return Ja.encode(t,e)});var i=t.endVector();return Qa.startFooter(t),Qa.addSchema(t,n),Qa.addVersion(t,qt.V4),Qa.addRecordBatches(t,r),Qa.addDictionaries(t,i),Qa.finishFooterBuffer(t,Qa.endFooter(t)),t.asUint8Array()}}]),e}(),Ga=function(e){function t(e,n){var r;return Object(D.a)(this,t),(r=Object(re.a)(this,Object(ie.a)(t).call(this,e,n.version())))._footer=n,r}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"getRecordBatch",value:function(e){if(e>=0&&e<this.numRecordBatches){var t=this._footer.recordBatches(e);if(t)return Ja.decode(t)}return null}},{key:"getDictionaryBatch",value:function(e){if(e>=0&&e<this.numDictionaries){var t=this._footer.dictionaries(e);if(t)return Ja.decode(t)}return null}},{key:"numRecordBatches",get:function(){return this._footer.recordBatchesLength()}},{key:"numDictionaries",get:function(){return this._footer.dictionariesLength()}}]),t}(qa),Ja=function(){function e(t,n,r){Object(D.a)(this,e),this.metaDataLength=t,this.offset="number"===typeof r?r:r.low,this.bodyLength="number"===typeof n?n:n.low}return Object(C.a)(e,null,[{key:"decode",value:function(t){return new e(t.metaDataLength(),t.bodyLength(),t.offset())}},{key:"encode",value:function(e,t){var n=t.metaDataLength,r=new $a(t.offset,0),i=new $a(t.bodyLength,0);return Ka.createBlock(e,r,n,i)}}]),e}();function Xa(e){var t,n,r,i=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);i--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new Za(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function Za(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:t}})}return(Za=function(e){this.s=e,this.n=e.next}).prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new Za(e)}var eo=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"write",value:function(e){if((e=Ve(e)).byteLength>0)return Ct(Object(ie.a)(t.prototype),"write",this).call(this,e)}},{key:"toString",value:function(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]?te(this.toUint8Array(!0)):this.toUint8Array(!1).then(te)}},{key:"toUint8Array",value:function(){var e=this;return arguments.length>0&&void 0!==arguments[0]&&arguments[0]?Me(this._values)[0]:B(L.mark(function t(){var n,r,i,a,o,u,s,c;return L.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:n=[],r=0,i=!1,a=!1,t.prev=3,u=Xa(e);case 5:return t.next=7,u.next();case 7:if(!(i=!(s=t.sent).done)){t.next=14;break}c=s.value,n.push(c),r+=c.byteLength;case 11:i=!1,t.next=5;break;case 14:t.next=20;break;case 16:t.prev=16,t.t0=t.catch(3),a=!0,o=t.t0;case 20:if(t.prev=20,t.prev=21,!i||null==u.return){t.next=25;break}return t.next=25,u.return();case 25:if(t.prev=25,!a){t.next=28;break}throw o;case 28:return t.finish(25);case 29:return t.finish(20);case 30:return t.abrupt("return",Me(n,r)[0]);case 31:case"end":return t.stop()}},t,null,[[3,16,20,30],[21,,25,29]])}))()}}]),t}(ce),to=function(){function e(t){Object(D.a)(this,e),t&&(this.source=new ro(_t.fromIterable(t)))}return Object(C.a)(e,[{key:Symbol.iterator,value:function(){return this}},{key:"next",value:function(e){return this.source.next(e)}},{key:"throw",value:function(e){return this.source.throw(e)}},{key:"return",value:function(e){return this.source.return(e)}},{key:"peek",value:function(e){return this.source.peek(e)}},{key:"read",value:function(e){return this.source.read(e)}}]),e}(),no=function(){function e(t){Object(D.a)(this,e),t instanceof e?this.source=t.source:t instanceof eo?this.source=new io(_t.fromAsyncIterable(t)):Ee(t)?this.source=new io(_t.fromNodeStream(t)):Ie(t)?this.source=new io(_t.fromDOMStream(t)):Te(t)?this.source=new io(_t.fromDOMStream(t.body)):_e(t)?this.source=new io(_t.fromIterable(t)):we(t)?this.source=new io(_t.fromAsyncIterable(t)):Oe(t)&&(this.source=new io(_t.fromAsyncIterable(t)))}return Object(C.a)(e,[{key:Symbol.asyncIterator,value:function(){return this}},{key:"next",value:function(e){return this.source.next(e)}},{key:"throw",value:function(e){return this.source.throw(e)}},{key:"return",value:function(e){return this.source.return(e)}},{key:"cancel",value:function(e){return this.source.cancel(e)}},{key:"peek",value:function(e){return this.source.peek(e)}},{key:"read",value:function(e){return this.source.read(e)}},{key:"closed",get:function(){return this.source.closed}}]),e}(),ro=function(){function e(t){Object(D.a)(this,e),this.source=t}return Object(C.a)(e,[{key:"cancel",value:function(e){this.return(e)}},{key:"peek",value:function(e){return this.next(e,"peek").value}},{key:"read",value:function(e){return this.next(e,"read").value}},{key:"next",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"read";return this.source.next({cmd:t,size:e})}},{key:"throw",value:function(e){return Object.create(this.source.throw&&this.source.throw(e)||oe)}},{key:"return",value:function(e){return Object.create(this.source.return&&this.source.return(e)||oe)}}]),e}(),io=function(){function e(t){var n=this;Object(D.a)(this,e),this.source=t,this._closedPromise=new Promise(function(e){return n._closedPromiseResolve=e})}return Object(C.a)(e,[{key:"cancel",value:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.return(t);case 2:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"read",value:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.next(t,"read");case 2:return e.abrupt("return",e.sent.value);case 3:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"peek",value:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.next(t,"peek");case 2:return e.abrupt("return",e.sent.value);case 3:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"next",value:function(){var e=B(L.mark(function e(t){var n,r=arguments;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return n=r.length>1&&void 0!==r[1]?r[1]:"read",e.next=3,this.source.next({cmd:n,size:t});case 3:return e.abrupt("return",e.sent);case 4:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"throw",value:function(){var e=B(L.mark(function e(t){var n;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.t1=this.source.throw,!e.t1){e.next=5;break}return e.next=4,this.source.throw(t);case 4:e.t1=e.sent;case 5:if(e.t0=e.t1,e.t0){e.next=8;break}e.t0=oe;case 8:return n=e.t0,this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,e.abrupt("return",Object.create(n));case 12:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"return",value:function(){var e=B(L.mark(function e(t){var n;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.t1=this.source.return,!e.t1){e.next=5;break}return e.next=4,this.source.return(t);case 4:e.t1=e.sent;case 5:if(e.t0=e.t1,e.t0){e.next=8;break}e.t0=oe;case 8:return n=e.t0,this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,e.abrupt("return",Object.create(n));case 12:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"closed",get:function(){return this._closedPromise}}]),e}(),ao=function(e){function t(e,n){var r;return Object(D.a)(this,t),(r=Object(re.a)(this,Object(ie.a)(t).call(this))).position=0,r.buffer=Ve(e),r.size="undefined"===typeof n?r.buffer.byteLength:n,r}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"readInt32",value:function(e){var t=this.readAt(e,4),n=t.buffer,r=t.byteOffset;return new DataView(n,r).getInt32(0,!0)}},{key:"seek",value:function(e){return this.position=Math.min(e,this.size),e<this.size}},{key:"read",value:function(e){var t=this.buffer,n=this.size,r=this.position;return t&&r<n?("number"!==typeof e&&(e=1/0),this.position=Math.min(n,r+Math.min(n-r,e)),t.subarray(r,this.position)):null}},{key:"readAt",value:function(e,t){var n=this.buffer,r=Math.min(this.size,e+t);return n?n.subarray(e,r):new Uint8Array(t)}},{key:"close",value:function(){this.buffer&&(this.buffer=null)}},{key:"throw",value:function(e){return this.close(),{done:!0,value:e}}},{key:"return",value:function(e){return this.close(),{done:!0,value:e}}}]),t}(to),oo=function(e){function t(e,n){var r;return Object(D.a)(this,t),(r=Object(re.a)(this,Object(ie.a)(t).call(this))).position=0,r._handle=e,"number"===typeof n?r.size=n:r._pending=B(L.mark(function t(){return L.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.stat();case 2:r.size=t.sent.size,delete r._pending;case 4:case"end":return t.stop()}},t)}))(),r}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"readInt32",value:function(){var e=B(L.mark(function e(t){var n,r,i;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.readAt(t,4);case 2:return n=e.sent,r=n.buffer,i=n.byteOffset,e.abrupt("return",new DataView(r,i).getInt32(0,!0));case 6:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"seek",value:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.t0=this._pending,!e.t0){e.next=4;break}return e.next=4,this._pending;case 4:return this.position=Math.min(t,this.size),e.abrupt("return",t<this.size);case 6:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"read",value:function(){var e=B(L.mark(function e(t){var n,r,i,a,o,u,s,c,l;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.t0=this._pending,!e.t0){e.next=4;break}return e.next=4,this._pending;case 4:if(n=this._handle,r=this.size,i=this.position,!(n&&i<r)){e.next=18;break}"number"!==typeof t&&(t=1/0),a=i,o=0,u=0,s=Math.min(r,a+Math.min(r-a,t)),c=new Uint8Array(Math.max(0,(this.position=s)-a));case 10:if(!((a+=u)<s&&(o+=u)<c.byteLength)){e.next=17;break}return e.next=13,n.read(c,o,c.byteLength-o,a);case 13:l=e.sent,u=l.bytesRead,e.next=10;break;case 17:return e.abrupt("return",c);case 18:return e.abrupt("return",null);case 19:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"readAt",value:function(){var e=B(L.mark(function e(t,n){var r,i,a,o;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.t0=this._pending,!e.t0){e.next=4;break}return e.next=4,this._pending;case 4:if(r=this._handle,i=this.size,!(r&&t+n<i)){e.next=11;break}return a=Math.min(i,t+n),o=new Uint8Array(a-t),e.next=10,r.read(o,0,n,t);case 10:return e.abrupt("return",e.sent.buffer);case 11:return e.abrupt("return",new Uint8Array(n));case 12:case"end":return e.stop()}},e,this)}));return function(t,n){return e.apply(this,arguments)}}()},{key:"close",value:function(){var e=B(L.mark(function e(){var t;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=this._handle,this._handle=null,e.t0=t,!e.t0){e.next=6;break}return e.next=6,t.close();case 6:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}()},{key:"throw",value:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.close();case 2:return e.abrupt("return",{done:!0,value:t});case 3:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"return",value:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.close();case 2:return e.abrupt("return",{done:!0,value:t});case 3:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()}]),t}(no);function uo(e){return e<0&&(e=4294967295+e+1),"0x".concat(e.toString(16))}var so=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8],co=function(){function e(t){Object(D.a)(this,e),this.buffer=t}return Object(C.a)(e,[{key:"high",value:function(){return this.buffer[1]}},{key:"low",value:function(){return this.buffer[0]}},{key:"_times",value:function(e){var t=new Uint32Array([this.buffer[1]>>>16,65535&this.buffer[1],this.buffer[0]>>>16,65535&this.buffer[0]]),n=new Uint32Array([e.buffer[1]>>>16,65535&e.buffer[1],e.buffer[0]>>>16,65535&e.buffer[0]]),r=t[3]*n[3];this.buffer[0]=65535&r;var i=r>>>16;return i+=r=t[2]*n[3],i+=r=t[3]*n[2]>>>0,this.buffer[0]+=i<<16,this.buffer[1]=i>>>0<r?65536:0,this.buffer[1]+=i>>>16,this.buffer[1]+=t[1]*n[3]+t[2]*n[2]+t[3]*n[1],this.buffer[1]+=t[0]*n[3]+t[1]*n[2]+t[2]*n[1]+t[3]*n[0]<<16,this}},{key:"_plus",value:function(e){var t=this.buffer[0]+e.buffer[0]>>>0;this.buffer[1]+=e.buffer[1],t<this.buffer[0]>>>0&&++this.buffer[1],this.buffer[0]=t}},{key:"lessThan",value:function(e){return this.buffer[1]<e.buffer[1]||this.buffer[1]===e.buffer[1]&&this.buffer[0]<e.buffer[0]}},{key:"equals",value:function(e){return this.buffer[1]===e.buffer[1]&&this.buffer[0]==e.buffer[0]}},{key:"greaterThan",value:function(e){return e.lessThan(this)}},{key:"hex",value:function(){return"".concat(uo(this.buffer[1])," ").concat(uo(this.buffer[0]))}}]),e}(),lo=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"times",value:function(e){return this._times(e),this}},{key:"plus",value:function(e){return this._plus(e),this}}],[{key:"from",value:function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);return t.fromString("string"===typeof e?e:e.toString(),n)}},{key:"fromNumber",value:function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);return t.fromString(e.toString(),n)}},{key:"fromString",value:function(e){for(var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2),r=e.length,i=new t(n),a=0;a<r;){var o=8<r-a?8:r-a,u=new t(new Uint32Array([parseInt(e.substr(a,o),10),0])),s=new t(new Uint32Array([so[o],0]));i.times(s),i.plus(u),a+=o}return i}},{key:"convertArray",value:function(e){for(var n=new Uint32Array(2*e.length),r=-1,i=e.length;++r<i;)t.from(e[r],new Uint32Array(n.buffer,n.byteOffset+2*r*4,2));return n}},{key:"multiply",value:function(e,n){return new t(new Uint32Array(e.buffer)).times(n)}},{key:"add",value:function(e,n){return new t(new Uint32Array(e.buffer)).plus(n)}}]),t}(co),fo=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"negate",value:function(){return this.buffer[0]=1+~this.buffer[0],this.buffer[1]=~this.buffer[1],0==this.buffer[0]&&++this.buffer[1],this}},{key:"times",value:function(e){return this._times(e),this}},{key:"plus",value:function(e){return this._plus(e),this}},{key:"lessThan",value:function(e){var t=this.buffer[1]<<0,n=e.buffer[1]<<0;return t<n||t===n&&this.buffer[0]<e.buffer[0]}}],[{key:"from",value:function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);return t.fromString("string"===typeof e?e:e.toString(),n)}},{key:"fromNumber",value:function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);return t.fromString(e.toString(),n)}},{key:"fromString",value:function(e){for(var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2),r=e.startsWith("-"),i=e.length,a=new t(n),o=r?1:0;o<i;){var u=8<i-o?8:i-o,s=new t(new Uint32Array([parseInt(e.substr(o,u),10),0])),c=new t(new Uint32Array([so[u],0]));a.times(c),a.plus(s),o+=u}return r?a.negate():a}},{key:"convertArray",value:function(e){for(var n=new Uint32Array(2*e.length),r=-1,i=e.length;++r<i;)t.from(e[r],new Uint32Array(n.buffer,n.byteOffset+2*r*4,2));return n}},{key:"multiply",value:function(e,n){return new t(new Uint32Array(e.buffer)).times(n)}},{key:"add",value:function(e,n){return new t(new Uint32Array(e.buffer)).plus(n)}}]),t}(co),ho=function(){function e(t){Object(D.a)(this,e),this.buffer=t}return Object(C.a)(e,[{key:"high",value:function(){return new fo(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2))}},{key:"low",value:function(){return new fo(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset,2))}},{key:"negate",value:function(){return this.buffer[0]=1+~this.buffer[0],this.buffer[1]=~this.buffer[1],this.buffer[2]=~this.buffer[2],this.buffer[3]=~this.buffer[3],0==this.buffer[0]&&++this.buffer[1],0==this.buffer[1]&&++this.buffer[2],0==this.buffer[2]&&++this.buffer[3],this}},{key:"times",value:function(e){var t=new lo(new Uint32Array([this.buffer[3],0])),n=new lo(new Uint32Array([this.buffer[2],0])),r=new lo(new Uint32Array([this.buffer[1],0])),i=new lo(new Uint32Array([this.buffer[0],0])),a=new lo(new Uint32Array([e.buffer[3],0])),o=new lo(new Uint32Array([e.buffer[2],0])),u=new lo(new Uint32Array([e.buffer[1],0])),s=new lo(new Uint32Array([e.buffer[0],0])),c=lo.multiply(i,s);this.buffer[0]=c.low();var l=new lo(new Uint32Array([c.high(),0]));return c=lo.multiply(r,s),l.plus(c),c=lo.multiply(i,u),l.plus(c),this.buffer[1]=l.low(),this.buffer[3]=l.lessThan(c)?1:0,this.buffer[2]=l.high(),new lo(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2)).plus(lo.multiply(n,s)).plus(lo.multiply(r,u)).plus(lo.multiply(i,o)),this.buffer[3]+=lo.multiply(t,s).plus(lo.multiply(n,u)).plus(lo.multiply(r,o)).plus(lo.multiply(i,a)).low(),this}},{key:"plus",value:function(e){var t=new Uint32Array(4);return t[3]=this.buffer[3]+e.buffer[3]>>>0,t[2]=this.buffer[2]+e.buffer[2]>>>0,t[1]=this.buffer[1]+e.buffer[1]>>>0,t[0]=this.buffer[0]+e.buffer[0]>>>0,t[0]<this.buffer[0]>>>0&&++t[1],t[1]<this.buffer[1]>>>0&&++t[2],t[2]<this.buffer[2]>>>0&&++t[3],this.buffer[3]=t[3],this.buffer[2]=t[2],this.buffer[1]=t[1],this.buffer[0]=t[0],this}},{key:"hex",value:function(){return"".concat(uo(this.buffer[3])," ").concat(uo(this.buffer[2])," ").concat(uo(this.buffer[1])," ").concat(uo(this.buffer[0]))}}],[{key:"multiply",value:function(t,n){return new e(new Uint32Array(t.buffer)).times(n)}},{key:"add",value:function(t,n){return new e(new Uint32Array(t.buffer)).plus(n)}},{key:"from",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(4);return e.fromString("string"===typeof t?t:t.toString(),n)}},{key:"fromNumber",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(4);return e.fromString(t.toString(),n)}},{key:"fromString",value:function(t){for(var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(4),r=t.startsWith("-"),i=t.length,a=new e(n),o=r?1:0;o<i;){var u=8<i-o?8:i-o,s=new e(new Uint32Array([parseInt(t.substr(o,u),10),0,0,0])),c=new e(new Uint32Array([so[u],0,0,0]));a.times(c),a.plus(s),o+=u}return r?a.negate():a}},{key:"convertArray",value:function(t){for(var n=new Uint32Array(4*t.length),r=-1,i=t.length;++r<i;)e.from(t[r],new Uint32Array(n.buffer,n.byteOffset+16*r,4));return n}}]),e}(),po=function(e){function t(e,n,r,i){var a;return Object(D.a)(this,t),(a=Object(re.a)(this,Object(ie.a)(t).call(this))).nodesIndex=-1,a.buffersIndex=-1,a.bytes=e,a.nodes=n,a.buffers=r,a.dictionaries=i,a}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"visit",value:function(e){return Ct(Object(ie.a)(t.prototype),"visit",this).call(this,e instanceof ca?e.type:e)}},{key:"visitNull",value:function(e){var t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode()).length;return ar.Null(e,0,t)}},{key:"visitBool",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return ar.Bool(e,0,n,r,this.readNullBitmap(e,r),this.readData(e))}},{key:"visitInt",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return ar.Int(e,0,n,r,this.readNullBitmap(e,r),this.readData(e))}},{key:"visitFloat",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return ar.Float(e,0,n,r,this.readNullBitmap(e,r),this.readData(e))}},{key:"visitUtf8",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return ar.Utf8(e,0,n,r,this.readNullBitmap(e,r),this.readOffsets(e),this.readData(e))}},{key:"visitBinary",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return ar.Binary(e,0,n,r,this.readNullBitmap(e,r),this.readOffsets(e),this.readData(e))}},{key:"visitFixedSizeBinary",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return ar.FixedSizeBinary(e,0,n,r,this.readNullBitmap(e,r),this.readData(e))}},{key:"visitDate",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return ar.Date(e,0,n,r,this.readNullBitmap(e,r),this.readData(e))}},{key:"visitTimestamp",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return ar.Timestamp(e,0,n,r,this.readNullBitmap(e,r),this.readData(e))}},{key:"visitTime",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return ar.Time(e,0,n,r,this.readNullBitmap(e,r),this.readData(e))}},{key:"visitDecimal",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return ar.Decimal(e,0,n,r,this.readNullBitmap(e,r),this.readData(e))}},{key:"visitList",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return ar.List(e,0,n,r,this.readNullBitmap(e,r),this.readOffsets(e),this.visit(e.children[0]))}},{key:"visitStruct",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return ar.Struct(e,0,n,r,this.readNullBitmap(e,r),this.visitMany(e.children))}},{key:"visitUnion",value:function(e){return e.mode===Yt.Sparse?this.visitSparseUnion(e):this.visitDenseUnion(e)}},{key:"visitDenseUnion",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return ar.Union(e,0,n,r,this.readNullBitmap(e,r),this.readTypeIds(e),this.readOffsets(e),this.visitMany(e.children))}},{key:"visitSparseUnion",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return ar.Union(e,0,n,r,this.readNullBitmap(e,r),this.readTypeIds(e),this.visitMany(e.children))}},{key:"visitDictionary",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return ar.Dictionary(e,0,n,r,this.readNullBitmap(e,r),this.readData(e.indices),this.readDictionary(e))}},{key:"visitInterval",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return ar.Interval(e,0,n,r,this.readNullBitmap(e,r),this.readData(e))}},{key:"visitFixedSizeList",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return ar.FixedSizeList(e,0,n,r,this.readNullBitmap(e,r),this.visit(e.children[0]))}},{key:"visitMap",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode(),n=t.length,r=t.nullCount;return ar.Map(e,0,n,r,this.readNullBitmap(e,r),this.readOffsets(e),this.visit(e.children[0]))}},{key:"nextFieldNode",value:function(){return this.nodes[++this.nodesIndex]}},{key:"nextBufferRange",value:function(){return this.buffers[++this.buffersIndex]}},{key:"readNullBitmap",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.nextBufferRange();return t>0&&this.readData(e,n)||new Uint8Array(0)}},{key:"readOffsets",value:function(e,t){return this.readData(e,t)}},{key:"readTypeIds",value:function(e,t){return this.readData(e,t)}},{key:"readData",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextBufferRange(),n=t.length,r=t.offset;return this.bytes.subarray(r,r+n)}},{key:"readDictionary",value:function(e){return this.dictionaries.get(e.id)}}]),t}(ln),yo=function(e){function t(e,n,r,i){var a;return Object(D.a)(this,t),(a=Object(re.a)(this,Object(ie.a)(t).call(this,new Uint8Array(0),n,r,i))).sources=e,a}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"readNullBitmap",value:function(e,t){var n=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.nextBufferRange()).offset;return t<=0?new Uint8Array(0):rn(this.sources[n])}},{key:"readOffsets",value:function(e){var t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextBufferRange()).offset;return Ne(Uint8Array,Ne(Int32Array,this.sources[t]))}},{key:"readTypeIds",value:function(e){var t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextBufferRange()).offset;return Ne(Uint8Array,Ne(e.ArrayType,this.sources[t]))}},{key:"readData",value:function(e){var t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextBufferRange()).offset,n=this.sources;return jn.isTimestamp(e)?Ne(Uint8Array,fo.convertArray(n[t])):(jn.isInt(e)||jn.isTime(e))&&64===e.bitWidth?Ne(Uint8Array,fo.convertArray(n[t])):jn.isDate(e)&&e.unit===Wt.MILLISECOND?Ne(Uint8Array,fo.convertArray(n[t])):jn.isDecimal(e)?Ne(Uint8Array,ho.convertArray(n[t])):jn.isBinary(e)||jn.isFixedSizeBinary(e)?function(e){for(var t=e.join(""),n=new Uint8Array(t.length/2),r=0;r<t.length;r+=2)n[r>>1]=parseInt(t.substr(r,2),16);return n}(n[t]):jn.isBool(e)?rn(n[t]):jn.isUtf8(e)?ne(n[t].join("")):Ne(Uint8Array,Ne(e.ArrayType,n[t].map(function(e){return+e})))}}]),t}(po);var vo=z.Long,bo=Lt.apache.arrow.flatbuf.Null,mo=Lt.apache.arrow.flatbuf.Int,go=Lt.apache.arrow.flatbuf.FloatingPoint,ko=Lt.apache.arrow.flatbuf.Binary,wo=Lt.apache.arrow.flatbuf.Bool,_o=Lt.apache.arrow.flatbuf.Utf8,Oo=Lt.apache.arrow.flatbuf.Decimal,jo=Lt.apache.arrow.flatbuf.Date,xo=Lt.apache.arrow.flatbuf.Time,So=Lt.apache.arrow.flatbuf.Timestamp,To=Lt.apache.arrow.flatbuf.Interval,Io=Lt.apache.arrow.flatbuf.List,Eo=Lt.apache.arrow.flatbuf.Struct_,Ao=Lt.apache.arrow.flatbuf.Union,Bo=Lt.apache.arrow.flatbuf.DictionaryEncoding,Do=Lt.apache.arrow.flatbuf.FixedSizeBinary,Co=Lt.apache.arrow.flatbuf.FixedSizeList,Lo=Lt.apache.arrow.flatbuf.Map,Fo=new(function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"visit",value:function(e,n){return null==e||null==n?void 0:Ct(Object(ie.a)(t.prototype),"visit",this).call(this,e,n)}},{key:"visitNull",value:function(e,t){return bo.startNull(t),bo.endNull(t)}},{key:"visitInt",value:function(e,t){return mo.startInt(t),mo.addBitWidth(t,e.bitWidth),mo.addIsSigned(t,e.isSigned),mo.endInt(t)}},{key:"visitFloat",value:function(e,t){return go.startFloatingPoint(t),go.addPrecision(t,e.precision),go.endFloatingPoint(t)}},{key:"visitBinary",value:function(e,t){return ko.startBinary(t),ko.endBinary(t)}},{key:"visitBool",value:function(e,t){return wo.startBool(t),wo.endBool(t)}},{key:"visitUtf8",value:function(e,t){return _o.startUtf8(t),_o.endUtf8(t)}},{key:"visitDecimal",value:function(e,t){return Oo.startDecimal(t),Oo.addScale(t,e.scale),Oo.addPrecision(t,e.precision),Oo.endDecimal(t)}},{key:"visitDate",value:function(e,t){return jo.startDate(t),jo.addUnit(t,e.unit),jo.endDate(t)}},{key:"visitTime",value:function(e,t){return xo.startTime(t),xo.addUnit(t,e.unit),xo.addBitWidth(t,e.bitWidth),xo.endTime(t)}},{key:"visitTimestamp",value:function(e,t){var n=e.timezone&&t.createString(e.timezone)||void 0;return So.startTimestamp(t),So.addUnit(t,e.unit),void 0!==n&&So.addTimezone(t,n),So.endTimestamp(t)}},{key:"visitInterval",value:function(e,t){return To.startInterval(t),To.addUnit(t,e.unit),To.endInterval(t)}},{key:"visitList",value:function(e,t){return Io.startList(t),Io.endList(t)}},{key:"visitStruct",value:function(e,t){return Eo.startStruct_(t),Eo.endStruct_(t)}},{key:"visitUnion",value:function(e,t){Ao.startTypeIdsVector(t,e.typeIds.length);var n=Ao.createTypeIdsVector(t,e.typeIds);return Ao.startUnion(t),Ao.addMode(t,e.mode),Ao.addTypeIds(t,n),Ao.endUnion(t)}},{key:"visitDictionary",value:function(e,t){var n=this.visit(e.indices,t);return Bo.startDictionaryEncoding(t),Bo.addId(t,new vo(e.id,0)),Bo.addIsOrdered(t,e.isOrdered),void 0!==n&&Bo.addIndexType(t,n),Bo.endDictionaryEncoding(t)}},{key:"visitFixedSizeBinary",value:function(e,t){return Do.startFixedSizeBinary(t),Do.addByteWidth(t,e.byteWidth),Do.endFixedSizeBinary(t)}},{key:"visitFixedSizeList",value:function(e,t){return Co.startFixedSizeList(t),Co.addListSize(t,e.listSize),Co.endFixedSizeList(t)}},{key:"visitMap",value:function(e,t){return Lo.startMap(t),Lo.addKeysSorted(t,e.keysSorted),Lo.endMap(t)}}]),t}(ln));function Mo(e){return new tu(e.count,function e(t){return(t||[]).reduce(function(t,n){return[].concat(cn(t),[new iu(n.count,(r=n.VALIDITY,(r||[]).reduce(function(e,t){return e+ +(0===t)},0)))],cn(e(n.children)));var r},[])}(e.columns),function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];for(var r=-1,i=(t||[]).length;++r<i;){var a=t[r];a.VALIDITY&&n.push(new ru(n.length,a.VALIDITY.length)),a.TYPE&&n.push(new ru(n.length,a.TYPE.length)),a.OFFSET&&n.push(new ru(n.length,a.OFFSET.length)),a.DATA&&n.push(new ru(n.length,a.DATA.length)),n=e(a.children,n)}return n}(e.columns))}function No(e,t){return(e.children||[]).filter(Boolean).map(function(e){return ca.fromJSON(e,t)})}function Uo(e){return new Map(Object.entries(e||{}))}function Po(e){return new Sn(e.isSigned,e.bitWidth)}function Ro(e,t){var n=e.type.name;switch(n){case"NONE":case"null":return new xn;case"binary":return new Pn;case"utf8":return new Rn;case"bool":return new zn;case"list":return new qn((t||[])[0]);case"struct":case"struct_":return new Gn(t||[])}switch(n){case"int":var r=e.type;return new Sn(r.isSigned,r.bitWidth);case"floatingpoint":var i=e.type;return new Fn(Ht[i.precision]);case"decimal":var a=e.type;return new Vn(a.scale,a.precision);case"date":var o=e.type;return new Wn(Wt[o.unit]);case"time":var u=e.type;return new Yn($t[u.unit],u.bitWidth);case"timestamp":var s=e.type;return new Kn($t[s.unit],s.timezone);case"interval":var c=e.type;return new Qn(Kt[c.unit]);case"union":var l=e.type;return new Jn(Yt[l.mode],l.typeIds||[],t||[]);case"fixedsizebinary":var f=e.type;return new Xn(f.byteWidth);case"fixedsizelist":var h=e.type;return new Zn(h.listSize,(t||[])[0]);case"map":var d=e.type;return new er((t||[])[0],d.keysSorted)}throw new Error('Unrecognized type: "'.concat(n,'"'))}var zo=z.Long,Vo=z.Builder,Wo=z.ByteBuffer,$o=Lt.apache.arrow.flatbuf.Type,Ho=Lt.apache.arrow.flatbuf.Field,Yo=Lt.apache.arrow.flatbuf.Schema,Ko=Lt.apache.arrow.flatbuf.Buffer,Qo=Pt.apache.arrow.flatbuf.Message,qo=Lt.apache.arrow.flatbuf.KeyValue,Go=Pt.apache.arrow.flatbuf.FieldNode,Jo=Lt.apache.arrow.flatbuf.Endianness,Xo=Pt.apache.arrow.flatbuf.RecordBatch,Zo=Pt.apache.arrow.flatbuf.DictionaryBatch,eu=function(){function e(t,n,r,i){Object(D.a)(this,e),this._version=n,this._headerType=r,this.body=new Uint8Array(0),i&&(this._createHeader=function(){return i}),this._bodyLength="number"===typeof t?t:t.low}return Object(C.a)(e,[{key:"header",value:function(){return this._createHeader()}},{key:"isSchema",value:function(){return this.headerType===Qt.Schema}},{key:"isRecordBatch",value:function(){return this.headerType===Qt.RecordBatch}},{key:"isDictionaryBatch",value:function(){return this.headerType===Qt.DictionaryBatch}},{key:"type",get:function(){return this.headerType}},{key:"version",get:function(){return this._version}},{key:"headerType",get:function(){return this._headerType}},{key:"bodyLength",get:function(){return this._bodyLength}}],[{key:"fromJSON",value:function(t,n){var r=new e(0,qt.V4,n);return r._createHeader=function(e,t){return function(){switch(t){case Qt.Schema:return sa.fromJSON(e);case Qt.RecordBatch:return tu.fromJSON(e);case Qt.DictionaryBatch:return nu.fromJSON(e)}throw new Error("Unrecognized Message type: { name: ".concat(Qt[t],", type: ").concat(t," }"))}}(t,n),r}},{key:"decode",value:function(t){t=new Wo(Ve(t));var n=Qo.getRootAsMessage(t),r=n.bodyLength(),i=n.version(),a=n.headerType(),o=new e(r,i,a);return o._createHeader=function(e,t){return function(){switch(t){case Qt.Schema:return sa.decode(e.header(new Yo));case Qt.RecordBatch:return tu.decode(e.header(new Xo),e.version());case Qt.DictionaryBatch:return nu.decode(e.header(new Zo),e.version())}throw new Error("Unrecognized Message type: { name: ".concat(Qt[t],", type: ").concat(t," }"))}}(n,a),o}},{key:"encode",value:function(e){var t=new Vo,n=-1;return e.isSchema()?n=sa.encode(t,e.header()):e.isRecordBatch()?n=tu.encode(t,e.header()):e.isDictionaryBatch()&&(n=nu.encode(t,e.header())),Qo.startMessage(t),Qo.addVersion(t,qt.V4),Qo.addHeader(t,n),Qo.addHeaderType(t,e.headerType),Qo.addBodyLength(t,new zo(e.bodyLength,0)),Qo.finishMessageBuffer(t,Qo.endMessage(t)),t.asUint8Array()}},{key:"from",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(t instanceof sa)return new e(0,qt.V4,Qt.Schema,t);if(t instanceof tu)return new e(n,qt.V4,Qt.RecordBatch,t);if(t instanceof nu)return new e(n,qt.V4,Qt.DictionaryBatch,t);throw new Error("Unrecognized Message header: ".concat(t))}}]),e}(),tu=function(){function e(t,n,r){Object(D.a)(this,e),this._nodes=n,this._buffers=r,this._length="number"===typeof t?t:t.low}return Object(C.a)(e,[{key:"nodes",get:function(){return this._nodes}},{key:"length",get:function(){return this._length}},{key:"buffers",get:function(){return this._buffers}}]),e}(),nu=function(){function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];Object(D.a)(this,e),this._data=t,this._isDelta=r,this._id="number"===typeof n?n:n.low}return Object(C.a)(e,[{key:"id",get:function(){return this._id}},{key:"data",get:function(){return this._data}},{key:"isDelta",get:function(){return this._isDelta}},{key:"length",get:function(){return this.data.length}},{key:"nodes",get:function(){return this.data.nodes}},{key:"buffers",get:function(){return this.data.buffers}}]),e}(),ru=function e(t,n){Object(D.a)(this,e),this.offset="number"===typeof t?t:t.low,this.length="number"===typeof n?n:n.low},iu=function e(t,n){Object(D.a)(this,e),this.length="number"===typeof t?t:t.low,this.nullCount="number"===typeof n?n:n.low};function au(e,t){for(var n,r=[],i=-1,a=-1,o=e.childrenLength();++i<o;)(n=e.children(i))&&(r[++a]=ca.decode(n,t));return r}function ou(e){var t=new Map;if(e)for(var n,r,i=-1,a=0|e.customMetadataLength();++i<a;)(n=e.customMetadata(i))&&null!=(r=n.key())&&t.set(r,n.value());return t}function uu(e){return new Sn(e.isSigned(),e.bitWidth())}function su(e,t){var n=e.typeType();switch(n){case $o.NONE:case $o.Null:return new xn;case $o.Binary:return new Pn;case $o.Utf8:return new Rn;case $o.Bool:return new zn;case $o.List:return new qn((t||[])[0]);case $o.Struct_:return new Gn(t||[])}switch(n){case $o.Int:var r=e.type(new Lt.apache.arrow.flatbuf.Int);return new Sn(r.isSigned(),r.bitWidth());case $o.FloatingPoint:var i=e.type(new Lt.apache.arrow.flatbuf.FloatingPoint);return new Fn(i.precision());case $o.Decimal:var a=e.type(new Lt.apache.arrow.flatbuf.Decimal);return new Vn(a.scale(),a.precision());case $o.Date:var o=e.type(new Lt.apache.arrow.flatbuf.Date);return new Wn(o.unit());case $o.Time:var u=e.type(new Lt.apache.arrow.flatbuf.Time);return new Yn(u.unit(),u.bitWidth());case $o.Timestamp:var s=e.type(new Lt.apache.arrow.flatbuf.Timestamp);return new Kn(s.unit(),s.timezone());case $o.Interval:var c=e.type(new Lt.apache.arrow.flatbuf.Interval);return new Qn(c.unit());case $o.Union:var l=e.type(new Lt.apache.arrow.flatbuf.Union);return new Jn(l.mode(),l.typeIdsArray()||[],t||[]);case $o.FixedSizeBinary:var f=e.type(new Lt.apache.arrow.flatbuf.FixedSizeBinary);return new Xn(f.byteWidth());case $o.FixedSizeList:var h=e.type(new Lt.apache.arrow.flatbuf.FixedSizeList);return new Zn(h.listSize(),(t||[])[0]);case $o.Map:var d=e.type(new Lt.apache.arrow.flatbuf.Map);return new er((t||[])[0],d.keysSorted())}throw new Error('Unrecognized type: "'.concat($o[n],'" (').concat(n,")"))}ca.encode=function(e,t){var n=-1,r=-1,i=-1,a=t.type,o=t.typeId;jn.isDictionary(a)?(o=a.dictionary.typeId,i=Fo.visit(a,e),r=Fo.visit(a.dictionary,e)):r=Fo.visit(a,e);var u=(a.children||[]).map(function(t){return ca.encode(e,t)}),s=Ho.createChildrenVector(e,u),c=t.metadata&&t.metadata.size>0?Ho.createCustomMetadataVector(e,cn(t.metadata).map(function(t){var n=F(t,2),r=n[0],i=n[1],a=e.createString("".concat(r)),o=e.createString("".concat(i));return qo.startKeyValue(e),qo.addKey(e,a),qo.addValue(e,o),qo.endKeyValue(e)})):-1;t.name&&(n=e.createString(t.name));Ho.startField(e),Ho.addType(e,r),Ho.addTypeType(e,o),Ho.addChildren(e,s),Ho.addNullable(e,!!t.nullable),-1!==n&&Ho.addName(e,n);-1!==i&&Ho.addDictionary(e,i);-1!==c&&Ho.addCustomMetadata(e,c);return Ho.endField(e)},ca.decode=function(e,t){var n,r,i,a,o,u;t&&(u=e.dictionary())?t.has(n=u.id().low)?(a=(a=u.indexType())?uu(a):new En,o=new rr(t.get(n),a,n,u.isOrdered()),r=new ca(e.name(),o,e.nullable(),ou(e))):(a=(a=u.indexType())?uu(a):new En,t.set(n,i=su(e,au(e,t))),o=new rr(i,a,n,u.isOrdered()),r=new ca(e.name(),o,e.nullable(),ou(e))):(i=su(e,au(e,t)),r=new ca(e.name(),i,e.nullable(),ou(e)));return r||null},ca.fromJSON=function(e,t){var n,r,i,a,o,u;return t&&(a=e.dictionary)?t.has(n=a.id)?(r=(r=a.indexType)?Po(r):new En,u=new rr(t.get(n),r,n,a.isOrdered),i=new ca(e.name,u,e.nullable,Uo(e.customMetadata))):(r=(r=a.indexType)?Po(r):new En,t.set(n,o=Ro(e,No(e,t))),u=new rr(o,r,n,a.isOrdered),i=new ca(e.name,u,e.nullable,Uo(e.customMetadata))):(o=Ro(e,No(e,t)),i=new ca(e.name,o,e.nullable,Uo(e.customMetadata))),i||null},sa.encode=function(e,t){var n=t.fields.map(function(t){return ca.encode(e,t)});Yo.startFieldsVector(e,n.length);var r=Yo.createFieldsVector(e,n),i=t.metadata&&t.metadata.size>0?Yo.createCustomMetadataVector(e,cn(t.metadata).map(function(t){var n=F(t,2),r=n[0],i=n[1],a=e.createString("".concat(r)),o=e.createString("".concat(i));return qo.startKeyValue(e),qo.addKey(e,a),qo.addValue(e,o),qo.endKeyValue(e)})):-1;Yo.startSchema(e),Yo.addFields(e,r),Yo.addEndianness(e,cu?Jo.Little:Jo.Big),-1!==i&&Yo.addCustomMetadata(e,i);return Yo.endSchema(e)},sa.decode=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Map,n=function(e,t){for(var n,r=[],i=-1,a=-1,o=e.fieldsLength();++i<o;)(n=e.fields(i))&&(r[++a]=ca.decode(n,t));return r}(e,t);return new sa(n,ou(e),t)},sa.fromJSON=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Map;return new sa(function(e,t){return(e.fields||[]).filter(Boolean).map(function(e){return ca.fromJSON(e,t)})}(e,t),Uo(e.customMetadata),t)},tu.encode=function(e,t){var n=t.nodes||[],r=t.buffers||[];Xo.startNodesVector(e,n.length),n.slice().reverse().forEach(function(t){return iu.encode(e,t)});var i=e.endVector();Xo.startBuffersVector(e,r.length),r.slice().reverse().forEach(function(t){return ru.encode(e,t)});var a=e.endVector();return Xo.startRecordBatch(e),Xo.addLength(e,new zo(t.length,0)),Xo.addNodes(e,i),Xo.addBuffers(e,a),Xo.endRecordBatch(e)},tu.decode=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:qt.V4;return new tu(e.length(),function(e){for(var t,n=[],r=-1,i=-1,a=e.nodesLength();++r<a;)(t=e.nodes(r))&&(n[++i]=iu.decode(t));return n}(e),function(e,t){for(var n,r=[],i=-1,a=-1,o=e.buffersLength();++i<o;)(n=e.buffers(i))&&(t<qt.V4&&(n.bb_pos+=8*(i+1)),r[++a]=ru.decode(n));return r}(e,t))},tu.fromJSON=Mo,nu.encode=function(e,t){var n=tu.encode(e,t.data);return Zo.startDictionaryBatch(e),Zo.addId(e,new zo(t.id,0)),Zo.addIsDelta(e,t.isDelta),Zo.addData(e,n),Zo.endDictionaryBatch(e)},nu.decode=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:qt.V4;return new nu(tu.decode(e.data(),t),e.id(),e.isDelta())},nu.fromJSON=function(e){return new nu(Mo(e.data),e.id,e.isDelta)},iu.encode=function(e,t){return Go.createFieldNode(e,new zo(t.length,0),new zo(t.nullCount,0))},iu.decode=function(e){return new iu(e.length(),e.nullCount())},ru.encode=function(e,t){return Ko.createBuffer(e,new zo(t.offset,0),new zo(t.length,0))},ru.decode=function(e){return new ru(e.offset(),e.length())};for(var cu=function(){var e=new ArrayBuffer(2);return new DataView(e).setInt16(0,256,!0),256===new Int16Array(e)[0]}(),lu=z.ByteBuffer,fu=function(e){return"Expected ".concat(Qt[e]," Message in stream, but was null or length 0.")},hu=function(e){return"Header pointer of flatbuffer-encoded ".concat(Qt[e]," Message is null or length 0.")},du=function(e,t){return"Expected to read ".concat(e," metadata bytes, but only read ").concat(t,".")},pu=function(e,t){return"Expected to read ".concat(e," bytes for message body, but only read ").concat(t,".")},yu=function(){function e(t){Object(D.a)(this,e),this.source=t instanceof to?t:new to(t)}return Object(C.a)(e,[{key:Symbol.iterator,value:function(){return this}},{key:"next",value:function(){var e;return(e=this.readMetadataLength()).done?oe:-1===e.value&&(e=this.readMetadataLength()).done?oe:(e=this.readMetadata(e.value)).done?oe:e}},{key:"throw",value:function(e){return this.source.throw(e)}},{key:"return",value:function(e){return this.source.return(e)}},{key:"readMessage",value:function(e){var t;if((t=this.next()).done)return null;if(null!=e&&t.value.headerType!==e)throw new Error(fu(e));return t.value}},{key:"readMessageBody",value:function(e){if(e<=0)return new Uint8Array(0);var t=Ve(this.source.read(e));if(t.byteLength<e)throw new Error(pu(e,t.byteLength));return t.byteOffset%8===0&&t.byteOffset+t.byteLength<=t.buffer.byteLength?t:t.slice()}},{key:"readSchema",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=Qt.Schema,n=this.readMessage(t),r=n&&n.header();if(e&&!r)throw new Error(hu(t));return r}},{key:"readMetadataLength",value:function(){var e=this.source.read(mu),t=e&&new lu(e),n=t&&t.readInt32(0)||0;return{done:0===n,value:n}}},{key:"readMetadata",value:function(e){var t=this.source.read(e);if(!t)return oe;if(t.byteLength<e)throw new Error(du(e,t.byteLength));return{done:!1,value:eu.decode(t)}}}]),e}(),vu=function(){function e(t,n){Object(D.a)(this,e),this.source=t instanceof no?t:Se(t)?new oo(t,n):new no(t)}return Object(C.a)(e,[{key:Symbol.asyncIterator,value:function(){return this}},{key:"next",value:function(){var e=B(L.mark(function e(){var t;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.readMetadataLength();case 2:if(!(t=e.sent).done){e.next=4;break}return e.abrupt("return",oe);case 4:if(e.t0=-1===t.value,!e.t0){e.next=9;break}return e.next=8,this.readMetadataLength();case 8:e.t0=(t=e.sent).done;case 9:if(!e.t0){e.next=11;break}return e.abrupt("return",oe);case 11:return e.next=13,this.readMetadata(t.value);case 13:if(!(t=e.sent).done){e.next=15;break}return e.abrupt("return",oe);case 15:return e.abrupt("return",t);case 16:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}()},{key:"throw",value:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.source.throw(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"return",value:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.source.return(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"readMessage",value:function(){var e=B(L.mark(function e(t){var n;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.next();case 2:if(!(n=e.sent).done){e.next=4;break}return e.abrupt("return",null);case 4:if(null==t||n.value.headerType===t){e.next=6;break}throw new Error(fu(t));case 6:return e.abrupt("return",n.value);case 7:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"readMessageBody",value:function(){var e=B(L.mark(function e(t){var n;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!(t<=0)){e.next=2;break}return e.abrupt("return",new Uint8Array(0));case 2:return e.t0=Ve,e.next=5,this.source.read(t);case 5:if(e.t1=e.sent,!((n=(0,e.t0)(e.t1)).byteLength<t)){e.next=9;break}throw new Error(pu(t,n.byteLength));case 9:return e.abrupt("return",n.byteOffset%8===0&&n.byteOffset+n.byteLength<=n.buffer.byteLength?n:n.slice());case 10:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"readSchema",value:function(){var e=B(L.mark(function e(){var t,n,r,i,a=arguments;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=a.length>0&&void 0!==a[0]&&a[0],n=Qt.Schema,e.next=4,this.readMessage(n);case 4:if(r=e.sent,i=r&&r.header(),!t||i){e.next=8;break}throw new Error(hu(n));case 8:return e.abrupt("return",i);case 9:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}()},{key:"readMetadataLength",value:function(){var e=B(L.mark(function e(){var t,n,r;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.source.read(mu);case 2:return t=e.sent,n=t&&new lu(t),r=n&&n.readInt32(0)||0,e.abrupt("return",{done:0===r,value:r});case 6:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}()},{key:"readMetadata",value:function(){var e=B(L.mark(function e(t){var n;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.source.read(t);case 2:if(n=e.sent){e.next=5;break}return e.abrupt("return",oe);case 5:if(!(n.byteLength<t)){e.next=7;break}throw new Error(du(t,n.byteLength));case 7:return e.abrupt("return",{done:!1,value:eu.decode(n)});case 8:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()}]),e}(),bu=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this,new Uint8Array(0))))._schema=!1,n._body=[],n._batchIndex=0,n._dictionaryIndex=0,n._json=e instanceof ue?e:new ue(e),n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"next",value:function(){var e=this._json;if(!this._schema)return this._schema=!0,{done:!1,value:eu.fromJSON(e.schema,Qt.Schema)};if(this._dictionaryIndex<e.dictionaries.length){var t=e.dictionaries[this._dictionaryIndex++];return this._body=t.data.columns,{done:!1,value:eu.fromJSON(t,Qt.DictionaryBatch)}}if(this._batchIndex<e.batches.length){var n=e.batches[this._batchIndex++];return this._body=n.columns,{done:!1,value:eu.fromJSON(n,Qt.RecordBatch)}}return this._body=[],oe}},{key:"readMessageBody",value:function(e){return function e(t){return(t||[]).reduce(function(t,n){return[].concat(cn(t),cn(n.VALIDITY&&[n.VALIDITY]||[]),cn(n.TYPE&&[n.TYPE]||[]),cn(n.OFFSET&&[n.OFFSET]||[]),cn(n.DATA&&[n.DATA]||[]),cn(e(n.children)))},[])}(this._body)}},{key:"readMessage",value:function(e){var t;if((t=this.next()).done)return null;if(null!=e&&t.value.headerType!==e)throw new Error(fu(e));return t.value}},{key:"readSchema",value:function(){var e=Qt.Schema,t=this.readMessage(e),n=t&&t.header();if(!t||!n)throw new Error(hu(e));return n}}]),t}(yu),mu=4,gu=new Uint8Array("ARROW1".length),ku=0;ku<"ARROW1".length;ku+=1)gu[ku]="ARROW1".charCodeAt(ku);function wu(e){for(var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=-1,r=gu.length;++n<r;)if(gu[n]!==e[t+n])return!1;return!0}var _u=gu.length,Ou=_u+mu,ju=2*_u+mu,xu=function(e){function t(){var e;return Object(D.a)(this,t),(e=Object(re.a)(this,Object(ie.a)(t).call(this)))._byteLength=0,e._nodes=[],e._buffers=[],e._bufferRegions=[],e}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"visit",value:function(e){if(!jn.isDictionary(e.type)){var n=e.data,r=e.length,i=e.nullCount;if(r>2147483647)throw new RangeError("Cannot write arrays larger than 2^31 - 1 in length");jn.isNull(e.type)||Su.call(this,i<=0?new Uint8Array(0):nn(n.offset,r,n.nullBitmap)),this.nodes.push(new iu(r,i))}return Ct(Object(ie.a)(t.prototype),"visit",this).call(this,e)}},{key:"visitNull",value:function(e){return this}},{key:"visitDictionary",value:function(e){return this.visit(e.indices)}},{key:"nodes",get:function(){return this._nodes}},{key:"buffers",get:function(){return this._buffers}},{key:"byteLength",get:function(){return this._byteLength}},{key:"bufferRegions",get:function(){return this._bufferRegions}}],[{key:"assemble",value:function(){for(var e=new t,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];var a=ia(Fc,r),o=F(e.visitMany(a),1)[0];return void 0===o?e:o}}]),t}(ln);function Su(e){var t=e.byteLength+7&-8;return this.buffers.push(e),this.bufferRegions.push(new ru(this._byteLength,t)),this._byteLength+=t,this}function Tu(e){return Su.call(this,e.values.subarray(0,e.length*e.stride))}function Iu(e){var t=e.length,n=e.values,r=e.valueOffsets,i=r[0],a=r[t],o=Math.min(a-i,n.byteLength-i);return Su.call(this,bt(-r[0],t,r)),Su.call(this,n.subarray(i,i+o)),this}function Eu(e){var t=e.length,n=e.valueOffsets;return n&&Su.call(this,bt(n[0],t,n)),this.visit(e.getChildAt(0))}function Au(e){return this.visitMany(e.type.children.map(function(t,n){return e.getChildAt(n)}).filter(Boolean))[0]}xu.prototype.visitBool=function(e){var t;return e.nullCount>=e.length?Su.call(this,new Uint8Array(0)):(t=e.values)instanceof Uint8Array?Su.call(this,nn(e.offset,e.length,t)):Su.call(this,rn(e))},xu.prototype.visitInt=Tu,xu.prototype.visitFloat=Tu,xu.prototype.visitUtf8=Iu,xu.prototype.visitBinary=Iu,xu.prototype.visitFixedSizeBinary=Tu,xu.prototype.visitDate=Tu,xu.prototype.visitTimestamp=Tu,xu.prototype.visitTime=Tu,xu.prototype.visitDecimal=Tu,xu.prototype.visitList=Eu,xu.prototype.visitStruct=Au,xu.prototype.visitUnion=function(e){var t=e.type,n=e.length,r=e.typeIds,i=e.valueOffsets;if(Su.call(this,r),t.mode===Yt.Sparse)return Au.call(this,e);if(t.mode===Yt.Dense){if(e.offset<=0)return Su.call(this,i),Au.call(this,e);for(var a,o,u=r.reduce(function(e,t){return Math.max(e,t)},r[0]),s=new Int32Array(u+1),c=new Int32Array(u+1).fill(-1),l=new Int32Array(n),f=bt(-i[0],n,i),h=-1;++h<n;)-1===(o=c[a=r[h]])&&(o=c[a]=f[a]),l[h]=f[h]-o,++s[a];Su.call(this,l);for(var d,p=-1,y=t.children.length;++p<y;)if(d=e.getChildAt(p)){var v=t.typeIds[p],b=Math.min(n,s[v]);this.visit(d.slice(c[v],b))}}return this},xu.prototype.visitInterval=Tu,xu.prototype.visitFixedSizeList=Eu,xu.prototype.visitMap=Eu;function Bu(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"===typeof e)return Du(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Du(e,t)}(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){u=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(u)throw a}}}}function Du(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Cu(e){var t,n,r,i=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);i--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new Lu(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function Lu(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:t}})}return(Lu=function(e){this.s=e,this.n=e.next}).prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new Lu(e)}var Fu=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this)))._position=0,n._started=!1,n._sink=new eo,n._schema=null,n._dictionaryBlocks=[],n._recordBatchBlocks=[],n._dictionaryDeltaOffsets=new Map,ke(e)||(e={autoDestroy:!0,writeLegacyIpcFormat:!1}),n._autoDestroy="boolean"!==typeof e.autoDestroy||e.autoDestroy,n._writeLegacyIpcFormat="boolean"===typeof e.writeLegacyIpcFormat&&e.writeLegacyIpcFormat,n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toString",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this._sink.toString(e)}},{key:"toUint8Array",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this._sink.toUint8Array(e)}},{key:"writeAll",value:function(e){var t=this;return we(e)?e.then(function(e){return t.writeAll(e)}):Oe(e)?Pu(this,e):Uu(this,e)}},{key:Symbol.asyncIterator,value:function(){return this._sink[Symbol.asyncIterator]()}},{key:"toDOMStream",value:function(e){return this._sink.toDOMStream(e)}},{key:"toNodeStream",value:function(e){return this._sink.toNodeStream(e)}},{key:"close",value:function(){return this.reset()._sink.close()}},{key:"abort",value:function(e){return this.reset()._sink.abort(e)}},{key:"finish",value:function(){return this._autoDestroy?this.close():this.reset(this._sink,this._schema),this}},{key:"reset",value:function(){var e,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this._sink,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return t===this._sink||t instanceof eo?this._sink=t:(this._sink=new eo,t&&(ke(e=t)&&ge(e.abort)&&ge(e.getWriter)&&!(e instanceof se))?this.toDOMStream({type:"bytes"}).pipeTo(t):t&&function(e){return ke(e)&&ge(e.end)&&ge(e.write)&&me(e.writable)&&!(e instanceof se)}(t)&&this.toNodeStream({objectMode:!1}).pipe(t)),this._started&&this._schema&&this._writeFooter(this._schema),this._started=!1,this._dictionaryBlocks=[],this._recordBatchBlocks=[],this._dictionaryDeltaOffsets=new Map,n&&n.compareTo(this._schema)||(null===n?(this._position=0,this._schema=null):(this._started=!0,this._schema=n,this._writeSchema(n))),this}},{key:"write",value:function(e){var t=null;if(!this._sink)throw new Error("RecordBatchWriter is closed");if(null===e||void 0===e)return this.finish()&&void 0;if(e instanceof Lc&&!(t=e.schema))return this.finish()&&void 0;if(e instanceof Fc&&!(t=e.schema))return this.finish()&&void 0;if(t&&!t.compareTo(this._schema)){if(this._started&&this._autoDestroy)return this.close();this.reset(this._sink,t)}e instanceof Fc?e instanceof Mc||this._writeRecordBatch(e):e instanceof Lc?this.writeAll(e.chunks):_e(e)&&this.writeAll(e)}},{key:"_writeMessage",value:function(e){var t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:8)-1,n=eu.encode(e),r=n.byteLength,i=this._writeLegacyIpcFormat?4:8,a=r+i+t&~t,o=a-r-i;return e.headerType===Qt.RecordBatch?this._recordBatchBlocks.push(new Ja(a,e.bodyLength,this._position)):e.headerType===Qt.DictionaryBatch&&this._dictionaryBlocks.push(new Ja(a,e.bodyLength,this._position)),this._writeLegacyIpcFormat||this._write(Int32Array.of(-1)),this._write(Int32Array.of(a-i)),r>0&&this._write(n),this._writePadding(o)}},{key:"_write",value:function(e){if(this._started){var t=Ve(e);t&&t.byteLength>0&&(this._sink.write(t),this._position+=t.byteLength)}return this}},{key:"_writeSchema",value:function(e){return this._writeMessage(eu.from(e))}},{key:"_writeFooter",value:function(e){return this._writeLegacyIpcFormat?this._write(Int32Array.of(0)):this._write(Int32Array.of(-1,0))}},{key:"_writeMagic",value:function(){return this._write(gu)}},{key:"_writePadding",value:function(e){return e>0?this._write(new Uint8Array(e)):this}},{key:"_writeRecordBatch",value:function(e){var t=xu.assemble(e),n=t.byteLength,r=t.nodes,i=t.bufferRegions,a=t.buffers,o=new tu(e.length,r,i),u=eu.from(o,n);return this._writeDictionaries(e)._writeMessage(u)._writeBodyBuffers(a)}},{key:"_writeDictionaryBatch",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this._dictionaryDeltaOffsets.set(t,e.length+(this._dictionaryDeltaOffsets.get(t)||0));var r=xu.assemble(e),i=r.byteLength,a=r.nodes,o=r.bufferRegions,u=r.buffers,s=new tu(e.length,a,o),c=new nu(s,t,n),l=eu.from(c,i);return this._writeMessage(l)._writeBodyBuffers(u)}},{key:"_writeBodyBuffers",value:function(e){for(var t,n,r,i=-1,a=e.length;++i<a;)(t=e[i])&&(n=t.byteLength)>0&&(this._write(t),(r=(n+7&-8)-n)>0&&this._writePadding(r));return this}},{key:"_writeDictionaries",value:function(e){var t,n=Bu(e.dictionaries);try{for(n.s();!(t=n.n()).done;){var r=F(t.value,2),i=r[0],a=r[1],o=this._dictionaryDeltaOffsets.get(i)||0;if(0===o||(a=a.slice(o)).length>0){var u,s=Bu("chunks"in a?a.chunks:[a]);try{for(s.s();!(u=s.n()).done;){var c=u.value;this._writeDictionaryBatch(c,i,o>0),o+=c.length}}catch(l){s.e(l)}finally{s.f()}}}}catch(l){n.e(l)}finally{n.f()}return this}},{key:"closed",get:function(){return this._sink.closed}}],[{key:"throughNode",value:function(e){throw new Error('"throughNode" not available in this environment')}},{key:"throughDOM",value:function(e,t){throw new Error('"throughDOM" not available in this environment')}}]),t}(se),Mu=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,null,[{key:"writeAll",value:function(e,n){var r=new t(n);return we(e)?e.then(function(e){return r.writeAll(e)}):Oe(e)?Pu(r,e):Uu(r,e)}}]),t}(Fu),Nu=function(e){function t(){var e;return Object(D.a)(this,t),(e=Object(re.a)(this,Object(ie.a)(t).call(this)))._autoDestroy=!0,e}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"_writeSchema",value:function(e){return this._writeMagic()._writePadding(2)}},{key:"_writeFooter",value:function(e){var n=qa.encode(new qa(e,qt.V4,this._recordBatchBlocks,this._dictionaryBlocks));return Ct(Object(ie.a)(t.prototype),"_writeFooter",this).call(this,e)._write(n)._write(Int32Array.of(n.byteLength))._writeMagic()}}],[{key:"writeAll",value:function(e){var n=new t;return we(e)?e.then(function(e){return n.writeAll(e)}):Oe(e)?Pu(n,e):Uu(n,e)}}]),t}(Fu);function Uu(e,t){var n=t;t instanceof Lc&&(n=t.chunks,e.reset(void 0,t.schema));var r,i=Bu(n);try{for(i.s();!(r=i.n()).done;){var a=r.value;e.write(a)}}catch(o){i.e(o)}finally{i.f()}return e.finish()}function Pu(e,t){return Ru.apply(this,arguments)}function Ru(){return(Ru=B(L.mark(function e(t,n){var r,i,a,o,u,s;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:r=!1,i=!1,e.prev=2,o=Cu(n);case 4:return e.next=6,o.next();case 6:if(!(r=!(u=e.sent).done)){e.next=12;break}s=u.value,t.write(s);case 9:r=!1,e.next=4;break;case 12:e.next=18;break;case 14:e.prev=14,e.t0=e.catch(2),i=!0,a=e.t0;case 18:if(e.prev=18,e.prev=19,!r||null==o.return){e.next=23;break}return e.next=23,o.return();case 23:if(e.prev=23,!i){e.next=26;break}throw a;case 26:return e.finish(23);case 27:return e.finish(18);case 28:return e.abrupt("return",t.finish());case 29:case"end":return e.stop()}},e,null,[[2,14,18,28],[19,,23,27]])}))).apply(this,arguments)}var zu=new Uint8Array(0),Vu=function(e){return[zu,zu,new Uint8Array(e),zu]};function Wu(e,t){return function(e,t){var n,r=cn(e.fields),i=[],a={numBatches:t.reduce(function(e,t){return Math.max(e,t.length)},0)},o=0,u=0,s=-1,c=t.length,l=[];for(;a.numBatches-- >0;){for(u=Number.POSITIVE_INFINITY,s=-1;++s<c;)l[s]=n=t[s].shift(),u=Math.min(u,n?n.length:u);isFinite(u)&&(l=$u(r,u,l,t,a),u>0&&(i[o++]=[u,l.slice()]))}return[e=new sa(r,e.metadata),i.map(function(t){return Pr(Fc,[e].concat(cn(t)))})]}(e,t.map(function(e){return e instanceof qi?e.chunks.map(function(e){return e.data}):[e.data]}))}function $u(e,t,n,r,i){for(var a,o,u=0,s=-1,c=r.length,l=(t+63&-64)>>3;++s<c;)(a=n[s])&&(u=a.length)>=t?u===t?n[s]=a:(n[s]=a.slice(0,t),a=a.slice(t,u-t),i.numBatches=Math.max(i.numBatches,r[s].unshift(a))):((o=e[s]).nullable||(e[s]=o.clone({nullable:!0})),n[s]=a?a._changeLengthAndBackfillNullBitmap(t):ar.new(o.type,0,t,t,Vu(l)));return n}function Hu(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}var Yu=function(e){function t(e,n){var r;return Object(D.a)(this,t),(r=Object(re.a)(this,Object(ie.a)(t).call(this)))._children=n,r.numChildren=e.childData.length,r._bindDataAccessors(r.data=e),r}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"clone",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._children;return Rt.new(e,t)}},{key:"concat",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return qi.concat.apply(qi,[this].concat(t))}},{key:"slice",value:function(e,t){return zi(this,e,t,this._sliceInternal)}},{key:"isValid",value:function(e){if(this.nullCount>0){var t=this.offset+e;return 0!==(this.nullBitmap[t>>3]&1<<t%8)}return!0}},{key:"getChildAt",value:function(e){return e<0||e>=this.numChildren?null:(this._children||(this._children=[]))[e]||(this._children[e]=Rt.new(this.data.childData[e]))}},{key:"toJSON",value:function(){return cn(this)}},{key:"_sliceInternal",value:function(e,t,n){return e.clone(e.data.slice(t,n-t),null)}},{key:"_bindDataAccessors",value:function(e){}},{key:"type",get:function(){return this.data.type}},{key:"typeId",get:function(){return this.data.typeId}},{key:"length",get:function(){return this.data.length}},{key:"offset",get:function(){return this.data.offset}},{key:"stride",get:function(){return this.data.stride}},{key:"nullCount",get:function(){return this.data.nullCount}},{key:"byteLength",get:function(){return this.data.byteLength}},{key:"VectorName",get:function(){return"".concat(zt[this.typeId],"Vector")}},{key:"ArrayType",get:function(){return this.type.ArrayType}},{key:"values",get:function(){return this.data.values}},{key:"typeIds",get:function(){return this.data.typeIds}},{key:"nullBitmap",get:function(){return this.data.nullBitmap}},{key:"valueOffsets",get:function(){return this.data.valueOffsets}},{key:Symbol.toStringTag,get:function(){return"".concat(this.VectorName,"<").concat(this.type[Symbol.toStringTag],">")}}]),t}(Rt);Yu.prototype[Symbol.isConcatSpreadable]=!0;var Ku=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"asUtf8",value:function(){return Rt.new(this.data.clone(new Rn))}}]),t}(Yu),Qu=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,null,[{key:"from",value:function(e){return Bc(function(){return new zn},e)}}]),t}(Yu),qu=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,null,[{key:"from",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return 2===t.length?Bc(function(){return t[1]===Wt.DAY?new $n:new Hn},t[0]):Bc(function(){return new Hn},t[0])}}]),t}(Yu),Gu=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(qu),Ju=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(qu),Xu=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Yu),Zu=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this,e))).indices=Rt.new(e.clone(n.type.indices)),n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"reverseLookup",value:function(e){return this.dictionary.indexOf(e)}},{key:"getKey",value:function(e){return this.indices.get(e)}},{key:"getValue",value:function(e){return this.dictionary.get(e)}},{key:"setKey",value:function(e,t){return this.indices.set(e,t)}},{key:"setValue",value:function(e,t){return this.dictionary.set(e,t)}},{key:"dictionary",get:function(){return this.data.dictionary}}],[{key:"from",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];if(3===t.length){var r=t[0],i=t[1],a=t[2],o=new rr(r.type,i,null,null);return Rt.new(ar.Dictionary(o,0,a.length,0,null,a,r))}return Bc(function(){return t[0].type},t[0])}}]),t}(Yu);Zu.prototype.indices=null;var es=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Yu),ts=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Yu),ns=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,null,[{key:"from",value:function(e){var t=ss(this);if(e instanceof ArrayBuffer||ArrayBuffer.isView(e)){var n=us(e.constructor)||t;if(null===t&&(t=n),t&&t===n){var r=new t,i=e.byteLength/r.ArrayType.BYTES_PER_ELEMENT;if(!os(t,e.constructor))return Rt.new(ar.Float(r,0,i,0,null,e))}}if(t)return Bc(function(){return new t},e);if(e instanceof DataView||e instanceof ArrayBuffer)throw new TypeError("Cannot infer float type from instance of ".concat(e.constructor.name));throw new TypeError("Unrecognized FloatVector input")}}]),t}(Yu),rs=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toFloat32Array",value:function(){return new Float32Array(this)}},{key:"toFloat64Array",value:function(){return new Float64Array(this)}}]),t}(ns),is=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(ns),as=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(ns),os=function(e,t){return e===Mn&&t!==Uint16Array},us=function(e){switch(e){case Uint16Array:return Mn;case Float32Array:return Nn;case Float64Array:return Un;default:return null}},ss=function(e){switch(e){case rs:return Mn;case is:return Nn;case as:return Un;default:return null}},cs=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Yu),ls=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(cs),fs=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(cs),hs=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,null,[{key:"from",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t[0],i=t[1],a=void 0!==i&&i,o=Os(this,a);if(r instanceof ArrayBuffer||ArrayBuffer.isView(r)){var u=_s(r.constructor,a)||o;if(null===o&&(o=u),o&&o===u){var s=new o,c=r.byteLength/s.ArrayType.BYTES_PER_ELEMENT;return ws(o,r.constructor)&&(c*=.5),Rt.new(ar.Int(s,0,c,0,null,r))}}if(o)return Bc(function(){return new o},r);if(r instanceof DataView||r instanceof ArrayBuffer)throw new TypeError("Cannot infer integer type from instance of ".concat(r.constructor.name));throw new TypeError("Unrecognized IntVector input")}}]),t}(Yu),ds=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(hs),ps=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(hs),ys=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(hs),vs=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toBigInt64Array",value:function(){return ze(this.values)}},{key:"values64",get:function(){return this._values64||(this._values64=this.toBigInt64Array())}}]),t}(hs),bs=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(hs),ms=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(hs),gs=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(hs),ks=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toBigUint64Array",value:function(){return He(this.values)}},{key:"values64",get:function(){return this._values64||(this._values64=this.toBigUint64Array())}}]),t}(hs),ws=function(e,t){return(e===An||e===Ln)&&(t===Int32Array||t===Uint32Array)},_s=function(e,t){switch(e){case Int8Array:return Tn;case Int16Array:return In;case Int32Array:return t?An:En;case pe:return An;case Uint8Array:return Bn;case Uint16Array:return Dn;case Uint32Array:return t?Ln:Cn;case ve:return Ln;default:return null}},Os=function(e,t){switch(e){case ds:return Tn;case ps:return In;case ys:return t?An:En;case vs:return An;case bs:return Bn;case ms:return Dn;case gs:return t?Ln:Cn;case ks:return Ln;default:return null}},js=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Yu),xs=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"asList",value:function(){var e=this.type.children[0];return Rt.new(this.data.clone(new qn(e)))}},{key:"bind",value:function(e){var t=this.getChildAt(0),n=this.valueOffsets,r=n[e],i=n[e+1];return new Fi(t.slice(r,i))}}]),t}(Yu),Ss=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Yu),Ts=Symbol.for("rowIndex"),Is=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"bind",value:function(e){var t=this._row||(this._row=new Mi(this)),n=Object.create(t);return n[Ts]=e,n}}]),t}(Yu),Es=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Yu),As=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Es),Bs=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Es),Ds=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Es),Cs=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Es),Ls=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Yu),Fs=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Ls),Ms=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Ls),Ns=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Ls),Us=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Ls),Ps=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"typeIdToChildIndex",get:function(){return this.data.type.typeIdToChildIndex}}]),t}(Yu),Rs=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"valueOffsets",get:function(){return this.data.valueOffsets}}]),t}(Ps),zs=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(Ps),Vs=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"asBinary",value:function(){return Rt.new(this.data.clone(new Pn))}}],[{key:"from",value:function(e){return Bc(function(){return new Rn},e)}}]),t}(Yu);function Ws(e){return function(){return e(this)}}function $s(e){return function(t,n){return e(this,t,n)}}var Hs=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(ln),Ys=function(e,t){return 4294967296*e[t+1]+(e[t]>>>0)},Ks=function(e){return new Date(e)},Qs=function(e,t,n){var r=t[n],i=t[n+1];return null!=r&&null!=i?e.subarray(r,i):null},qs=function(e,t){return function(e,t){return Ks(function(e,t){return 864e5*e[t]}(e,t))}(e.values,t)},Gs=function(e,t){return function(e,t){return Ks(Ys(e,t))}(e.values,2*t)},Js=function(e,t){var n=e.stride;return e.values[n*t]},Xs=function(e,t){var n=e.stride;return Dr(e.values[n*t])},Zs=function(e,t){var n=e.stride,r=e.values,i=e.type;return Gr.new(r.subarray(n*t,n*(t+1)),i.isSigned)},ec=function(e,t){var n=e.values;return 1e3*Ys(n,2*t)},tc=function(e,t){var n=e.values;return Ys(n,2*t)},nc=function(e,t){return function(e,t){return e[t+1]/1e3*4294967296+(e[t]>>>0)/1e3}(e.values,2*t)},rc=function(e,t){return function(e,t){return e[t+1]/1e6*4294967296+(e[t]>>>0)/1e6}(e.values,2*t)},ic=function(e,t){return e.values[e.stride*t]},ac=function(e,t){return e.values[e.stride*t]},oc=function(e,t){var n=e.values;return Gr.signed(n.subarray(2*t,2*(t+1)))},uc=function(e,t){var n=e.values;return Gr.signed(n.subarray(2*t,2*(t+1)))},sc=function(e,t){var n=e.typeIdToChildIndex[e.typeIds[t]],r=e.getChildAt(n);return r?r.get(e.valueOffsets[t]):null},cc=function(e,t){var n=e.typeIdToChildIndex[e.typeIds[t]],r=e.getChildAt(n);return r?r.get(t):null},lc=function(e,t){return e.values.subarray(2*t,2*(t+1))},fc=function(e,t){var n=e.values[t],r=new Int32Array(2);return r[0]=n/12|0,r[1]=n%12|0,r};Hs.prototype.visitNull=function(e,t){return null},Hs.prototype.visitBool=function(e,t){var n=e.offset+t;return 0!==(e.values[n>>3]&1<<n%8)},Hs.prototype.visitInt=function(e,t){return e.type.bitWidth<64?Js(e,t):Zs(e,t)},Hs.prototype.visitInt8=Js,Hs.prototype.visitInt16=Js,Hs.prototype.visitInt32=Js,Hs.prototype.visitInt64=Zs,Hs.prototype.visitUint8=Js,Hs.prototype.visitUint16=Js,Hs.prototype.visitUint32=Js,Hs.prototype.visitUint64=Zs,Hs.prototype.visitFloat=function(e,t){return e.type.precision!==Ht.HALF?Js(e,t):Xs(e,t)},Hs.prototype.visitFloat16=Xs,Hs.prototype.visitFloat32=Js,Hs.prototype.visitFloat64=Js,Hs.prototype.visitUtf8=function(e,t){var n=e.values,r=e.valueOffsets,i=Qs(n,r,t);return null!==i?te(i):null},Hs.prototype.visitBinary=function(e,t){var n=e.values,r=e.valueOffsets;return Qs(n,r,t)},Hs.prototype.visitFixedSizeBinary=function(e,t){var n=e.stride;return e.values.subarray(n*t,n*(t+1))},Hs.prototype.visitDate=function(e,t){return e.type.unit===Wt.DAY?qs(e,t):Gs(e,t)},Hs.prototype.visitDateDay=qs,Hs.prototype.visitDateMillisecond=Gs,Hs.prototype.visitTimestamp=function(e,t){switch(e.type.unit){case $t.SECOND:return ec(e,t);case $t.MILLISECOND:return tc(e,t);case $t.MICROSECOND:return nc(e,t);case $t.NANOSECOND:return rc(e,t)}},Hs.prototype.visitTimestampSecond=ec,Hs.prototype.visitTimestampMillisecond=tc,Hs.prototype.visitTimestampMicrosecond=nc,Hs.prototype.visitTimestampNanosecond=rc,Hs.prototype.visitTime=function(e,t){switch(e.type.unit){case $t.SECOND:return ic(e,t);case $t.MILLISECOND:return ac(e,t);case $t.MICROSECOND:return oc(e,t);case $t.NANOSECOND:return uc(e,t)}},Hs.prototype.visitTimeSecond=ic,Hs.prototype.visitTimeMillisecond=ac,Hs.prototype.visitTimeMicrosecond=oc,Hs.prototype.visitTimeNanosecond=uc,Hs.prototype.visitDecimal=function(e,t){var n=e.values;return Gr.decimal(n.subarray(4*t,4*(t+1)))},Hs.prototype.visitList=function(e,t){var n=e.getChildAt(0),r=e.valueOffsets,i=e.stride;return n.slice(r[t*i],r[t*i+1])},Hs.prototype.visitStruct=function(e,t){return e.bind(t)},Hs.prototype.visitUnion=function(e,t){return e.type.mode===Yt.Dense?sc(e,t):cc(e,t)},Hs.prototype.visitDenseUnion=sc,Hs.prototype.visitSparseUnion=cc,Hs.prototype.visitDictionary=function(e,t){return e.getValue(e.getKey(t))},Hs.prototype.visitInterval=function(e,t){return e.type.unit===Kt.DAY_TIME?lc(e,t):fc(e,t)},Hs.prototype.visitIntervalDayTime=lc,Hs.prototype.visitIntervalYearMonth=fc,Hs.prototype.visitFixedSizeList=function(e,t){var n=e.getChildAt(0),r=e.stride;return n.slice(t*r,(t+1)*r)},Hs.prototype.visitMap=function(e,t){return e.bind(t)};var hc=new Hs;function dc(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"===typeof e)return pc(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return pc(e,t)}(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){u=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(u)throw a}}}}function pc(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var yc=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(ln);function vc(e,t,n){if(void 0===t)return-1;if(null===t)return function(e,t){var n=e.nullBitmap;if(!n||e.nullCount<=0)return-1;var r,i=0,a=dc(an(n,e.data.offset+(t||0),e.length,n,Zt));try{for(a.s();!(r=a.n()).done;){if(!r.value)return i;++i}}catch(o){a.e(o)}finally{a.f()}return-1}(e,n);for(var r=$i(t),i=(n||0)-1,a=e.length;++i<a;)if(r(e.get(i)))return i;return-1}function bc(e,t,n){for(var r=$i(t),i=(n||0)-1,a=e.length;++i<a;)if(r(e.get(i)))return i;return-1}yc.prototype.visitNull=function(e,t){return null===t&&e.length>0?0:-1},yc.prototype.visitBool=vc,yc.prototype.visitInt=vc,yc.prototype.visitInt8=vc,yc.prototype.visitInt16=vc,yc.prototype.visitInt32=vc,yc.prototype.visitInt64=vc,yc.prototype.visitUint8=vc,yc.prototype.visitUint16=vc,yc.prototype.visitUint32=vc,yc.prototype.visitUint64=vc,yc.prototype.visitFloat=vc,yc.prototype.visitFloat16=vc,yc.prototype.visitFloat32=vc,yc.prototype.visitFloat64=vc,yc.prototype.visitUtf8=vc,yc.prototype.visitBinary=vc,yc.prototype.visitFixedSizeBinary=vc,yc.prototype.visitDate=vc,yc.prototype.visitDateDay=vc,yc.prototype.visitDateMillisecond=vc,yc.prototype.visitTimestamp=vc,yc.prototype.visitTimestampSecond=vc,yc.prototype.visitTimestampMillisecond=vc,yc.prototype.visitTimestampMicrosecond=vc,yc.prototype.visitTimestampNanosecond=vc,yc.prototype.visitTime=vc,yc.prototype.visitTimeSecond=vc,yc.prototype.visitTimeMillisecond=vc,yc.prototype.visitTimeMicrosecond=vc,yc.prototype.visitTimeNanosecond=vc,yc.prototype.visitDecimal=vc,yc.prototype.visitList=vc,yc.prototype.visitStruct=vc,yc.prototype.visitUnion=vc,yc.prototype.visitDenseUnion=bc,yc.prototype.visitSparseUnion=bc,yc.prototype.visitDictionary=vc,yc.prototype.visitInterval=vc,yc.prototype.visitIntervalDayTime=vc,yc.prototype.visitIntervalYearMonth=vc,yc.prototype.visitFixedSizeList=vc,yc.prototype.visitMap=vc;var mc=new yc,gc=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(ln);function kc(e){if(e.nullCount>0)return function(e){var t=hc.getVisitFn(e);return an(e.nullBitmap,e.offset,e.length,e,function(e,n,r,i){return 0!==(r&1<<i)?t(e,n):null})}(e);var t=e.type,n=e.typeId,r=e.length;return 1===e.stride&&(n===zt.Timestamp||n===zt.Int&&64!==t.bitWidth||n===zt.Time&&64!==t.bitWidth||n===zt.Float&&t.precision>0)?e.values.subarray(0,r)[Symbol.iterator]():L.mark(function t(n){var i;return L.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:i=-1;case 1:if(!(++i<r)){t.next=6;break}return t.next=4,n(e,i);case 4:t.next=1;break;case 6:case"end":return t.stop()}},t)})(hc.getVisitFn(e))}gc.prototype.visitNull=kc,gc.prototype.visitBool=kc,gc.prototype.visitInt=kc,gc.prototype.visitInt8=kc,gc.prototype.visitInt16=kc,gc.prototype.visitInt32=kc,gc.prototype.visitInt64=kc,gc.prototype.visitUint8=kc,gc.prototype.visitUint16=kc,gc.prototype.visitUint32=kc,gc.prototype.visitUint64=kc,gc.prototype.visitFloat=kc,gc.prototype.visitFloat16=kc,gc.prototype.visitFloat32=kc,gc.prototype.visitFloat64=kc,gc.prototype.visitUtf8=kc,gc.prototype.visitBinary=kc,gc.prototype.visitFixedSizeBinary=kc,gc.prototype.visitDate=kc,gc.prototype.visitDateDay=kc,gc.prototype.visitDateMillisecond=kc,gc.prototype.visitTimestamp=kc,gc.prototype.visitTimestampSecond=kc,gc.prototype.visitTimestampMillisecond=kc,gc.prototype.visitTimestampMicrosecond=kc,gc.prototype.visitTimestampNanosecond=kc,gc.prototype.visitTime=kc,gc.prototype.visitTimeSecond=kc,gc.prototype.visitTimeMillisecond=kc,gc.prototype.visitTimeMicrosecond=kc,gc.prototype.visitTimeNanosecond=kc,gc.prototype.visitDecimal=kc,gc.prototype.visitList=kc,gc.prototype.visitStruct=kc,gc.prototype.visitUnion=kc,gc.prototype.visitDenseUnion=kc,gc.prototype.visitSparseUnion=kc,gc.prototype.visitDictionary=kc,gc.prototype.visitInterval=kc,gc.prototype.visitIntervalDayTime=kc,gc.prototype.visitIntervalYearMonth=kc,gc.prototype.visitFixedSizeList=kc,gc.prototype.visitMap=kc;var wc=new gc,_c=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),t}(ln);function Oc(e){var t=e.type,n=e.length,r=e.stride;switch(t.typeId){case zt.Int:case zt.Float:case zt.Decimal:case zt.Time:case zt.Timestamp:return e.values.subarray(0,n*r)}return cn(wc.visit(e))}_c.prototype.visitNull=Oc,_c.prototype.visitBool=Oc,_c.prototype.visitInt=Oc,_c.prototype.visitInt8=Oc,_c.prototype.visitInt16=Oc,_c.prototype.visitInt32=Oc,_c.prototype.visitInt64=Oc,_c.prototype.visitUint8=Oc,_c.prototype.visitUint16=Oc,_c.prototype.visitUint32=Oc,_c.prototype.visitUint64=Oc,_c.prototype.visitFloat=Oc,_c.prototype.visitFloat16=Oc,_c.prototype.visitFloat32=Oc,_c.prototype.visitFloat64=Oc,_c.prototype.visitUtf8=Oc,_c.prototype.visitBinary=Oc,_c.prototype.visitFixedSizeBinary=Oc,_c.prototype.visitDate=Oc,_c.prototype.visitDateDay=Oc,_c.prototype.visitDateMillisecond=Oc,_c.prototype.visitTimestamp=Oc,_c.prototype.visitTimestampSecond=Oc,_c.prototype.visitTimestampMillisecond=Oc,_c.prototype.visitTimestampMicrosecond=Oc,_c.prototype.visitTimestampNanosecond=Oc,_c.prototype.visitTime=Oc,_c.prototype.visitTimeSecond=Oc,_c.prototype.visitTimeMillisecond=Oc,_c.prototype.visitTimeMicrosecond=Oc,_c.prototype.visitTimeNanosecond=Oc,_c.prototype.visitDecimal=Oc,_c.prototype.visitList=Oc,_c.prototype.visitStruct=Oc,_c.prototype.visitUnion=Oc,_c.prototype.visitDenseUnion=Oc,_c.prototype.visitSparseUnion=Oc,_c.prototype.visitDictionary=Oc,_c.prototype.visitInterval=Oc,_c.prototype.visitIntervalDayTime=Oc,_c.prototype.visitIntervalYearMonth=Oc,_c.prototype.visitFixedSizeList=Oc,_c.prototype.visitMap=Oc;var jc=new _c,xc=function(e,t){return e+t},Sc=function(e){return"Cannot compute the byte width of variable-width column ".concat(e)},Tc=new(function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"visitNull",value:function(e){return 0}},{key:"visitInt",value:function(e){return e.bitWidth/8}},{key:"visitFloat",value:function(e){return e.ArrayType.BYTES_PER_ELEMENT}},{key:"visitBinary",value:function(e){throw new Error(Sc(e))}},{key:"visitUtf8",value:function(e){throw new Error(Sc(e))}},{key:"visitBool",value:function(e){return 1/8}},{key:"visitDecimal",value:function(e){return 16}},{key:"visitDate",value:function(e){return 4*(e.unit+1)}},{key:"visitTime",value:function(e){return e.bitWidth/8}},{key:"visitTimestamp",value:function(e){return e.unit===$t.SECOND?4:8}},{key:"visitInterval",value:function(e){return 4*(e.unit+1)}},{key:"visitList",value:function(e){throw new Error(Sc(e))}},{key:"visitStruct",value:function(e){return this.visitFields(e.children).reduce(xc,0)}},{key:"visitUnion",value:function(e){return this.visitFields(e.children).reduce(xc,0)}},{key:"visitFixedSizeBinary",value:function(e){return e.byteWidth}},{key:"visitFixedSizeList",value:function(e){return e.listSize*this.visitFields(e.children).reduce(xc,0)}},{key:"visitMap",value:function(e){return this.visitFields(e.children).reduce(xc,0)}},{key:"visitDictionary",value:function(e){return this.visit(e.indices)}},{key:"visitFields",value:function(e){var t=this;return(e||[]).map(function(e){return t.visit(e.type)})}},{key:"visitSchema",value:function(e){return this.visitFields(e.fields).reduce(xc,0)}}]),t}(ln)),Ic=new(function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"visitNull",value:function(){return Ss}},{key:"visitBool",value:function(){return Qu}},{key:"visitInt",value:function(){return hs}},{key:"visitInt8",value:function(){return ds}},{key:"visitInt16",value:function(){return ps}},{key:"visitInt32",value:function(){return ys}},{key:"visitInt64",value:function(){return vs}},{key:"visitUint8",value:function(){return bs}},{key:"visitUint16",value:function(){return ms}},{key:"visitUint32",value:function(){return gs}},{key:"visitUint64",value:function(){return ks}},{key:"visitFloat",value:function(){return ns}},{key:"visitFloat16",value:function(){return rs}},{key:"visitFloat32",value:function(){return is}},{key:"visitFloat64",value:function(){return as}},{key:"visitUtf8",value:function(){return Vs}},{key:"visitBinary",value:function(){return Ku}},{key:"visitFixedSizeBinary",value:function(){return es}},{key:"visitDate",value:function(){return qu}},{key:"visitDateDay",value:function(){return Gu}},{key:"visitDateMillisecond",value:function(){return Ju}},{key:"visitTimestamp",value:function(){return Es}},{key:"visitTimestampSecond",value:function(){return As}},{key:"visitTimestampMillisecond",value:function(){return Bs}},{key:"visitTimestampMicrosecond",value:function(){return Ds}},{key:"visitTimestampNanosecond",value:function(){return Cs}},{key:"visitTime",value:function(){return Ls}},{key:"visitTimeSecond",value:function(){return Fs}},{key:"visitTimeMillisecond",value:function(){return Ms}},{key:"visitTimeMicrosecond",value:function(){return Ns}},{key:"visitTimeNanosecond",value:function(){return Us}},{key:"visitDecimal",value:function(){return Xu}},{key:"visitList",value:function(){return js}},{key:"visitStruct",value:function(){return Is}},{key:"visitUnion",value:function(){return Ps}},{key:"visitDenseUnion",value:function(){return Rs}},{key:"visitSparseUnion",value:function(){return zs}},{key:"visitDictionary",value:function(){return Zu}},{key:"visitInterval",value:function(){return cs}},{key:"visitIntervalDayTime",value:function(){return ls}},{key:"visitIntervalYearMonth",value:function(){return fs}},{key:"visitFixedSizeList",value:function(){return ts}},{key:"visitMap",value:function(){return xs}}]),t}(ln));function Ec(e){var t,n,r,i=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);i--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new Ac(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function Ac(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:t}})}return(Ac=function(e){this.s=e,this.n=e.next}).prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new Ac(e)}function Bc(e,t){if(_e(t))return Rt.from({nullValues:[null,void 0],type:e(),values:t});if(Oe(t))return Rt.from({nullValues:[null,void 0],type:e(),values:t});var n=Object(Dt.a)({},t),r=n.values,i=void 0===r?[]:r,a=n.type,o=void 0===a?e():a,u=n.nullValues,s=void 0===u?[null,void 0]:u;return _e(i),Rt.from(Object(Dt.a)({nullValues:s},t,{type:o}))}function Dc(e){var t,n,r,i=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);i--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new Cc(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function Cc(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:t}})}return(Cc=function(e){this.s=e,this.n=e.next}).prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new Cc(e)}Rt.new=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return Pr(Ic.getVisitFn(e)(),[e].concat(n))},Rt.from=function(e){var t=Object(Dt.a)({nullValues:[null,void 0]},e),n=t.values,r=void 0===n?[]:n,i=Hu(t,["values"]);if(_e(r)){var a=cn(gr.throughIterable(i)(r));return 1===a.length?a[0]:qi.concat(a)}return function(){var e=B(L.mark(function e(t){var n,a,o,u,s,c,l;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:n=gr.throughAsyncIterable(i),a=!1,o=!1,e.prev=3,s=Ec(n(r));case 5:return e.next=7,s.next();case 7:if(!(a=!(c=e.sent).done)){e.next=13;break}l=c.value,t.push(l);case 10:a=!1,e.next=5;break;case 13:e.next=19;break;case 15:e.prev=15,e.t0=e.catch(3),o=!0,u=e.t0;case 19:if(e.prev=19,e.prev=20,!a||null==s.return){e.next=24;break}return e.next=24,s.return();case 24:if(e.prev=24,!o){e.next=27;break}throw u;case 27:return e.finish(24);case 28:return e.finish(19);case 29:return e.abrupt("return",1===t.length?t[0]:qi.concat(t));case 30:case"end":return e.stop()}},e,null,[[3,15,19,29],[20,,24,28]])}));return function(t){return e.apply(this,arguments)}}()([])},Yu.prototype.get=function(e){return hc.visit(this,e)},Yu.prototype.set=function(e,t){return Va.visit(this,e,t)},Yu.prototype.indexOf=function(e,t){return mc.visit(this,e,t)},Yu.prototype.toArray=function(){return jc.visit(this)},Yu.prototype.getByteWidth=function(){return Tc.visit(this.type)},Yu.prototype[Symbol.iterator]=function(){return wc.visit(this)},Yu.prototype._bindDataAccessors=function(){var e=this.nullBitmap;e&&e.byteLength>0&&(this.get=(t=this.get,function(e){return this.isValid(e)?t.call(this,e):null}),this.set=function(e){return function(t,n){tn(this.nullBitmap,this.offset+t,!(null===n||void 0===n))&&e.call(this,t,n)}}(this.set));var t},Object.keys(zt).map(function(e){return zt[e]}).filter(function(e){return"number"===typeof e}).filter(function(e){return e!==zt.NONE}).forEach(function(e){var t,n=Ic.visit(e);n.prototype.get=(t=hc.getVisitFn(e),function(e){return t(this,e)}),n.prototype.set=$s(Va.getVisitFn(e)),n.prototype.indexOf=$s(mc.getVisitFn(e)),n.prototype.toArray=Ws(jc.getVisitFn(e)),n.prototype.getByteWidth=function(e){return function(){return e(this.type)}}(Tc.getVisitFn(e)),n.prototype[Symbol.iterator]=Ws(wc.getVisitFn(e))});var Lc=function(e){function t(){var e;Object(D.a)(this,t);for(var n=null,r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];i[0]instanceof sa&&(n=i.shift());var o=ta(Fc,i);if(!n&&!(n=o[0]&&o[0].schema))throw new TypeError("Table must be initialized with a Schema or at least one RecordBatch");return o[0]||(o[0]=new Mc(n)),(e=Object(re.a)(this,Object(ie.a)(t).call(this,new Gn(n.fields),o)))._schema=n,e._chunks=o,e}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"clone",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this._chunks;return new t(this._schema,e)}},{key:"getColumn",value:function(e){return this.getColumnAt(this.getColumnIndex(e))}},{key:"getColumnAt",value:function(e){return this.getChildAt(e)}},{key:"getColumnIndex",value:function(e){return this._schema.fields.findIndex(function(t){return t.name===e})}},{key:"getChildAt",value:function(e){if(e<0||e>=this.numChildren)return null;var t,n,r=this._schema.fields,i=this._children||(this._children=[]);if(n=i[e])return n;if(t=r[e]){var a=this._chunks.map(function(t){return t.getChildAt(e)}).filter(function(e){return null!=e});if(a.length>0)return i[e]=new Xi(t,a)}return null}},{key:"serialize",value:function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0];return(!(arguments.length>1&&void 0!==arguments[1])||arguments[1]?Mu:Nu).writeAll(this).toUint8Array(!0)}},{key:"count",value:function(){return this._length}},{key:"select",value:function(){for(var e=this._schema.fields.reduce(function(e,t,n){return e.set(t.name,n)},new Map),t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return this.selectAt.apply(this,cn(n.map(function(t){return e.get(t)}).filter(function(e){return e>-1})))}},{key:"selectAt",value:function(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];var a=(e=this._schema).selectAt.apply(e,r);return new t(a,this._chunks.map(function(e){var t=e.length,n=e.data.childData;return new Fc(a,t,r.map(function(e){return n[e]}).filter(Boolean))}))}},{key:"assign",value:function(e){var n=this,r=this._schema.fields,i=e.schema.fields.reduce(function(e,t,n){var i=F(e,2),a=i[0],o=i[1],u=r.findIndex(function(e){return e.name===t.name});return~u?o[u]=n:a.push(n),e},[[],[]]),a=F(i,2),o=a[0],u=a[1],s=this._schema.assign(e.schema),c=[].concat(cn(r.map(function(t,r,i){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:u[r];return void 0===a?n.getColumnAt(r):e.getColumnAt(a)})),cn(o.map(function(t){return e.getColumnAt(t)}))).filter(Boolean);return Pr(t,cn(Wu(s,c)))}},{key:"schema",get:function(){return this._schema}},{key:"length",get:function(){return this._length}},{key:"chunks",get:function(){return this._chunks}},{key:"numCols",get:function(){return this._numChildren}}],[{key:"empty",value:function(){return new t(arguments.length>0&&void 0!==arguments[0]?arguments[0]:new sa([]),[])}},{key:"from",value:function(e){if(!e)return t.empty();if("object"===typeof e){var n=_e(e.values)?function(e){if(e.type instanceof Gn)return Lc.fromStruct(Is.from(e));return null}(e):Oe(e.values)?function(e){if(e.type instanceof Gn)return Is.from(e).then(function(e){return Lc.fromStruct(e)});return null}(e):null;if(null!==n)return n}var r=Wc.from(e);return we(r)?B(L.mark(function e(){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.t0=t,e.next=3,r;case 3:return e.t1=e.sent,e.next=6,e.t0.from.call(e.t0,e.t1);case 6:return e.abrupt("return",e.sent);case 7:case"end":return e.stop()}},e)}))():r.isSync()&&(r=r.open())?r.schema?new t(r.schema,cn(r)):t.empty():function(){var e=B(L.mark(function e(n){var r,i,a,o,u,s,c,l,f;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n;case 2:if(r=e.sent,i=r.schema,a=[],!i){e.next=35;break}o=!1,u=!1,e.prev=8,c=Dc(r);case 10:return e.next=12,c.next();case 12:if(!(o=!(l=e.sent).done)){e.next=18;break}f=l.value,a.push(f);case 15:o=!1,e.next=10;break;case 18:e.next=24;break;case 20:e.prev=20,e.t0=e.catch(8),u=!0,s=e.t0;case 24:if(e.prev=24,e.prev=25,!o||null==c.return){e.next=29;break}return e.next=29,c.return();case 29:if(e.prev=29,!u){e.next=32;break}throw s;case 32:return e.finish(29);case 33:return e.finish(24);case 34:return e.abrupt("return",new t(i,a));case 35:return e.abrupt("return",t.empty());case 36:case"end":return e.stop()}},e,null,[[8,20,24,34],[25,,29,33]])}));return function(t){return e.apply(this,arguments)}}()(r.open())}},{key:"fromAsync",value:function(){var e=B(L.mark(function e(n){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.from(n);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}()},{key:"fromStruct",value:function(e){return t.new(e.data.childData,e.type.children)}},{key:"new",value:function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return Pr(t,cn((i=function(e){var t=F(ua(e,[[],[]]),2),n=t[0];return t[1].map(function(e,t){return e instanceof Xi?Xi.new(e.field.clone(n[t]),e):e instanceof Rt?Xi.new(n[t],e):Xi.new(n[t],[])})}(n),Wu(new sa(i.map(function(e){return e.field})),i))));var i}}]),t}(qi);var Fc=function(e){function t(){var e,n;Object(D.a)(this,t);for(var r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];var o,u=i[0];if(i[1]instanceof ar)n=i[1],o=i[2];else{var s=u.fields,c=i[1],l=i[2];n=ar.Struct(new Gn(s),0,c,0,null,l)}return(e=Object(re.a)(this,Object(ie.a)(t).call(this,n,o)))._schema=u,e}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"clone",value:function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._children;return new t(this._schema,e,n)}},{key:"concat",value:function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];var i=this._schema,a=qi.flatten.apply(qi,[this].concat(n));return new Lc(i,a.map(function(e){var n=e.data;return new t(i,n)}))}},{key:"select",value:function(){for(var e=this._schema.fields.reduce(function(e,t,n){return e.set(t.name,n)},new Map),t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return this.selectAt.apply(this,cn(n.map(function(t){return e.get(t)}).filter(function(e){return e>-1})))}},{key:"selectAt",value:function(){for(var e,n=this,r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];var o=(e=this._schema).selectAt.apply(e,i),u=i.map(function(e){return n.data.childData[e]}).filter(Boolean);return new t(o,this.length,u)}},{key:"schema",get:function(){return this._schema}},{key:"numCols",get:function(){return this._schema.fields.length}},{key:"dictionaries",get:function(){return this._dictionaries||(this._dictionaries=Nc.collect(this))}}],[{key:"from",value:function(e){return _e(e.values),Lc.from(e)}},{key:"new",value:function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];var i=F(na(n),2),a=i[0],o=i[1].filter(function(e){return e instanceof Rt});return Pr(t,cn(function(e,t){for(var n,r,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t.reduce(function(e,t){return Math.max(e,t.length)},0),a=-1,o=t.length,u=cn(e.fields),s=[],c=(i+63&-64)>>3;++a<o;)(n=t[a])&&n.length===i?s[a]=n:((r=u[a]).nullable||(u[a]=u[a].clone({nullable:!0})),s[a]=n?n._changeLengthAndBackfillNullBitmap(i):ar.new(r.type,0,i,i,Vu(c)));return[new sa(u),i,s]}(new sa(a),o.map(function(e){return e.data}))))}}]),t}(Is),Mc=function(e){function t(e){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).call(this,e,0,e.fields.map(function(e){return ar.new(e.type,0,0,0)})))}return Object(ae.a)(t,e),t}(Fc),Nc=function(e){function t(){var e;return Object(D.a)(this,t),(e=Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))).dictionaries=new Map,e}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"visit",value:function(e,t){var n=this;return jn.isDictionary(t)?this.visitDictionary(e,t):(e.childData.forEach(function(e,r){return n.visit(e,t.children[r].type)}),this)}},{key:"visitDictionary",value:function(e,t){var n=e.dictionary;return n&&n.length>0&&this.dictionaries.set(t.id,n),this}}],[{key:"collect",value:function(e){return(new t).visit(e.data,new Gn(e.schema.fields)).dictionaries}}]),t}(ln),Uc=L.mark(tl);function Pc(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"===typeof e)return Rc(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Rc(e,t)}(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){u=!0,a=e},f:function(){try{o||null==n.return||n.return()}finally{if(u)throw a}}}}function Rc(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function zc(e){var t,n,r,i=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);i--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new Vc(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function Vc(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:t}})}return(Vc=function(e){this.s=e,this.n=e.next}).prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new Vc(e)}var Wc=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this)))._impl=e,n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"isSync",value:function(){return this._impl.isSync()}},{key:"isAsync",value:function(){return this._impl.isAsync()}},{key:"isFile",value:function(){return this._impl.isFile()}},{key:"isStream",value:function(){return this._impl.isStream()}},{key:"next",value:function(){return this._impl.next()}},{key:"throw",value:function(e){return this._impl.throw(e)}},{key:"return",value:function(e){return this._impl.return(e)}},{key:"cancel",value:function(){return this._impl.cancel()}},{key:"reset",value:function(e){return this._impl.reset(e),this._DOMStream=void 0,this._nodeStream=void 0,this}},{key:"open",value:function(e){var t=this,n=this._impl.open(e);return we(n)?n.then(function(){return t}):this}},{key:"readRecordBatch",value:function(e){return this._impl.isFile()?this._impl.readRecordBatch(e):null}},{key:Symbol.iterator,value:function(){return this._impl[Symbol.iterator]()}},{key:Symbol.asyncIterator,value:function(){return this._impl[Symbol.asyncIterator]()}},{key:"toDOMStream",value:function(){var e=this;return _t.toDOMStream(this.isSync()?Object(Si.a)({},Symbol.iterator,function(){return e}):Object(Si.a)({},Symbol.asyncIterator,function(){return e}))}},{key:"toNodeStream",value:function(){var e=this;return _t.toNodeStream(this.isSync()?Object(Si.a)({},Symbol.iterator,function(){return e}):Object(Si.a)({},Symbol.asyncIterator,function(){return e}),{objectMode:!0})}},{key:"closed",get:function(){return this._impl.closed}},{key:"schema",get:function(){return this._impl.schema}},{key:"autoDestroy",get:function(){return this._impl.autoDestroy}},{key:"dictionaries",get:function(){return this._impl.dictionaries}},{key:"numDictionaries",get:function(){return this._impl.numDictionaries}},{key:"numRecordBatches",get:function(){return this._impl.numRecordBatches}},{key:"footer",get:function(){return this._impl.isFile()?this._impl.footer:null}}],[{key:"throughNode",value:function(e){throw new Error('"throughNode" not available in this environment')}},{key:"throughDOM",value:function(e,t){throw new Error('"throughDOM" not available in this environment')}},{key:"from",value:function(e){return e instanceof t?e:je(e)?function(e){return new $c(new Zc(e))}(e):Se(e)?function(e){return al.apply(this,arguments)}(e):we(e)?B(L.mark(function n(){return L.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.t0=t,n.next=3,e;case 3:return n.t1=n.sent,n.next=6,n.t0.from.call(n.t0,n.t1);case 6:return n.abrupt("return",n.sent);case 7:case"end":return n.stop()}},n)}))():Te(e)||Ie(e)||Ee(e)||Oe(e)?function(e){return il.apply(this,arguments)}(new no(e)):function(e){var t=e.peek(_u+7&-8);return t&&t.byteLength>=4?wu(t)?new Yc(new Jc(e.read())):new $c(new qc(e)):new $c(new qc(L.mark(function e(){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}},e)})()))}(new to(e))}},{key:"readAll",value:function(e){return e instanceof t?e.isSync()?tl(e):nl(e):je(e)||ArrayBuffer.isView(e)||_e(e)||xe(e)?tl(e):nl(e)}}]),t}(se),$c=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this,e)))._impl=e,n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:Symbol.iterator,value:function(){return this._impl[Symbol.iterator]()}},{key:Symbol.asyncIterator,value:function(){var e=this;return P(L.mark(function t(){return L.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.delegateYield(R(zc(e[Symbol.iterator]()),N),"t0",1);case 1:case"end":return t.stop()}},t)}))()}}]),t}(Wc),Hc=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this,e)))._impl=e,n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:Symbol.iterator,value:function(){throw new Error("AsyncRecordBatchStreamReader is not Iterable")}},{key:Symbol.asyncIterator,value:function(){return this._impl[Symbol.asyncIterator]()}}]),t}(Wc),Yc=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this,e)))._impl=e,n}return Object(ae.a)(t,e),t}($c),Kc=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this,e)))._impl=e,n}return Object(ae.a)(t,e),t}(Hc),Qc=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Map;Object(D.a)(this,e),this.closed=!1,this.autoDestroy=!0,this._dictionaryIndex=0,this._recordBatchIndex=0,this.dictionaries=t}return Object(C.a)(e,[{key:"isSync",value:function(){return!1}},{key:"isAsync",value:function(){return!1}},{key:"isFile",value:function(){return!1}},{key:"isStream",value:function(){return!1}},{key:"reset",value:function(e){return this._dictionaryIndex=0,this._recordBatchIndex=0,this.schema=e,this.dictionaries=new Map,this}},{key:"_loadRecordBatch",value:function(e,t){return new Fc(this.schema,e.length,this._loadVectors(e,t,this.schema.fields))}},{key:"_loadDictionaryBatch",value:function(e,t){var n=e.id,r=e.isDelta,i=e.data,a=this.dictionaries,o=this.schema,u=a.get(n);if(r||!u){var s=o.dictionaries.get(n);return u&&r?u.concat(Rt.new(this._loadVectors(i,t,[s])[0])):Rt.new(this._loadVectors(i,t,[s])[0])}return u}},{key:"_loadVectors",value:function(e,t,n){return new po(t,e.nodes,e.buffers,this.dictionaries).visitMany(n)}},{key:"numDictionaries",get:function(){return this._dictionaryIndex}},{key:"numRecordBatches",get:function(){return this._recordBatchIndex}}]),e}(),qc=function(e){function t(e,n){var r;return Object(D.a)(this,t),(r=Object(re.a)(this,Object(ie.a)(t).call(this,n)))._reader=je(e)?new bu(r._handle=e):new yu(r._handle=e),r}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"isSync",value:function(){return!0}},{key:"isStream",value:function(){return!0}},{key:Symbol.iterator,value:function(){return this}},{key:"cancel",value:function(){!this.closed&&(this.closed=!0)&&(this.reset()._reader.return(),this._reader=null,this.dictionaries=null)}},{key:"open",value:function(e){return this.closed||(this.autoDestroy=el(this,e),this.schema||(this.schema=this._reader.readSchema())||this.cancel()),this}},{key:"throw",value:function(e){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.throw(e):oe}},{key:"return",value:function(e){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.return(e):oe}},{key:"next",value:function(){if(this.closed)return oe;for(var e,t=this._reader;e=this._readNextMessageAndValidate();)if(e.isSchema())this.reset(e.header());else{if(e.isRecordBatch()){this._recordBatchIndex++;var n=e.header(),r=t.readMessageBody(e.bodyLength);return{done:!1,value:this._loadRecordBatch(n,r)}}if(e.isDictionaryBatch()){this._dictionaryIndex++;var i=e.header(),a=t.readMessageBody(e.bodyLength),o=this._loadDictionaryBatch(i,a);this.dictionaries.set(i.id,o)}}return this.schema&&0===this._recordBatchIndex?(this._recordBatchIndex++,{done:!1,value:new Mc(this.schema)}):this.return()}},{key:"_readNextMessageAndValidate",value:function(e){return this._reader.readMessage(e)}}]),t}(Qc),Gc=function(e){function t(e,n){var r;return Object(D.a)(this,t),(r=Object(re.a)(this,Object(ie.a)(t).call(this,n)))._reader=new vu(r._handle=e),r}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"isAsync",value:function(){return!0}},{key:"isStream",value:function(){return!0}},{key:Symbol.asyncIterator,value:function(){return this}},{key:"cancel",value:function(){var e=B(L.mark(function e(){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.closed||!(this.closed=!0)){e.next=5;break}return e.next=3,this.reset()._reader.return();case 3:this._reader=null,this.dictionaries=null;case 5:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}()},{key:"open",value:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.closed){e.next=10;break}if(this.autoDestroy=el(this,t),e.t0=this.schema,e.t0){e.next=7;break}return e.next=6,this._reader.readSchema();case 6:e.t0=this.schema=e.sent;case 7:if(e.t0){e.next=10;break}return e.next=10,this.cancel();case 10:return e.abrupt("return",this);case 11:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"throw",value:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.closed||!this.autoDestroy||!(this.closed=!0)){e.next=4;break}return e.next=3,this.reset()._reader.throw(t);case 3:return e.abrupt("return",e.sent);case 4:return e.abrupt("return",oe);case 5:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"return",value:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.closed||!this.autoDestroy||!(this.closed=!0)){e.next=4;break}return e.next=3,this.reset()._reader.return(t);case 3:return e.abrupt("return",e.sent);case 4:return e.abrupt("return",oe);case 5:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"next",value:function(){var e=B(L.mark(function e(){var t,n,r,i,a,o,u,s;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.closed){e.next=2;break}return e.abrupt("return",oe);case 2:n=this._reader;case 3:return e.next=5,this._readNextMessageAndValidate();case 5:if(!(t=e.sent)){e.next=31;break}if(!t.isSchema()){e.next=11;break}return e.next=9,this.reset(t.header());case 9:e.next=29;break;case 11:if(!t.isRecordBatch()){e.next=21;break}return this._recordBatchIndex++,r=t.header(),e.next=16,n.readMessageBody(t.bodyLength);case 16:return i=e.sent,a=this._loadRecordBatch(r,i),e.abrupt("return",{done:!1,value:a});case 21:if(!t.isDictionaryBatch()){e.next=29;break}return this._dictionaryIndex++,o=t.header(),e.next=26,n.readMessageBody(t.bodyLength);case 26:u=e.sent,s=this._loadDictionaryBatch(o,u),this.dictionaries.set(o.id,s);case 29:e.next=3;break;case 31:if(!this.schema||0!==this._recordBatchIndex){e.next=34;break}return this._recordBatchIndex++,e.abrupt("return",{done:!1,value:new Mc(this.schema)});case 34:return e.next=36,this.return();case 36:return e.abrupt("return",e.sent);case 37:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}()},{key:"_readNextMessageAndValidate",value:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this._reader.readMessage(t);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()}]),t}(Qc),Jc=function(e){function t(e,n){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).call(this,e instanceof ao?e:new ao(e),n))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"isSync",value:function(){return!0}},{key:"isFile",value:function(){return!0}},{key:"open",value:function(e){if(!this.closed&&!this._footer){this.schema=(this._footer=this._readFooter()).schema;var n,r=Pc(this._footer.dictionaryBatches());try{for(r.s();!(n=r.n()).done;){n.value&&this._readDictionaryBatch(this._dictionaryIndex++)}}catch(i){r.e(i)}finally{r.f()}}return Ct(Object(ie.a)(t.prototype),"open",this).call(this,e)}},{key:"readRecordBatch",value:function(e){if(this.closed)return null;this._footer||this.open();var t=this._footer&&this._footer.getRecordBatch(e);if(t&&this._handle.seek(t.offset)){var n=this._reader.readMessage(Qt.RecordBatch);if(n&&n.isRecordBatch()){var r=n.header(),i=this._reader.readMessageBody(n.bodyLength);return this._loadRecordBatch(r,i)}}return null}},{key:"_readDictionaryBatch",value:function(e){var t=this._footer&&this._footer.getDictionaryBatch(e);if(t&&this._handle.seek(t.offset)){var n=this._reader.readMessage(Qt.DictionaryBatch);if(n&&n.isDictionaryBatch()){var r=n.header(),i=this._reader.readMessageBody(n.bodyLength),a=this._loadDictionaryBatch(r,i);this.dictionaries.set(r.id,a)}}}},{key:"_readFooter",value:function(){var e=this._handle,t=e.size-Ou,n=e.readInt32(t),r=e.readAt(t-n,n);return qa.decode(r)}},{key:"_readNextMessageAndValidate",value:function(e){if(this._footer||this.open(),this._footer&&this._recordBatchIndex<this.numRecordBatches){var t=this._footer&&this._footer.getRecordBatch(this._recordBatchIndex);if(t&&this._handle.seek(t.offset))return this._reader.readMessage(e)}return null}},{key:"footer",get:function(){return this._footer}},{key:"numDictionaries",get:function(){return this._footer?this._footer.numDictionaries:0}},{key:"numRecordBatches",get:function(){return this._footer?this._footer.numRecordBatches:0}}]),t}(qc),Xc=function(e){function t(e){Object(D.a)(this,t);for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];var a="number"!==typeof r[0]?r.shift():void 0,o=r[0]instanceof Map?r.shift():void 0;return Object(re.a)(this,Object(ie.a)(t).call(this,e instanceof oo?e:new oo(e,a),o))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"isFile",value:function(){return!0}},{key:"isAsync",value:function(){return!0}},{key:"open",value:function(){var e=B(L.mark(function e(n){var r,i,a;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.closed||this._footer){e.next=23;break}return e.next=3,this._readFooter();case 3:this.schema=(this._footer=e.sent).schema,r=Pc(this._footer.dictionaryBatches()),e.prev=5,r.s();case 7:if((i=r.n()).done){e.next=15;break}if(a=i.value,e.t0=a,!e.t0){e.next=13;break}return e.next=13,this._readDictionaryBatch(this._dictionaryIndex++);case 13:e.next=7;break;case 15:e.next=20;break;case 17:e.prev=17,e.t1=e.catch(5),r.e(e.t1);case 20:return e.prev=20,r.f(),e.finish(20);case 23:return e.next=25,Ct(Object(ie.a)(t.prototype),"open",this).call(this,n);case 25:return e.abrupt("return",e.sent);case 26:case"end":return e.stop()}},e,this,[[5,17,20,23]])}));return function(t){return e.apply(this,arguments)}}()},{key:"readRecordBatch",value:function(){var e=B(L.mark(function e(t){var n,r,i,a,o;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!this.closed){e.next=2;break}return e.abrupt("return",null);case 2:if(this._footer){e.next=5;break}return e.next=5,this.open();case 5:if(n=this._footer&&this._footer.getRecordBatch(t),e.t0=n,!e.t0){e.next=11;break}return e.next=10,this._handle.seek(n.offset);case 10:e.t0=e.sent;case 11:if(!e.t0){e.next=22;break}return e.next=14,this._reader.readMessage(Qt.RecordBatch);case 14:if(!(r=e.sent)||!r.isRecordBatch()){e.next=22;break}return i=r.header(),e.next=19,this._reader.readMessageBody(r.bodyLength);case 19:return a=e.sent,o=this._loadRecordBatch(i,a),e.abrupt("return",o);case 22:return e.abrupt("return",null);case 23:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"_readDictionaryBatch",value:function(){var e=B(L.mark(function e(t){var n,r,i,a,o;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=this._footer&&this._footer.getDictionaryBatch(t),e.t0=n,!e.t0){e.next=6;break}return e.next=5,this._handle.seek(n.offset);case 5:e.t0=e.sent;case 6:if(!e.t0){e.next=17;break}return e.next=9,this._reader.readMessage(Qt.DictionaryBatch);case 9:if(!(r=e.sent)||!r.isDictionaryBatch()){e.next=17;break}return i=r.header(),e.next=14,this._reader.readMessageBody(r.bodyLength);case 14:a=e.sent,o=this._loadDictionaryBatch(i,a),this.dictionaries.set(i.id,o);case 17:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"_readFooter",value:function(){var e=B(L.mark(function e(){var t,n,r,i;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=this._handle,e.t0=t._pending,!e.t0){e.next=5;break}return e.next=5,t._pending;case 5:return n=t.size-Ou,e.next=8,t.readInt32(n);case 8:return r=e.sent,e.next=11,t.readAt(n-r,r);case 11:return i=e.sent,e.abrupt("return",qa.decode(i));case 13:case"end":return e.stop()}},e,this)}));return function(){return e.apply(this,arguments)}}()},{key:"_readNextMessageAndValidate",value:function(){var e=B(L.mark(function e(t){var n;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this._footer){e.next=3;break}return e.next=3,this.open();case 3:if(!(this._footer&&this._recordBatchIndex<this.numRecordBatches)){e.next=14;break}if(n=this._footer.getRecordBatch(this._recordBatchIndex),e.t0=n,!e.t0){e.next=10;break}return e.next=9,this._handle.seek(n.offset);case 9:e.t0=e.sent;case 10:if(!e.t0){e.next=14;break}return e.next=13,this._reader.readMessage(t);case 13:return e.abrupt("return",e.sent);case 14:return e.abrupt("return",null);case 15:case"end":return e.stop()}},e,this)}));return function(t){return e.apply(this,arguments)}}()},{key:"footer",get:function(){return this._footer}},{key:"numDictionaries",get:function(){return this._footer?this._footer.numDictionaries:0}},{key:"numRecordBatches",get:function(){return this._footer?this._footer.numRecordBatches:0}}]),t}(Gc),Zc=function(e){function t(e,n){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).call(this,e,n))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"_loadVectors",value:function(e,t,n){return new yo(t,e.nodes,e.buffers,this.dictionaries).visitMany(n)}}]),t}(qc);function el(e,t){return t&&"boolean"===typeof t.autoDestroy?t.autoDestroy:e.autoDestroy}function tl(e){var t;return L.wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(t=Wc.from(e),n.prev=1,t.open({autoDestroy:!1}).closed){n.next=6;break}case 3:return n.next=5,t;case 5:if(!t.reset().open().closed){n.next=3;break}case 6:return n.prev=6,t.cancel(),n.finish(6);case 9:case"end":return n.stop()}},Uc,null,[[1,,6,9]])}function nl(e){return rl.apply(this,arguments)}function rl(){return(rl=P(L.mark(function e(t){var n;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,N(Wc.from(t));case 2:return n=e.sent,e.prev=3,e.next=6,N(n.open({autoDestroy:!1}));case 6:if(e.sent.closed){e.next=12;break}case 7:return e.next=9,n;case 9:return e.next=11,N(n.reset().open());case 11:if(!e.sent.closed){e.next=7;break}case 12:return e.prev=12,e.next=15,N(n.cancel());case 15:return e.finish(12);case 16:case"end":return e.stop()}},e,null,[[3,,12,16]])}))).apply(this,arguments)}function il(){return(il=B(L.mark(function e(t){var n;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.peek(_u+7&-8);case 2:if(!((n=e.sent)&&n.byteLength>=4)){e.next=18;break}if(wu(n)){e.next=8;break}e.t1=new Hc(new Gc(t)),e.next=15;break;case 8:return e.t2=Yc,e.t3=Jc,e.next=12,t.read();case 12:e.t4=e.sent,e.t5=new e.t3(e.t4),e.t1=new e.t2(e.t5);case 15:e.t0=e.t1,e.next=19;break;case 18:e.t0=new Hc(new Gc(P(L.mark(function e(){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}},e)}))()));case 19:return e.abrupt("return",e.t0);case 20:case"end":return e.stop()}},e)}))).apply(this,arguments)}function al(){return(al=B(L.mark(function e(t){var n,r,i;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.stat();case 2:if(n=e.sent,r=n.size,i=new oo(t,r),!(r>=ju)){e.next=12;break}return e.t0=wu,e.next=9,i.readAt(0,_u+7&-8);case 9:if(e.t1=e.sent,!(0,e.t0)(e.t1)){e.next=12;break}return e.abrupt("return",new Kc(new Xc(i)));case 12:return e.abrupt("return",new Hc(new Gc(i)));case 13:case"end":return e.stop()}},e)}))).apply(this,arguments)}var ol=function(){function e(t){var n,r,i=this;Object(D.a)(this,e),this._numChunks=0,this._finished=!1,this._bufferedSize=0;var a=t.readableStrategy,o=t.writableStrategy,u=t.queueingStrategy,s=void 0===u?"count":u,c=Hu(t,["readableStrategy","writableStrategy","queueingStrategy"]);this._controller=null,this._builder=gr.new(c),this._getSize="bytes"!==s?ul:sl;var l=Object(Dt.a)({},a).highWaterMark,f=void 0===l?"bytes"===s?Math.pow(2,14):1e3:l,h=Object(Dt.a)({},o).highWaterMark,d=void 0===h?"bytes"===s?Math.pow(2,14):1e3:h;this.readable=new ReadableStream((n={},Object(Si.a)(n,"cancel",function(){i._builder.clear()}),Object(Si.a)(n,"pull",function(e){i._maybeFlush(i._builder,i._controller=e)}),Object(Si.a)(n,"start",function(e){i._maybeFlush(i._builder,i._controller=e)}),n),{highWaterMark:f,size:"bytes"!==s?ul:sl}),this.writable=new WritableStream((r={},Object(Si.a)(r,"abort",function(){i._builder.clear()}),Object(Si.a)(r,"write",function(){i._maybeFlush(i._builder,i._controller)}),Object(Si.a)(r,"close",function(){i._maybeFlush(i._builder.finish(),i._controller)}),r),{highWaterMark:d,size:function(e){return i._writeValueAndReturnChunkSize(e)}})}return Object(C.a)(e,[{key:"_writeValueAndReturnChunkSize",value:function(e){var t=this._bufferedSize;return this._bufferedSize=this._getSize(this._builder.append(e)),this._bufferedSize-t}},{key:"_maybeFlush",value:function(e,t){null!==t&&(this._bufferedSize>=t.desiredSize&&++this._numChunks&&this._enqueue(t,e.toVector()),e.finished&&((e.length>0||0===this._numChunks)&&++this._numChunks&&this._enqueue(t,e.toVector()),!this._finished&&(this._finished=!0)&&this._enqueue(t,null)))}},{key:"_enqueue",value:function(e,t){this._bufferedSize=0,this._controller=null,null===t?e.close():e.enqueue(t)}}]),e}(),ul=function(e){return e.length},sl=function(e){return e.byteLength};var cl=function(){function e(){Object(D.a)(this,e)}return Object(C.a)(e,[{key:"eq",value:function(t){return t instanceof e||(t=new ll(t)),new bl(this,t)}},{key:"le",value:function(t){return t instanceof e||(t=new ll(t)),new ml(this,t)}},{key:"ge",value:function(t){return t instanceof e||(t=new ll(t)),new gl(this,t)}},{key:"lt",value:function(e){return new kl(this.ge(e))}},{key:"gt",value:function(e){return new kl(this.le(e))}},{key:"ne",value:function(e){return new kl(this.eq(e))}}]),e}(),ll=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this))).v=e,n}return Object(ae.a)(t,e),t}(cl),fl=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this))).name=e,n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"bind",value:function(e){if(!this.colidx){this.colidx=-1;for(var t=e.schema.fields,n=-1;++n<t.length;)if(t[n].name===this.name){this.colidx=n;break}if(this.colidx<0)throw new Error('Failed to bind Col "'.concat(this.name,'"'))}var r=this.vector=e.getChildAt(this.colidx);return function(e){return r.get(e)}}}]),t}(cl),hl=function(){function e(){Object(D.a)(this,e)}return Object(C.a)(e,[{key:"and",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Pr(yl,[this].concat(t))}},{key:"or",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Pr(vl,[this].concat(t))}},{key:"not",value:function(){return new kl(this)}}]),e}(),dl=function(e){function t(e,n){var r;return Object(D.a)(this,t),(r=Object(re.a)(this,Object(ie.a)(t).call(this))).left=e,r.right=n,r}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"bind",value:function(e){return this.left instanceof ll?this.right instanceof ll?this._bindLitLit(e,this.left,this.right):this._bindLitCol(e,this.left,this.right):this.right instanceof ll?this._bindColLit(e,this.left,this.right):this._bindColCol(e,this.left,this.right)}}]),t}(hl),pl=function(e){function t(){var e;Object(D.a)(this,t),e=Object(re.a)(this,Object(ie.a)(t).call(this));for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return e.children=r,e}return Object(ae.a)(t,e),t}(hl);pl.prototype.children=Object.freeze([]);var yl=function(e){function t(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return Object(D.a)(this,t),r=r.reduce(function(e,n){return e.concat(n instanceof t?n.children:n)},[]),Object(re.a)(this,(e=Object(ie.a)(t)).call.apply(e,[this].concat(cn(r))))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"bind",value:function(e){var t=this.children.map(function(t){return t.bind(e)});return function(e,n){return t.every(function(t){return t(e,n)})}}}]),t}(pl),vl=function(e){function t(){for(var e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return Object(D.a)(this,t),r=r.reduce(function(e,n){return e.concat(n instanceof t?n.children:n)},[]),Object(re.a)(this,(e=Object(ie.a)(t)).call.apply(e,[this].concat(cn(r))))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"bind",value:function(e){var t=this.children.map(function(t){return t.bind(e)});return function(e,n){return t.some(function(t){return t(e,n)})}}}]),t}(pl),bl=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"_bindLitLit",value:function(e,t,n){var r=t.v==n.v;return function(){return r}}},{key:"_bindColCol",value:function(e,t,n){var r=t.bind(e),i=n.bind(e);return function(e,t){return r(e,t)==i(e,t)}}},{key:"_bindColLit",value:function(e,t,n){var r=t.bind(e);if(t.vector instanceof Zu){var i,a=t.vector;return a.dictionary!==this.lastDictionary?(i=a.reverseLookup(n.v),this.lastDictionary=a.dictionary,this.lastKey=i):i=this.lastKey,-1===i?function(){return!1}:function(e){return a.getKey(e)===i}}return function(e,t){return r(e,t)==n.v}}},{key:"_bindLitCol",value:function(e,t,n){return this._bindColLit(e,n,t)}}]),t}(dl),ml=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"_bindLitLit",value:function(e,t,n){var r=t.v<=n.v;return function(){return r}}},{key:"_bindColCol",value:function(e,t,n){var r=t.bind(e),i=n.bind(e);return function(e,t){return r(e,t)<=i(e,t)}}},{key:"_bindColLit",value:function(e,t,n){var r=t.bind(e);return function(e,t){return r(e,t)<=n.v}}},{key:"_bindLitCol",value:function(e,t,n){var r=n.bind(e);return function(e,n){return t.v<=r(e,n)}}}]),t}(dl),gl=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"_bindLitLit",value:function(e,t,n){var r=t.v>=n.v;return function(){return r}}},{key:"_bindColCol",value:function(e,t,n){var r=t.bind(e),i=n.bind(e);return function(e,t){return r(e,t)>=i(e,t)}}},{key:"_bindColLit",value:function(e,t,n){var r=t.bind(e);return function(e,t){return r(e,t)>=n.v}}},{key:"_bindLitCol",value:function(e,t,n){var r=n.bind(e);return function(e,n){return t.v>=r(e,n)}}}]),t}(dl),kl=function(e){function t(e){var n;return Object(D.a)(this,t),(n=Object(re.a)(this,Object(ie.a)(t).call(this))).child=e,n}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"bind",value:function(e){var t=this.child.bind(e);return function(e,n){return!t(e,n)}}}]),t}(hl);Lc.prototype.countBy=function(e){return new wl(this.chunks).countBy(e)},Lc.prototype.scan=function(e,t){return new wl(this.chunks).scan(e,t)},Lc.prototype.scanReverse=function(e,t){return new wl(this.chunks).scanReverse(e,t)},Lc.prototype.filter=function(e){return new wl(this.chunks).filter(e)};var wl=function(e){function t(){return Object(D.a)(this,t),Object(re.a)(this,Object(ie.a)(t).apply(this,arguments))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"filter",value:function(e){return new Ol(this.chunks,e)}},{key:"scan",value:function(e,t){for(var n=this.chunks,r=n.length,i=-1;++i<r;){var a=n[i];t&&t(a);for(var o=-1,u=a.length;++o<u;)e(o,a)}}},{key:"scanReverse",value:function(e,t){for(var n=this.chunks,r=n.length;--r>=0;){var i=n[r];t&&t(i);for(var a=i.length;--a>=0;)e(a,i)}}},{key:"countBy",value:function(e){var t=this.chunks,n=t.length,r="string"===typeof e?new fl(e):e;r.bind(t[n-1]);var i=r.vector;if(!jn.isDictionary(i.type))throw new Error("countBy currently only supports dictionary-encoded columns");for(var a=Math.ceil(Math.log(i.length)/Math.log(256)),o=new(4==a?Uint32Array:a>=2?Uint16Array:Uint8Array)(i.dictionary.length),u=-1;++u<n;){var s=t[u];r.bind(s);for(var c=r.vector.indices,l=-1,f=s.length;++l<f;){var h=c.get(l);null!==h&&o[h]++}}return new _l(i.dictionary,hs.from(o))}}]),t}(Lc),_l=function(e){function t(e,n){Object(D.a)(this,t);var r=new sa([new ca("values",e.type),new ca("counts",n.type)]);return Object(re.a)(this,Object(ie.a)(t).call(this,new Fc(r,n.length,[e,n])))}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"toJSON",value:function(){for(var e=this.getColumnAt(0),t=this.getColumnAt(1),n={},r=-1;++r<this.length;)n[e.get(r)]=t.get(r);return n}}]),t}(Lc),Ol=function(e){function t(e,n){var r;return Object(D.a)(this,t),(r=Object(re.a)(this,Object(ie.a)(t).call(this,e)))._predicate=n,r}return Object(ae.a)(t,e),Object(C.a)(t,[{key:"scan",value:function(e,t){for(var n=this._chunks,r=n.length,i=-1;++i<r;)for(var a=n[i],o=this._predicate.bind(a),u=!1,s=-1,c=a.length;++s<c;)o(s,a)&&(t&&!u&&(t(a),u=!0),e(s,a))}},{key:"scanReverse",value:function(e,t){for(var n=this._chunks,r=n.length;--r>=0;)for(var i=n[r],a=this._predicate.bind(i),o=!1,u=i.length;--u>=0;)a(u,i)&&(t&&!o&&(t(i),o=!0),e(u,i))}},{key:"count",value:function(){for(var e=0,t=this._chunks,n=t.length,r=-1;++r<n;)for(var i=t[r],a=this._predicate.bind(i),o=-1,u=i.length;++o<u;)a(o,i)&&++e;return e}},{key:Symbol.iterator,value:L.mark(function e(){var t,n,r,i,a,o,u;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t=this._chunks,n=t.length,r=-1;case 3:if(!(++r<n)){e.next=15;break}i=t[r],a=this._predicate.bind(i),o=-1,u=i.length;case 7:if(!(++o<u)){e.next=13;break}if(!a(o,i)){e.next=11;break}return e.next=11,i.get(o);case 11:e.next=7;break;case 13:e.next=3;break;case 15:case"end":return e.stop()}},e,this)})},{key:"filter",value:function(e){return new t(this._chunks,this._predicate.and(e))}},{key:"countBy",value:function(e){var t=this._chunks,n=t.length,r="string"===typeof e?new fl(e):e;r.bind(t[n-1]);var i=r.vector;if(!jn.isDictionary(i.type))throw new Error("countBy currently only supports dictionary-encoded columns");for(var a=Math.ceil(Math.log(i.length)/Math.log(256)),o=new(4==a?Uint32Array:a>=2?Uint16Array:Uint8Array)(i.dictionary.length),u=-1;++u<n;){var s=t[u],c=this._predicate.bind(s);r.bind(s);for(var l=r.vector.indices,f=-1,h=s.length;++f<h;){var d=l.get(f);null!==d&&c(f,s)&&o[d]++}}return new _l(i.dictionary,hs.from(o))}}]),t}(wl);Object(Dt.a)({},o,s,i,a,r,u);_t.toDOMStream=function(e,t){if(Oe(e))return function(e,t){var n=null,r=t&&"bytes"===t.type||!1,i=t&&t.highWaterMark||Math.pow(2,24);return new ReadableStream(Object(Dt.a)({},t,{start:function(){var t=B(L.mark(function t(r){return L.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,a(r,n||(n=e[Symbol.asyncIterator]()));case 2:case"end":return t.stop()}},t)}));return function(e){return t.apply(this,arguments)}}(),pull:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!n){e.next=5;break}return e.next=3,a(t,n);case 3:e.next=6;break;case 5:t.close();case 6:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}(),cancel:function(){var e=B(L.mark(function e(){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.t1=n,!e.t1){e.next=8;break}if(e.t2=n.return,!e.t2){e.next=7;break}return e.next=6,n.return();case 6:e.t2=e.sent;case 7:e.t1=e.t2;case 8:if(e.t0=e.t1,e.t0){e.next=11;break}e.t0=!0;case 11:if(e.t3=e.t0,!e.t3){e.next=14;break}n=null;case 14:case"end":return e.stop()}},e)}));return function(){return e.apply(this,arguments)}}()}),Object(Dt.a)({highWaterMark:r?i:void 0},t));function a(e,t){return o.apply(this,arguments)}function o(){return(o=B(L.mark(function e(t,n){var i,a,o;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:a=null,o=t.desiredSize||null;case 2:return e.next=4,n.next(r?o:null);case 4:if((a=e.sent).done){e.next=11;break}if(ArrayBuffer.isView(a.value)&&(i=Ve(a.value))&&(null!=o&&r&&(o=o-i.byteLength+1),a.value=i),t.enqueue(a.value),!(null!=o&&--o<=0)){e.next=9;break}return e.abrupt("return");case 9:e.next=2;break;case 11:t.close();case 12:case"end":return e.stop()}},e)}))).apply(this,arguments)}}(e,t);if(_e(e))return function(e,t){var n=null,r=t&&"bytes"===t.type||!1,i=t&&t.highWaterMark||Math.pow(2,24);return new ReadableStream(Object(Dt.a)({},t,{start:function(t){a(t,n||(n=e[Symbol.iterator]()))},pull:function(e){n?a(e,n):e.close()},cancel:function(){n&&n.return&&n.return(),n=null}}),Object(Dt.a)({highWaterMark:r?i:void 0},t));function a(e,t){for(var n,i=null,a=e.desiredSize||null;!(i=t.next(r?a:null)).done;)if(ArrayBuffer.isView(i.value)&&(n=Ve(i.value))&&(null!=a&&r&&(a=a-n.byteLength+1),i.value=n),e.enqueue(i.value),null!=a&&--a<=0)return;e.close()}}(e,t);throw new Error("toDOMStream() must be called with an Iterable or AsyncIterable")},gr.throughDOM=function(e){return new ol(e)},Wc.throughDOM=function(e,t){var n=new eo,r=null,i=new ReadableStream({cancel:function(){var e=B(L.mark(function e(){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n.close();case 2:case"end":return e.stop()}},e)}));return function(){return e.apply(this,arguments)}}(),start:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(e.t0=u,e.t1=t,e.t2=r,e.t2){e.next=7;break}return e.next=6,a();case 6:e.t2=r=e.sent;case 7:return e.t3=e.t2,e.next=10,(0,e.t0)(e.t1,e.t3);case 10:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}(),pull:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!r){e.next=5;break}return e.next=3,u(t,r);case 3:e.next=6;break;case 5:t.close();case 6:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}()});return{writable:new WritableStream(n,Object(Dt.a)({highWaterMark:Math.pow(2,14)},e)),readable:i};function a(){return o.apply(this,arguments)}function o(){return(o=B(L.mark(function e(){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Wc.from(n);case 2:return e.next=4,e.sent.open(t);case 4:return e.abrupt("return",e.sent);case 5:case"end":return e.stop()}},e)}))).apply(this,arguments)}function u(e,t){return s.apply(this,arguments)}function s(){return(s=B(L.mark(function e(t,n){var r,i;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:r=t.desiredSize,i=null;case 2:return e.next=4,n.next();case 4:if((i=e.sent).done){e.next=10;break}if(t.enqueue(i.value),!(null!=r&&--r<=0)){e.next=8;break}return e.abrupt("return");case 8:e.next=2;break;case 10:t.close();case 11:case"end":return e.stop()}},e)}))).apply(this,arguments)}},Fu.throughDOM=function(e,t){var n=new this(e),r=new no(n),i=new ReadableStream({type:"bytes",cancel:function(){var e=B(L.mark(function e(){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r.cancel();case 2:case"end":return e.stop()}},e)}));return function(){return e.apply(this,arguments)}}(),pull:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,a(t);case 2:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}(),start:function(){var e=B(L.mark(function e(t){return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,a(t);case 2:case"end":return e.stop()}},e)}));return function(t){return e.apply(this,arguments)}}()},Object(Dt.a)({highWaterMark:Math.pow(2,14)},t));return{writable:new WritableStream(n,e),readable:i};function a(e){return o.apply(this,arguments)}function o(){return(o=B(L.mark(function e(t){var n,i;return L.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:n=null,i=t.desiredSize;case 2:return e.next=4,r.read(i||null);case 4:if(!(n=e.sent)){e.next=10;break}if(t.enqueue(n),!(null!=i&&(i-=n.byteLength)<=0)){e.next=8;break}return e.abrupt("return");case 8:e.next=2;break;case 10:t.close();case 11:case"end":return e.stop()}},e)}))).apply(this,arguments)}};var jl,xl=function(){function e(e,t,n,r){var i=this;this.getCell=function(e,t){var n=e<i.headerRows&&t<i.headerColumns,r=e>=i.headerRows&&t<i.headerColumns,a=e<i.headerRows&&t>=i.headerColumns;if(n){var o=["blank"];return t>0&&o.push("level"+e),{type:"blank",classNames:o.join(" "),content:""}}if(a)return{type:"columns",classNames:(o=["col_heading","level"+e,"col"+(s=t-i.headerColumns)]).join(" "),content:i.getContent(i.columnsTable,s,e)};if(r){o=["row_heading","level"+t,"row"+(u=e-i.headerRows)];return{type:"index",id:"T_"+i.uuid+"level"+t+"_row"+u,classNames:o.join(" "),content:i.getContent(i.indexTable,u,t)}}o=["data","row"+(u=e-i.headerRows),"col"+(s=t-i.headerColumns)];var u,s,c=i.styler?i.getContent(i.styler.displayValuesTable,u,s):i.getContent(i.dataTable,u,s);return{type:"data",id:"T_"+i.uuid+"row"+u+"_col"+s,classNames:o.join(" "),content:c}},this.getContent=function(e,t,n){var r=e.getColumnAt(n);if(null===r)return"";switch(i.getColumnTypeId(e,n)){case zt.Timestamp:return i.nanosToDate(r.get(t));default:return r.get(t)}},this.dataTable=Lc.from(e),this.indexTable=Lc.from(t),this.columnsTable=Lc.from(n),this.styler=r?{caption:r.caption,displayValuesTable:Lc.from(r.displayValues),styles:r.styles,uuid:r.uuid}:void 0}return Object.defineProperty(e.prototype,"rows",{get:function(){return this.indexTable.length+this.columnsTable.numCols},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"columns",{get:function(){return this.indexTable.numCols+this.columnsTable.length},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"headerRows",{get:function(){return this.rows-this.dataRows},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"headerColumns",{get:function(){return this.columns-this.dataColumns},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"dataRows",{get:function(){return this.dataTable.length},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"dataColumns",{get:function(){return this.dataTable.numCols},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"uuid",{get:function(){return this.styler&&this.styler.uuid},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"caption",{get:function(){return this.styler&&this.styler.caption},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"styles",{get:function(){return this.styler&&this.styler.styles},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"table",{get:function(){return this.dataTable},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"index",{get:function(){return this.indexTable},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"columnTable",{get:function(){return this.columnsTable},enumerable:!0,configurable:!0}),e.prototype.serialize=function(){return{data:this.dataTable.serialize(),index:this.indexTable.serialize(),columns:this.columnsTable.serialize()}},e.prototype.getColumnTypeId=function(e,t){return e.schema.fields[t].type.typeId},e.prototype.nanosToDate=function(e){return new Date(e/1e6)},e}(),Sl=function(){return(Sl=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};!function(e){e.COMPONENT_READY="streamlit:componentReady",e.SET_COMPONENT_VALUE="streamlit:setComponentValue",e.SET_FRAME_HEIGHT="streamlit:setFrameHeight"}(jl||(jl={}));var Tl=function(){function e(){}return e.API_VERSION=1,e.RENDER_EVENT="streamlit:render",e.events=new E,e.registeredMessageListener=!1,e.setComponentReady=function(){e.registeredMessageListener||(window.addEventListener("message",e.onMessageEvent),e.registeredMessageListener=!0),e.sendBackMsg(jl.COMPONENT_READY,{apiVersion:e.API_VERSION})},e.setFrameHeight=function(t){void 0===t&&(t=document.body.scrollHeight),t!==e.lastFrameHeight&&(e.lastFrameHeight=t,e.sendBackMsg(jl.SET_FRAME_HEIGHT,{height:t}))},e.setComponentValue=function(t){var n;t instanceof xl?(n="dataframe",t=t.serialize()):!function(e){var t=!1;try{t=e instanceof BigInt64Array||e instanceof BigUint64Array}catch(n){}return e instanceof Int8Array||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array||t}(t)?t instanceof ArrayBuffer?(n="bytes",t=new Uint8Array(t)):n="json":(n="bytes",t=new Uint8Array(t.buffer)),e.sendBackMsg(jl.SET_COMPONENT_VALUE,{value:t,dataType:n})},e.onMessageEvent=function(t){switch(t.data.type){case e.RENDER_EVENT:e.onRenderMessage(t.data)}},e.onRenderMessage=function(t){var n=t.args;null==n&&(console.error("Got null args in onRenderMessage. This should never happen"),n={});var r=t.dfs&&t.dfs.length>0?e.argsDataframeToObject(t.dfs):{};n=Sl(Sl({},n),r);var i=Boolean(t.disabled),a=t.theme;a&&Il(a);var o={disabled:i,args:n,theme:a},u=new CustomEvent(e.RENDER_EVENT,{detail:o});e.events.dispatchEvent(u)},e.argsDataframeToObject=function(t){var n=t.map(function(t){var n=t.key,r=t.value;return[n,e.toArrowTable(r)]});return Object.fromEntries(n)},e.toArrowTable=function(e){var t=e.data,n=t.data,r=t.index,i=t.columns,a=t.styler;return new xl(n,r,i,a)},e.sendBackMsg=function(e,t){window.parent.postMessage(Sl({isStreamlitMessage:!0,type:e},t),"*")},e}(),Il=function(e){var t=document.createElement("style");document.head.appendChild(t),t.innerHTML="\n    :root {\n      --primary-color: "+e.primaryColor+";\n      --background-color: "+e.backgroundColor+";\n      --secondary-background-color: "+e.secondaryBackgroundColor+";\n      --text-color: "+e.textColor+";\n      --font: "+e.font+";\n    }\n\n    body {\n      background-color: var(--background-color);\n      color: var(--text-color);\n    }\n  "};var El=function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Al=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return El(t,e),t.prototype.componentDidMount=function(){Tl.setFrameHeight()},t.prototype.componentDidUpdate=function(){Tl.setFrameHeight()},t}(h.a.PureComponent);function Bl(e){var t=function(t){function n(n){var r=t.call(this,n)||this;return r.componentDidMount=function(){Tl.events.addEventListener(Tl.RENDER_EVENT,r.onRenderEvent),Tl.setComponentReady()},r.componentDidUpdate=function(){null!=r.state.componentError&&Tl.setFrameHeight()},r.componentWillUnmount=function(){Tl.events.removeEventListener(Tl.RENDER_EVENT,r.onRenderEvent)},r.onRenderEvent=function(e){var t=e;r.setState({renderData:t.detail})},r.render=function(){return null!=r.state.componentError?h.a.createElement("div",null,h.a.createElement("h1",null,"Component Error"),h.a.createElement("span",null,r.state.componentError.message)):null==r.state.renderData?null:h.a.createElement(e,{width:window.innerWidth,disabled:r.state.renderData.disabled,args:r.state.renderData.args,theme:r.state.renderData.theme})},r.state={renderData:void 0,componentError:void 0},r}return El(n,t),n.getDerivedStateFromError=function(e){return{componentError:e}},n}(h.a.PureComponent);return l()(t,e)}n.d(t,"b",function(){return Al}),n.d(t,"c",function(){return Bl}),n.d(t,"a",function(){return Tl})},function(e,t,n){"use strict";n(26);var r=n(6);var i=function(e){var t=Object.create(null);return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}},a=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|download|draggable|encType|enterKeyHint|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,o=i(function(e){return a.test(e)||111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)<91});function u(){return(u=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var s=function(){function e(e){var t=this;this._insertTag=function(e){var n;n=0===t.tags.length?t.insertionPoint?t.insertionPoint.nextSibling:t.prepend?t.container.firstChild:t.before:t.tags[t.tags.length-1].nextSibling,t.container.insertBefore(e,n),t.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(e){e.forEach(this._insertTag)},t.insert=function(e){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(function(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),void 0!==e.nonce&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}(this));var t=this.tags[this.tags.length-1];if(this.isSpeedy){var n=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}(t);try{n.insertRule(e,n.cssRules.length)}catch(c){0}}else t.appendChild(document.createTextNode(e));this.ctr++},t.flush=function(){this.tags.forEach(function(e){return e.parentNode&&e.parentNode.removeChild(e)}),this.tags=[],this.ctr=0},e}(),c="-ms-",l="-moz-",f="-webkit-",h="comm",d="rule",p="decl",y="@import",v="@keyframes",b=Math.abs,m=String.fromCharCode,g=Object.assign;function k(e){return e.trim()}function w(e,t,n){return e.replace(t,n)}function _(e,t){return e.indexOf(t)}function O(e,t){return 0|e.charCodeAt(t)}function j(e,t,n){return e.slice(t,n)}function x(e){return e.length}function S(e){return e.length}function T(e,t){return t.push(e),e}function I(e,t){return e.map(t).join("")}var E=1,A=1,B=0,D=0,C=0,L="";function F(e,t,n,r,i,a,o){return{value:e,root:t,parent:n,type:r,props:i,children:a,line:E,column:A,length:o,return:""}}function M(e,t){return g(F("",null,null,"",null,null,0),e,{length:-e.length},t)}function N(){return C=D>0?O(L,--D):0,A--,10===C&&(A=1,E--),C}function U(){return C=D<B?O(L,D++):0,A++,10===C&&(A=1,E++),C}function P(){return O(L,D)}function R(){return D}function z(e,t){return j(L,e,t)}function V(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function W(e){return E=A=1,B=x(L=e),D=0,[]}function $(e){return L="",e}function H(e){return k(z(D-1,function e(t){for(;U();)switch(C){case t:return D;case 34:case 39:34!==t&&39!==t&&e(C);break;case 40:41===t&&e(t);break;case 92:U()}return D}(91===e?e+2:40===e?e+1:e)))}function Y(e){for(;(C=P())&&C<33;)U();return V(e)>2||V(C)>3?"":" "}function K(e,t){for(;--t&&U()&&!(C<48||C>102||C>57&&C<65||C>70&&C<97););return z(e,R()+(t<6&&32==P()&&32==U()))}function Q(e,t){for(;U()&&e+C!==57&&(e+C!==84||47!==P()););return"/*"+z(t,D-1)+"*"+m(47===e?e:U())}function q(e){for(;!V(P());)U();return z(e,D)}function G(e){return $(function e(t,n,r,i,a,o,u,s,c){var l=0;var f=0;var h=u;var d=0;var p=0;var y=0;var v=1;var b=1;var g=1;var k=0;var O="";var j=a;var S=o;var I=i;var E=O;for(;b;)switch(y=k,k=U()){case 40:if(108!=y&&58==E.charCodeAt(h-1)){-1!=_(E+=w(H(k),"&","&\f"),"&\f")&&(g=-1);break}case 34:case 39:case 91:E+=H(k);break;case 9:case 10:case 13:case 32:E+=Y(y);break;case 92:E+=K(R()-1,7);continue;case 47:switch(P()){case 42:case 47:T(X(Q(U(),R()),n,r),c);break;default:E+="/"}break;case 123*v:s[l++]=x(E)*g;case 125*v:case 59:case 0:switch(k){case 0:case 125:b=0;case 59+f:p>0&&x(E)-h&&T(p>32?Z(E+";",i,r,h-1):Z(w(E," ","")+";",i,r,h-2),c);break;case 59:E+=";";default:if(T(I=J(E,n,r,l,f,a,s,O,j=[],S=[],h),o),123===k)if(0===f)e(E,n,I,I,j,o,h,s,S);else switch(d){case 100:case 109:case 115:e(t,I,I,i&&T(J(t,I,I,0,0,a,s,O,a,j=[],h),S),a,S,h,s,i?j:S);break;default:e(E,I,I,I,[""],S,0,s,S)}}l=f=p=0,v=g=1,O=E="",h=u;break;case 58:h=1+x(E),p=y;default:if(v<1)if(123==k)--v;else if(125==k&&0==v++&&125==N())continue;switch(E+=m(k),k*v){case 38:g=f>0?1:(E+="\f",-1);break;case 44:s[l++]=(x(E)-1)*g,g=1;break;case 64:45===P()&&(E+=H(U())),d=P(),f=h=x(O=E+=q(R())),k++;break;case 45:45===y&&2==x(E)&&(v=0)}}return o}("",null,null,null,[""],e=W(e),0,[0],e))}function J(e,t,n,r,i,a,o,u,s,c,l){for(var f=i-1,h=0===i?a:[""],p=S(h),y=0,v=0,m=0;y<r;++y)for(var g=0,_=j(e,f+1,f=b(v=o[y])),O=e;g<p;++g)(O=k(v>0?h[g]+" "+_:w(_,/&\f/g,h[g])))&&(s[m++]=O);return F(e,t,n,0===i?d:u,s,c,l)}function X(e,t,n){return F(e,t,n,h,m(C),j(e,2,-2),0)}function Z(e,t,n,r){return F(e,t,n,p,j(e,0,r),j(e,r+1,-1),r)}function ee(e,t){switch(function(e,t){return(((t<<2^O(e,0))<<2^O(e,1))<<2^O(e,2))<<2^O(e,3)}(e,t)){case 5103:return f+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return f+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return f+e+l+e+c+e+e;case 6828:case 4268:return f+e+c+e+e;case 6165:return f+e+c+"flex-"+e+e;case 5187:return f+e+w(e,/(\w+).+(:[^]+)/,f+"box-$1$2"+c+"flex-$1$2")+e;case 5443:return f+e+c+"flex-item-"+w(e,/flex-|-self/,"")+e;case 4675:return f+e+c+"flex-line-pack"+w(e,/align-content|flex-|-self/,"")+e;case 5548:return f+e+c+w(e,"shrink","negative")+e;case 5292:return f+e+c+w(e,"basis","preferred-size")+e;case 6060:return f+"box-"+w(e,"-grow","")+f+e+c+w(e,"grow","positive")+e;case 4554:return f+w(e,/([^-])(transform)/g,"$1"+f+"$2")+e;case 6187:return w(w(w(e,/(zoom-|grab)/,f+"$1"),/(image-set)/,f+"$1"),e,"")+e;case 5495:case 3959:return w(e,/(image-set\([^]*)/,f+"$1$`$1");case 4968:return w(w(e,/(.+:)(flex-)?(.*)/,f+"box-pack:$3"+c+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+f+e+e;case 4095:case 3583:case 4068:case 2532:return w(e,/(.+)-inline(.+)/,f+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(x(e)-1-t>6)switch(O(e,t+1)){case 109:if(45!==O(e,t+4))break;case 102:return w(e,/(.+:)(.+)-([^]+)/,"$1"+f+"$2-$3$1"+l+(108==O(e,t+3)?"$3":"$2-$3"))+e;case 115:return~_(e,"stretch")?ee(w(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(115!==O(e,t+1))break;case 6444:switch(O(e,x(e)-3-(~_(e,"!important")&&10))){case 107:return w(e,":",":"+f)+e;case 101:return w(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+f+(45===O(e,14)?"inline-":"")+"box$3$1"+f+"$2$3$1"+c+"$2box$3")+e}break;case 5936:switch(O(e,t+11)){case 114:return f+e+c+w(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return f+e+c+w(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return f+e+c+w(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return f+e+c+e+e}return e}function te(e,t){for(var n="",r=S(e),i=0;i<r;i++)n+=t(e[i],i,e,t)||"";return n}function ne(e,t,n,r){switch(e.type){case y:case p:return e.return=e.return||e.value;case h:return"";case v:return e.return=e.value+"{"+te(e.children,r)+"}";case d:e.value=e.props.join(",")}return x(n=te(e.children,r))?e.return=e.value+"{"+n+"}":""}var re=function(e,t,n){for(var r=0,i=0;r=i,i=P(),38===r&&12===i&&(t[n]=1),!V(i);)U();return z(e,D)},ie=function(e,t){return $(function(e,t){var n=-1,r=44;do{switch(V(r)){case 0:38===r&&12===P()&&(t[n]=1),e[n]+=re(D-1,t,n);break;case 2:e[n]+=H(r);break;case 4:if(44===r){e[++n]=58===P()?"&\f":"",t[n]=e[n].length;break}default:e[n]+=m(r)}}while(r=U());return e}(W(e),t))},ae=new WeakMap,oe=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var t=e.value,n=e.parent,r=e.column===n.column&&e.line===n.line;"rule"!==n.type;)if(!(n=n.parent))return;if((1!==e.props.length||58===t.charCodeAt(0)||ae.get(n))&&!r){ae.set(e,!0);for(var i=[],a=ie(t,i),o=n.props,u=0,s=0;u<a.length;u++)for(var c=0;c<o.length;c++,s++)e.props[s]=i[u]?a[u].replace(/&\f/g,o[c]):o[c]+" "+a[u]}}},ue=function(e){if("decl"===e.type){var t=e.value;108===t.charCodeAt(0)&&98===t.charCodeAt(2)&&(e.return="",e.value="")}},se=[function(e,t,n,r){if(e.length>-1&&!e.return)switch(e.type){case p:e.return=ee(e.value,e.length);break;case v:return te([M(e,{value:w(e.value,"@","@"+f)})],r);case d:if(e.length)return I(e.props,function(t){switch(function(e,t){return(e=t.exec(e))?e[0]:e}(t,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return te([M(e,{props:[w(t,/:(read-\w+)/,":"+l+"$1")]})],r);case"::placeholder":return te([M(e,{props:[w(t,/:(plac\w+)/,":"+f+"input-$1")]}),M(e,{props:[w(t,/:(plac\w+)/,":"+l+"$1")]}),M(e,{props:[w(t,/:(plac\w+)/,c+"input-$1")]})],r)}return""})}}],ce=function(e){var t=e.key;if("css"===t){var n=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(n,function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))})}var r=e.stylisPlugins||se;var i,a,o={},u=[];i=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+t+' "]'),function(e){for(var t=e.getAttribute("data-emotion").split(" "),n=1;n<t.length;n++)o[t[n]]=!0;u.push(e)});var c,l,f=[ne,(l=function(e){c.insert(e)},function(e){e.root||(e=e.return)&&l(e)})],h=function(e){var t=S(e);return function(n,r,i,a){for(var o="",u=0;u<t;u++)o+=e[u](n,r,i,a)||"";return o}}([oe,ue].concat(r,f));a=function(e,t,n,r){c=n,te(G(e?e+"{"+t.styles+"}":t.styles),h),r&&(d.inserted[t.name]=!0)};var d={key:t,sheet:new s({key:t,container:i,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:o,registered:{},insert:a};return d.sheet.hydrate(u),d};n(13);function le(e,t,n){var r="";return n.split(" ").forEach(function(n){void 0!==e[n]?t.push(e[n]+";"):r+=n+" "}),r}var fe=function(e,t,n){var r=e.key+"-"+t.name;!1===n&&void 0===e.registered[r]&&(e.registered[r]=t.styles)},he=function(e,t,n){fe(e,t,n);var r=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var i=t;do{e.insert(t===i?"."+r:"",i,e.sheet,!0);i=i.next}while(void 0!==i)}};var de=function(e){for(var t,n=0,r=0,i=e.length;i>=4;++r,i-=4)t=1540483477*(65535&(t=255&e.charCodeAt(r)|(255&e.charCodeAt(++r))<<8|(255&e.charCodeAt(++r))<<16|(255&e.charCodeAt(++r))<<24))+(59797*(t>>>16)<<16),n=1540483477*(65535&(t^=t>>>24))+(59797*(t>>>16)<<16)^1540483477*(65535&n)+(59797*(n>>>16)<<16);switch(i){case 3:n^=(255&e.charCodeAt(r+2))<<16;case 2:n^=(255&e.charCodeAt(r+1))<<8;case 1:n=1540483477*(65535&(n^=255&e.charCodeAt(r)))+(59797*(n>>>16)<<16)}return(((n=1540483477*(65535&(n^=n>>>13))+(59797*(n>>>16)<<16))^n>>>15)>>>0).toString(36)},pe={animationIterationCount:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},ye=/[A-Z]|^ms/g,ve=/_EMO_([^_]+?)_([^]*?)_EMO_/g,be=function(e){return 45===e.charCodeAt(1)},me=function(e){return null!=e&&"boolean"!==typeof e},ge=i(function(e){return be(e)?e:e.replace(ye,"-$&").toLowerCase()}),ke=function(e,t){switch(e){case"animation":case"animationName":if("string"===typeof t)return t.replace(ve,function(e,t,n){return _e={name:t,styles:n,next:_e},t})}return 1===pe[e]||be(e)||"number"!==typeof t||0===t?t:t+"px"};function we(e,t,n){if(null==n)return"";if(void 0!==n.__emotion_styles)return n;switch(typeof n){case"boolean":return"";case"object":if(1===n.anim)return _e={name:n.name,styles:n.styles,next:_e},n.name;if(void 0!==n.styles){var r=n.next;if(void 0!==r)for(;void 0!==r;)_e={name:r.name,styles:r.styles,next:_e},r=r.next;return n.styles+";"}return function(e,t,n){var r="";if(Array.isArray(n))for(var i=0;i<n.length;i++)r+=we(e,t,n[i])+";";else for(var a in n){var o=n[a];if("object"!==typeof o)null!=t&&void 0!==t[o]?r+=a+"{"+t[o]+"}":me(o)&&(r+=ge(a)+":"+ke(a,o)+";");else if(!Array.isArray(o)||"string"!==typeof o[0]||null!=t&&void 0!==t[o[0]]){var u=we(e,t,o);switch(a){case"animation":case"animationName":r+=ge(a)+":"+u+";";break;default:r+=a+"{"+u+"}"}}else for(var s=0;s<o.length;s++)me(o[s])&&(r+=ge(a)+":"+ke(a,o[s])+";")}return r}(e,t,n);case"function":if(void 0!==e){var i=_e,a=n(e);return _e=i,we(e,t,a)}break;case"string":}if(null==t)return n;var o=t[n];return void 0!==o?o:n}var _e,Oe=/label:\s*([^\s;\n{]+)\s*(;|$)/g;var je=function(e,t,n){if(1===e.length&&"object"===typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var r=!0,i="";_e=void 0;var a=e[0];null==a||void 0===a.raw?(r=!1,i+=we(n,t,a)):i+=a[0];for(var o=1;o<e.length;o++)i+=we(n,t,e[o]),r&&(i+=a[o]);Oe.lastIndex=0;for(var u,s="";null!==(u=Oe.exec(i));)s+="-"+u[1];return{name:de(i)+s,styles:i,next:_e}},xe=Object(r.createContext)("undefined"!==typeof HTMLElement?ce({key:"css"}):null);xe.Provider;var Se=function(e){return Object(r.forwardRef)(function(t,n){var i=Object(r.useContext)(xe);return e(t,i,n)})},Te=Object(r.createContext)({});r.useInsertionEffect&&r.useInsertionEffect;var Ie=o,Ee=function(e){return"theme"!==e},Ae=function(e){return"string"===typeof e&&e.charCodeAt(0)>96?Ie:Ee},Be=function(e,t,n){var r;if(t){var i=t.shouldForwardProp;r=e.__emotion_forwardProp&&i?function(t){return e.__emotion_forwardProp(t)&&i(t)}:i}return"function"!==typeof r&&n&&(r=e.__emotion_forwardProp),r},De=r.useInsertionEffect?r.useInsertionEffect:function(e){e()};var Ce=function(e){var t=e.cache,n=e.serialized,r=e.isStringTag;fe(t,n,r);De(function(){return he(t,n,r)});return null},Le=function e(t,n){var i,a,o=t.__emotion_real===t,s=o&&t.__emotion_base||t;void 0!==n&&(i=n.label,a=n.target);var c=Be(t,n,o),l=c||Ae(s),f=!l("as");return function(){var h=arguments,d=o&&void 0!==t.__emotion_styles?t.__emotion_styles.slice(0):[];if(void 0!==i&&d.push("label:"+i+";"),null==h[0]||void 0===h[0].raw)d.push.apply(d,h);else{d.push(h[0][0]);for(var p=h.length,y=1;y<p;y++)d.push(h[y],h[0][y])}var v=Se(function(e,t,n){var i=f&&e.as||s,o="",u=[],h=e;if(null==e.theme){for(var p in h={},e)h[p]=e[p];h.theme=Object(r.useContext)(Te)}"string"===typeof e.className?o=le(t.registered,u,e.className):null!=e.className&&(o=e.className+" ");var y=je(d.concat(u),t.registered,h);o+=t.key+"-"+y.name,void 0!==a&&(o+=" "+a);var v=f&&void 0===c?Ae(i):l,b={};for(var m in e)f&&"as"===m||v(m)&&(b[m]=e[m]);return b.className=o,b.ref=n,Object(r.createElement)(r.Fragment,null,Object(r.createElement)(Ce,{cache:t,serialized:y,isStringTag:"string"===typeof i}),Object(r.createElement)(i,b))});return v.displayName=void 0!==i?i:"Styled("+("string"===typeof s?s:s.displayName||s.name||"Component")+")",v.defaultProps=t.defaultProps,v.__emotion_real=v,v.__emotion_base=s,v.__emotion_styles=d,v.__emotion_forwardProp=c,Object.defineProperty(v,"toString",{value:function(){return"."+a}}),v.withComponent=function(t,r){return e(t,u({},n,r,{shouldForwardProp:Be(v,r,!0)})).apply(void 0,d)},v}}.bind();["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"].forEach(function(e){Le[e]=Le(e)});t.a=Le},function(e,t,n){"use strict";function r(e,t){return(r=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}n.d(t,"a",function(){return r})},function(e,t,n){"use strict";var r=n(22),i={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},a={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},o={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},u={};function s(e){return r.isMemo(e)?o:u[e.$$typeof]||i}u[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},u[r.Memo]=o;var c=Object.defineProperty,l=Object.getOwnPropertyNames,f=Object.getOwnPropertySymbols,h=Object.getOwnPropertyDescriptor,d=Object.getPrototypeOf,p=Object.prototype;e.exports=function e(t,n,r){if("string"!==typeof n){if(p){var i=d(n);i&&i!==p&&e(t,i,r)}var o=l(n);f&&(o=o.concat(f(n)));for(var u=s(t),y=s(n),v=0;v<o.length;++v){var b=o[v];if(!a[b]&&(!r||!r[b])&&(!y||!y[b])&&(!u||!u[b])){var m=h(n,b);try{c(t,b,m)}catch(g){}}}}return t}},function(e,t,n){"use strict";var r=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,a=Object.prototype.propertyIsEnumerable;e.exports=function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},n=0;n<10;n++)t["_"+String.fromCharCode(n)]=n;if("0123456789"!==Object.getOwnPropertyNames(t).map(function(e){return t[e]}).join(""))return!1;var r={};return"abcdefghijklmnopqrst".split("").forEach(function(e){r[e]=e}),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},r)).join("")}catch(i){return!1}}()?Object.assign:function(e,t){for(var n,o,u=function(e){if(null===e||void 0===e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}(e),s=1;s<arguments.length;s++){for(var c in n=Object(arguments[s]))i.call(n,c)&&(u[c]=n[c]);if(r){o=r(n);for(var l=0;l<o.length;l++)a.call(n,o[l])&&(u[o[l]]=n[o[l]])}}return u}},function(e,t,n){"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(19)},,,function(e,t,n){"use strict";var r=n(14),i="function"===typeof Symbol&&Symbol.for,a=i?Symbol.for("react.element"):60103,o=i?Symbol.for("react.portal"):60106,u=i?Symbol.for("react.fragment"):60107,s=i?Symbol.for("react.strict_mode"):60108,c=i?Symbol.for("react.profiler"):60114,l=i?Symbol.for("react.provider"):60109,f=i?Symbol.for("react.context"):60110,h=i?Symbol.for("react.forward_ref"):60112,d=i?Symbol.for("react.suspense"):60113,p=i?Symbol.for("react.memo"):60115,y=i?Symbol.for("react.lazy"):60116,v="function"===typeof Symbol&&Symbol.iterator;function b(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var m={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},g={};function k(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}function w(){}function _(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}k.prototype.isReactComponent={},k.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error(b(85));this.updater.enqueueSetState(this,e,t,"setState")},k.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},w.prototype=k.prototype;var O=_.prototype=new w;O.constructor=_,r(O,k.prototype),O.isPureReactComponent=!0;var j={current:null},x=Object.prototype.hasOwnProperty,S={key:!0,ref:!0,__self:!0,__source:!0};function T(e,t,n){var r,i={},o=null,u=null;if(null!=t)for(r in void 0!==t.ref&&(u=t.ref),void 0!==t.key&&(o=""+t.key),t)x.call(t,r)&&!S.hasOwnProperty(r)&&(i[r]=t[r]);var s=arguments.length-2;if(1===s)i.children=n;else if(1<s){for(var c=Array(s),l=0;l<s;l++)c[l]=arguments[l+2];i.children=c}if(e&&e.defaultProps)for(r in s=e.defaultProps)void 0===i[r]&&(i[r]=s[r]);return{$$typeof:a,type:e,key:o,ref:u,props:i,_owner:j.current}}function I(e){return"object"===typeof e&&null!==e&&e.$$typeof===a}var E=/\/+/g,A=[];function B(e,t,n,r){if(A.length){var i=A.pop();return i.result=e,i.keyPrefix=t,i.func=n,i.context=r,i.count=0,i}return{result:e,keyPrefix:t,func:n,context:r,count:0}}function D(e){e.result=null,e.keyPrefix=null,e.func=null,e.context=null,e.count=0,10>A.length&&A.push(e)}function C(e,t,n){return null==e?0:function e(t,n,r,i){var u=typeof t;"undefined"!==u&&"boolean"!==u||(t=null);var s=!1;if(null===t)s=!0;else switch(u){case"string":case"number":s=!0;break;case"object":switch(t.$$typeof){case a:case o:s=!0}}if(s)return r(i,t,""===n?"."+L(t,0):n),1;if(s=0,n=""===n?".":n+":",Array.isArray(t))for(var c=0;c<t.length;c++){var l=n+L(u=t[c],c);s+=e(u,l,r,i)}else if(l=null===t||"object"!==typeof t?null:"function"===typeof(l=v&&t[v]||t["@@iterator"])?l:null,"function"===typeof l)for(t=l.call(t),c=0;!(u=t.next()).done;)s+=e(u=u.value,l=n+L(u,c++),r,i);else if("object"===u)throw r=""+t,Error(b(31,"[object Object]"===r?"object with keys {"+Object.keys(t).join(", ")+"}":r,""));return s}(e,"",t,n)}function L(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+(""+e).replace(/[=:]/g,function(e){return t[e]})}(e.key):t.toString(36)}function F(e,t){e.func.call(e.context,t,e.count++)}function M(e,t,n){var r=e.result,i=e.keyPrefix;e=e.func.call(e.context,t,e.count++),Array.isArray(e)?N(e,r,n,function(e){return e}):null!=e&&(I(e)&&(e=function(e,t){return{$$typeof:a,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(e,i+(!e.key||t&&t.key===e.key?"":(""+e.key).replace(E,"$&/")+"/")+n)),r.push(e))}function N(e,t,n,r,i){var a="";null!=n&&(a=(""+n).replace(E,"$&/")+"/"),C(e,M,t=B(t,a,r,i)),D(t)}var U={current:null};function P(){var e=U.current;if(null===e)throw Error(b(321));return e}var R={ReactCurrentDispatcher:U,ReactCurrentBatchConfig:{suspense:null},ReactCurrentOwner:j,IsSomeRendererActing:{current:!1},assign:r};t.Children={map:function(e,t,n){if(null==e)return e;var r=[];return N(e,r,null,t,n),r},forEach:function(e,t,n){if(null==e)return e;C(e,F,t=B(null,null,t,n)),D(t)},count:function(e){return C(e,function(){return null},null)},toArray:function(e){var t=[];return N(e,t,null,function(e){return e}),t},only:function(e){if(!I(e))throw Error(b(143));return e}},t.Component=k,t.Fragment=u,t.Profiler=c,t.PureComponent=_,t.StrictMode=s,t.Suspense=d,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=R,t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error(b(267,e));var i=r({},e.props),o=e.key,u=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(u=t.ref,s=j.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var c=e.type.defaultProps;for(l in t)x.call(t,l)&&!S.hasOwnProperty(l)&&(i[l]=void 0===t[l]&&void 0!==c?c[l]:t[l])}var l=arguments.length-2;if(1===l)i.children=n;else if(1<l){c=Array(l);for(var f=0;f<l;f++)c[f]=arguments[f+2];i.children=c}return{$$typeof:a,type:e.type,key:o,ref:u,props:i,_owner:s}},t.createContext=function(e,t){return void 0===t&&(t=null),(e={$$typeof:f,_calculateChangedBits:t,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider={$$typeof:l,_context:e},e.Consumer=e},t.createElement=T,t.createFactory=function(e){var t=T.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:h,render:e}},t.isValidElement=I,t.lazy=function(e){return{$$typeof:y,_ctor:e,_status:-1,_result:null}},t.memo=function(e,t){return{$$typeof:p,type:e,compare:void 0===t?null:t}},t.useCallback=function(e,t){return P().useCallback(e,t)},t.useContext=function(e,t){return P().useContext(e,t)},t.useDebugValue=function(){},t.useEffect=function(e,t){return P().useEffect(e,t)},t.useImperativeHandle=function(e,t,n){return P().useImperativeHandle(e,t,n)},t.useLayoutEffect=function(e,t){return P().useLayoutEffect(e,t)},t.useMemo=function(e,t){return P().useMemo(e,t)},t.useReducer=function(e,t,n){return P().useReducer(e,t,n)},t.useRef=function(e){return P().useRef(e)},t.useState=function(e){return P().useState(e)},t.version="16.14.0"},function(e,t,n){"use strict";var r=n(6),i=n(14),a=n(20);function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}if(!r)throw Error(o(227));var u=!1,s=null,c=!1,l=null,f={onError:function(e){u=!0,s=e}};function h(e,t,n,r,i,a,o,c,l){u=!1,s=null,function(e,t,n,r,i,a,o,u,s){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(l){this.onError(l)}}.apply(f,arguments)}var d=null,p=null,y=null;function v(e,t,n){var r=e.type||"unknown-event";e.currentTarget=y(n),function(e,t,n,r,i,a,f,d,p){if(h.apply(this,arguments),u){if(!u)throw Error(o(198));var y=s;u=!1,s=null,c||(c=!0,l=y)}}(r,t,void 0,e),e.currentTarget=null}var b=null,m={};function g(){if(b)for(var e in m){var t=m[e],n=b.indexOf(e);if(!(-1<n))throw Error(o(96,e));if(!w[n]){if(!t.extractEvents)throw Error(o(97,e));for(var r in w[n]=t,n=t.eventTypes){var i=void 0,a=n[r],u=t,s=r;if(_.hasOwnProperty(s))throw Error(o(99,s));_[s]=a;var c=a.phasedRegistrationNames;if(c){for(i in c)c.hasOwnProperty(i)&&k(c[i],u,s);i=!0}else a.registrationName?(k(a.registrationName,u,s),i=!0):i=!1;if(!i)throw Error(o(98,r,e))}}}}function k(e,t,n){if(O[e])throw Error(o(100,e));O[e]=t,j[e]=t.eventTypes[n].dependencies}var w=[],_={},O={},j={};function x(e){var t,n=!1;for(t in e)if(e.hasOwnProperty(t)){var r=e[t];if(!m.hasOwnProperty(t)||m[t]!==r){if(m[t])throw Error(o(102,t));m[t]=r,n=!0}}n&&g()}var S=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),T=null,I=null,E=null;function A(e){if(e=p(e)){if("function"!==typeof T)throw Error(o(280));var t=e.stateNode;t&&(t=d(t),T(e.stateNode,e.type,t))}}function B(e){I?E?E.push(e):E=[e]:I=e}function D(){if(I){var e=I,t=E;if(E=I=null,A(e),t)for(e=0;e<t.length;e++)A(t[e])}}function C(e,t){return e(t)}function L(e,t,n,r,i){return e(t,n,r,i)}function F(){}var M=C,N=!1,U=!1;function P(){null===I&&null===E||(F(),D())}function R(e,t,n){if(U)return e(t,n);U=!0;try{return M(e,t,n)}finally{U=!1,P()}}var z=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,V=Object.prototype.hasOwnProperty,W={},$={};function H(e,t,n,r,i,a){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a}var Y={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Y[e]=new H(e,0,!1,e,null,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Y[t]=new H(t,1,!1,e[1],null,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){Y[e]=new H(e,2,!1,e.toLowerCase(),null,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Y[e]=new H(e,2,!1,e,null,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Y[e]=new H(e,3,!1,e.toLowerCase(),null,!1)}),["checked","multiple","muted","selected"].forEach(function(e){Y[e]=new H(e,3,!0,e,null,!1)}),["capture","download"].forEach(function(e){Y[e]=new H(e,4,!1,e,null,!1)}),["cols","rows","size","span"].forEach(function(e){Y[e]=new H(e,6,!1,e,null,!1)}),["rowSpan","start"].forEach(function(e){Y[e]=new H(e,5,!1,e.toLowerCase(),null,!1)});var K=/[\-:]([a-z])/g;function Q(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(K,Q);Y[t]=new H(t,1,!1,e,null,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(K,Q);Y[t]=new H(t,1,!1,e,"http://www.w3.org/1999/xlink",!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(K,Q);Y[t]=new H(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1)}),["tabIndex","crossOrigin"].forEach(function(e){Y[e]=new H(e,1,!1,e.toLowerCase(),null,!1)}),Y.xlinkHref=new H("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0),["src","href","action","formAction"].forEach(function(e){Y[e]=new H(e,1,!1,e.toLowerCase(),null,!0)});var q=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function G(e,t,n,r){var i=Y.hasOwnProperty(t)?Y[t]:null;(null!==i?0===i.type:!r&&(2<t.length&&("o"===t[0]||"O"===t[0])&&("n"===t[1]||"N"===t[1])))||(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,i,r)&&(n=null),r||null===i?function(e){return!!V.call($,e)||!V.call(W,e)&&(z.test(e)?$[e]=!0:(W[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=null===n?3!==i.type&&"":n:(t=i.attributeName,r=i.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(i=i.type)||4===i&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}q.hasOwnProperty("ReactCurrentDispatcher")||(q.ReactCurrentDispatcher={current:null}),q.hasOwnProperty("ReactCurrentBatchConfig")||(q.ReactCurrentBatchConfig={suspense:null});var J=/^(.*)[\\\/]/,X="function"===typeof Symbol&&Symbol.for,Z=X?Symbol.for("react.element"):60103,ee=X?Symbol.for("react.portal"):60106,te=X?Symbol.for("react.fragment"):60107,ne=X?Symbol.for("react.strict_mode"):60108,re=X?Symbol.for("react.profiler"):60114,ie=X?Symbol.for("react.provider"):60109,ae=X?Symbol.for("react.context"):60110,oe=X?Symbol.for("react.concurrent_mode"):60111,ue=X?Symbol.for("react.forward_ref"):60112,se=X?Symbol.for("react.suspense"):60113,ce=X?Symbol.for("react.suspense_list"):60120,le=X?Symbol.for("react.memo"):60115,fe=X?Symbol.for("react.lazy"):60116,he=X?Symbol.for("react.block"):60121,de="function"===typeof Symbol&&Symbol.iterator;function pe(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=de&&e[de]||e["@@iterator"])?e:null}function ye(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case te:return"Fragment";case ee:return"Portal";case re:return"Profiler";case ne:return"StrictMode";case se:return"Suspense";case ce:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case ae:return"Context.Consumer";case ie:return"Context.Provider";case ue:var t=e.render;return t=t.displayName||t.name||"",e.displayName||(""!==t?"ForwardRef("+t+")":"ForwardRef");case le:return ye(e.type);case he:return ye(e.render);case fe:if(e=1===e._status?e._result:null)return ye(e)}return null}function ve(e){var t="";do{e:switch(e.tag){case 3:case 4:case 6:case 7:case 10:case 9:var n="";break e;default:var r=e._debugOwner,i=e._debugSource,a=ye(e.type);n=null,r&&(n=ye(r.type)),r=a,a="",i?a=" (at "+i.fileName.replace(J,"")+":"+i.lineNumber+")":n&&(a=" (created by "+n+")"),n="\n    in "+(r||"Unknown")+a}t+=n,e=e.return}while(e);return t}function be(e){switch(typeof e){case"boolean":case"number":case"object":case"string":case"undefined":return e;default:return""}}function me(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function ge(e){e._valueTracker||(e._valueTracker=function(e){var t=me(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var i=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function ke(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=me(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function we(e,t){var n=t.checked;return i({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function _e(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=be(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Oe(e,t){null!=(t=t.checked)&&G(e,"checked",t,!1)}function je(e,t){Oe(e,t);var n=be(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?Se(e,t.type,n):t.hasOwnProperty("defaultValue")&&Se(e,t.type,be(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function xe(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function Se(e,t,n){"number"===t&&e.ownerDocument.activeElement===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}function Te(e,t){return e=i({children:void 0},t),(t=function(e){var t="";return r.Children.forEach(e,function(e){null!=e&&(t+=e)}),t}(t.children))&&(e.children=t),e}function Ie(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+be(n),t=null,i=0;i<e.length;i++){if(e[i].value===n)return e[i].selected=!0,void(r&&(e[i].defaultSelected=!0));null!==t||e[i].disabled||(t=e[i])}null!==t&&(t.selected=!0)}}function Ee(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(o(91));return i({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(o(92));if(Array.isArray(n)){if(!(1>=n.length))throw Error(o(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:be(n)}}function Be(e,t){var n=be(t.value),r=be(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function De(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}var Ce="http://www.w3.org/1999/xhtml",Le="http://www.w3.org/2000/svg";function Fe(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Me(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?Fe(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var Ne,Ue,Pe=(Ue=function(e,t){if(e.namespaceURI!==Le||"innerHTML"in e)e.innerHTML=t;else{for((Ne=Ne||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ne.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction(function(){return Ue(e,t)})}:Ue);function Re(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}function ze(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Ve={animationend:ze("Animation","AnimationEnd"),animationiteration:ze("Animation","AnimationIteration"),animationstart:ze("Animation","AnimationStart"),transitionend:ze("Transition","TransitionEnd")},We={},$e={};function He(e){if(We[e])return We[e];if(!Ve[e])return e;var t,n=Ve[e];for(t in n)if(n.hasOwnProperty(t)&&t in $e)return We[e]=n[t];return e}S&&($e=document.createElement("div").style,"AnimationEvent"in window||(delete Ve.animationend.animation,delete Ve.animationiteration.animation,delete Ve.animationstart.animation),"TransitionEvent"in window||delete Ve.transitionend.transition);var Ye=He("animationend"),Ke=He("animationiteration"),Qe=He("animationstart"),qe=He("transitionend"),Ge="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Je=new("function"===typeof WeakMap?WeakMap:Map);function Xe(e){var t=Je.get(e);return void 0===t&&(t=new Map,Je.set(e,t)),t}function Ze(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(1026&(t=e).effectTag)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function et(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function tt(e){if(Ze(e)!==e)throw Error(o(188))}function nt(e){if(!(e=function(e){var t=e.alternate;if(!t){if(null===(t=Ze(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(null===i)break;var a=i.alternate;if(null===a){if(null!==(r=i.return)){n=r;continue}break}if(i.child===a.child){for(a=i.child;a;){if(a===n)return tt(i),e;if(a===r)return tt(i),t;a=a.sibling}throw Error(o(188))}if(n.return!==r.return)n=i,r=a;else{for(var u=!1,s=i.child;s;){if(s===n){u=!0,n=i,r=a;break}if(s===r){u=!0,r=i,n=a;break}s=s.sibling}if(!u){for(s=a.child;s;){if(s===n){u=!0,n=a,r=i;break}if(s===r){u=!0,r=a,n=i;break}s=s.sibling}if(!u)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(e)))return null;for(var t=e;;){if(5===t.tag||6===t.tag)return t;if(t.child)t.child.return=t,t=t.child;else{if(t===e)break;for(;!t.sibling;){if(!t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}}return null}function rt(e,t){if(null==t)throw Error(o(30));return null==e?t:Array.isArray(e)?Array.isArray(t)?(e.push.apply(e,t),e):(e.push(t),e):Array.isArray(t)?[e].concat(t):[e,t]}function it(e,t,n){Array.isArray(e)?e.forEach(t,n):e&&t.call(n,e)}var at=null;function ot(e){if(e){var t=e._dispatchListeners,n=e._dispatchInstances;if(Array.isArray(t))for(var r=0;r<t.length&&!e.isPropagationStopped();r++)v(e,t[r],n[r]);else t&&v(e,t,n);e._dispatchListeners=null,e._dispatchInstances=null,e.isPersistent()||e.constructor.release(e)}}function ut(e){if(null!==e&&(at=rt(at,e)),e=at,at=null,e){if(it(e,ot),at)throw Error(o(95));if(c)throw e=l,c=!1,l=null,e}}function st(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}function ct(e){if(!S)return!1;var t=(e="on"+e)in document;return t||((t=document.createElement("div")).setAttribute(e,"return;"),t="function"===typeof t[e]),t}var lt=[];function ft(e){e.topLevelType=null,e.nativeEvent=null,e.targetInst=null,e.ancestors.length=0,10>lt.length&&lt.push(e)}function ht(e,t,n,r){if(lt.length){var i=lt.pop();return i.topLevelType=e,i.eventSystemFlags=r,i.nativeEvent=t,i.targetInst=n,i}return{topLevelType:e,eventSystemFlags:r,nativeEvent:t,targetInst:n,ancestors:[]}}function dt(e){var t=e.targetInst,n=t;do{if(!n){e.ancestors.push(n);break}var r=n;if(3===r.tag)r=r.stateNode.containerInfo;else{for(;r.return;)r=r.return;r=3!==r.tag?null:r.stateNode.containerInfo}if(!r)break;5!==(t=n.tag)&&6!==t||e.ancestors.push(n),n=En(r)}while(n);for(n=0;n<e.ancestors.length;n++){t=e.ancestors[n];var i=st(e.nativeEvent);r=e.topLevelType;var a=e.nativeEvent,o=e.eventSystemFlags;0===n&&(o|=64);for(var u=null,s=0;s<w.length;s++){var c=w[s];c&&(c=c.extractEvents(r,t,a,i,o))&&(u=rt(u,c))}ut(u)}}function pt(e,t,n){if(!n.has(e)){switch(e){case"scroll":Qt(t,"scroll",!0);break;case"focus":case"blur":Qt(t,"focus",!0),Qt(t,"blur",!0),n.set("blur",null),n.set("focus",null);break;case"cancel":case"close":ct(e)&&Qt(t,e,!0);break;case"invalid":case"submit":case"reset":break;default:-1===Ge.indexOf(e)&&Kt(e,t)}n.set(e,null)}}var yt,vt,bt,mt=!1,gt=[],kt=null,wt=null,_t=null,Ot=new Map,jt=new Map,xt=[],St="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput close cancel copy cut paste click change contextmenu reset submit".split(" "),Tt="focus blur dragenter dragleave mouseover mouseout pointerover pointerout gotpointercapture lostpointercapture".split(" ");function It(e,t,n,r,i){return{blockedOn:e,topLevelType:t,eventSystemFlags:32|n,nativeEvent:i,container:r}}function Et(e,t){switch(e){case"focus":case"blur":kt=null;break;case"dragenter":case"dragleave":wt=null;break;case"mouseover":case"mouseout":_t=null;break;case"pointerover":case"pointerout":Ot.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":jt.delete(t.pointerId)}}function At(e,t,n,r,i,a){return null===e||e.nativeEvent!==a?(e=It(t,n,r,i,a),null!==t&&(null!==(t=An(t))&&vt(t)),e):(e.eventSystemFlags|=r,e)}function Bt(e){var t=En(e.target);if(null!==t){var n=Ze(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=et(n)))return e.blockedOn=t,void a.unstable_runWithPriority(e.priority,function(){bt(n)})}else if(3===t&&n.stateNode.hydrate)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Dt(e){if(null!==e.blockedOn)return!1;var t=Gt(e.topLevelType,e.eventSystemFlags,e.container,e.nativeEvent);if(null!==t){var n=An(t);return null!==n&&vt(n),e.blockedOn=t,!1}return!0}function Ct(e,t,n){Dt(e)&&n.delete(t)}function Lt(){for(mt=!1;0<gt.length;){var e=gt[0];if(null!==e.blockedOn){null!==(e=An(e.blockedOn))&&yt(e);break}var t=Gt(e.topLevelType,e.eventSystemFlags,e.container,e.nativeEvent);null!==t?e.blockedOn=t:gt.shift()}null!==kt&&Dt(kt)&&(kt=null),null!==wt&&Dt(wt)&&(wt=null),null!==_t&&Dt(_t)&&(_t=null),Ot.forEach(Ct),jt.forEach(Ct)}function Ft(e,t){e.blockedOn===t&&(e.blockedOn=null,mt||(mt=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Lt)))}function Mt(e){function t(t){return Ft(t,e)}if(0<gt.length){Ft(gt[0],e);for(var n=1;n<gt.length;n++){var r=gt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==kt&&Ft(kt,e),null!==wt&&Ft(wt,e),null!==_t&&Ft(_t,e),Ot.forEach(t),jt.forEach(t),n=0;n<xt.length;n++)(r=xt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<xt.length&&null===(n=xt[0]).blockedOn;)Bt(n),null===n.blockedOn&&xt.shift()}var Nt={},Ut=new Map,Pt=new Map,Rt=["abort","abort",Ye,"animationEnd",Ke,"animationIteration",Qe,"animationStart","canplay","canPlay","canplaythrough","canPlayThrough","durationchange","durationChange","emptied","emptied","encrypted","encrypted","ended","ended","error","error","gotpointercapture","gotPointerCapture","load","load","loadeddata","loadedData","loadedmetadata","loadedMetadata","loadstart","loadStart","lostpointercapture","lostPointerCapture","playing","playing","progress","progress","seeking","seeking","stalled","stalled","suspend","suspend","timeupdate","timeUpdate",qe,"transitionEnd","waiting","waiting"];function zt(e,t){for(var n=0;n<e.length;n+=2){var r=e[n],i=e[n+1],a="on"+(i[0].toUpperCase()+i.slice(1));a={phasedRegistrationNames:{bubbled:a,captured:a+"Capture"},dependencies:[r],eventPriority:t},Pt.set(r,t),Ut.set(r,a),Nt[i]=a}}zt("blur blur cancel cancel click click close close contextmenu contextMenu copy copy cut cut auxclick auxClick dblclick doubleClick dragend dragEnd dragstart dragStart drop drop focus focus input input invalid invalid keydown keyDown keypress keyPress keyup keyUp mousedown mouseDown mouseup mouseUp paste paste pause pause play play pointercancel pointerCancel pointerdown pointerDown pointerup pointerUp ratechange rateChange reset reset seeked seeked submit submit touchcancel touchCancel touchend touchEnd touchstart touchStart volumechange volumeChange".split(" "),0),zt("drag drag dragenter dragEnter dragexit dragExit dragleave dragLeave dragover dragOver mousemove mouseMove mouseout mouseOut mouseover mouseOver pointermove pointerMove pointerout pointerOut pointerover pointerOver scroll scroll toggle toggle touchmove touchMove wheel wheel".split(" "),1),zt(Rt,2);for(var Vt="change selectionchange textInput compositionstart compositionend compositionupdate".split(" "),Wt=0;Wt<Vt.length;Wt++)Pt.set(Vt[Wt],0);var $t=a.unstable_UserBlockingPriority,Ht=a.unstable_runWithPriority,Yt=!0;function Kt(e,t){Qt(t,e,!1)}function Qt(e,t,n){var r=Pt.get(t);switch(void 0===r?2:r){case 0:r=function(e,t,n,r){N||F();var i=qt,a=N;N=!0;try{L(i,e,t,n,r)}finally{(N=a)||P()}}.bind(null,t,1,e);break;case 1:r=function(e,t,n,r){Ht($t,qt.bind(null,e,t,n,r))}.bind(null,t,1,e);break;default:r=qt.bind(null,t,1,e)}n?e.addEventListener(t,r,!0):e.addEventListener(t,r,!1)}function qt(e,t,n,r){if(Yt)if(0<gt.length&&-1<St.indexOf(e))e=It(null,e,t,n,r),gt.push(e);else{var i=Gt(e,t,n,r);if(null===i)Et(e,r);else if(-1<St.indexOf(e))e=It(i,e,t,n,r),gt.push(e);else if(!function(e,t,n,r,i){switch(t){case"focus":return kt=At(kt,e,t,n,r,i),!0;case"dragenter":return wt=At(wt,e,t,n,r,i),!0;case"mouseover":return _t=At(_t,e,t,n,r,i),!0;case"pointerover":var a=i.pointerId;return Ot.set(a,At(Ot.get(a)||null,e,t,n,r,i)),!0;case"gotpointercapture":return a=i.pointerId,jt.set(a,At(jt.get(a)||null,e,t,n,r,i)),!0}return!1}(i,e,t,n,r)){Et(e,r),e=ht(e,r,null,t);try{R(dt,e)}finally{ft(e)}}}}function Gt(e,t,n,r){if(null!==(n=En(n=st(r)))){var i=Ze(n);if(null===i)n=null;else{var a=i.tag;if(13===a){if(null!==(n=et(i)))return n;n=null}else if(3===a){if(i.stateNode.hydrate)return 3===i.tag?i.stateNode.containerInfo:null;n=null}else i!==n&&(n=null)}}e=ht(e,r,n,t);try{R(dt,e)}finally{ft(e)}return null}var Jt={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Xt=["Webkit","ms","Moz","O"];function Zt(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||Jt.hasOwnProperty(e)&&Jt[e]?(""+t).trim():t+"px"}function en(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),i=Zt(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}Object.keys(Jt).forEach(function(e){Xt.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Jt[t]=Jt[e]})});var tn=i({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function nn(e,t){if(t){if(tn[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(o(137,e,""));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(o(60));if(!("object"===typeof t.dangerouslySetInnerHTML&&"__html"in t.dangerouslySetInnerHTML))throw Error(o(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(o(62,""))}}function rn(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var an=Ce;function on(e,t){var n=Xe(e=9===e.nodeType||11===e.nodeType?e:e.ownerDocument);t=j[t];for(var r=0;r<t.length;r++)pt(t[r],e,n)}function un(){}function sn(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function cn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ln(e,t){var n,r=cn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=cn(r)}}function fn(){for(var e=window,t=sn();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=sn((e=t.contentWindow).document)}return t}function hn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var dn="$",pn="/$",yn="$?",vn="$!",bn=null,mn=null;function gn(e,t){switch(e){case"button":case"input":case"select":case"textarea":return!!t.autoFocus}return!1}function kn(e,t){return"textarea"===e||"option"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var wn="function"===typeof setTimeout?setTimeout:void 0,_n="function"===typeof clearTimeout?clearTimeout:void 0;function On(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break}return e}function jn(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if(n===dn||n===vn||n===yn){if(0===t)return e;t--}else n===pn&&t++}e=e.previousSibling}return null}var xn=Math.random().toString(36).slice(2),Sn="__reactInternalInstance$"+xn,Tn="__reactEventHandlers$"+xn,In="__reactContainere$"+xn;function En(e){var t=e[Sn];if(t)return t;for(var n=e.parentNode;n;){if(t=n[In]||n[Sn]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=jn(e);null!==e;){if(n=e[Sn])return n;e=jn(e)}return t}n=(e=n).parentNode}return null}function An(e){return!(e=e[Sn]||e[In])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function Bn(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(o(33))}function Dn(e){return e[Tn]||null}function Cn(e){do{e=e.return}while(e&&5!==e.tag);return e||null}function Ln(e,t){var n=e.stateNode;if(!n)return null;var r=d(n);if(!r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(o(231,t,typeof n));return n}function Fn(e,t,n){(t=Ln(e,n.dispatchConfig.phasedRegistrationNames[t]))&&(n._dispatchListeners=rt(n._dispatchListeners,t),n._dispatchInstances=rt(n._dispatchInstances,e))}function Mn(e){if(e&&e.dispatchConfig.phasedRegistrationNames){for(var t=e._targetInst,n=[];t;)n.push(t),t=Cn(t);for(t=n.length;0<t--;)Fn(n[t],"captured",e);for(t=0;t<n.length;t++)Fn(n[t],"bubbled",e)}}function Nn(e,t,n){e&&n&&n.dispatchConfig.registrationName&&(t=Ln(e,n.dispatchConfig.registrationName))&&(n._dispatchListeners=rt(n._dispatchListeners,t),n._dispatchInstances=rt(n._dispatchInstances,e))}function Un(e){e&&e.dispatchConfig.registrationName&&Nn(e._targetInst,null,e)}function Pn(e){it(e,Mn)}var Rn=null,zn=null,Vn=null;function Wn(){if(Vn)return Vn;var e,t,n=zn,r=n.length,i="value"in Rn?Rn.value:Rn.textContent,a=i.length;for(e=0;e<r&&n[e]===i[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===i[a-t];t++);return Vn=i.slice(e,1<t?1-t:void 0)}function $n(){return!0}function Hn(){return!1}function Yn(e,t,n,r){for(var i in this.dispatchConfig=e,this._targetInst=t,this.nativeEvent=n,e=this.constructor.Interface)e.hasOwnProperty(i)&&((t=e[i])?this[i]=t(n):"target"===i?this.target=r:this[i]=n[i]);return this.isDefaultPrevented=(null!=n.defaultPrevented?n.defaultPrevented:!1===n.returnValue)?$n:Hn,this.isPropagationStopped=Hn,this}function Kn(e,t,n,r){if(this.eventPool.length){var i=this.eventPool.pop();return this.call(i,e,t,n,r),i}return new this(e,t,n,r)}function Qn(e){if(!(e instanceof this))throw Error(o(279));e.destructor(),10>this.eventPool.length&&this.eventPool.push(e)}function qn(e){e.eventPool=[],e.getPooled=Kn,e.release=Qn}i(Yn.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=$n)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=$n)},persist:function(){this.isPersistent=$n},isPersistent:Hn,destructor:function(){var e,t=this.constructor.Interface;for(e in t)this[e]=null;this.nativeEvent=this._targetInst=this.dispatchConfig=null,this.isPropagationStopped=this.isDefaultPrevented=Hn,this._dispatchInstances=this._dispatchListeners=null}}),Yn.Interface={type:null,target:null,currentTarget:function(){return null},eventPhase:null,bubbles:null,cancelable:null,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:null,isTrusted:null},Yn.extend=function(e){function t(){}function n(){return r.apply(this,arguments)}var r=this;t.prototype=r.prototype;var a=new t;return i(a,n.prototype),n.prototype=a,n.prototype.constructor=n,n.Interface=i({},r.Interface,e),n.extend=r.extend,qn(n),n},qn(Yn);var Gn=Yn.extend({data:null}),Jn=Yn.extend({data:null}),Xn=[9,13,27,32],Zn=S&&"CompositionEvent"in window,er=null;S&&"documentMode"in document&&(er=document.documentMode);var tr=S&&"TextEvent"in window&&!er,nr=S&&(!Zn||er&&8<er&&11>=er),rr=String.fromCharCode(32),ir={beforeInput:{phasedRegistrationNames:{bubbled:"onBeforeInput",captured:"onBeforeInputCapture"},dependencies:["compositionend","keypress","textInput","paste"]},compositionEnd:{phasedRegistrationNames:{bubbled:"onCompositionEnd",captured:"onCompositionEndCapture"},dependencies:"blur compositionend keydown keypress keyup mousedown".split(" ")},compositionStart:{phasedRegistrationNames:{bubbled:"onCompositionStart",captured:"onCompositionStartCapture"},dependencies:"blur compositionstart keydown keypress keyup mousedown".split(" ")},compositionUpdate:{phasedRegistrationNames:{bubbled:"onCompositionUpdate",captured:"onCompositionUpdateCapture"},dependencies:"blur compositionupdate keydown keypress keyup mousedown".split(" ")}},ar=!1;function or(e,t){switch(e){case"keyup":return-1!==Xn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"blur":return!0;default:return!1}}function ur(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var sr=!1;var cr={eventTypes:ir,extractEvents:function(e,t,n,r){var i;if(Zn)e:{switch(e){case"compositionstart":var a=ir.compositionStart;break e;case"compositionend":a=ir.compositionEnd;break e;case"compositionupdate":a=ir.compositionUpdate;break e}a=void 0}else sr?or(e,n)&&(a=ir.compositionEnd):"keydown"===e&&229===n.keyCode&&(a=ir.compositionStart);return a?(nr&&"ko"!==n.locale&&(sr||a!==ir.compositionStart?a===ir.compositionEnd&&sr&&(i=Wn()):(zn="value"in(Rn=r)?Rn.value:Rn.textContent,sr=!0)),a=Gn.getPooled(a,t,n,r),i?a.data=i:null!==(i=ur(n))&&(a.data=i),Pn(a),i=a):i=null,(e=tr?function(e,t){switch(e){case"compositionend":return ur(t);case"keypress":return 32!==t.which?null:(ar=!0,rr);case"textInput":return(e=t.data)===rr&&ar?null:e;default:return null}}(e,n):function(e,t){if(sr)return"compositionend"===e||!Zn&&or(e,t)?(e=Wn(),Vn=zn=Rn=null,sr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return nr&&"ko"!==t.locale?null:t.data;default:return null}}(e,n))?((t=Jn.getPooled(ir.beforeInput,t,n,r)).data=e,Pn(t)):t=null,null===i?t:null===t?i:[i,t]}},lr={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function fr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!lr[e.type]:"textarea"===t}var hr={change:{phasedRegistrationNames:{bubbled:"onChange",captured:"onChangeCapture"},dependencies:"blur change click focus input keydown keyup selectionchange".split(" ")}};function dr(e,t,n){return(e=Yn.getPooled(hr.change,e,t,n)).type="change",B(n),Pn(e),e}var pr=null,yr=null;function vr(e){ut(e)}function br(e){if(ke(Bn(e)))return e}function mr(e,t){if("change"===e)return t}var gr=!1;function kr(){pr&&(pr.detachEvent("onpropertychange",wr),yr=pr=null)}function wr(e){if("value"===e.propertyName&&br(yr))if(e=dr(yr,e,st(e)),N)ut(e);else{N=!0;try{C(vr,e)}finally{N=!1,P()}}}function _r(e,t,n){"focus"===e?(kr(),yr=n,(pr=t).attachEvent("onpropertychange",wr)):"blur"===e&&kr()}function Or(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return br(yr)}function jr(e,t){if("click"===e)return br(t)}function xr(e,t){if("input"===e||"change"===e)return br(t)}S&&(gr=ct("input")&&(!document.documentMode||9<document.documentMode));var Sr={eventTypes:hr,_isInputEventSupported:gr,extractEvents:function(e,t,n,r){var i=t?Bn(t):window,a=i.nodeName&&i.nodeName.toLowerCase();if("select"===a||"input"===a&&"file"===i.type)var o=mr;else if(fr(i))if(gr)o=xr;else{o=Or;var u=_r}else(a=i.nodeName)&&"input"===a.toLowerCase()&&("checkbox"===i.type||"radio"===i.type)&&(o=jr);if(o&&(o=o(e,t)))return dr(o,n,r);u&&u(e,i,t),"blur"===e&&(e=i._wrapperState)&&e.controlled&&"number"===i.type&&Se(i,"number",i.value)}},Tr=Yn.extend({view:null,detail:null}),Ir={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Er(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Ir[e])&&!!t[e]}function Ar(){return Er}var Br=0,Dr=0,Cr=!1,Lr=!1,Fr=Tr.extend({screenX:null,screenY:null,clientX:null,clientY:null,pageX:null,pageY:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,getModifierState:Ar,button:null,buttons:null,relatedTarget:function(e){return e.relatedTarget||(e.fromElement===e.srcElement?e.toElement:e.fromElement)},movementX:function(e){if("movementX"in e)return e.movementX;var t=Br;return Br=e.screenX,Cr?"mousemove"===e.type?e.screenX-t:0:(Cr=!0,0)},movementY:function(e){if("movementY"in e)return e.movementY;var t=Dr;return Dr=e.screenY,Lr?"mousemove"===e.type?e.screenY-t:0:(Lr=!0,0)}}),Mr=Fr.extend({pointerId:null,width:null,height:null,pressure:null,tangentialPressure:null,tiltX:null,tiltY:null,twist:null,pointerType:null,isPrimary:null}),Nr={mouseEnter:{registrationName:"onMouseEnter",dependencies:["mouseout","mouseover"]},mouseLeave:{registrationName:"onMouseLeave",dependencies:["mouseout","mouseover"]},pointerEnter:{registrationName:"onPointerEnter",dependencies:["pointerout","pointerover"]},pointerLeave:{registrationName:"onPointerLeave",dependencies:["pointerout","pointerover"]}},Ur={eventTypes:Nr,extractEvents:function(e,t,n,r,i){var a="mouseover"===e||"pointerover"===e,o="mouseout"===e||"pointerout"===e;if(a&&0===(32&i)&&(n.relatedTarget||n.fromElement)||!o&&!a)return null;(a=r.window===r?r:(a=r.ownerDocument)?a.defaultView||a.parentWindow:window,o)?(o=t,null!==(t=(t=n.relatedTarget||n.toElement)?En(t):null)&&(t!==Ze(t)||5!==t.tag&&6!==t.tag)&&(t=null)):o=null;if(o===t)return null;if("mouseout"===e||"mouseover"===e)var u=Fr,s=Nr.mouseLeave,c=Nr.mouseEnter,l="mouse";else"pointerout"!==e&&"pointerover"!==e||(u=Mr,s=Nr.pointerLeave,c=Nr.pointerEnter,l="pointer");if(e=null==o?a:Bn(o),a=null==t?a:Bn(t),(s=u.getPooled(s,o,n,r)).type=l+"leave",s.target=e,s.relatedTarget=a,(n=u.getPooled(c,t,n,r)).type=l+"enter",n.target=a,n.relatedTarget=e,l=t,(r=o)&&l)e:{for(c=l,o=0,e=u=r;e;e=Cn(e))o++;for(e=0,t=c;t;t=Cn(t))e++;for(;0<o-e;)u=Cn(u),o--;for(;0<e-o;)c=Cn(c),e--;for(;o--;){if(u===c||u===c.alternate)break e;u=Cn(u),c=Cn(c)}u=null}else u=null;for(c=u,u=[];r&&r!==c&&(null===(o=r.alternate)||o!==c);)u.push(r),r=Cn(r);for(r=[];l&&l!==c&&(null===(o=l.alternate)||o!==c);)r.push(l),l=Cn(l);for(l=0;l<u.length;l++)Nn(u[l],"bubbled",s);for(l=r.length;0<l--;)Nn(r[l],"captured",n);return 0===(64&i)?[s]:[s,n]}};var Pr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},Rr=Object.prototype.hasOwnProperty;function zr(e,t){if(Pr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++)if(!Rr.call(t,n[r])||!Pr(e[n[r]],t[n[r]]))return!1;return!0}var Vr=S&&"documentMode"in document&&11>=document.documentMode,Wr={select:{phasedRegistrationNames:{bubbled:"onSelect",captured:"onSelectCapture"},dependencies:"blur contextmenu dragend focus keydown keyup mousedown mouseup selectionchange".split(" ")}},$r=null,Hr=null,Yr=null,Kr=!1;function Qr(e,t){var n=t.window===t?t.document:9===t.nodeType?t:t.ownerDocument;return Kr||null==$r||$r!==sn(n)?null:("selectionStart"in(n=$r)&&hn(n)?n={start:n.selectionStart,end:n.selectionEnd}:n={anchorNode:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset},Yr&&zr(Yr,n)?null:(Yr=n,(e=Yn.getPooled(Wr.select,Hr,e,t)).type="select",e.target=$r,Pn(e),e))}var qr={eventTypes:Wr,extractEvents:function(e,t,n,r,i,a){if(!(a=!(i=a||(r.window===r?r.document:9===r.nodeType?r:r.ownerDocument)))){e:{i=Xe(i),a=j.onSelect;for(var o=0;o<a.length;o++)if(!i.has(a[o])){i=!1;break e}i=!0}a=!i}if(a)return null;switch(i=t?Bn(t):window,e){case"focus":(fr(i)||"true"===i.contentEditable)&&($r=i,Hr=t,Yr=null);break;case"blur":Yr=Hr=$r=null;break;case"mousedown":Kr=!0;break;case"contextmenu":case"mouseup":case"dragend":return Kr=!1,Qr(n,r);case"selectionchange":if(Vr)break;case"keydown":case"keyup":return Qr(n,r)}return null}},Gr=Yn.extend({animationName:null,elapsedTime:null,pseudoElement:null}),Jr=Yn.extend({clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Xr=Tr.extend({relatedTarget:null});function Zr(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}var ei={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},ti={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ni=Tr.extend({key:function(e){if(e.key){var t=ei[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Zr(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?ti[e.keyCode]||"Unidentified":""},location:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,repeat:null,locale:null,getModifierState:Ar,charCode:function(e){return"keypress"===e.type?Zr(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Zr(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),ri=Fr.extend({dataTransfer:null}),ii=Tr.extend({touches:null,targetTouches:null,changedTouches:null,altKey:null,metaKey:null,ctrlKey:null,shiftKey:null,getModifierState:Ar}),ai=Yn.extend({propertyName:null,elapsedTime:null,pseudoElement:null}),oi=Fr.extend({deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:null,deltaMode:null}),ui={eventTypes:Nt,extractEvents:function(e,t,n,r){var i=Ut.get(e);if(!i)return null;switch(e){case"keypress":if(0===Zr(n))return null;case"keydown":case"keyup":e=ni;break;case"blur":case"focus":e=Xr;break;case"click":if(2===n.button)return null;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":e=Fr;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":e=ri;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":e=ii;break;case Ye:case Ke:case Qe:e=Gr;break;case qe:e=ai;break;case"scroll":e=Tr;break;case"wheel":e=oi;break;case"copy":case"cut":case"paste":e=Jr;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":e=Mr;break;default:e=Yn}return Pn(t=e.getPooled(i,t,n,r)),t}};if(b)throw Error(o(101));b=Array.prototype.slice.call("ResponderEventPlugin SimpleEventPlugin EnterLeaveEventPlugin ChangeEventPlugin SelectEventPlugin BeforeInputEventPlugin".split(" ")),g(),d=Dn,p=An,y=Bn,x({SimpleEventPlugin:ui,EnterLeaveEventPlugin:Ur,ChangeEventPlugin:Sr,SelectEventPlugin:qr,BeforeInputEventPlugin:cr});var si=[],ci=-1;function li(e){0>ci||(e.current=si[ci],si[ci]=null,ci--)}function fi(e,t){si[++ci]=e.current,e.current=t}var hi={},di={current:hi},pi={current:!1},yi=hi;function vi(e,t){var n=e.type.contextTypes;if(!n)return hi;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i,a={};for(i in n)a[i]=t[i];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function bi(e){return null!==(e=e.childContextTypes)&&void 0!==e}function mi(){li(pi),li(di)}function gi(e,t,n){if(di.current!==hi)throw Error(o(168));fi(di,t),fi(pi,n)}function ki(e,t,n){var r=e.stateNode;if(e=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in e))throw Error(o(108,ye(t)||"Unknown",a));return i({},n,{},r)}function wi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||hi,yi=di.current,fi(di,e),fi(pi,pi.current),!0}function _i(e,t,n){var r=e.stateNode;if(!r)throw Error(o(169));n?(e=ki(e,t,yi),r.__reactInternalMemoizedMergedChildContext=e,li(pi),li(di),fi(di,e)):li(pi),fi(pi,n)}var Oi=a.unstable_runWithPriority,ji=a.unstable_scheduleCallback,xi=a.unstable_cancelCallback,Si=a.unstable_requestPaint,Ti=a.unstable_now,Ii=a.unstable_getCurrentPriorityLevel,Ei=a.unstable_ImmediatePriority,Ai=a.unstable_UserBlockingPriority,Bi=a.unstable_NormalPriority,Di=a.unstable_LowPriority,Ci=a.unstable_IdlePriority,Li={},Fi=a.unstable_shouldYield,Mi=void 0!==Si?Si:function(){},Ni=null,Ui=null,Pi=!1,Ri=Ti(),zi=1e4>Ri?Ti:function(){return Ti()-Ri};function Vi(){switch(Ii()){case Ei:return 99;case Ai:return 98;case Bi:return 97;case Di:return 96;case Ci:return 95;default:throw Error(o(332))}}function Wi(e){switch(e){case 99:return Ei;case 98:return Ai;case 97:return Bi;case 96:return Di;case 95:return Ci;default:throw Error(o(332))}}function $i(e,t){return e=Wi(e),Oi(e,t)}function Hi(e,t,n){return e=Wi(e),ji(e,t,n)}function Yi(e){return null===Ni?(Ni=[e],Ui=ji(Ei,Qi)):Ni.push(e),Li}function Ki(){if(null!==Ui){var e=Ui;Ui=null,xi(e)}Qi()}function Qi(){if(!Pi&&null!==Ni){Pi=!0;var e=0;try{var t=Ni;$i(99,function(){for(;e<t.length;e++){var n=t[e];do{n=n(!0)}while(null!==n)}}),Ni=null}catch(n){throw null!==Ni&&(Ni=Ni.slice(e+1)),ji(Ei,Ki),n}finally{Pi=!1}}}function qi(e,t,n){return 1073741821-(1+((1073741821-e+t/10)/(n/=10)|0))*n}function Gi(e,t){if(e&&e.defaultProps)for(var n in t=i({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}var Ji={current:null},Xi=null,Zi=null,ea=null;function ta(){ea=Zi=Xi=null}function na(e){var t=Ji.current;li(Ji),e.type._context._currentValue=t}function ra(e,t){for(;null!==e;){var n=e.alternate;if(e.childExpirationTime<t)e.childExpirationTime=t,null!==n&&n.childExpirationTime<t&&(n.childExpirationTime=t);else{if(!(null!==n&&n.childExpirationTime<t))break;n.childExpirationTime=t}e=e.return}}function ia(e,t){Xi=e,ea=Zi=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(e.expirationTime>=t&&(Do=!0),e.firstContext=null)}function aa(e,t){if(ea!==e&&!1!==t&&0!==t)if("number"===typeof t&&1073741823!==t||(ea=e,t=1073741823),t={context:e,observedBits:t,next:null},null===Zi){if(null===Xi)throw Error(o(308));Zi=t,Xi.dependencies={expirationTime:0,firstContext:t,responders:null}}else Zi=Zi.next=t;return e._currentValue}var oa=!1;function ua(e){e.updateQueue={baseState:e.memoizedState,baseQueue:null,shared:{pending:null},effects:null}}function sa(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,baseQueue:e.baseQueue,shared:e.shared,effects:e.effects})}function ca(e,t){return(e={expirationTime:e,suspenseConfig:t,tag:0,payload:null,callback:null,next:null}).next=e}function la(e,t){if(null!==(e=e.updateQueue)){var n=(e=e.shared).pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}}function fa(e,t){var n=e.alternate;null!==n&&sa(n,e),null===(n=(e=e.updateQueue).baseQueue)?(e.baseQueue=t.next=t,t.next=t):(t.next=n.next,n.next=t)}function ha(e,t,n,r){var a=e.updateQueue;oa=!1;var o=a.baseQueue,u=a.shared.pending;if(null!==u){if(null!==o){var s=o.next;o.next=u.next,u.next=s}o=u,a.shared.pending=null,null!==(s=e.alternate)&&(null!==(s=s.updateQueue)&&(s.baseQueue=u))}if(null!==o){s=o.next;var c=a.baseState,l=0,f=null,h=null,d=null;if(null!==s)for(var p=s;;){if((u=p.expirationTime)<r){var y={expirationTime:p.expirationTime,suspenseConfig:p.suspenseConfig,tag:p.tag,payload:p.payload,callback:p.callback,next:null};null===d?(h=d=y,f=c):d=d.next=y,u>l&&(l=u)}else{null!==d&&(d=d.next={expirationTime:1073741823,suspenseConfig:p.suspenseConfig,tag:p.tag,payload:p.payload,callback:p.callback,next:null}),ys(u,p.suspenseConfig);e:{var v=e,b=p;switch(u=t,y=n,b.tag){case 1:if("function"===typeof(v=b.payload)){c=v.call(y,c,u);break e}c=v;break e;case 3:v.effectTag=-4097&v.effectTag|64;case 0:if(null===(u="function"===typeof(v=b.payload)?v.call(y,c,u):v)||void 0===u)break e;c=i({},c,u);break e;case 2:oa=!0}}null!==p.callback&&(e.effectTag|=32,null===(u=a.effects)?a.effects=[p]:u.push(p))}if(null===(p=p.next)||p===s){if(null===(u=a.shared.pending))break;p=o.next=u.next,u.next=s,a.baseQueue=o=u,a.shared.pending=null}}null===d?f=c:d.next=h,a.baseState=f,a.baseQueue=d,vs(l),e.expirationTime=l,e.memoizedState=c}}function da(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(null!==i){if(r.callback=null,r=i,i=n,"function"!==typeof r)throw Error(o(191,r));r.call(i)}}}var pa=q.ReactCurrentBatchConfig,ya=(new r.Component).refs;function va(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:i({},t,n),e.memoizedState=n,0===e.expirationTime&&(e.updateQueue.baseState=n)}var ba={isMounted:function(e){return!!(e=e._reactInternalFiber)&&Ze(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternalFiber;var r=rs(),i=pa.suspense;(i=ca(r=is(r,e,i),i)).payload=t,void 0!==n&&null!==n&&(i.callback=n),la(e,i),as(e,r)},enqueueReplaceState:function(e,t,n){e=e._reactInternalFiber;var r=rs(),i=pa.suspense;(i=ca(r=is(r,e,i),i)).tag=1,i.payload=t,void 0!==n&&null!==n&&(i.callback=n),la(e,i),as(e,r)},enqueueForceUpdate:function(e,t){e=e._reactInternalFiber;var n=rs(),r=pa.suspense;(r=ca(n=is(n,e,r),r)).tag=2,void 0!==t&&null!==t&&(r.callback=t),la(e,r),as(e,n)}};function ma(e,t,n,r,i,a,o){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,o):!t.prototype||!t.prototype.isPureReactComponent||(!zr(n,r)||!zr(i,a))}function ga(e,t,n){var r=!1,i=hi,a=t.contextType;return"object"===typeof a&&null!==a?a=aa(a):(i=bi(t)?yi:di.current,a=(r=null!==(r=t.contextTypes)&&void 0!==r)?vi(e,i):hi),t=new t(n,a),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ba,e.stateNode=t,t._reactInternalFiber=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=a),t}function ka(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ba.enqueueReplaceState(t,t.state,null)}function wa(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs=ya,ua(e);var a=t.contextType;"object"===typeof a&&null!==a?i.context=aa(a):(a=bi(t)?yi:di.current,i.context=vi(e,a)),ha(e,n,i,r),i.state=e.memoizedState,"function"===typeof(a=t.getDerivedStateFromProps)&&(va(e,t,a,n),i.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof i.getSnapshotBeforeUpdate||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||(t=i.state,"function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount(),t!==i.state&&ba.enqueueReplaceState(i,i.state,null),ha(e,n,i,r),i.state=e.memoizedState),"function"===typeof i.componentDidMount&&(e.effectTag|=4)}var _a=Array.isArray;function Oa(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(o(309));var r=n.stateNode}if(!r)throw Error(o(147,e));var i=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===i?t.ref:((t=function(e){var t=r.refs;t===ya&&(t=r.refs={}),null===e?delete t[i]:t[i]=e})._stringRef=i,t)}if("string"!==typeof e)throw Error(o(284));if(!n._owner)throw Error(o(290,e))}return e}function ja(e,t){if("textarea"!==e.type)throw Error(o(31,"[object Object]"===Object.prototype.toString.call(t)?"object with keys {"+Object.keys(t).join(", ")+"}":t,""))}function xa(e){function t(t,n){if(e){var r=t.lastEffect;null!==r?(r.nextEffect=n,t.lastEffect=n):t.firstEffect=t.lastEffect=n,n.nextEffect=null,n.effectTag=8}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function i(e,t){return(e=Ls(e,t)).index=0,e.sibling=null,e}function a(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.effectTag=2,n):r:(t.effectTag=2,n):n}function u(t){return e&&null===t.alternate&&(t.effectTag=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Ns(n,e.mode,r)).return=e,t):((t=i(t,n)).return=e,t)}function c(e,t,n,r){return null!==t&&t.elementType===n.type?((r=i(t,n.props)).ref=Oa(e,t,n),r.return=e,r):((r=Fs(n.type,n.key,n.props,null,e.mode,r)).ref=Oa(e,t,n),r.return=e,r)}function l(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Us(n,e.mode,r)).return=e,t):((t=i(t,n.children||[])).return=e,t)}function f(e,t,n,r,a){return null===t||7!==t.tag?((t=Ms(n,e.mode,r,a)).return=e,t):((t=i(t,n)).return=e,t)}function h(e,t,n){if("string"===typeof t||"number"===typeof t)return(t=Ns(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case Z:return(n=Fs(t.type,t.key,t.props,null,e.mode,n)).ref=Oa(e,null,t),n.return=e,n;case ee:return(t=Us(t,e.mode,n)).return=e,t}if(_a(t)||pe(t))return(t=Ms(t,e.mode,n,null)).return=e,t;ja(e,t)}return null}function d(e,t,n,r){var i=null!==t?t.key:null;if("string"===typeof n||"number"===typeof n)return null!==i?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case Z:return n.key===i?n.type===te?f(e,t,n.props.children,r,i):c(e,t,n,r):null;case ee:return n.key===i?l(e,t,n,r):null}if(_a(n)||pe(n))return null!==i?null:f(e,t,n,r,null);ja(e,n)}return null}function p(e,t,n,r,i){if("string"===typeof r||"number"===typeof r)return s(t,e=e.get(n)||null,""+r,i);if("object"===typeof r&&null!==r){switch(r.$$typeof){case Z:return e=e.get(null===r.key?n:r.key)||null,r.type===te?f(t,e,r.props.children,i,r.key):c(t,e,r,i);case ee:return l(t,e=e.get(null===r.key?n:r.key)||null,r,i)}if(_a(r)||pe(r))return f(t,e=e.get(n)||null,r,i,null);ja(t,r)}return null}function y(i,o,u,s){for(var c=null,l=null,f=o,y=o=0,v=null;null!==f&&y<u.length;y++){f.index>y?(v=f,f=null):v=f.sibling;var b=d(i,f,u[y],s);if(null===b){null===f&&(f=v);break}e&&f&&null===b.alternate&&t(i,f),o=a(b,o,y),null===l?c=b:l.sibling=b,l=b,f=v}if(y===u.length)return n(i,f),c;if(null===f){for(;y<u.length;y++)null!==(f=h(i,u[y],s))&&(o=a(f,o,y),null===l?c=f:l.sibling=f,l=f);return c}for(f=r(i,f);y<u.length;y++)null!==(v=p(f,i,y,u[y],s))&&(e&&null!==v.alternate&&f.delete(null===v.key?y:v.key),o=a(v,o,y),null===l?c=v:l.sibling=v,l=v);return e&&f.forEach(function(e){return t(i,e)}),c}function v(i,u,s,c){var l=pe(s);if("function"!==typeof l)throw Error(o(150));if(null==(s=l.call(s)))throw Error(o(151));for(var f=l=null,y=u,v=u=0,b=null,m=s.next();null!==y&&!m.done;v++,m=s.next()){y.index>v?(b=y,y=null):b=y.sibling;var g=d(i,y,m.value,c);if(null===g){null===y&&(y=b);break}e&&y&&null===g.alternate&&t(i,y),u=a(g,u,v),null===f?l=g:f.sibling=g,f=g,y=b}if(m.done)return n(i,y),l;if(null===y){for(;!m.done;v++,m=s.next())null!==(m=h(i,m.value,c))&&(u=a(m,u,v),null===f?l=m:f.sibling=m,f=m);return l}for(y=r(i,y);!m.done;v++,m=s.next())null!==(m=p(y,i,v,m.value,c))&&(e&&null!==m.alternate&&y.delete(null===m.key?v:m.key),u=a(m,u,v),null===f?l=m:f.sibling=m,f=m);return e&&y.forEach(function(e){return t(i,e)}),l}return function(e,r,a,s){var c="object"===typeof a&&null!==a&&a.type===te&&null===a.key;c&&(a=a.props.children);var l="object"===typeof a&&null!==a;if(l)switch(a.$$typeof){case Z:e:{for(l=a.key,c=r;null!==c;){if(c.key===l){switch(c.tag){case 7:if(a.type===te){n(e,c.sibling),(r=i(c,a.props.children)).return=e,e=r;break e}break;default:if(c.elementType===a.type){n(e,c.sibling),(r=i(c,a.props)).ref=Oa(e,c,a),r.return=e,e=r;break e}}n(e,c);break}t(e,c),c=c.sibling}a.type===te?((r=Ms(a.props.children,e.mode,s,a.key)).return=e,e=r):((s=Fs(a.type,a.key,a.props,null,e.mode,s)).ref=Oa(e,r,a),s.return=e,e=s)}return u(e);case ee:e:{for(c=a.key;null!==r;){if(r.key===c){if(4===r.tag&&r.stateNode.containerInfo===a.containerInfo&&r.stateNode.implementation===a.implementation){n(e,r.sibling),(r=i(r,a.children||[])).return=e,e=r;break e}n(e,r);break}t(e,r),r=r.sibling}(r=Us(a,e.mode,s)).return=e,e=r}return u(e)}if("string"===typeof a||"number"===typeof a)return a=""+a,null!==r&&6===r.tag?(n(e,r.sibling),(r=i(r,a)).return=e,e=r):(n(e,r),(r=Ns(a,e.mode,s)).return=e,e=r),u(e);if(_a(a))return y(e,r,a,s);if(pe(a))return v(e,r,a,s);if(l&&ja(e,a),"undefined"===typeof a&&!c)switch(e.tag){case 1:case 0:throw e=e.type,Error(o(152,e.displayName||e.name||"Component"))}return n(e,r)}}var Sa=xa(!0),Ta=xa(!1),Ia={},Ea={current:Ia},Aa={current:Ia},Ba={current:Ia};function Da(e){if(e===Ia)throw Error(o(174));return e}function Ca(e,t){switch(fi(Ba,t),fi(Aa,e),fi(Ea,Ia),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Me(null,"");break;default:t=Me(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}li(Ea),fi(Ea,t)}function La(){li(Ea),li(Aa),li(Ba)}function Fa(e){Da(Ba.current);var t=Da(Ea.current),n=Me(t,e.type);t!==n&&(fi(Aa,e),fi(Ea,n))}function Ma(e){Aa.current===e&&(li(Ea),li(Aa))}var Na={current:0};function Ua(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||n.data===yn||n.data===vn))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(64&t.effectTag))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Pa(e,t){return{responder:e,props:t}}var Ra=q.ReactCurrentDispatcher,za=q.ReactCurrentBatchConfig,Va=0,Wa=null,$a=null,Ha=null,Ya=!1;function Ka(){throw Error(o(321))}function Qa(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Pr(e[n],t[n]))return!1;return!0}function qa(e,t,n,r,i,a){if(Va=a,Wa=t,t.memoizedState=null,t.updateQueue=null,t.expirationTime=0,Ra.current=null===e||null===e.memoizedState?go:ko,e=n(r,i),t.expirationTime===Va){a=0;do{if(t.expirationTime=0,!(25>a))throw Error(o(301));a+=1,Ha=$a=null,t.updateQueue=null,Ra.current=wo,e=n(r,i)}while(t.expirationTime===Va)}if(Ra.current=mo,t=null!==$a&&null!==$a.next,Va=0,Ha=$a=Wa=null,Ya=!1,t)throw Error(o(300));return e}function Ga(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===Ha?Wa.memoizedState=Ha=e:Ha=Ha.next=e,Ha}function Ja(){if(null===$a){var e=Wa.alternate;e=null!==e?e.memoizedState:null}else e=$a.next;var t=null===Ha?Wa.memoizedState:Ha.next;if(null!==t)Ha=t,$a=e;else{if(null===e)throw Error(o(310));e={memoizedState:($a=e).memoizedState,baseState:$a.baseState,baseQueue:$a.baseQueue,queue:$a.queue,next:null},null===Ha?Wa.memoizedState=Ha=e:Ha=Ha.next=e}return Ha}function Xa(e,t){return"function"===typeof t?t(e):t}function Za(e){var t=Ja(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=$a,i=r.baseQueue,a=n.pending;if(null!==a){if(null!==i){var u=i.next;i.next=a.next,a.next=u}r.baseQueue=i=a,n.pending=null}if(null!==i){i=i.next,r=r.baseState;var s=u=a=null,c=i;do{var l=c.expirationTime;if(l<Va){var f={expirationTime:c.expirationTime,suspenseConfig:c.suspenseConfig,action:c.action,eagerReducer:c.eagerReducer,eagerState:c.eagerState,next:null};null===s?(u=s=f,a=r):s=s.next=f,l>Wa.expirationTime&&(Wa.expirationTime=l,vs(l))}else null!==s&&(s=s.next={expirationTime:1073741823,suspenseConfig:c.suspenseConfig,action:c.action,eagerReducer:c.eagerReducer,eagerState:c.eagerState,next:null}),ys(l,c.suspenseConfig),r=c.eagerReducer===e?c.eagerState:e(r,c.action);c=c.next}while(null!==c&&c!==i);null===s?a=r:s.next=u,Pr(r,t.memoizedState)||(Do=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=s,n.lastRenderedState=r}return[t.memoizedState,n.dispatch]}function eo(e){var t=Ja(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,a=t.memoizedState;if(null!==i){n.pending=null;var u=i=i.next;do{a=e(a,u.action),u=u.next}while(u!==i);Pr(a,t.memoizedState)||(Do=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),n.lastRenderedState=a}return[a,r]}function to(e){var t=Ga();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e=(e=t.queue={pending:null,dispatch:null,lastRenderedReducer:Xa,lastRenderedState:e}).dispatch=bo.bind(null,Wa,e),[t.memoizedState,e]}function no(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=Wa.updateQueue)?(t={lastEffect:null},Wa.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function ro(){return Ja().memoizedState}function io(e,t,n,r){var i=Ga();Wa.effectTag|=e,i.memoizedState=no(1|t,n,void 0,void 0===r?null:r)}function ao(e,t,n,r){var i=Ja();r=void 0===r?null:r;var a=void 0;if(null!==$a){var o=$a.memoizedState;if(a=o.destroy,null!==r&&Qa(r,o.deps))return void no(t,n,a,r)}Wa.effectTag|=e,i.memoizedState=no(1|t,n,a,r)}function oo(e,t){return io(516,4,e,t)}function uo(e,t){return ao(516,4,e,t)}function so(e,t){return ao(4,2,e,t)}function co(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function lo(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,ao(4,2,co.bind(null,t,e),n)}function fo(){}function ho(e,t){return Ga().memoizedState=[e,void 0===t?null:t],e}function po(e,t){var n=Ja();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&Qa(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function yo(e,t){var n=Ja();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&Qa(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function vo(e,t,n){var r=Vi();$i(98>r?98:r,function(){e(!0)}),$i(97<r?97:r,function(){var r=za.suspense;za.suspense=void 0===t?null:t;try{e(!1),n()}finally{za.suspense=r}})}function bo(e,t,n){var r=rs(),i=pa.suspense;i={expirationTime:r=is(r,e,i),suspenseConfig:i,action:n,eagerReducer:null,eagerState:null,next:null};var a=t.pending;if(null===a?i.next=i:(i.next=a.next,a.next=i),t.pending=i,a=e.alternate,e===Wa||null!==a&&a===Wa)Ya=!0,i.expirationTime=Va,Wa.expirationTime=Va;else{if(0===e.expirationTime&&(null===a||0===a.expirationTime)&&null!==(a=t.lastRenderedReducer))try{var o=t.lastRenderedState,u=a(o,n);if(i.eagerReducer=a,i.eagerState=u,Pr(u,o))return}catch(s){}as(e,r)}}var mo={readContext:aa,useCallback:Ka,useContext:Ka,useEffect:Ka,useImperativeHandle:Ka,useLayoutEffect:Ka,useMemo:Ka,useReducer:Ka,useRef:Ka,useState:Ka,useDebugValue:Ka,useResponder:Ka,useDeferredValue:Ka,useTransition:Ka},go={readContext:aa,useCallback:ho,useContext:aa,useEffect:oo,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,io(4,2,co.bind(null,t,e),n)},useLayoutEffect:function(e,t){return io(4,2,e,t)},useMemo:function(e,t){var n=Ga();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ga();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e=(e=r.queue={pending:null,dispatch:null,lastRenderedReducer:e,lastRenderedState:t}).dispatch=bo.bind(null,Wa,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Ga().memoizedState=e},useState:to,useDebugValue:fo,useResponder:Pa,useDeferredValue:function(e,t){var n=to(e),r=n[0],i=n[1];return oo(function(){var n=za.suspense;za.suspense=void 0===t?null:t;try{i(e)}finally{za.suspense=n}},[e,t]),r},useTransition:function(e){var t=to(!1),n=t[0];return t=t[1],[ho(vo.bind(null,t,e),[t,e]),n]}},ko={readContext:aa,useCallback:po,useContext:aa,useEffect:uo,useImperativeHandle:lo,useLayoutEffect:so,useMemo:yo,useReducer:Za,useRef:ro,useState:function(){return Za(Xa)},useDebugValue:fo,useResponder:Pa,useDeferredValue:function(e,t){var n=Za(Xa),r=n[0],i=n[1];return uo(function(){var n=za.suspense;za.suspense=void 0===t?null:t;try{i(e)}finally{za.suspense=n}},[e,t]),r},useTransition:function(e){var t=Za(Xa),n=t[0];return t=t[1],[po(vo.bind(null,t,e),[t,e]),n]}},wo={readContext:aa,useCallback:po,useContext:aa,useEffect:uo,useImperativeHandle:lo,useLayoutEffect:so,useMemo:yo,useReducer:eo,useRef:ro,useState:function(){return eo(Xa)},useDebugValue:fo,useResponder:Pa,useDeferredValue:function(e,t){var n=eo(Xa),r=n[0],i=n[1];return uo(function(){var n=za.suspense;za.suspense=void 0===t?null:t;try{i(e)}finally{za.suspense=n}},[e,t]),r},useTransition:function(e){var t=eo(Xa),n=t[0];return t=t[1],[po(vo.bind(null,t,e),[t,e]),n]}},_o=null,Oo=null,jo=!1;function xo(e,t){var n=Ds(5,null,null,0);n.elementType="DELETED",n.type="DELETED",n.stateNode=t,n.return=e,n.effectTag=8,null!==e.lastEffect?(e.lastEffect.nextEffect=n,e.lastEffect=n):e.firstEffect=e.lastEffect=n}function So(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,!0);case 13:default:return!1}}function To(e){if(jo){var t=Oo;if(t){var n=t;if(!So(e,t)){if(!(t=On(n.nextSibling))||!So(e,t))return e.effectTag=-1025&e.effectTag|2,jo=!1,void(_o=e);xo(_o,n)}_o=e,Oo=On(t.firstChild)}else e.effectTag=-1025&e.effectTag|2,jo=!1,_o=e}}function Io(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;_o=e}function Eo(e){if(e!==_o)return!1;if(!jo)return Io(e),jo=!0,!1;var t=e.type;if(5!==e.tag||"head"!==t&&"body"!==t&&!kn(t,e.memoizedProps))for(t=Oo;t;)xo(e,t),t=On(t.nextSibling);if(Io(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if(n===pn){if(0===t){Oo=On(e.nextSibling);break e}t--}else n!==dn&&n!==vn&&n!==yn||t++}e=e.nextSibling}Oo=null}}else Oo=_o?On(e.stateNode.nextSibling):null;return!0}function Ao(){Oo=_o=null,jo=!1}var Bo=q.ReactCurrentOwner,Do=!1;function Co(e,t,n,r){t.child=null===e?Ta(t,null,n,r):Sa(t,e.child,n,r)}function Lo(e,t,n,r,i){n=n.render;var a=t.ref;return ia(t,i),r=qa(e,t,n,r,a,i),null===e||Do?(t.effectTag|=1,Co(e,t,r,i),t.child):(t.updateQueue=e.updateQueue,t.effectTag&=-517,e.expirationTime<=i&&(e.expirationTime=0),Jo(e,t,i))}function Fo(e,t,n,r,i,a){if(null===e){var o=n.type;return"function"!==typeof o||Cs(o)||void 0!==o.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Fs(n.type,null,r,null,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,Mo(e,t,o,r,i,a))}return o=e.child,i<a&&(i=o.memoizedProps,(n=null!==(n=n.compare)?n:zr)(i,r)&&e.ref===t.ref)?Jo(e,t,a):(t.effectTag|=1,(e=Ls(o,r)).ref=t.ref,e.return=t,t.child=e)}function Mo(e,t,n,r,i,a){return null!==e&&zr(e.memoizedProps,r)&&e.ref===t.ref&&(Do=!1,i<a)?(t.expirationTime=e.expirationTime,Jo(e,t,a)):Uo(e,t,n,r,a)}function No(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.effectTag|=128)}function Uo(e,t,n,r,i){var a=bi(n)?yi:di.current;return a=vi(t,a),ia(t,i),n=qa(e,t,n,r,a,i),null===e||Do?(t.effectTag|=1,Co(e,t,n,i),t.child):(t.updateQueue=e.updateQueue,t.effectTag&=-517,e.expirationTime<=i&&(e.expirationTime=0),Jo(e,t,i))}function Po(e,t,n,r,i){if(bi(n)){var a=!0;wi(t)}else a=!1;if(ia(t,i),null===t.stateNode)null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),ga(t,n,r),wa(t,n,r,i),r=!0;else if(null===e){var o=t.stateNode,u=t.memoizedProps;o.props=u;var s=o.context,c=n.contextType;"object"===typeof c&&null!==c?c=aa(c):c=vi(t,c=bi(n)?yi:di.current);var l=n.getDerivedStateFromProps,f="function"===typeof l||"function"===typeof o.getSnapshotBeforeUpdate;f||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(u!==r||s!==c)&&ka(t,o,r,c),oa=!1;var h=t.memoizedState;o.state=h,ha(t,r,o,i),s=t.memoizedState,u!==r||h!==s||pi.current||oa?("function"===typeof l&&(va(t,n,l,r),s=t.memoizedState),(u=oa||ma(t,n,u,r,h,s,c))?(f||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||("function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"===typeof o.componentDidMount&&(t.effectTag|=4)):("function"===typeof o.componentDidMount&&(t.effectTag|=4),t.memoizedProps=r,t.memoizedState=s),o.props=r,o.state=s,o.context=c,r=u):("function"===typeof o.componentDidMount&&(t.effectTag|=4),r=!1)}else o=t.stateNode,sa(e,t),u=t.memoizedProps,o.props=t.type===t.elementType?u:Gi(t.type,u),s=o.context,"object"===typeof(c=n.contextType)&&null!==c?c=aa(c):c=vi(t,c=bi(n)?yi:di.current),(f="function"===typeof(l=n.getDerivedStateFromProps)||"function"===typeof o.getSnapshotBeforeUpdate)||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(u!==r||s!==c)&&ka(t,o,r,c),oa=!1,s=t.memoizedState,o.state=s,ha(t,r,o,i),h=t.memoizedState,u!==r||s!==h||pi.current||oa?("function"===typeof l&&(va(t,n,l,r),h=t.memoizedState),(l=oa||ma(t,n,u,r,s,h,c))?(f||"function"!==typeof o.UNSAFE_componentWillUpdate&&"function"!==typeof o.componentWillUpdate||("function"===typeof o.componentWillUpdate&&o.componentWillUpdate(r,h,c),"function"===typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(r,h,c)),"function"===typeof o.componentDidUpdate&&(t.effectTag|=4),"function"===typeof o.getSnapshotBeforeUpdate&&(t.effectTag|=256)):("function"!==typeof o.componentDidUpdate||u===e.memoizedProps&&s===e.memoizedState||(t.effectTag|=4),"function"!==typeof o.getSnapshotBeforeUpdate||u===e.memoizedProps&&s===e.memoizedState||(t.effectTag|=256),t.memoizedProps=r,t.memoizedState=h),o.props=r,o.state=h,o.context=c,r=l):("function"!==typeof o.componentDidUpdate||u===e.memoizedProps&&s===e.memoizedState||(t.effectTag|=4),"function"!==typeof o.getSnapshotBeforeUpdate||u===e.memoizedProps&&s===e.memoizedState||(t.effectTag|=256),r=!1);return Ro(e,t,n,r,a,i)}function Ro(e,t,n,r,i,a){No(e,t);var o=0!==(64&t.effectTag);if(!r&&!o)return i&&_i(t,n,!1),Jo(e,t,a);r=t.stateNode,Bo.current=t;var u=o&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.effectTag|=1,null!==e&&o?(t.child=Sa(t,e.child,null,a),t.child=Sa(t,null,u,a)):Co(e,t,u,a),t.memoizedState=r.state,i&&_i(t,n,!0),t.child}function zo(e){var t=e.stateNode;t.pendingContext?gi(0,t.pendingContext,t.pendingContext!==t.context):t.context&&gi(0,t.context,!1),Ca(e,t.containerInfo)}var Vo,Wo,$o,Ho,Yo={dehydrated:null,retryTime:0};function Ko(e,t,n){var r,i=t.mode,a=t.pendingProps,o=Na.current,u=!1;if((r=0!==(64&t.effectTag))||(r=0!==(2&o)&&(null===e||null!==e.memoizedState)),r?(u=!0,t.effectTag&=-65):null!==e&&null===e.memoizedState||void 0===a.fallback||!0===a.unstable_avoidThisFallback||(o|=1),fi(Na,1&o),null===e){if(void 0!==a.fallback&&To(t),u){if(u=a.fallback,(a=Ms(null,i,0,null)).return=t,0===(2&t.mode))for(e=null!==t.memoizedState?t.child.child:t.child,a.child=e;null!==e;)e.return=a,e=e.sibling;return(n=Ms(u,i,n,null)).return=t,a.sibling=n,t.memoizedState=Yo,t.child=a,n}return i=a.children,t.memoizedState=null,t.child=Ta(t,null,i,n)}if(null!==e.memoizedState){if(i=(e=e.child).sibling,u){if(a=a.fallback,(n=Ls(e,e.pendingProps)).return=t,0===(2&t.mode)&&(u=null!==t.memoizedState?t.child.child:t.child)!==e.child)for(n.child=u;null!==u;)u.return=n,u=u.sibling;return(i=Ls(i,a)).return=t,n.sibling=i,n.childExpirationTime=0,t.memoizedState=Yo,t.child=n,i}return n=Sa(t,e.child,a.children,n),t.memoizedState=null,t.child=n}if(e=e.child,u){if(u=a.fallback,(a=Ms(null,i,0,null)).return=t,a.child=e,null!==e&&(e.return=a),0===(2&t.mode))for(e=null!==t.memoizedState?t.child.child:t.child,a.child=e;null!==e;)e.return=a,e=e.sibling;return(n=Ms(u,i,n,null)).return=t,a.sibling=n,n.effectTag|=2,a.childExpirationTime=0,t.memoizedState=Yo,t.child=a,n}return t.memoizedState=null,t.child=Sa(t,e,a.children,n)}function Qo(e,t){e.expirationTime<t&&(e.expirationTime=t);var n=e.alternate;null!==n&&n.expirationTime<t&&(n.expirationTime=t),ra(e.return,t)}function qo(e,t,n,r,i,a){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailExpiration:0,tailMode:i,lastEffect:a}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailExpiration=0,o.tailMode=i,o.lastEffect=a)}function Go(e,t,n){var r=t.pendingProps,i=r.revealOrder,a=r.tail;if(Co(e,t,r.children,n),0!==(2&(r=Na.current)))r=1&r|2,t.effectTag|=64;else{if(null!==e&&0!==(64&e.effectTag))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Qo(e,n);else if(19===e.tag)Qo(e,n);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(fi(Na,r),0===(2&t.mode))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;null!==n;)null!==(e=n.alternate)&&null===Ua(e)&&(i=n),n=n.sibling;null===(n=i)?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),qo(t,!1,i,n,a,t.lastEffect);break;case"backwards":for(n=null,i=t.child,t.child=null;null!==i;){if(null!==(e=i.alternate)&&null===Ua(e)){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}qo(t,!0,n,null,a,t.lastEffect);break;case"together":qo(t,!1,null,null,void 0,t.lastEffect);break;default:t.memoizedState=null}return t.child}function Jo(e,t,n){null!==e&&(t.dependencies=e.dependencies);var r=t.expirationTime;if(0!==r&&vs(r),t.childExpirationTime<n)return null;if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=Ls(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Ls(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Xo(e,t){switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Zo(e,t,n){var r=t.pendingProps;switch(t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return null;case 1:return bi(t.type)&&mi(),null;case 3:return La(),li(pi),li(di),(n=t.stateNode).pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||!Eo(t)||(t.effectTag|=4),Wo(t),null;case 5:Ma(t),n=Da(Ba.current);var a=t.type;if(null!==e&&null!=t.stateNode)$o(e,t,a,r,n),e.ref!==t.ref&&(t.effectTag|=128);else{if(!r){if(null===t.stateNode)throw Error(o(166));return null}if(e=Da(Ea.current),Eo(t)){r=t.stateNode,a=t.type;var u=t.memoizedProps;switch(r[Sn]=t,r[Tn]=u,a){case"iframe":case"object":case"embed":Kt("load",r);break;case"video":case"audio":for(e=0;e<Ge.length;e++)Kt(Ge[e],r);break;case"source":Kt("error",r);break;case"img":case"image":case"link":Kt("error",r),Kt("load",r);break;case"form":Kt("reset",r),Kt("submit",r);break;case"details":Kt("toggle",r);break;case"input":_e(r,u),Kt("invalid",r),on(n,"onChange");break;case"select":r._wrapperState={wasMultiple:!!u.multiple},Kt("invalid",r),on(n,"onChange");break;case"textarea":Ae(r,u),Kt("invalid",r),on(n,"onChange")}for(var s in nn(a,u),e=null,u)if(u.hasOwnProperty(s)){var c=u[s];"children"===s?"string"===typeof c?r.textContent!==c&&(e=["children",c]):"number"===typeof c&&r.textContent!==""+c&&(e=["children",""+c]):O.hasOwnProperty(s)&&null!=c&&on(n,s)}switch(a){case"input":ge(r),xe(r,u,!0);break;case"textarea":ge(r),De(r);break;case"select":case"option":break;default:"function"===typeof u.onClick&&(r.onclick=un)}n=e,t.updateQueue=n,null!==n&&(t.effectTag|=4)}else{switch(s=9===n.nodeType?n:n.ownerDocument,e===an&&(e=Fe(a)),e===an?"script"===a?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=s.createElement(a,{is:r.is}):(e=s.createElement(a),"select"===a&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,a),e[Sn]=t,e[Tn]=r,Vo(e,t,!1,!1),t.stateNode=e,s=rn(a,r),a){case"iframe":case"object":case"embed":Kt("load",e),c=r;break;case"video":case"audio":for(c=0;c<Ge.length;c++)Kt(Ge[c],e);c=r;break;case"source":Kt("error",e),c=r;break;case"img":case"image":case"link":Kt("error",e),Kt("load",e),c=r;break;case"form":Kt("reset",e),Kt("submit",e),c=r;break;case"details":Kt("toggle",e),c=r;break;case"input":_e(e,r),c=we(e,r),Kt("invalid",e),on(n,"onChange");break;case"option":c=Te(e,r);break;case"select":e._wrapperState={wasMultiple:!!r.multiple},c=i({},r,{value:void 0}),Kt("invalid",e),on(n,"onChange");break;case"textarea":Ae(e,r),c=Ee(e,r),Kt("invalid",e),on(n,"onChange");break;default:c=r}nn(a,c);var l=c;for(u in l)if(l.hasOwnProperty(u)){var f=l[u];"style"===u?en(e,f):"dangerouslySetInnerHTML"===u?null!=(f=f?f.__html:void 0)&&Pe(e,f):"children"===u?"string"===typeof f?("textarea"!==a||""!==f)&&Re(e,f):"number"===typeof f&&Re(e,""+f):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(O.hasOwnProperty(u)?null!=f&&on(n,u):null!=f&&G(e,u,f,s))}switch(a){case"input":ge(e),xe(e,r,!1);break;case"textarea":ge(e),De(e);break;case"option":null!=r.value&&e.setAttribute("value",""+be(r.value));break;case"select":e.multiple=!!r.multiple,null!=(n=r.value)?Ie(e,!!r.multiple,n,!1):null!=r.defaultValue&&Ie(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof c.onClick&&(e.onclick=un)}gn(a,r)&&(t.effectTag|=4)}null!==t.ref&&(t.effectTag|=128)}return null;case 6:if(e&&null!=t.stateNode)Ho(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(o(166));n=Da(Ba.current),Da(Ea.current),Eo(t)?(n=t.stateNode,r=t.memoizedProps,n[Sn]=t,n.nodeValue!==r&&(t.effectTag|=4)):((n=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[Sn]=t,t.stateNode=n)}return null;case 13:return li(Na),r=t.memoizedState,0!==(64&t.effectTag)?(t.expirationTime=n,t):(n=null!==r,r=!1,null===e?void 0!==t.memoizedProps.fallback&&Eo(t):(r=null!==(a=e.memoizedState),n||null===a||null!==(a=e.child.sibling)&&(null!==(u=t.firstEffect)?(t.firstEffect=a,a.nextEffect=u):(t.firstEffect=t.lastEffect=a,a.nextEffect=null),a.effectTag=8)),n&&!r&&0!==(2&t.mode)&&(null===e&&!0!==t.memoizedProps.unstable_avoidThisFallback||0!==(1&Na.current)?Nu===Tu&&(Nu=Au):(Nu!==Tu&&Nu!==Au||(Nu=Bu),0!==Vu&&null!==Lu&&(zs(Lu,Mu),Vs(Lu,Vu)))),(n||r)&&(t.effectTag|=4),null);case 4:return La(),Wo(t),null;case 10:return na(t),null;case 17:return bi(t.type)&&mi(),null;case 19:if(li(Na),null===(r=t.memoizedState))return null;if(a=0!==(64&t.effectTag),null===(u=r.rendering)){if(a)Xo(r,!1);else if(Nu!==Tu||null!==e&&0!==(64&e.effectTag))for(u=t.child;null!==u;){if(null!==(e=Ua(u))){for(t.effectTag|=64,Xo(r,!1),null!==(a=e.updateQueue)&&(t.updateQueue=a,t.effectTag|=4),null===r.lastEffect&&(t.firstEffect=null),t.lastEffect=r.lastEffect,r=t.child;null!==r;)u=n,(a=r).effectTag&=2,a.nextEffect=null,a.firstEffect=null,a.lastEffect=null,null===(e=a.alternate)?(a.childExpirationTime=0,a.expirationTime=u,a.child=null,a.memoizedProps=null,a.memoizedState=null,a.updateQueue=null,a.dependencies=null):(a.childExpirationTime=e.childExpirationTime,a.expirationTime=e.expirationTime,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,u=e.dependencies,a.dependencies=null===u?null:{expirationTime:u.expirationTime,firstContext:u.firstContext,responders:u.responders}),r=r.sibling;return fi(Na,1&Na.current|2),t.child}u=u.sibling}}else{if(!a)if(null!==(e=Ua(u))){if(t.effectTag|=64,a=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.effectTag|=4),Xo(r,!0),null===r.tail&&"hidden"===r.tailMode&&!u.alternate)return null!==(t=t.lastEffect=r.lastEffect)&&(t.nextEffect=null),null}else 2*zi()-r.renderingStartTime>r.tailExpiration&&1<n&&(t.effectTag|=64,a=!0,Xo(r,!1),t.expirationTime=t.childExpirationTime=n-1);r.isBackwards?(u.sibling=t.child,t.child=u):(null!==(n=r.last)?n.sibling=u:t.child=u,r.last=u)}return null!==r.tail?(0===r.tailExpiration&&(r.tailExpiration=zi()+500),n=r.tail,r.rendering=n,r.tail=n.sibling,r.lastEffect=t.lastEffect,r.renderingStartTime=zi(),n.sibling=null,t=Na.current,fi(Na,a?1&t|2:1&t),n):null}throw Error(o(156,t.tag))}function eu(e){switch(e.tag){case 1:bi(e.type)&&mi();var t=e.effectTag;return 4096&t?(e.effectTag=-4097&t|64,e):null;case 3:if(La(),li(pi),li(di),0!==(64&(t=e.effectTag)))throw Error(o(285));return e.effectTag=-4097&t|64,e;case 5:return Ma(e),null;case 13:return li(Na),4096&(t=e.effectTag)?(e.effectTag=-4097&t|64,e):null;case 19:return li(Na),null;case 4:return La(),null;case 10:return na(e),null;default:return null}}function tu(e,t){return{value:e,source:t,stack:ve(t)}}Vo=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Wo=function(){},$o=function(e,t,n,r,a){var o=e.memoizedProps;if(o!==r){var u,s,c=t.stateNode;switch(Da(Ea.current),e=null,n){case"input":o=we(c,o),r=we(c,r),e=[];break;case"option":o=Te(c,o),r=Te(c,r),e=[];break;case"select":o=i({},o,{value:void 0}),r=i({},r,{value:void 0}),e=[];break;case"textarea":o=Ee(c,o),r=Ee(c,r),e=[];break;default:"function"!==typeof o.onClick&&"function"===typeof r.onClick&&(c.onclick=un)}for(u in nn(n,r),n=null,o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&null!=o[u])if("style"===u)for(s in c=o[u])c.hasOwnProperty(s)&&(n||(n={}),n[s]="");else"dangerouslySetInnerHTML"!==u&&"children"!==u&&"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&"autoFocus"!==u&&(O.hasOwnProperty(u)?e||(e=[]):(e=e||[]).push(u,null));for(u in r){var l=r[u];if(c=null!=o?o[u]:void 0,r.hasOwnProperty(u)&&l!==c&&(null!=l||null!=c))if("style"===u)if(c){for(s in c)!c.hasOwnProperty(s)||l&&l.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in l)l.hasOwnProperty(s)&&c[s]!==l[s]&&(n||(n={}),n[s]=l[s])}else n||(e||(e=[]),e.push(u,n)),n=l;else"dangerouslySetInnerHTML"===u?(l=l?l.__html:void 0,c=c?c.__html:void 0,null!=l&&c!==l&&(e=e||[]).push(u,l)):"children"===u?c===l||"string"!==typeof l&&"number"!==typeof l||(e=e||[]).push(u,""+l):"suppressContentEditableWarning"!==u&&"suppressHydrationWarning"!==u&&(O.hasOwnProperty(u)?(null!=l&&on(a,u),e||c===l||(e=[])):(e=e||[]).push(u,l))}n&&(e=e||[]).push("style",n),a=e,(t.updateQueue=a)&&(t.effectTag|=4)}},Ho=function(e,t,n,r){n!==r&&(t.effectTag|=4)};var nu="function"===typeof WeakSet?WeakSet:Set;function ru(e,t){var n=t.source,r=t.stack;null===r&&null!==n&&(r=ve(n)),null!==n&&ye(n.type),t=t.value,null!==e&&1===e.tag&&ye(e.type);try{console.error(t)}catch(i){setTimeout(function(){throw i})}}function iu(e){var t=e.ref;if(null!==t)if("function"===typeof t)try{t(null)}catch(n){Ts(e,n)}else t.current=null}function au(e,t){switch(t.tag){case 0:case 11:case 15:case 22:return;case 1:if(256&t.effectTag&&null!==e){var n=e.memoizedProps,r=e.memoizedState;t=(e=t.stateNode).getSnapshotBeforeUpdate(t.elementType===t.type?n:Gi(t.type,n),r),e.__reactInternalSnapshotBeforeUpdate=t}return;case 3:case 5:case 6:case 4:case 17:return}throw Error(o(163))}function ou(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.destroy;n.destroy=void 0,void 0!==r&&r()}n=n.next}while(n!==t)}}function uu(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function su(e,t,n){switch(n.tag){case 0:case 11:case 15:case 22:return void uu(3,n);case 1:if(e=n.stateNode,4&n.effectTag)if(null===t)e.componentDidMount();else{var r=n.elementType===n.type?t.memoizedProps:Gi(n.type,t.memoizedProps);e.componentDidUpdate(r,t.memoizedState,e.__reactInternalSnapshotBeforeUpdate)}return void(null!==(t=n.updateQueue)&&da(n,t,e));case 3:if(null!==(t=n.updateQueue)){if(e=null,null!==n.child)switch(n.child.tag){case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}da(n,t,e)}return;case 5:return e=n.stateNode,void(null===t&&4&n.effectTag&&gn(n.type,n.memoizedProps)&&e.focus());case 6:case 4:case 12:return;case 13:return void(null===n.memoizedState&&(n=n.alternate,null!==n&&(n=n.memoizedState,null!==n&&(n=n.dehydrated,null!==n&&Mt(n)))));case 19:case 17:case 20:case 21:return}throw Error(o(163))}function cu(e,t,n){switch("function"===typeof As&&As(t),t.tag){case 0:case 11:case 14:case 15:case 22:if(null!==(e=t.updateQueue)&&null!==(e=e.lastEffect)){var r=e.next;$i(97<n?97:n,function(){var e=r;do{var n=e.destroy;if(void 0!==n){var i=t;try{n()}catch(a){Ts(i,a)}}e=e.next}while(e!==r)})}break;case 1:iu(t),"function"===typeof(n=t.stateNode).componentWillUnmount&&function(e,t){try{t.props=e.memoizedProps,t.state=e.memoizedState,t.componentWillUnmount()}catch(n){Ts(e,n)}}(t,n);break;case 5:iu(t);break;case 4:du(e,t,n)}}function lu(e){var t=e.alternate;e.return=null,e.child=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.alternate=null,e.firstEffect=null,e.lastEffect=null,e.pendingProps=null,e.memoizedProps=null,e.stateNode=null,null!==t&&lu(t)}function fu(e){return 5===e.tag||3===e.tag||4===e.tag}function hu(e){e:{for(var t=e.return;null!==t;){if(fu(t)){var n=t;break e}t=t.return}throw Error(o(160))}switch(t=n.stateNode,n.tag){case 5:var r=!1;break;case 3:case 4:t=t.containerInfo,r=!0;break;default:throw Error(o(161))}16&n.effectTag&&(Re(t,""),n.effectTag&=-17);e:t:for(n=e;;){for(;null===n.sibling;){if(null===n.return||fu(n.return)){n=null;break e}n=n.return}for(n.sibling.return=n.return,n=n.sibling;5!==n.tag&&6!==n.tag&&18!==n.tag;){if(2&n.effectTag)continue t;if(null===n.child||4===n.tag)continue t;n.child.return=n,n=n.child}if(!(2&n.effectTag)){n=n.stateNode;break e}}r?function e(t,n,r){var i=t.tag,a=5===i||6===i;if(a)t=a?t.stateNode:t.stateNode.instance,n?8===r.nodeType?r.parentNode.insertBefore(t,n):r.insertBefore(t,n):(8===r.nodeType?(n=r.parentNode,n.insertBefore(t,r)):(n=r,n.appendChild(t)),r=r._reactRootContainer,null!==r&&void 0!==r||null!==n.onclick||(n.onclick=un));else if(4!==i&&(t=t.child,null!==t))for(e(t,n,r),t=t.sibling;null!==t;)e(t,n,r),t=t.sibling}(e,n,t):function e(t,n,r){var i=t.tag,a=5===i||6===i;if(a)t=a?t.stateNode:t.stateNode.instance,n?r.insertBefore(t,n):r.appendChild(t);else if(4!==i&&(t=t.child,null!==t))for(e(t,n,r),t=t.sibling;null!==t;)e(t,n,r),t=t.sibling}(e,n,t)}function du(e,t,n){for(var r,i,a=t,u=!1;;){if(!u){u=a.return;e:for(;;){if(null===u)throw Error(o(160));switch(r=u.stateNode,u.tag){case 5:i=!1;break e;case 3:case 4:r=r.containerInfo,i=!0;break e}u=u.return}u=!0}if(5===a.tag||6===a.tag){e:for(var s=e,c=a,l=n,f=c;;)if(cu(s,f,l),null!==f.child&&4!==f.tag)f.child.return=f,f=f.child;else{if(f===c)break e;for(;null===f.sibling;){if(null===f.return||f.return===c)break e;f=f.return}f.sibling.return=f.return,f=f.sibling}i?(s=r,c=a.stateNode,8===s.nodeType?s.parentNode.removeChild(c):s.removeChild(c)):r.removeChild(a.stateNode)}else if(4===a.tag){if(null!==a.child){r=a.stateNode.containerInfo,i=!0,a.child.return=a,a=a.child;continue}}else if(cu(e,a,n),null!==a.child){a.child.return=a,a=a.child;continue}if(a===t)break;for(;null===a.sibling;){if(null===a.return||a.return===t)return;4===(a=a.return).tag&&(u=!1)}a.sibling.return=a.return,a=a.sibling}}function pu(e,t){switch(t.tag){case 0:case 11:case 14:case 15:case 22:return void ou(3,t);case 1:return;case 5:var n=t.stateNode;if(null!=n){var r=t.memoizedProps,i=null!==e?e.memoizedProps:r;e=t.type;var a=t.updateQueue;if(t.updateQueue=null,null!==a){for(n[Tn]=r,"input"===e&&"radio"===r.type&&null!=r.name&&Oe(n,r),rn(e,i),t=rn(e,r),i=0;i<a.length;i+=2){var u=a[i],s=a[i+1];"style"===u?en(n,s):"dangerouslySetInnerHTML"===u?Pe(n,s):"children"===u?Re(n,s):G(n,u,s,t)}switch(e){case"input":je(n,r);break;case"textarea":Be(n,r);break;case"select":t=n._wrapperState.wasMultiple,n._wrapperState.wasMultiple=!!r.multiple,null!=(e=r.value)?Ie(n,!!r.multiple,e,!1):t!==!!r.multiple&&(null!=r.defaultValue?Ie(n,!!r.multiple,r.defaultValue,!0):Ie(n,!!r.multiple,r.multiple?[]:"",!1))}}}return;case 6:if(null===t.stateNode)throw Error(o(162));return void(t.stateNode.nodeValue=t.memoizedProps);case 3:return void((t=t.stateNode).hydrate&&(t.hydrate=!1,Mt(t.containerInfo)));case 12:return;case 13:if(n=t,null===t.memoizedState?r=!1:(r=!0,n=t.child,$u=zi()),null!==n)e:for(e=n;;){if(5===e.tag)a=e.stateNode,r?"function"===typeof(a=a.style).setProperty?a.setProperty("display","none","important"):a.display="none":(a=e.stateNode,i=void 0!==(i=e.memoizedProps.style)&&null!==i&&i.hasOwnProperty("display")?i.display:null,a.style.display=Zt("display",i));else if(6===e.tag)e.stateNode.nodeValue=r?"":e.memoizedProps;else{if(13===e.tag&&null!==e.memoizedState&&null===e.memoizedState.dehydrated){(a=e.child.sibling).return=e,e=a;continue}if(null!==e.child){e.child.return=e,e=e.child;continue}}if(e===n)break;for(;null===e.sibling;){if(null===e.return||e.return===n)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}return void yu(t);case 19:return void yu(t);case 17:return}throw Error(o(163))}function yu(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new nu),t.forEach(function(t){var r=function(e,t){var n=e.stateNode;null!==n&&n.delete(t),0===(t=0)&&(t=is(t=rs(),e,null)),null!==(e=os(e,t))&&ss(e)}.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}}var vu="function"===typeof WeakMap?WeakMap:Map;function bu(e,t,n){(n=ca(n,null)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ku||(Ku=!0,Qu=r),ru(e,t)},n}function mu(e,t,n){(n=ca(n,null)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var i=t.value;n.payload=function(){return ru(e,t),r(i)}}var a=e.stateNode;return null!==a&&"function"===typeof a.componentDidCatch&&(n.callback=function(){"function"!==typeof r&&(null===qu?qu=new Set([this]):qu.add(this),ru(e,t));var n=t.stack;this.componentDidCatch(t.value,{componentStack:null!==n?n:""})}),n}var gu,ku=Math.ceil,wu=q.ReactCurrentDispatcher,_u=q.ReactCurrentOwner,Ou=0,ju=8,xu=16,Su=32,Tu=0,Iu=1,Eu=2,Au=3,Bu=4,Du=5,Cu=Ou,Lu=null,Fu=null,Mu=0,Nu=Tu,Uu=null,Pu=1073741823,Ru=1073741823,zu=null,Vu=0,Wu=!1,$u=0,Hu=500,Yu=null,Ku=!1,Qu=null,qu=null,Gu=!1,Ju=null,Xu=90,Zu=null,es=0,ts=null,ns=0;function rs(){return(Cu&(xu|Su))!==Ou?1073741821-(zi()/10|0):0!==ns?ns:ns=1073741821-(zi()/10|0)}function is(e,t,n){if(0===(2&(t=t.mode)))return 1073741823;var r=Vi();if(0===(4&t))return 99===r?1073741823:1073741822;if((Cu&xu)!==Ou)return Mu;if(null!==n)e=qi(e,0|n.timeoutMs||5e3,250);else switch(r){case 99:e=1073741823;break;case 98:e=qi(e,150,100);break;case 97:case 96:e=qi(e,5e3,250);break;case 95:e=2;break;default:throw Error(o(326))}return null!==Lu&&e===Mu&&--e,e}function as(e,t){if(50<es)throw es=0,ts=null,Error(o(185));if(null!==(e=os(e,t))){var n=Vi();1073741823===t?(Cu&ju)!==Ou&&(Cu&(xu|Su))===Ou?cs(e):(ss(e),Cu===Ou&&Ki()):ss(e),(4&Cu)===Ou||98!==n&&99!==n||(null===Zu?Zu=new Map([[e,t]]):(void 0===(n=Zu.get(e))||n>t)&&Zu.set(e,t))}}function os(e,t){e.expirationTime<t&&(e.expirationTime=t);var n=e.alternate;null!==n&&n.expirationTime<t&&(n.expirationTime=t);var r=e.return,i=null;if(null===r&&3===e.tag)i=e.stateNode;else for(;null!==r;){if(n=r.alternate,r.childExpirationTime<t&&(r.childExpirationTime=t),null!==n&&n.childExpirationTime<t&&(n.childExpirationTime=t),null===r.return&&3===r.tag){i=r.stateNode;break}r=r.return}return null!==i&&(Lu===i&&(vs(t),Nu===Bu&&zs(i,Mu)),Vs(i,t)),i}function us(e){var t=e.lastExpiredTime;if(0!==t)return t;if(!Rs(e,t=e.firstPendingTime))return t;var n=e.lastPingedTime;return 2>=(e=n>(e=e.nextKnownPendingLevel)?n:e)&&t!==e?0:e}function ss(e){if(0!==e.lastExpiredTime)e.callbackExpirationTime=1073741823,e.callbackPriority=99,e.callbackNode=Yi(cs.bind(null,e));else{var t=us(e),n=e.callbackNode;if(0===t)null!==n&&(e.callbackNode=null,e.callbackExpirationTime=0,e.callbackPriority=90);else{var r=rs();if(1073741823===t?r=99:1===t||2===t?r=95:r=0>=(r=10*(1073741821-t)-10*(1073741821-r))?99:250>=r?98:5250>=r?97:95,null!==n){var i=e.callbackPriority;if(e.callbackExpirationTime===t&&i>=r)return;n!==Li&&xi(n)}e.callbackExpirationTime=t,e.callbackPriority=r,t=1073741823===t?Yi(cs.bind(null,e)):Hi(r,function e(t,n){ns=0;if(n)return n=rs(),Ws(t,n),ss(t),null;var r=us(t);if(0!==r){if(n=t.callbackNode,(Cu&(xu|Su))!==Ou)throw Error(o(327));if(js(),t===Lu&&r===Mu||hs(t,r),null!==Fu){var i=Cu;Cu|=xu;for(var a=ps();;)try{ms();break}catch(c){ds(t,c)}if(ta(),Cu=i,wu.current=a,Nu===Iu)throw n=Uu,hs(t,r),zs(t,r),ss(t),n;if(null===Fu)switch(a=t.finishedWork=t.current.alternate,t.finishedExpirationTime=r,i=Nu,Lu=null,i){case Tu:case Iu:throw Error(o(345));case Eu:Ws(t,2<r?2:r);break;case Au:if(zs(t,r),i=t.lastSuspendedTime,r===i&&(t.nextKnownPendingLevel=ws(a)),1073741823===Pu&&10<(a=$u+Hu-zi())){if(Wu){var u=t.lastPingedTime;if(0===u||u>=r){t.lastPingedTime=r,hs(t,r);break}}if(0!==(u=us(t))&&u!==r)break;if(0!==i&&i!==r){t.lastPingedTime=i;break}t.timeoutHandle=wn(_s.bind(null,t),a);break}_s(t);break;case Bu:if(zs(t,r),i=t.lastSuspendedTime,r===i&&(t.nextKnownPendingLevel=ws(a)),Wu&&(0===(a=t.lastPingedTime)||a>=r)){t.lastPingedTime=r,hs(t,r);break}if(0!==(a=us(t))&&a!==r)break;if(0!==i&&i!==r){t.lastPingedTime=i;break}if(1073741823!==Ru?i=10*(1073741821-Ru)-zi():1073741823===Pu?i=0:(i=10*(1073741821-Pu)-5e3,a=zi(),r=10*(1073741821-r)-a,0>(i=a-i)&&(i=0),i=(120>i?120:480>i?480:1080>i?1080:1920>i?1920:3e3>i?3e3:4320>i?4320:1960*ku(i/1960))-i,r<i&&(i=r)),10<i){t.timeoutHandle=wn(_s.bind(null,t),i);break}_s(t);break;case Du:if(1073741823!==Pu&&null!==zu){u=Pu;var s=zu;if(0>=(i=0|s.busyMinDurationMs)?i=0:(a=0|s.busyDelayMs,u=zi()-(10*(1073741821-u)-(0|s.timeoutMs||5e3)),i=u<=a?0:a+i-u),10<i){zs(t,r),t.timeoutHandle=wn(_s.bind(null,t),i);break}}_s(t);break;default:throw Error(o(329))}if(ss(t),t.callbackNode===n)return e.bind(null,t)}}return null}.bind(null,e),{timeout:10*(1073741821-t)-zi()}),e.callbackNode=t}}}function cs(e){var t=e.lastExpiredTime;if(t=0!==t?t:1073741823,(Cu&(xu|Su))!==Ou)throw Error(o(327));if(js(),e===Lu&&t===Mu||hs(e,t),null!==Fu){var n=Cu;Cu|=xu;for(var r=ps();;)try{bs();break}catch(i){ds(e,i)}if(ta(),Cu=n,wu.current=r,Nu===Iu)throw n=Uu,hs(e,t),zs(e,t),ss(e),n;if(null!==Fu)throw Error(o(261));e.finishedWork=e.current.alternate,e.finishedExpirationTime=t,Lu=null,_s(e),ss(e)}return null}function ls(e,t){var n=Cu;Cu|=1;try{return e(t)}finally{(Cu=n)===Ou&&Ki()}}function fs(e,t){var n=Cu;Cu&=-2,Cu|=ju;try{return e(t)}finally{(Cu=n)===Ou&&Ki()}}function hs(e,t){e.finishedWork=null,e.finishedExpirationTime=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,_n(n)),null!==Fu)for(n=Fu.return;null!==n;){var r=n;switch(r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&mi();break;case 3:La(),li(pi),li(di);break;case 5:Ma(r);break;case 4:La();break;case 13:case 19:li(Na);break;case 10:na(r)}n=n.return}Lu=e,Fu=Ls(e.current,null),Mu=t,Nu=Tu,Uu=null,Ru=Pu=1073741823,zu=null,Vu=0,Wu=!1}function ds(e,t){for(;;){try{if(ta(),Ra.current=mo,Ya)for(var n=Wa.memoizedState;null!==n;){var r=n.queue;null!==r&&(r.pending=null),n=n.next}if(Va=0,Ha=$a=Wa=null,Ya=!1,null===Fu||null===Fu.return)return Nu=Iu,Uu=t,Fu=null;e:{var i=e,a=Fu.return,o=Fu,u=t;if(t=Mu,o.effectTag|=2048,o.firstEffect=o.lastEffect=null,null!==u&&"object"===typeof u&&"function"===typeof u.then){var s=u;if(0===(2&o.mode)){var c=o.alternate;c?(o.updateQueue=c.updateQueue,o.memoizedState=c.memoizedState,o.expirationTime=c.expirationTime):(o.updateQueue=null,o.memoizedState=null)}var l=0!==(1&Na.current),f=a;do{var h;if(h=13===f.tag){var d=f.memoizedState;if(null!==d)h=null!==d.dehydrated;else{var p=f.memoizedProps;h=void 0!==p.fallback&&(!0!==p.unstable_avoidThisFallback||!l)}}if(h){var y=f.updateQueue;if(null===y){var v=new Set;v.add(s),f.updateQueue=v}else y.add(s);if(0===(2&f.mode)){if(f.effectTag|=64,o.effectTag&=-2981,1===o.tag)if(null===o.alternate)o.tag=17;else{var b=ca(1073741823,null);b.tag=2,la(o,b)}o.expirationTime=1073741823;break e}u=void 0,o=t;var m=i.pingCache;if(null===m?(m=i.pingCache=new vu,u=new Set,m.set(s,u)):void 0===(u=m.get(s))&&(u=new Set,m.set(s,u)),!u.has(o)){u.add(o);var g=Is.bind(null,i,s,o);s.then(g,g)}f.effectTag|=4096,f.expirationTime=t;break e}f=f.return}while(null!==f);u=Error((ye(o.type)||"A React component")+" suspended while rendering, but no fallback UI was specified.\n\nAdd a <Suspense fallback=...> component higher in the tree to provide a loading indicator or placeholder to display."+ve(o))}Nu!==Du&&(Nu=Eu),u=tu(u,o),f=a;do{switch(f.tag){case 3:s=u,f.effectTag|=4096,f.expirationTime=t,fa(f,bu(f,s,t));break e;case 1:s=u;var k=f.type,w=f.stateNode;if(0===(64&f.effectTag)&&("function"===typeof k.getDerivedStateFromError||null!==w&&"function"===typeof w.componentDidCatch&&(null===qu||!qu.has(w)))){f.effectTag|=4096,f.expirationTime=t,fa(f,mu(f,s,t));break e}}f=f.return}while(null!==f)}Fu=ks(Fu)}catch(_){t=_;continue}break}}function ps(){var e=wu.current;return wu.current=mo,null===e?mo:e}function ys(e,t){e<Pu&&2<e&&(Pu=e),null!==t&&e<Ru&&2<e&&(Ru=e,zu=t)}function vs(e){e>Vu&&(Vu=e)}function bs(){for(;null!==Fu;)Fu=gs(Fu)}function ms(){for(;null!==Fu&&!Fi();)Fu=gs(Fu)}function gs(e){var t=gu(e.alternate,e,Mu);return e.memoizedProps=e.pendingProps,null===t&&(t=ks(e)),_u.current=null,t}function ks(e){Fu=e;do{var t=Fu.alternate;if(e=Fu.return,0===(2048&Fu.effectTag)){if(t=Zo(t,Fu,Mu),1===Mu||1!==Fu.childExpirationTime){for(var n=0,r=Fu.child;null!==r;){var i=r.expirationTime,a=r.childExpirationTime;i>n&&(n=i),a>n&&(n=a),r=r.sibling}Fu.childExpirationTime=n}if(null!==t)return t;null!==e&&0===(2048&e.effectTag)&&(null===e.firstEffect&&(e.firstEffect=Fu.firstEffect),null!==Fu.lastEffect&&(null!==e.lastEffect&&(e.lastEffect.nextEffect=Fu.firstEffect),e.lastEffect=Fu.lastEffect),1<Fu.effectTag&&(null!==e.lastEffect?e.lastEffect.nextEffect=Fu:e.firstEffect=Fu,e.lastEffect=Fu))}else{if(null!==(t=eu(Fu)))return t.effectTag&=2047,t;null!==e&&(e.firstEffect=e.lastEffect=null,e.effectTag|=2048)}if(null!==(t=Fu.sibling))return t;Fu=e}while(null!==Fu);return Nu===Tu&&(Nu=Du),null}function ws(e){var t=e.expirationTime;return t>(e=e.childExpirationTime)?t:e}function _s(e){var t=Vi();return $i(99,function(e,t){do{js()}while(null!==Ju);if((Cu&(xu|Su))!==Ou)throw Error(o(327));var n=e.finishedWork,r=e.finishedExpirationTime;if(null===n)return null;if(e.finishedWork=null,e.finishedExpirationTime=0,n===e.current)throw Error(o(177));e.callbackNode=null,e.callbackExpirationTime=0,e.callbackPriority=90,e.nextKnownPendingLevel=0;var i=ws(n);if(e.firstPendingTime=i,r<=e.lastSuspendedTime?e.firstSuspendedTime=e.lastSuspendedTime=e.nextKnownPendingLevel=0:r<=e.firstSuspendedTime&&(e.firstSuspendedTime=r-1),r<=e.lastPingedTime&&(e.lastPingedTime=0),r<=e.lastExpiredTime&&(e.lastExpiredTime=0),e===Lu&&(Fu=Lu=null,Mu=0),1<n.effectTag?null!==n.lastEffect?(n.lastEffect.nextEffect=n,i=n.firstEffect):i=n:i=n.firstEffect,null!==i){var a=Cu;Cu|=Su,_u.current=null,bn=Yt;var u=fn();if(hn(u)){if("selectionStart"in u)var s={start:u.selectionStart,end:u.selectionEnd};else e:{var c=(s=(s=u.ownerDocument)&&s.defaultView||window).getSelection&&s.getSelection();if(c&&0!==c.rangeCount){s=c.anchorNode;var l=c.anchorOffset,f=c.focusNode;c=c.focusOffset;try{s.nodeType,f.nodeType}catch(S){s=null;break e}var h=0,d=-1,p=-1,y=0,v=0,b=u,m=null;t:for(;;){for(var g;b!==s||0!==l&&3!==b.nodeType||(d=h+l),b!==f||0!==c&&3!==b.nodeType||(p=h+c),3===b.nodeType&&(h+=b.nodeValue.length),null!==(g=b.firstChild);)m=b,b=g;for(;;){if(b===u)break t;if(m===s&&++y===l&&(d=h),m===f&&++v===c&&(p=h),null!==(g=b.nextSibling))break;m=(b=m).parentNode}b=g}s=-1===d||-1===p?null:{start:d,end:p}}else s=null}s=s||{start:0,end:0}}else s=null;mn={activeElementDetached:null,focusedElem:u,selectionRange:s},Yt=!1,Yu=i;do{try{Os()}catch(S){if(null===Yu)throw Error(o(330));Ts(Yu,S),Yu=Yu.nextEffect}}while(null!==Yu);Yu=i;do{try{for(u=e,s=t;null!==Yu;){var k=Yu.effectTag;if(16&k&&Re(Yu.stateNode,""),128&k){var w=Yu.alternate;if(null!==w){var _=w.ref;null!==_&&("function"===typeof _?_(null):_.current=null)}}switch(1038&k){case 2:hu(Yu),Yu.effectTag&=-3;break;case 6:hu(Yu),Yu.effectTag&=-3,pu(Yu.alternate,Yu);break;case 1024:Yu.effectTag&=-1025;break;case 1028:Yu.effectTag&=-1025,pu(Yu.alternate,Yu);break;case 4:pu(Yu.alternate,Yu);break;case 8:du(u,l=Yu,s),lu(l)}Yu=Yu.nextEffect}}catch(S){if(null===Yu)throw Error(o(330));Ts(Yu,S),Yu=Yu.nextEffect}}while(null!==Yu);if(_=mn,w=fn(),k=_.focusedElem,s=_.selectionRange,w!==k&&k&&k.ownerDocument&&function e(t,n){return!(!t||!n)&&(t===n||(!t||3!==t.nodeType)&&(n&&3===n.nodeType?e(t,n.parentNode):"contains"in t?t.contains(n):!!t.compareDocumentPosition&&!!(16&t.compareDocumentPosition(n))))}(k.ownerDocument.documentElement,k)){null!==s&&hn(k)&&(w=s.start,void 0===(_=s.end)&&(_=w),"selectionStart"in k?(k.selectionStart=w,k.selectionEnd=Math.min(_,k.value.length)):(_=(w=k.ownerDocument||document)&&w.defaultView||window).getSelection&&(_=_.getSelection(),l=k.textContent.length,u=Math.min(s.start,l),s=void 0===s.end?u:Math.min(s.end,l),!_.extend&&u>s&&(l=s,s=u,u=l),l=ln(k,u),f=ln(k,s),l&&f&&(1!==_.rangeCount||_.anchorNode!==l.node||_.anchorOffset!==l.offset||_.focusNode!==f.node||_.focusOffset!==f.offset)&&((w=w.createRange()).setStart(l.node,l.offset),_.removeAllRanges(),u>s?(_.addRange(w),_.extend(f.node,f.offset)):(w.setEnd(f.node,f.offset),_.addRange(w))))),w=[];for(_=k;_=_.parentNode;)1===_.nodeType&&w.push({element:_,left:_.scrollLeft,top:_.scrollTop});for("function"===typeof k.focus&&k.focus(),k=0;k<w.length;k++)(_=w[k]).element.scrollLeft=_.left,_.element.scrollTop=_.top}Yt=!!bn,mn=bn=null,e.current=n,Yu=i;do{try{for(k=e;null!==Yu;){var O=Yu.effectTag;if(36&O&&su(k,Yu.alternate,Yu),128&O){w=void 0;var j=Yu.ref;if(null!==j){var x=Yu.stateNode;switch(Yu.tag){case 5:w=x;break;default:w=x}"function"===typeof j?j(w):j.current=w}}Yu=Yu.nextEffect}}catch(S){if(null===Yu)throw Error(o(330));Ts(Yu,S),Yu=Yu.nextEffect}}while(null!==Yu);Yu=null,Mi(),Cu=a}else e.current=n;if(Gu)Gu=!1,Ju=e,Xu=t;else for(Yu=i;null!==Yu;)t=Yu.nextEffect,Yu.nextEffect=null,Yu=t;if(0===(t=e.firstPendingTime)&&(qu=null),1073741823===t?e===ts?es++:(es=0,ts=e):es=0,"function"===typeof Es&&Es(n.stateNode,r),ss(e),Ku)throw Ku=!1,e=Qu,Qu=null,e;return(Cu&ju)!==Ou?null:(Ki(),null)}.bind(null,e,t)),null}function Os(){for(;null!==Yu;){var e=Yu.effectTag;0!==(256&e)&&au(Yu.alternate,Yu),0===(512&e)||Gu||(Gu=!0,Hi(97,function(){return js(),null})),Yu=Yu.nextEffect}}function js(){if(90!==Xu){var e=97<Xu?97:Xu;return Xu=90,$i(e,xs)}}function xs(){if(null===Ju)return!1;var e=Ju;if(Ju=null,(Cu&(xu|Su))!==Ou)throw Error(o(331));var t=Cu;for(Cu|=Su,e=e.current.firstEffect;null!==e;){try{var n=e;if(0!==(512&n.effectTag))switch(n.tag){case 0:case 11:case 15:case 22:ou(5,n),uu(5,n)}}catch(r){if(null===e)throw Error(o(330));Ts(e,r)}n=e.nextEffect,e.nextEffect=null,e=n}return Cu=t,Ki(),!0}function Ss(e,t,n){la(e,t=bu(e,t=tu(n,t),1073741823)),null!==(e=os(e,1073741823))&&ss(e)}function Ts(e,t){if(3===e.tag)Ss(e,e,t);else for(var n=e.return;null!==n;){if(3===n.tag){Ss(n,e,t);break}if(1===n.tag){var r=n.stateNode;if("function"===typeof n.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===qu||!qu.has(r))){la(n,e=mu(n,e=tu(t,e),1073741823)),null!==(n=os(n,1073741823))&&ss(n);break}}n=n.return}}function Is(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),Lu===e&&Mu===n?Nu===Bu||Nu===Au&&1073741823===Pu&&zi()-$u<Hu?hs(e,Mu):Wu=!0:Rs(e,n)&&(0!==(t=e.lastPingedTime)&&t<n||(e.lastPingedTime=n,ss(e)))}gu=function(e,t,n){var r=t.expirationTime;if(null!==e){var i=t.pendingProps;if(e.memoizedProps!==i||pi.current)Do=!0;else{if(r<n){switch(Do=!1,t.tag){case 3:zo(t),Ao();break;case 5:if(Fa(t),4&t.mode&&1!==n&&i.hidden)return t.expirationTime=t.childExpirationTime=1,null;break;case 1:bi(t.type)&&wi(t);break;case 4:Ca(t,t.stateNode.containerInfo);break;case 10:r=t.memoizedProps.value,i=t.type._context,fi(Ji,i._currentValue),i._currentValue=r;break;case 13:if(null!==t.memoizedState)return 0!==(r=t.child.childExpirationTime)&&r>=n?Ko(e,t,n):(fi(Na,1&Na.current),null!==(t=Jo(e,t,n))?t.sibling:null);fi(Na,1&Na.current);break;case 19:if(r=t.childExpirationTime>=n,0!==(64&e.effectTag)){if(r)return Go(e,t,n);t.effectTag|=64}if(null!==(i=t.memoizedState)&&(i.rendering=null,i.tail=null),fi(Na,Na.current),!r)return null}return Jo(e,t,n)}Do=!1}}else Do=!1;switch(t.expirationTime=0,t.tag){case 2:if(r=t.type,null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),e=t.pendingProps,i=vi(t,di.current),ia(t,n),i=qa(null,t,r,e,i,n),t.effectTag|=1,"object"===typeof i&&null!==i&&"function"===typeof i.render&&void 0===i.$$typeof){if(t.tag=1,t.memoizedState=null,t.updateQueue=null,bi(r)){var a=!0;wi(t)}else a=!1;t.memoizedState=null!==i.state&&void 0!==i.state?i.state:null,ua(t);var u=r.getDerivedStateFromProps;"function"===typeof u&&va(t,r,u,e),i.updater=ba,t.stateNode=i,i._reactInternalFiber=t,wa(t,r,e,n),t=Ro(null,t,r,!0,a,n)}else t.tag=0,Co(null,t,i,n),t=t.child;return t;case 16:e:{if(i=t.elementType,null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),e=t.pendingProps,function(e){if(-1===e._status){e._status=0;var t=e._ctor;t=t(),e._result=t,t.then(function(t){0===e._status&&(t=t.default,e._status=1,e._result=t)},function(t){0===e._status&&(e._status=2,e._result=t)})}}(i),1!==i._status)throw i._result;switch(i=i._result,t.type=i,a=t.tag=function(e){if("function"===typeof e)return Cs(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===ue)return 11;if(e===le)return 14}return 2}(i),e=Gi(i,e),a){case 0:t=Uo(null,t,i,e,n);break e;case 1:t=Po(null,t,i,e,n);break e;case 11:t=Lo(null,t,i,e,n);break e;case 14:t=Fo(null,t,i,Gi(i.type,e),r,n);break e}throw Error(o(306,i,""))}return t;case 0:return r=t.type,i=t.pendingProps,Uo(e,t,r,i=t.elementType===r?i:Gi(r,i),n);case 1:return r=t.type,i=t.pendingProps,Po(e,t,r,i=t.elementType===r?i:Gi(r,i),n);case 3:if(zo(t),r=t.updateQueue,null===e||null===r)throw Error(o(282));if(r=t.pendingProps,i=null!==(i=t.memoizedState)?i.element:null,sa(e,t),ha(t,r,null,n),(r=t.memoizedState.element)===i)Ao(),t=Jo(e,t,n);else{if((i=t.stateNode.hydrate)&&(Oo=On(t.stateNode.containerInfo.firstChild),_o=t,i=jo=!0),i)for(n=Ta(t,null,r,n),t.child=n;n;)n.effectTag=-3&n.effectTag|1024,n=n.sibling;else Co(e,t,r,n),Ao();t=t.child}return t;case 5:return Fa(t),null===e&&To(t),r=t.type,i=t.pendingProps,a=null!==e?e.memoizedProps:null,u=i.children,kn(r,i)?u=null:null!==a&&kn(r,a)&&(t.effectTag|=16),No(e,t),4&t.mode&&1!==n&&i.hidden?(t.expirationTime=t.childExpirationTime=1,t=null):(Co(e,t,u,n),t=t.child),t;case 6:return null===e&&To(t),null;case 13:return Ko(e,t,n);case 4:return Ca(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=Sa(t,null,r,n):Co(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,Lo(e,t,r,i=t.elementType===r?i:Gi(r,i),n);case 7:return Co(e,t,t.pendingProps,n),t.child;case 8:case 12:return Co(e,t,t.pendingProps.children,n),t.child;case 10:e:{r=t.type._context,i=t.pendingProps,u=t.memoizedProps,a=i.value;var s=t.type._context;if(fi(Ji,s._currentValue),s._currentValue=a,null!==u)if(s=u.value,0===(a=Pr(s,a)?0:0|("function"===typeof r._calculateChangedBits?r._calculateChangedBits(s,a):1073741823))){if(u.children===i.children&&!pi.current){t=Jo(e,t,n);break e}}else for(null!==(s=t.child)&&(s.return=t);null!==s;){var c=s.dependencies;if(null!==c){u=s.child;for(var l=c.firstContext;null!==l;){if(l.context===r&&0!==(l.observedBits&a)){1===s.tag&&((l=ca(n,null)).tag=2,la(s,l)),s.expirationTime<n&&(s.expirationTime=n),null!==(l=s.alternate)&&l.expirationTime<n&&(l.expirationTime=n),ra(s.return,n),c.expirationTime<n&&(c.expirationTime=n);break}l=l.next}}else u=10===s.tag&&s.type===t.type?null:s.child;if(null!==u)u.return=s;else for(u=s;null!==u;){if(u===t){u=null;break}if(null!==(s=u.sibling)){s.return=u.return,u=s;break}u=u.return}s=u}Co(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=(a=t.pendingProps).children,ia(t,n),r=r(i=aa(i,a.unstable_observedBits)),t.effectTag|=1,Co(e,t,r,n),t.child;case 14:return a=Gi(i=t.type,t.pendingProps),Fo(e,t,i,a=Gi(i.type,a),r,n);case 15:return Mo(e,t,t.type,t.pendingProps,r,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Gi(r,i),null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),t.tag=1,bi(r)?(e=!0,wi(t)):e=!1,ia(t,n),ga(t,r,i),wa(t,r,i,n),Ro(null,t,r,!0,e,n);case 19:return Go(e,t,n)}throw Error(o(156,t.tag))};var Es=null,As=null;function Bs(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.effectTag=0,this.lastEffect=this.firstEffect=this.nextEffect=null,this.childExpirationTime=this.expirationTime=0,this.alternate=null}function Ds(e,t,n,r){return new Bs(e,t,n,r)}function Cs(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Ls(e,t){var n=e.alternate;return null===n?((n=Ds(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.effectTag=0,n.nextEffect=null,n.firstEffect=null,n.lastEffect=null),n.childExpirationTime=e.childExpirationTime,n.expirationTime=e.expirationTime,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{expirationTime:t.expirationTime,firstContext:t.firstContext,responders:t.responders},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Fs(e,t,n,r,i,a){var u=2;if(r=e,"function"===typeof e)Cs(e)&&(u=1);else if("string"===typeof e)u=5;else e:switch(e){case te:return Ms(n.children,i,a,t);case oe:u=8,i|=7;break;case ne:u=8,i|=1;break;case re:return(e=Ds(12,n,t,8|i)).elementType=re,e.type=re,e.expirationTime=a,e;case se:return(e=Ds(13,n,t,i)).type=se,e.elementType=se,e.expirationTime=a,e;case ce:return(e=Ds(19,n,t,i)).elementType=ce,e.expirationTime=a,e;default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case ie:u=10;break e;case ae:u=9;break e;case ue:u=11;break e;case le:u=14;break e;case fe:u=16,r=null;break e;case he:u=22;break e}throw Error(o(130,null==e?e:typeof e,""))}return(t=Ds(u,n,t,i)).elementType=e,t.type=r,t.expirationTime=a,t}function Ms(e,t,n,r){return(e=Ds(7,e,r,t)).expirationTime=n,e}function Ns(e,t,n){return(e=Ds(6,e,null,t)).expirationTime=n,e}function Us(e,t,n){return(t=Ds(4,null!==e.children?e.children:[],e.key,t)).expirationTime=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Ps(e,t,n){this.tag=t,this.current=null,this.containerInfo=e,this.pingCache=this.pendingChildren=null,this.finishedExpirationTime=0,this.finishedWork=null,this.timeoutHandle=-1,this.pendingContext=this.context=null,this.hydrate=n,this.callbackNode=null,this.callbackPriority=90,this.lastExpiredTime=this.lastPingedTime=this.nextKnownPendingLevel=this.lastSuspendedTime=this.firstSuspendedTime=this.firstPendingTime=0}function Rs(e,t){var n=e.firstSuspendedTime;return e=e.lastSuspendedTime,0!==n&&n>=t&&e<=t}function zs(e,t){var n=e.firstSuspendedTime,r=e.lastSuspendedTime;n<t&&(e.firstSuspendedTime=t),(r>t||0===n)&&(e.lastSuspendedTime=t),t<=e.lastPingedTime&&(e.lastPingedTime=0),t<=e.lastExpiredTime&&(e.lastExpiredTime=0)}function Vs(e,t){t>e.firstPendingTime&&(e.firstPendingTime=t);var n=e.firstSuspendedTime;0!==n&&(t>=n?e.firstSuspendedTime=e.lastSuspendedTime=e.nextKnownPendingLevel=0:t>=e.lastSuspendedTime&&(e.lastSuspendedTime=t+1),t>e.nextKnownPendingLevel&&(e.nextKnownPendingLevel=t))}function Ws(e,t){var n=e.lastExpiredTime;(0===n||n>t)&&(e.lastExpiredTime=t)}function $s(e,t,n,r){var i=t.current,a=rs(),u=pa.suspense;a=is(a,i,u);e:if(n){t:{if(Ze(n=n._reactInternalFiber)!==n||1!==n.tag)throw Error(o(170));var s=n;do{switch(s.tag){case 3:s=s.stateNode.context;break t;case 1:if(bi(s.type)){s=s.stateNode.__reactInternalMemoizedMergedChildContext;break t}}s=s.return}while(null!==s);throw Error(o(171))}if(1===n.tag){var c=n.type;if(bi(c)){n=ki(n,c,s);break e}}n=s}else n=hi;return null===t.context?t.context=n:t.pendingContext=n,(t=ca(a,u)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),la(i,t),as(i,a),a}function Hs(e){if(!(e=e.current).child)return null;switch(e.child.tag){case 5:default:return e.child.stateNode}}function Ys(e,t){null!==(e=e.memoizedState)&&null!==e.dehydrated&&e.retryTime<t&&(e.retryTime=t)}function Ks(e,t){Ys(e,t),(e=e.alternate)&&Ys(e,t)}function Qs(e,t,n){var r=new Ps(e,t,n=null!=n&&!0===n.hydrate),i=Ds(3,null,null,2===t?7:1===t?3:0);r.current=i,i.stateNode=r,ua(i),e[In]=r.current,n&&0!==t&&function(e,t){var n=Xe(t);St.forEach(function(e){pt(e,t,n)}),Tt.forEach(function(e){pt(e,t,n)})}(0,9===e.nodeType?e:e.ownerDocument),this._internalRoot=r}function qs(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Gs(e,t,n,r,i){var a=n._reactRootContainer;if(a){var o=a._internalRoot;if("function"===typeof i){var u=i;i=function(){var e=Hs(o);u.call(e)}}$s(t,o,e,i)}else{if(a=n._reactRootContainer=function(e,t){if(t||(t=!(!(t=e?9===e.nodeType?e.documentElement:e.firstChild:null)||1!==t.nodeType||!t.hasAttribute("data-reactroot"))),!t)for(var n;n=e.lastChild;)e.removeChild(n);return new Qs(e,0,t?{hydrate:!0}:void 0)}(n,r),o=a._internalRoot,"function"===typeof i){var s=i;i=function(){var e=Hs(o);s.call(e)}}fs(function(){$s(t,o,e,i)})}return Hs(o)}function Js(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!qs(t))throw Error(o(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:ee,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)}Qs.prototype.render=function(e){$s(e,this._internalRoot,null,null)},Qs.prototype.unmount=function(){var e=this._internalRoot,t=e.containerInfo;$s(null,e,null,function(){t[In]=null})},yt=function(e){if(13===e.tag){var t=qi(rs(),150,100);as(e,t),Ks(e,t)}},vt=function(e){13===e.tag&&(as(e,3),Ks(e,3))},bt=function(e){if(13===e.tag){var t=rs();as(e,t=is(t,e,null)),Ks(e,t)}},T=function(e,t,n){switch(t){case"input":if(je(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Dn(r);if(!i)throw Error(o(90));ke(r),je(r,i)}}}break;case"textarea":Be(e,n);break;case"select":null!=(t=n.value)&&Ie(e,!!n.multiple,t,!1)}},C=ls,L=function(e,t,n,r,i){var a=Cu;Cu|=4;try{return $i(98,e.bind(null,t,n,r,i))}finally{(Cu=a)===Ou&&Ki()}},F=function(){(Cu&(1|xu|Su))===Ou&&(function(){if(null!==Zu){var e=Zu;Zu=null,e.forEach(function(e,t){Ws(t,e),ss(t)}),Ki()}}(),js())},M=function(e,t){var n=Cu;Cu|=2;try{return e(t)}finally{(Cu=n)===Ou&&Ki()}};var Xs={Events:[An,Bn,Dn,x,_,Pn,function(e){it(e,Un)},B,D,qt,ut,js,{current:!1}]};!function(e){var t=e.findFiberByHostInstance;(function(e){if("undefined"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__)return!1;var t=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(t.isDisabled||!t.supportsFiber)return!0;try{var n=t.inject(e);Es=function(e){try{t.onCommitFiberRoot(n,e,void 0,64===(64&e.current.effectTag))}catch(r){}},As=function(e){try{t.onCommitFiberUnmount(n,e)}catch(r){}}}catch(r){}})(i({},e,{overrideHookState:null,overrideProps:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:q.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=nt(e))?null:e.stateNode},findFiberByHostInstance:function(e){return t?t(e):null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null}))}({findFiberByHostInstance:En,bundleType:0,version:"16.14.0",rendererPackageName:"react-dom"}),t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Xs,t.createPortal=Js,t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternalFiber;if(void 0===t){if("function"===typeof e.render)throw Error(o(188));throw Error(o(268,Object.keys(e)))}return e=null===(e=nt(t))?null:e.stateNode},t.flushSync=function(e,t){if((Cu&(xu|Su))!==Ou)throw Error(o(187));var n=Cu;Cu|=1;try{return $i(99,e.bind(null,t))}finally{Cu=n,Ki()}},t.hydrate=function(e,t,n){if(!qs(t))throw Error(o(200));return Gs(null,e,t,!0,n)},t.render=function(e,t,n){if(!qs(t))throw Error(o(200));return Gs(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!qs(e))throw Error(o(40));return!!e._reactRootContainer&&(fs(function(){Gs(null,null,e,!1,function(){e._reactRootContainer=null,e[In]=null})}),!0)},t.unstable_batchedUpdates=ls,t.unstable_createPortal=function(e,t){return Js(e,t,2<arguments.length&&void 0!==arguments[2]?arguments[2]:null)},t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!qs(n))throw Error(o(200));if(null==e||void 0===e._reactInternalFiber)throw Error(o(38));return Gs(e,t,n,!1,r)},t.version="16.14.0"},function(e,t,n){"use strict";e.exports=n(21)},function(e,t,n){"use strict";var r,i,a,o,u;if("undefined"===typeof window||"function"!==typeof MessageChannel){var s=null,c=null,l=function e(){if(null!==s)try{var n=t.unstable_now();s(!0,n),s=null}catch(r){throw setTimeout(e,0),r}},f=Date.now();t.unstable_now=function(){return Date.now()-f},r=function(e){null!==s?setTimeout(r,0,e):(s=e,setTimeout(l,0))},i=function(e,t){c=setTimeout(e,t)},a=function(){clearTimeout(c)},o=function(){return!1},u=t.unstable_forceFrameRate=function(){}}else{var h=window.performance,d=window.Date,p=window.setTimeout,y=window.clearTimeout;if("undefined"!==typeof console){var v=window.cancelAnimationFrame;"function"!==typeof window.requestAnimationFrame&&console.error("This browser doesn't support requestAnimationFrame. Make sure that you load a polyfill in older browsers. https://fb.me/react-polyfills"),"function"!==typeof v&&console.error("This browser doesn't support cancelAnimationFrame. Make sure that you load a polyfill in older browsers. https://fb.me/react-polyfills")}if("object"===typeof h&&"function"===typeof h.now)t.unstable_now=function(){return h.now()};else{var b=d.now();t.unstable_now=function(){return d.now()-b}}var m=!1,g=null,k=-1,w=5,_=0;o=function(){return t.unstable_now()>=_},u=function(){},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing framerates higher than 125 fps is not unsupported"):w=0<e?Math.floor(1e3/e):5};var O=new MessageChannel,j=O.port2;O.port1.onmessage=function(){if(null!==g){var e=t.unstable_now();_=e+w;try{g(!0,e)?j.postMessage(null):(m=!1,g=null)}catch(n){throw j.postMessage(null),n}}else m=!1},r=function(e){g=e,m||(m=!0,j.postMessage(null))},i=function(e,n){k=p(function(){e(t.unstable_now())},n)},a=function(){y(k),k=-1}}function x(e,t){var n=e.length;e.push(t);e:for(;;){var r=n-1>>>1,i=e[r];if(!(void 0!==i&&0<I(i,t)))break e;e[r]=t,e[n]=i,n=r}}function S(e){return void 0===(e=e[0])?null:e}function T(e){var t=e[0];if(void 0!==t){var n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,i=e.length;r<i;){var a=2*(r+1)-1,o=e[a],u=a+1,s=e[u];if(void 0!==o&&0>I(o,n))void 0!==s&&0>I(s,o)?(e[r]=s,e[u]=n,r=u):(e[r]=o,e[a]=n,r=a);else{if(!(void 0!==s&&0>I(s,n)))break e;e[r]=s,e[u]=n,r=u}}}return t}return null}function I(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}var E=[],A=[],B=1,D=null,C=3,L=!1,F=!1,M=!1;function N(e){for(var t=S(A);null!==t;){if(null===t.callback)T(A);else{if(!(t.startTime<=e))break;T(A),t.sortIndex=t.expirationTime,x(E,t)}t=S(A)}}function U(e){if(M=!1,N(e),!F)if(null!==S(E))F=!0,r(P);else{var t=S(A);null!==t&&i(U,t.startTime-e)}}function P(e,n){F=!1,M&&(M=!1,a()),L=!0;var r=C;try{for(N(n),D=S(E);null!==D&&(!(D.expirationTime>n)||e&&!o());){var u=D.callback;if(null!==u){D.callback=null,C=D.priorityLevel;var s=u(D.expirationTime<=n);n=t.unstable_now(),"function"===typeof s?D.callback=s:D===S(E)&&T(E),N(n)}else T(E);D=S(E)}if(null!==D)var c=!0;else{var l=S(A);null!==l&&i(U,l.startTime-n),c=!1}return c}finally{D=null,C=r,L=!1}}function R(e){switch(e){case 1:return-1;case 2:return 250;case 5:return 1073741823;case 4:return 1e4;default:return 5e3}}var z=u;t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){F||L||(F=!0,r(P))},t.unstable_getCurrentPriorityLevel=function(){return C},t.unstable_getFirstCallbackNode=function(){return S(E)},t.unstable_next=function(e){switch(C){case 1:case 2:case 3:var t=3;break;default:t=C}var n=C;C=t;try{return e()}finally{C=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=z,t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=C;C=e;try{return t()}finally{C=n}},t.unstable_scheduleCallback=function(e,n,o){var u=t.unstable_now();if("object"===typeof o&&null!==o){var s=o.delay;s="number"===typeof s&&0<s?u+s:u,o="number"===typeof o.timeout?o.timeout:R(e)}else o=R(e),s=u;return e={id:B++,callback:n,priorityLevel:e,startTime:s,expirationTime:o=s+o,sortIndex:-1},s>u?(e.sortIndex=s,x(A,e),null===S(E)&&e===S(A)&&(M?a():M=!0,i(U,s-u))):(e.sortIndex=o,x(E,e),F||L||(F=!0,r(P))),e},t.unstable_shouldYield=function(){var e=t.unstable_now();N(e);var n=S(E);return n!==D&&null!==D&&null!==n&&null!==n.callback&&n.startTime<=e&&n.expirationTime<D.expirationTime||o()},t.unstable_wrapCallback=function(e){var t=C;return function(){var n=C;C=t;try{return e.apply(this,arguments)}finally{C=n}}}},function(e,t,n){"use strict";e.exports=n(23)},function(e,t,n){"use strict";var r="function"===typeof Symbol&&Symbol.for,i=r?Symbol.for("react.element"):60103,a=r?Symbol.for("react.portal"):60106,o=r?Symbol.for("react.fragment"):60107,u=r?Symbol.for("react.strict_mode"):60108,s=r?Symbol.for("react.profiler"):60114,c=r?Symbol.for("react.provider"):60109,l=r?Symbol.for("react.context"):60110,f=r?Symbol.for("react.async_mode"):60111,h=r?Symbol.for("react.concurrent_mode"):60111,d=r?Symbol.for("react.forward_ref"):60112,p=r?Symbol.for("react.suspense"):60113,y=r?Symbol.for("react.suspense_list"):60120,v=r?Symbol.for("react.memo"):60115,b=r?Symbol.for("react.lazy"):60116,m=r?Symbol.for("react.block"):60121,g=r?Symbol.for("react.fundamental"):60117,k=r?Symbol.for("react.responder"):60118,w=r?Symbol.for("react.scope"):60119;function _(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case i:switch(e=e.type){case f:case h:case o:case s:case u:case p:return e;default:switch(e=e&&e.$$typeof){case l:case d:case b:case v:case c:return e;default:return t}}case a:return t}}}function O(e){return _(e)===h}t.AsyncMode=f,t.ConcurrentMode=h,t.ContextConsumer=l,t.ContextProvider=c,t.Element=i,t.ForwardRef=d,t.Fragment=o,t.Lazy=b,t.Memo=v,t.Portal=a,t.Profiler=s,t.StrictMode=u,t.Suspense=p,t.isAsyncMode=function(e){return O(e)||_(e)===f},t.isConcurrentMode=O,t.isContextConsumer=function(e){return _(e)===l},t.isContextProvider=function(e){return _(e)===c},t.isElement=function(e){return"object"===typeof e&&null!==e&&e.$$typeof===i},t.isForwardRef=function(e){return _(e)===d},t.isFragment=function(e){return _(e)===o},t.isLazy=function(e){return _(e)===b},t.isMemo=function(e){return _(e)===v},t.isPortal=function(e){return _(e)===a},t.isProfiler=function(e){return _(e)===s},t.isStrictMode=function(e){return _(e)===u},t.isSuspense=function(e){return _(e)===p},t.isValidElementType=function(e){return"string"===typeof e||"function"===typeof e||e===o||e===h||e===s||e===u||e===p||e===y||"object"===typeof e&&null!==e&&(e.$$typeof===b||e.$$typeof===v||e.$$typeof===c||e.$$typeof===l||e.$$typeof===d||e.$$typeof===g||e.$$typeof===k||e.$$typeof===w||e.$$typeof===m)},t.typeOf=_},function(e,t,n){var r=function(){return this||"object"===typeof self&&self}()||Function("return this")(),i=r.regeneratorRuntime&&Object.getOwnPropertyNames(r).indexOf("regeneratorRuntime")>=0,a=i&&r.regeneratorRuntime;if(r.regeneratorRuntime=void 0,e.exports=n(25),i)r.regeneratorRuntime=a;else try{delete r.regeneratorRuntime}catch(o){r.regeneratorRuntime=void 0}},function(e,t){!function(t){"use strict";var n,r=Object.prototype,i=r.hasOwnProperty,a="function"===typeof Symbol?Symbol:{},o=a.iterator||"@@iterator",u=a.asyncIterator||"@@asyncIterator",s=a.toStringTag||"@@toStringTag",c="object"===typeof e,l=t.regeneratorRuntime;if(l)c&&(e.exports=l);else{(l=t.regeneratorRuntime=c?e.exports:{}).wrap=k;var f="suspendedStart",h="suspendedYield",d="executing",p="completed",y={},v={};v[o]=function(){return this};var b=Object.getPrototypeOf,m=b&&b(b(B([])));m&&m!==r&&i.call(m,o)&&(v=m);var g=j.prototype=_.prototype=Object.create(v);O.prototype=g.constructor=j,j.constructor=O,j[s]=O.displayName="GeneratorFunction",l.isGeneratorFunction=function(e){var t="function"===typeof e&&e.constructor;return!!t&&(t===O||"GeneratorFunction"===(t.displayName||t.name))},l.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,j):(e.__proto__=j,s in e||(e[s]="GeneratorFunction")),e.prototype=Object.create(g),e},l.awrap=function(e){return{__await:e}},x(S.prototype),S.prototype[u]=function(){return this},l.AsyncIterator=S,l.async=function(e,t,n,r){var i=new S(k(e,t,n,r));return l.isGeneratorFunction(t)?i:i.next().then(function(e){return e.done?e.value:i.next()})},x(g),g[s]="Generator",g[o]=function(){return this},g.toString=function(){return"[object Generator]"},l.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var r=t.pop();if(r in e)return n.value=r,n.done=!1,n}return n.done=!0,n}},l.values=B,A.prototype={constructor:A,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=n,this.done=!1,this.delegate=null,this.method="next",this.arg=n,this.tryEntries.forEach(E),!e)for(var t in this)"t"===t.charAt(0)&&i.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=n)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function r(r,i){return u.type="throw",u.arg=e,t.next=r,i&&(t.method="next",t.arg=n),!!i}for(var a=this.tryEntries.length-1;a>=0;--a){var o=this.tryEntries[a],u=o.completion;if("root"===o.tryLoc)return r("end");if(o.tryLoc<=this.prev){var s=i.call(o,"catchLoc"),c=i.call(o,"finallyLoc");if(s&&c){if(this.prev<o.catchLoc)return r(o.catchLoc,!0);if(this.prev<o.finallyLoc)return r(o.finallyLoc)}else if(s){if(this.prev<o.catchLoc)return r(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return r(o.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&i.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var a=r;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var o=a?a.completion:{};return o.type=e,o.arg=t,a?(this.method="next",this.next=a.finallyLoc,y):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),y},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),E(n),y}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var i=r.arg;E(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:B(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=n),y}}}function k(e,t,n,r){var i=t&&t.prototype instanceof _?t:_,a=Object.create(i.prototype),o=new A(r||[]);return a._invoke=function(e,t,n){var r=f;return function(i,a){if(r===d)throw new Error("Generator is already running");if(r===p){if("throw"===i)throw a;return D()}for(n.method=i,n.arg=a;;){var o=n.delegate;if(o){var u=T(o,n);if(u){if(u===y)continue;return u}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(r===f)throw r=p,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);r=d;var s=w(e,t,n);if("normal"===s.type){if(r=n.done?p:h,s.arg===y)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(r=p,n.method="throw",n.arg=s.arg)}}}(e,n,o),a}function w(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(r){return{type:"throw",arg:r}}}function _(){}function O(){}function j(){}function x(e){["next","throw","return"].forEach(function(t){e[t]=function(e){return this._invoke(t,e)}})}function S(e){var t;this._invoke=function(n,r){function a(){return new Promise(function(t,a){!function t(n,r,a,o){var u=w(e[n],e,r);if("throw"!==u.type){var s=u.arg,c=s.value;return c&&"object"===typeof c&&i.call(c,"__await")?Promise.resolve(c.__await).then(function(e){t("next",e,a,o)},function(e){t("throw",e,a,o)}):Promise.resolve(c).then(function(e){s.value=e,a(s)},function(e){return t("throw",e,a,o)})}o(u.arg)}(n,r,t,a)})}return t=t?t.then(a,a):a()}}function T(e,t){var r=e.iterator[t.method];if(r===n){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=n,T(e,t),"throw"===t.method))return y;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return y}var i=w(r,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,y;var a=i.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=n),t.delegate=null,y):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,y)}function I(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function E(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function A(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(I,this),this.reset(!0)}function B(e){if(e){var t=e[o];if(t)return t.call(e);if("function"===typeof e.next)return e;if(!isNaN(e.length)){var r=-1,a=function t(){for(;++r<e.length;)if(i.call(e,r))return t.value=e[r],t.done=!1,t;return t.value=n,t.done=!0,t};return a.next=a}}return{next:D}}function D(){return{value:n,done:!0}}}(function(){return this||"object"===typeof self&&self}()||Function("return this")())},function(e,t){function n(){return e.exports=n=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},e.exports.__esModule=!0,e.exports.default=e.exports,n.apply(this,arguments)}e.exports=n,e.exports.__esModule=!0,e.exports.default=e.exports}]]);
//# sourceMappingURL=2.f9e75198.chunk.js.map