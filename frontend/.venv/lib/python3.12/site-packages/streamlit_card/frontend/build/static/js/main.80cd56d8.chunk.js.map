{"version": 3, "sources": ["stCard.tsx", "index.tsx"], "names": ["Card", "render", "_this$props$args", "_this", "props", "args", "title", "text", "image", "styles", "theme", "react_default", "a", "createElement", "margin", "card", "styled", "div", "Object", "objectSpread", "display", "cursor", "fontFamily", "concat", "font", "height", "borderRadius", "overflow", "backgroundImage", "backgroundPosition", "backgroundSize", "backgroundRepeat", "boxShadow", "marginLeft", "marginRight", "flexDirection", "justifyContent", "alignItems", "textAlign", "color", "textColor", "width", "position", "transition", "Parent", "padding", "Title", "h2", "zIndex", "fontSize", "fontWeight", "Text", "p", "Filter", "backgroundColor", "top", "left", "filter", "texts", "i", "length", "t", "push", "onClick", "url", "window", "open", "Streamlit", "setComponentValue", "setFrameHeight", "StreamlitComponentBase", "withStreamlitConnection", "ReactDOM", "StrictMode", "stCard", "document", "getElementById"], "mappings": "mOAQMA,6MASGC,OAAS,WAAiB,IAAAC,EACMC,EAAKC,MAAMC,KAA1CC,EADyBJ,EACzBI,MAAOC,EADkBL,EAClBK,KAAMC,EADYN,EACZM,MAAOC,EADKP,EACLO,OAKlBC,EAAUP,EAAKC,MAAfM,MAIR,IAAKA,EACH,OAAOC,EAAAC,EAAAC,cAAA,kEAGT,IAEMC,EAASL,EAAOM,KAAKD,QAAU,GAE/Bd,EAAOgB,IAAOC,IAAPC,OAAAC,EAAA,EAAAD,CAAA,CAETE,QAAS,OACTC,OAAQ,UACRC,WAAU,GAAAC,OAAKb,EAAMc,KAAX,sCACVC,OAAM,GAAAF,OATK,IASL,MACNG,aAAc,OACdC,SAAU,SACVC,gBAAiBpB,EAAK,OAAAe,OAAUf,EAAV,KAAqB,OAC3CqB,mBAAoB,SACpBC,eAAgB,QAChBC,iBAAkB,YAClBC,UAAW,kCACXlB,OAAM,GAAAS,OAAKT,EAAL,MACNmB,WAAY,OACZC,YAAa,OACbC,cAAe,SACfC,eAAgB,SAChBC,WAAY,SACZC,UAAW,SACXC,MAAO7B,EAAM8B,UACbC,MAAK,GAAAlB,OAxBK,IAwBL,MACLmB,SAAU,WACVC,WAAY,wBACTlC,EAAOM,MAxBD,qLAuCP6B,EAAS5B,IAAOC,IAAPC,OAAAC,EAAA,EAAAD,CAAA,CACb2B,QAAS/B,EACT2B,MAAO,QACJhC,EAAOQ,MAGN6B,EAAQ9B,IAAO+B,GAAP7B,OAAAC,EAAA,EAAAD,CAAA,CACZqB,MAAO,QACPS,OAAQ,IACRC,SAAU,MACVC,WAAY,UACTzC,EAAOH,QAGN6C,EAAOnC,IAAOoC,EAAPlC,OAAAC,EAAA,EAAAD,CAAA,CACXqB,MAAO,QACPW,WAAY,SACZF,OAAQ,IACRC,SAAU,MACVnC,OAAQ,KACLL,EAAOF,OAGN8C,EAASrC,IAAOC,IAAPC,OAAAC,EAAA,EAAAD,CAAA,CACboC,gBAAiB,qBACjB7B,OAAQ,OACRgB,MAAO,OACPC,SAAU,WACVa,IAAK,IACLC,KAAM,IACNR,OAAQ,KACLvC,EAAOgD,SAIQ,kBAATlD,IACTA,EAAO,CAACA,IAIV,IADA,IAAImD,EAAQ,GACHC,EAAI,EAAGA,EAAIpD,EAAKqD,OAAQD,IAAK,CACpC,IAAME,EAAItD,EAAKoD,GACfD,EAAMI,KAAKnD,EAAAC,EAAAC,cAACsC,EAAD,KAAOU,IAGpB,OACElD,EAAAC,EAAAC,cAAC+B,EAAD,KACEjC,EAAAC,EAAAC,cAACb,EAAD,CAAM+D,QAAS5D,EAAK4D,SAClBpD,EAAAC,EAAAC,cAACwC,EAAD,MACA1C,EAAAC,EAAAC,cAACiC,EAAD,KAAQxC,GACPoD,OAMDK,QAAU,WACZ5D,EAAKC,MAAMC,KAAK2D,KAClBC,OAAOC,KAAK/D,EAAKC,MAAMC,KAAK2D,IAAK,UAEnCG,IAAUC,mBAAkB,wFA5H5BD,IAAUE,6DAIVF,IAAUE,wBANKC,KAkIJC,cAAwBvE,GCtIvCwE,IAASvE,OACPU,EAAAC,EAAAC,cAACF,EAAAC,EAAM6D,WAAP,KACE9D,EAAAC,EAAAC,cAAC6D,EAAD,OAEFC,SAASC,eAAe", "file": "static/js/main.80cd56d8.chunk.js", "sourcesContent": ["import {\n  Streamlit,\n  StreamlitComponentBase,\n  withStreamlitConnection,\n} from \"streamlit-component-lib\";\nimport React, { ReactNode } from \"react\";\nimport styled from \"@emotion/styled\";\n\nclass Card extends StreamlitComponentBase {\n  public componentDidUpdate(): void {\n    Streamlit.setFrameHeight();\n  }\n\n  public componentDidMount(): void {\n    Streamlit.setFrameHeight();\n  }\n\n  public render = (): ReactNode => {\n    let { title, text, image, styles } = this.props.args;\n\n    // Streamlit sends us a theme object via props that we can use to ensure\n    // that our component has visuals that match the active theme in a\n    // streamlit app.\n    const { theme } = this.props;\n\n    // Maintain compatibility with older versions of Streamlit that don't send\n    // a theme object.\n    if (!theme) {\n      return <div>Theme is undefined, please check streamlit version.</div>;\n    }\n\n    const height = 250;\n    const width = 300;\n    const margin = styles.card.margin || 40;\n\n    const Card = styled.div(\n      {\n        display: \"flex\",\n        cursor: \"pointer\",\n        fontFamily: `${theme.font}, 'Segoe UI', 'Roboto', sans-serif`,\n        height: `${height}px`,\n        borderRadius: \"20px\",\n        overflow: \"hidden\",\n        backgroundImage: image ? `url(${image})` : \"none\",\n        backgroundPosition: \"center\",\n        backgroundSize: \"cover\",\n        backgroundRepeat: \"no-repeat\",\n        boxShadow: \"0px 0px 40px rgba(0, 0, 0, 0.2)\",\n        margin: `${margin}px`,\n        marginLeft: \"auto\",\n        marginRight: \"auto\",\n        flexDirection: \"column\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        textAlign: \"center\",\n        color: theme.textColor,\n        width: `${width}px`,\n        position: \"relative\",\n        transition: \"all 0.3s ease-in-out\",\n        ...styles.card,\n      },\n      `\n      & {\n        transform: scale(0.95);\n      }\n      &:hover {\n        transform: scale(1);\n      }\n      &:active {\n        transform: scale(0.95);\n      }\n      `\n    );\n\n    const Parent = styled.div({\n      padding: margin,\n      width: \"100%\",\n      ...styles.div,\n    });\n\n    const Title = styled.h2({\n      color: \"white\",\n      zIndex: \"2\",\n      fontSize: \"2em\",\n      fontWeight: \"bolder\",\n      ...styles.title,\n    });\n\n    const Text = styled.p({\n      color: \"white\",\n      fontWeight: \"bolder\",\n      zIndex: \"2\",\n      fontSize: \"1em\",\n      margin: \"0\",\n      ...styles.text,\n    });\n\n    const Filter = styled.div({\n      backgroundColor: \"rgba(0, 0, 0, 0.5)\",\n      height: \"100%\",\n      width: \"100%\",\n      position: \"absolute\",\n      top: \"0\",\n      left: \"0\",\n      zIndex: \"1\",\n      ...styles.filter,\n    });\n\n    // if the text is a string, convert it to an array\n    if (typeof text === \"string\") {\n      text = [text];\n    }\n\n    let texts = [];\n    for (let i = 0; i < text.length; i++) {\n      const t = text[i];\n      texts.push(<Text>{t}</Text>);\n    }\n\n    return (\n      <Parent>\n        <Card onClick={this.onClick}>\n          <Filter />\n          <Title>{title}</Title>\n          {texts}\n        </Card>\n      </Parent>\n    );\n  };\n\n  private onClick = (): void => {\n    if (this.props.args.url) {\n      window.open(this.props.args.url, \"_blank\");\n    }\n    Streamlit.setComponentValue(true);\n  };\n}\n\nexport default withStreamlitConnection(Card);\n", "import React from \"react\"\nimport ReactDOM from \"react-dom\"\nimport Card from \"./stCard\"\n\nReactDOM.render(\n  <React.StrictMode>\n    <Card />\n  </React.StrictMode>,\n  document.getElementById(\"root\")\n)\n"], "sourceRoot": ""}