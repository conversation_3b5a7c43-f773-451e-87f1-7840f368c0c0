(window.webpackJsonp=window.webpackJsonp||[]).push([[0],{17:function(e,t,n){e.exports=n(27)},27:function(e,t,n){"use strict";n.r(t);var a=n(6),o=n.n(a),r=n(15),i=n.n(r),c=n(7),l=n(0),s=n(4),u=n(2),d=n(1),p=n(3),m=n(10),g=n(11),f=function(e){function t(){var e,n;Object(l.a)(this,t);for(var a=arguments.length,r=new Array(a),i=0;i<a;i++)r[i]=arguments[i];return(n=Object(u.a)(this,(e=Object(d.a)(t)).call.apply(e,[this].concat(r)))).render=function(){var e=n.props.args,t=e.title,a=e.text,r=e.image,i=e.styles,l=n.props.theme;if(!l)return o.a.createElement("div",null,"Theme is undefined, please check streamlit version.");var s=i.card.margin||40,u=g.a.div(Object(c.a)({display:"flex",cursor:"pointer",fontFamily:"".concat(l.font,", 'Segoe UI', 'Roboto', sans-serif"),height:"".concat(250,"px"),borderRadius:"20px",overflow:"hidden",backgroundImage:r?"url(".concat(r,")"):"none",backgroundPosition:"center",backgroundSize:"cover",backgroundRepeat:"no-repeat",boxShadow:"0px 0px 40px rgba(0, 0, 0, 0.2)",margin:"".concat(s,"px"),marginLeft:"auto",marginRight:"auto",flexDirection:"column",justifyContent:"center",alignItems:"center",textAlign:"center",color:l.textColor,width:"".concat(300,"px"),position:"relative",transition:"all 0.3s ease-in-out"},i.card),"\n      & {\n        transform: scale(0.95);\n      }\n      &:hover {\n        transform: scale(1);\n      }\n      &:active {\n        transform: scale(0.95);\n      }\n      "),d=g.a.div(Object(c.a)({padding:s,width:"100%"},i.div)),p=g.a.h2(Object(c.a)({color:"white",zIndex:"2",fontSize:"2em",fontWeight:"bolder"},i.title)),m=g.a.p(Object(c.a)({color:"white",fontWeight:"bolder",zIndex:"2",fontSize:"1em",margin:"0"},i.text)),f=g.a.div(Object(c.a)({backgroundColor:"rgba(0, 0, 0, 0.5)",height:"100%",width:"100%",position:"absolute",top:"0",left:"0",zIndex:"1"},i.filter));"string"===typeof a&&(a=[a]);for(var b=[],h=0;h<a.length;h++){var v=a[h];b.push(o.a.createElement(m,null,v))}return o.a.createElement(d,null,o.a.createElement(u,{onClick:n.onClick},o.a.createElement(f,null),o.a.createElement(p,null,t),b))},n.onClick=function(){n.props.args.url&&window.open(n.props.args.url,"_blank"),m.a.setComponentValue(!0)},n}return Object(p.a)(t,e),Object(s.a)(t,[{key:"componentDidUpdate",value:function(){m.a.setFrameHeight()}},{key:"componentDidMount",value:function(){m.a.setFrameHeight()}}]),t}(m.b),b=Object(m.c)(f);i.a.render(o.a.createElement(o.a.StrictMode,null,o.a.createElement(b,null)),document.getElementById("root"))}},[[17,1,2]]]);
//# sourceMappingURL=main.80cd56d8.chunk.js.map