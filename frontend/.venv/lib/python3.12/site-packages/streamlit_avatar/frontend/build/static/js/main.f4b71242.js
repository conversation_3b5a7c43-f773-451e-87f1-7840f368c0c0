/*! For license information please see main.f4b71242.js.LICENSE.txt */
(()=>{"use strict";var t={394:(t,e,n)=>{var r=n(956),i={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},o={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},s={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},a={};function l(t){return r.isMemo(t)?s:a[t.$$typeof]||i}a[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},a[r.Memo]=s;var u=Object.defineProperty,c=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,f=Object.getOwnPropertyDescriptor,h=Object.getPrototypeOf,p=Object.prototype;t.exports=function t(e,n,r){if("string"!==typeof n){if(p){var i=h(n);i&&i!==p&&t(e,i,r)}var s=c(n);d&&(s=s.concat(d(n)));for(var a=l(e),y=l(n),b=0;b<s.length;++b){var m=s[b];if(!o[m]&&(!r||!r[m])&&(!y||!y[m])&&(!a||!a[m])){var g=f(n,m);try{u(e,m,g)}catch(v){}}}}return e}},4:t=>{var e=Object.getOwnPropertySymbols,n=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable;t.exports=function(){try{if(!Object.assign)return!1;var t=new String("abc");if(t[5]="de","5"===Object.getOwnPropertyNames(t)[0])return!1;for(var e={},n=0;n<10;n++)e["_"+String.fromCharCode(n)]=n;if("0123456789"!==Object.getOwnPropertyNames(e).map((function(t){return e[t]})).join(""))return!1;var r={};return"abcdefghijklmnopqrst".split("").forEach((function(t){r[t]=t})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},r)).join("")}catch(i){return!1}}()?Object.assign:function(t,i){for(var o,s,a=function(t){if(null===t||void 0===t)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(t)}(t),l=1;l<arguments.length;l++){for(var u in o=Object(arguments[l]))n.call(o,u)&&(a[u]=o[u]);if(e){s=e(o);for(var c=0;c<s.length;c++)r.call(o,s[c])&&(a[s[c]]=o[s[c]])}}return a}},678:(t,e,n)=>{var r=n(108),i=n(4),o=n(841);function s(t){for(var e="https://reactjs.org/docs/error-decoder.html?invariant="+t,n=1;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}if(!r)throw Error(s(227));function a(t,e,n,r,i,o,s,a,l){var u=Array.prototype.slice.call(arguments,3);try{e.apply(n,u)}catch(c){this.onError(c)}}var l=!1,u=null,c=!1,d=null,f={onError:function(t){l=!0,u=t}};function h(t,e,n,r,i,o,s,c,d){l=!1,u=null,a.apply(f,arguments)}var p=null,y=null,b=null;function m(t,e,n){var r=t.type||"unknown-event";t.currentTarget=b(n),function(t,e,n,r,i,o,a,f,p){if(h.apply(this,arguments),l){if(!l)throw Error(s(198));var y=u;l=!1,u=null,c||(c=!0,d=y)}}(r,e,void 0,t),t.currentTarget=null}var g=null,v={};function w(){if(g)for(var t in v){var e=v[t],n=g.indexOf(t);if(!(-1<n))throw Error(s(96,t));if(!S[n]){if(!e.extractEvents)throw Error(s(97,t));for(var r in S[n]=e,n=e.eventTypes){var i=void 0,o=n[r],a=e,l=r;if(T.hasOwnProperty(l))throw Error(s(99,l));T[l]=o;var u=o.phasedRegistrationNames;if(u){for(i in u)u.hasOwnProperty(i)&&_(u[i],a,l);i=!0}else o.registrationName?(_(o.registrationName,a,l),i=!0):i=!1;if(!i)throw Error(s(98,r,t))}}}}function _(t,e,n){if(I[t])throw Error(s(100,t));I[t]=e,x[t]=e.eventTypes[n].dependencies}var S=[],T={},I={},x={};function E(t){var e,n=!1;for(e in t)if(t.hasOwnProperty(e)){var r=t[e];if(!v.hasOwnProperty(e)||v[e]!==r){if(v[e])throw Error(s(102,e));v[e]=r,n=!0}}n&&w()}var k=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),O=null,A=null,C=null;function D(t){if(t=y(t)){if("function"!==typeof O)throw Error(s(280));var e=t.stateNode;e&&(e=p(e),O(t.stateNode,t.type,e))}}function B(t){A?C?C.push(t):C=[t]:A=t}function N(){if(A){var t=A,e=C;if(C=A=null,D(t),e)for(t=0;t<e.length;t++)D(e[t])}}function M(t,e){return t(e)}function F(t,e,n,r,i){return t(e,n,r,i)}function P(){}var L=M,U=!1,R=!1;function z(){null===A&&null===C||(P(),N())}function V(t,e,n){if(R)return t(e,n);R=!0;try{return L(t,e,n)}finally{R=!1,z()}}var j=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,W=Object.prototype.hasOwnProperty,H={},Y={};function $(t,e,n,r,i,o){this.acceptsBooleans=2===e||3===e||4===e,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=t,this.type=e,this.sanitizeURL=o}var Q={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(t){Q[t]=new $(t,0,!1,t,null,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(t){var e=t[0];Q[e]=new $(e,1,!1,t[1],null,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(t){Q[t]=new $(t,2,!1,t.toLowerCase(),null,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(t){Q[t]=new $(t,2,!1,t,null,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(t){Q[t]=new $(t,3,!1,t.toLowerCase(),null,!1)})),["checked","multiple","muted","selected"].forEach((function(t){Q[t]=new $(t,3,!0,t,null,!1)})),["capture","download"].forEach((function(t){Q[t]=new $(t,4,!1,t,null,!1)})),["cols","rows","size","span"].forEach((function(t){Q[t]=new $(t,6,!1,t,null,!1)})),["rowSpan","start"].forEach((function(t){Q[t]=new $(t,5,!1,t.toLowerCase(),null,!1)}));var K=/[\-:]([a-z])/g;function q(t){return t[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(t){var e=t.replace(K,q);Q[e]=new $(e,1,!1,t,null,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(t){var e=t.replace(K,q);Q[e]=new $(e,1,!1,t,"http://www.w3.org/1999/xlink",!1)})),["xml:base","xml:lang","xml:space"].forEach((function(t){var e=t.replace(K,q);Q[e]=new $(e,1,!1,t,"http://www.w3.org/XML/1998/namespace",!1)})),["tabIndex","crossOrigin"].forEach((function(t){Q[t]=new $(t,1,!1,t.toLowerCase(),null,!1)})),Q.xlinkHref=new $("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0),["src","href","action","formAction"].forEach((function(t){Q[t]=new $(t,1,!1,t.toLowerCase(),null,!0)}));var G=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function J(t,e,n,r){var i=Q.hasOwnProperty(e)?Q[e]:null;(null!==i?0===i.type:!r&&(2<e.length&&("o"===e[0]||"O"===e[0])&&("n"===e[1]||"N"===e[1])))||(function(t,e,n,r){if(null===e||"undefined"===typeof e||function(t,e,n,r){if(null!==n&&0===n.type)return!1;switch(typeof e){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(t=t.toLowerCase().slice(0,5))&&"aria-"!==t);default:return!1}}(t,e,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!e;case 4:return!1===e;case 5:return isNaN(e);case 6:return isNaN(e)||1>e}return!1}(e,n,i,r)&&(n=null),r||null===i?function(t){return!!W.call(Y,t)||!W.call(H,t)&&(j.test(t)?Y[t]=!0:(H[t]=!0,!1))}(e)&&(null===n?t.removeAttribute(e):t.setAttribute(e,""+n)):i.mustUseProperty?t[i.propertyName]=null===n?3!==i.type&&"":n:(e=i.attributeName,r=i.attributeNamespace,null===n?t.removeAttribute(e):(n=3===(i=i.type)||4===i&&!0===n?"":""+n,r?t.setAttributeNS(r,e,n):t.setAttribute(e,n))))}G.hasOwnProperty("ReactCurrentDispatcher")||(G.ReactCurrentDispatcher={current:null}),G.hasOwnProperty("ReactCurrentBatchConfig")||(G.ReactCurrentBatchConfig={suspense:null});var X=/^(.*)[\\\/]/,Z="function"===typeof Symbol&&Symbol.for,tt=Z?Symbol.for("react.element"):60103,et=Z?Symbol.for("react.portal"):60106,nt=Z?Symbol.for("react.fragment"):60107,rt=Z?Symbol.for("react.strict_mode"):60108,it=Z?Symbol.for("react.profiler"):60114,ot=Z?Symbol.for("react.provider"):60109,st=Z?Symbol.for("react.context"):60110,at=Z?Symbol.for("react.concurrent_mode"):60111,lt=Z?Symbol.for("react.forward_ref"):60112,ut=Z?Symbol.for("react.suspense"):60113,ct=Z?Symbol.for("react.suspense_list"):60120,dt=Z?Symbol.for("react.memo"):60115,ft=Z?Symbol.for("react.lazy"):60116,ht=Z?Symbol.for("react.block"):60121,pt="function"===typeof Symbol&&Symbol.iterator;function yt(t){return null===t||"object"!==typeof t?null:"function"===typeof(t=pt&&t[pt]||t["@@iterator"])?t:null}function bt(t){if(null==t)return null;if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t;switch(t){case nt:return"Fragment";case et:return"Portal";case it:return"Profiler";case rt:return"StrictMode";case ut:return"Suspense";case ct:return"SuspenseList"}if("object"===typeof t)switch(t.$$typeof){case st:return"Context.Consumer";case ot:return"Context.Provider";case lt:var e=t.render;return e=e.displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case dt:return bt(t.type);case ht:return bt(t.render);case ft:if(t=1===t._status?t._result:null)return bt(t)}return null}function mt(t){var e="";do{t:switch(t.tag){case 3:case 4:case 6:case 7:case 10:case 9:var n="";break t;default:var r=t._debugOwner,i=t._debugSource,o=bt(t.type);n=null,r&&(n=bt(r.type)),r=o,o="",i?o=" (at "+i.fileName.replace(X,"")+":"+i.lineNumber+")":n&&(o=" (created by "+n+")"),n="\n    in "+(r||"Unknown")+o}e+=n,t=t.return}while(t);return e}function gt(t){switch(typeof t){case"boolean":case"number":case"object":case"string":case"undefined":return t;default:return""}}function vt(t){var e=t.type;return(t=t.nodeName)&&"input"===t.toLowerCase()&&("checkbox"===e||"radio"===e)}function wt(t){t._valueTracker||(t._valueTracker=function(t){var e=vt(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),r=""+t[e];if(!t.hasOwnProperty(e)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var i=n.get,o=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return i.call(this)},set:function(t){r=""+t,o.call(this,t)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(t){r=""+t},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}(t))}function _t(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),r="";return t&&(r=vt(t)?t.checked?"true":"false":t.value),(t=r)!==n&&(e.setValue(t),!0)}function St(t,e){var n=e.checked;return i({},e,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:t._wrapperState.initialChecked})}function Tt(t,e){var n=null==e.defaultValue?"":e.defaultValue,r=null!=e.checked?e.checked:e.defaultChecked;n=gt(null!=e.value?e.value:n),t._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===e.type||"radio"===e.type?null!=e.checked:null!=e.value}}function It(t,e){null!=(e=e.checked)&&J(t,"checked",e,!1)}function xt(t,e){It(t,e);var n=gt(e.value),r=e.type;if(null!=n)"number"===r?(0===n&&""===t.value||t.value!=n)&&(t.value=""+n):t.value!==""+n&&(t.value=""+n);else if("submit"===r||"reset"===r)return void t.removeAttribute("value");e.hasOwnProperty("value")?kt(t,e.type,n):e.hasOwnProperty("defaultValue")&&kt(t,e.type,gt(e.defaultValue)),null==e.checked&&null!=e.defaultChecked&&(t.defaultChecked=!!e.defaultChecked)}function Et(t,e,n){if(e.hasOwnProperty("value")||e.hasOwnProperty("defaultValue")){var r=e.type;if(!("submit"!==r&&"reset"!==r||void 0!==e.value&&null!==e.value))return;e=""+t._wrapperState.initialValue,n||e===t.value||(t.value=e),t.defaultValue=e}""!==(n=t.name)&&(t.name=""),t.defaultChecked=!!t._wrapperState.initialChecked,""!==n&&(t.name=n)}function kt(t,e,n){"number"===e&&t.ownerDocument.activeElement===t||(null==n?t.defaultValue=""+t._wrapperState.initialValue:t.defaultValue!==""+n&&(t.defaultValue=""+n))}function Ot(t,e){return t=i({children:void 0},e),(e=function(t){var e="";return r.Children.forEach(t,(function(t){null!=t&&(e+=t)})),e}(e.children))&&(t.children=e),t}function At(t,e,n,r){if(t=t.options,e){e={};for(var i=0;i<n.length;i++)e["$"+n[i]]=!0;for(n=0;n<t.length;n++)i=e.hasOwnProperty("$"+t[n].value),t[n].selected!==i&&(t[n].selected=i),i&&r&&(t[n].defaultSelected=!0)}else{for(n=""+gt(n),e=null,i=0;i<t.length;i++){if(t[i].value===n)return t[i].selected=!0,void(r&&(t[i].defaultSelected=!0));null!==e||t[i].disabled||(e=t[i])}null!==e&&(e.selected=!0)}}function Ct(t,e){if(null!=e.dangerouslySetInnerHTML)throw Error(s(91));return i({},e,{value:void 0,defaultValue:void 0,children:""+t._wrapperState.initialValue})}function Dt(t,e){var n=e.value;if(null==n){if(n=e.children,e=e.defaultValue,null!=n){if(null!=e)throw Error(s(92));if(Array.isArray(n)){if(!(1>=n.length))throw Error(s(93));n=n[0]}e=n}null==e&&(e=""),n=e}t._wrapperState={initialValue:gt(n)}}function Bt(t,e){var n=gt(e.value),r=gt(e.defaultValue);null!=n&&((n=""+n)!==t.value&&(t.value=n),null==e.defaultValue&&t.defaultValue!==n&&(t.defaultValue=n)),null!=r&&(t.defaultValue=""+r)}function Nt(t){var e=t.textContent;e===t._wrapperState.initialValue&&""!==e&&null!==e&&(t.value=e)}var Mt="http://www.w3.org/1999/xhtml",Ft="http://www.w3.org/2000/svg";function Pt(t){switch(t){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Lt(t,e){return null==t||"http://www.w3.org/1999/xhtml"===t?Pt(e):"http://www.w3.org/2000/svg"===t&&"foreignObject"===e?"http://www.w3.org/1999/xhtml":t}var Ut,Rt,zt=(Rt=function(t,e){if(t.namespaceURI!==Ft||"innerHTML"in t)t.innerHTML=e;else{for((Ut=Ut||document.createElement("div")).innerHTML="<svg>"+e.valueOf().toString()+"</svg>",e=Ut.firstChild;t.firstChild;)t.removeChild(t.firstChild);for(;e.firstChild;)t.appendChild(e.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(t,e,n,r){MSApp.execUnsafeLocalFunction((function(){return Rt(t,e)}))}:Rt);function Vt(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&3===n.nodeType)return void(n.nodeValue=e)}t.textContent=e}function jt(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var Wt={animationend:jt("Animation","AnimationEnd"),animationiteration:jt("Animation","AnimationIteration"),animationstart:jt("Animation","AnimationStart"),transitionend:jt("Transition","TransitionEnd")},Ht={},Yt={};function $t(t){if(Ht[t])return Ht[t];if(!Wt[t])return t;var e,n=Wt[t];for(e in n)if(n.hasOwnProperty(e)&&e in Yt)return Ht[t]=n[e];return t}k&&(Yt=document.createElement("div").style,"AnimationEvent"in window||(delete Wt.animationend.animation,delete Wt.animationiteration.animation,delete Wt.animationstart.animation),"TransitionEvent"in window||delete Wt.transitionend.transition);var Qt=$t("animationend"),Kt=$t("animationiteration"),qt=$t("animationstart"),Gt=$t("transitionend"),Jt="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Xt=new("function"===typeof WeakMap?WeakMap:Map);function Zt(t){var e=Xt.get(t);return void 0===e&&(e=new Map,Xt.set(t,e)),e}function te(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do{0!==(1026&(e=t).effectTag)&&(n=e.return),t=e.return}while(t)}return 3===e.tag?n:null}function ee(t){if(13===t.tag){var e=t.memoizedState;if(null===e&&(null!==(t=t.alternate)&&(e=t.memoizedState)),null!==e)return e.dehydrated}return null}function ne(t){if(te(t)!==t)throw Error(s(188))}function re(t){if(t=function(t){var e=t.alternate;if(!e){if(null===(e=te(t)))throw Error(s(188));return e!==t?null:t}for(var n=t,r=e;;){var i=n.return;if(null===i)break;var o=i.alternate;if(null===o){if(null!==(r=i.return)){n=r;continue}break}if(i.child===o.child){for(o=i.child;o;){if(o===n)return ne(i),t;if(o===r)return ne(i),e;o=o.sibling}throw Error(s(188))}if(n.return!==r.return)n=i,r=o;else{for(var a=!1,l=i.child;l;){if(l===n){a=!0,n=i,r=o;break}if(l===r){a=!0,r=i,n=o;break}l=l.sibling}if(!a){for(l=o.child;l;){if(l===n){a=!0,n=o,r=i;break}if(l===r){a=!0,r=o,n=i;break}l=l.sibling}if(!a)throw Error(s(189))}}if(n.alternate!==r)throw Error(s(190))}if(3!==n.tag)throw Error(s(188));return n.stateNode.current===n?t:e}(t),!t)return null;for(var e=t;;){if(5===e.tag||6===e.tag)return e;if(e.child)e.child.return=e,e=e.child;else{if(e===t)break;for(;!e.sibling;){if(!e.return||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}}return null}function ie(t,e){if(null==e)throw Error(s(30));return null==t?e:Array.isArray(t)?Array.isArray(e)?(t.push.apply(t,e),t):(t.push(e),t):Array.isArray(e)?[t].concat(e):[t,e]}function oe(t,e,n){Array.isArray(t)?t.forEach(e,n):t&&e.call(n,t)}var se=null;function ae(t){if(t){var e=t._dispatchListeners,n=t._dispatchInstances;if(Array.isArray(e))for(var r=0;r<e.length&&!t.isPropagationStopped();r++)m(t,e[r],n[r]);else e&&m(t,e,n);t._dispatchListeners=null,t._dispatchInstances=null,t.isPersistent()||t.constructor.release(t)}}function le(t){if(null!==t&&(se=ie(se,t)),t=se,se=null,t){if(oe(t,ae),se)throw Error(s(95));if(c)throw t=d,c=!1,d=null,t}}function ue(t){return(t=t.target||t.srcElement||window).correspondingUseElement&&(t=t.correspondingUseElement),3===t.nodeType?t.parentNode:t}function ce(t){if(!k)return!1;var e=(t="on"+t)in document;return e||((e=document.createElement("div")).setAttribute(t,"return;"),e="function"===typeof e[t]),e}var de=[];function fe(t){t.topLevelType=null,t.nativeEvent=null,t.targetInst=null,t.ancestors.length=0,10>de.length&&de.push(t)}function he(t,e,n,r){if(de.length){var i=de.pop();return i.topLevelType=t,i.eventSystemFlags=r,i.nativeEvent=e,i.targetInst=n,i}return{topLevelType:t,eventSystemFlags:r,nativeEvent:e,targetInst:n,ancestors:[]}}function pe(t){var e=t.targetInst,n=e;do{if(!n){t.ancestors.push(n);break}var r=n;if(3===r.tag)r=r.stateNode.containerInfo;else{for(;r.return;)r=r.return;r=3!==r.tag?null:r.stateNode.containerInfo}if(!r)break;5!==(e=n.tag)&&6!==e||t.ancestors.push(n),n=Nn(r)}while(n);for(n=0;n<t.ancestors.length;n++){e=t.ancestors[n];var i=ue(t.nativeEvent);r=t.topLevelType;var o=t.nativeEvent,s=t.eventSystemFlags;0===n&&(s|=64);for(var a=null,l=0;l<S.length;l++){var u=S[l];u&&(u=u.extractEvents(r,e,o,i,s))&&(a=ie(a,u))}le(a)}}function ye(t,e,n){if(!n.has(t)){switch(t){case"scroll":qe(e,"scroll",!0);break;case"focus":case"blur":qe(e,"focus",!0),qe(e,"blur",!0),n.set("blur",null),n.set("focus",null);break;case"cancel":case"close":ce(t)&&qe(e,t,!0);break;case"invalid":case"submit":case"reset":break;default:-1===Jt.indexOf(t)&&Ke(t,e)}n.set(t,null)}}var be,me,ge,ve=!1,we=[],_e=null,Se=null,Te=null,Ie=new Map,xe=new Map,Ee=[],ke="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput close cancel copy cut paste click change contextmenu reset submit".split(" "),Oe="focus blur dragenter dragleave mouseover mouseout pointerover pointerout gotpointercapture lostpointercapture".split(" ");function Ae(t,e,n,r,i){return{blockedOn:t,topLevelType:e,eventSystemFlags:32|n,nativeEvent:i,container:r}}function Ce(t,e){switch(t){case"focus":case"blur":_e=null;break;case"dragenter":case"dragleave":Se=null;break;case"mouseover":case"mouseout":Te=null;break;case"pointerover":case"pointerout":Ie.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":xe.delete(e.pointerId)}}function De(t,e,n,r,i,o){return null===t||t.nativeEvent!==o?(t=Ae(e,n,r,i,o),null!==e&&(null!==(e=Mn(e))&&me(e)),t):(t.eventSystemFlags|=r,t)}function Be(t){var e=Nn(t.target);if(null!==e){var n=te(e);if(null!==n)if(13===(e=n.tag)){if(null!==(e=ee(n)))return t.blockedOn=e,void o.unstable_runWithPriority(t.priority,(function(){ge(n)}))}else if(3===e&&n.stateNode.hydrate)return void(t.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}t.blockedOn=null}function Ne(t){if(null!==t.blockedOn)return!1;var e=Ze(t.topLevelType,t.eventSystemFlags,t.container,t.nativeEvent);if(null!==e){var n=Mn(e);return null!==n&&me(n),t.blockedOn=e,!1}return!0}function Me(t,e,n){Ne(t)&&n.delete(e)}function Fe(){for(ve=!1;0<we.length;){var t=we[0];if(null!==t.blockedOn){null!==(t=Mn(t.blockedOn))&&be(t);break}var e=Ze(t.topLevelType,t.eventSystemFlags,t.container,t.nativeEvent);null!==e?t.blockedOn=e:we.shift()}null!==_e&&Ne(_e)&&(_e=null),null!==Se&&Ne(Se)&&(Se=null),null!==Te&&Ne(Te)&&(Te=null),Ie.forEach(Me),xe.forEach(Me)}function Pe(t,e){t.blockedOn===e&&(t.blockedOn=null,ve||(ve=!0,o.unstable_scheduleCallback(o.unstable_NormalPriority,Fe)))}function Le(t){function e(e){return Pe(e,t)}if(0<we.length){Pe(we[0],t);for(var n=1;n<we.length;n++){var r=we[n];r.blockedOn===t&&(r.blockedOn=null)}}for(null!==_e&&Pe(_e,t),null!==Se&&Pe(Se,t),null!==Te&&Pe(Te,t),Ie.forEach(e),xe.forEach(e),n=0;n<Ee.length;n++)(r=Ee[n]).blockedOn===t&&(r.blockedOn=null);for(;0<Ee.length&&null===(n=Ee[0]).blockedOn;)Be(n),null===n.blockedOn&&Ee.shift()}var Ue={},Re=new Map,ze=new Map,Ve=["abort","abort",Qt,"animationEnd",Kt,"animationIteration",qt,"animationStart","canplay","canPlay","canplaythrough","canPlayThrough","durationchange","durationChange","emptied","emptied","encrypted","encrypted","ended","ended","error","error","gotpointercapture","gotPointerCapture","load","load","loadeddata","loadedData","loadedmetadata","loadedMetadata","loadstart","loadStart","lostpointercapture","lostPointerCapture","playing","playing","progress","progress","seeking","seeking","stalled","stalled","suspend","suspend","timeupdate","timeUpdate",Gt,"transitionEnd","waiting","waiting"];function je(t,e){for(var n=0;n<t.length;n+=2){var r=t[n],i=t[n+1],o="on"+(i[0].toUpperCase()+i.slice(1));o={phasedRegistrationNames:{bubbled:o,captured:o+"Capture"},dependencies:[r],eventPriority:e},ze.set(r,e),Re.set(r,o),Ue[i]=o}}je("blur blur cancel cancel click click close close contextmenu contextMenu copy copy cut cut auxclick auxClick dblclick doubleClick dragend dragEnd dragstart dragStart drop drop focus focus input input invalid invalid keydown keyDown keypress keyPress keyup keyUp mousedown mouseDown mouseup mouseUp paste paste pause pause play play pointercancel pointerCancel pointerdown pointerDown pointerup pointerUp ratechange rateChange reset reset seeked seeked submit submit touchcancel touchCancel touchend touchEnd touchstart touchStart volumechange volumeChange".split(" "),0),je("drag drag dragenter dragEnter dragexit dragExit dragleave dragLeave dragover dragOver mousemove mouseMove mouseout mouseOut mouseover mouseOver pointermove pointerMove pointerout pointerOut pointerover pointerOver scroll scroll toggle toggle touchmove touchMove wheel wheel".split(" "),1),je(Ve,2);for(var We="change selectionchange textInput compositionstart compositionend compositionupdate".split(" "),He=0;He<We.length;He++)ze.set(We[He],0);var Ye=o.unstable_UserBlockingPriority,$e=o.unstable_runWithPriority,Qe=!0;function Ke(t,e){qe(e,t,!1)}function qe(t,e,n){var r=ze.get(e);switch(void 0===r?2:r){case 0:r=Ge.bind(null,e,1,t);break;case 1:r=Je.bind(null,e,1,t);break;default:r=Xe.bind(null,e,1,t)}n?t.addEventListener(e,r,!0):t.addEventListener(e,r,!1)}function Ge(t,e,n,r){U||P();var i=Xe,o=U;U=!0;try{F(i,t,e,n,r)}finally{(U=o)||z()}}function Je(t,e,n,r){$e(Ye,Xe.bind(null,t,e,n,r))}function Xe(t,e,n,r){if(Qe)if(0<we.length&&-1<ke.indexOf(t))t=Ae(null,t,e,n,r),we.push(t);else{var i=Ze(t,e,n,r);if(null===i)Ce(t,r);else if(-1<ke.indexOf(t))t=Ae(i,t,e,n,r),we.push(t);else if(!function(t,e,n,r,i){switch(e){case"focus":return _e=De(_e,t,e,n,r,i),!0;case"dragenter":return Se=De(Se,t,e,n,r,i),!0;case"mouseover":return Te=De(Te,t,e,n,r,i),!0;case"pointerover":var o=i.pointerId;return Ie.set(o,De(Ie.get(o)||null,t,e,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,xe.set(o,De(xe.get(o)||null,t,e,n,r,i)),!0}return!1}(i,t,e,n,r)){Ce(t,r),t=he(t,r,null,e);try{V(pe,t)}finally{fe(t)}}}}function Ze(t,e,n,r){if(null!==(n=Nn(n=ue(r)))){var i=te(n);if(null===i)n=null;else{var o=i.tag;if(13===o){if(null!==(n=ee(i)))return n;n=null}else if(3===o){if(i.stateNode.hydrate)return 3===i.tag?i.stateNode.containerInfo:null;n=null}else i!==n&&(n=null)}}t=he(t,r,n,e);try{V(pe,t)}finally{fe(t)}return null}var tn={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},en=["Webkit","ms","Moz","O"];function nn(t,e,n){return null==e||"boolean"===typeof e||""===e?"":n||"number"!==typeof e||0===e||tn.hasOwnProperty(t)&&tn[t]?(""+e).trim():e+"px"}function rn(t,e){for(var n in t=t.style,e)if(e.hasOwnProperty(n)){var r=0===n.indexOf("--"),i=nn(n,e[n],r);"float"===n&&(n="cssFloat"),r?t.setProperty(n,i):t[n]=i}}Object.keys(tn).forEach((function(t){en.forEach((function(e){e=e+t.charAt(0).toUpperCase()+t.substring(1),tn[e]=tn[t]}))}));var on=i({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function sn(t,e){if(e){if(on[t]&&(null!=e.children||null!=e.dangerouslySetInnerHTML))throw Error(s(137,t,""));if(null!=e.dangerouslySetInnerHTML){if(null!=e.children)throw Error(s(60));if("object"!==typeof e.dangerouslySetInnerHTML||!("__html"in e.dangerouslySetInnerHTML))throw Error(s(61))}if(null!=e.style&&"object"!==typeof e.style)throw Error(s(62,""))}}function an(t,e){if(-1===t.indexOf("-"))return"string"===typeof e.is;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ln=Mt;function un(t,e){var n=Zt(t=9===t.nodeType||11===t.nodeType?t:t.ownerDocument);e=x[e];for(var r=0;r<e.length;r++)ye(e[r],t,n)}function cn(){}function dn(t){if("undefined"===typeof(t=t||("undefined"!==typeof document?document:void 0)))return null;try{return t.activeElement||t.body}catch(e){return t.body}}function fn(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function hn(t,e){var n,r=fn(t);for(t=0;r;){if(3===r.nodeType){if(n=t+r.textContent.length,t<=e&&n>=e)return{node:r,offset:e-t};t=n}t:{for(;r;){if(r.nextSibling){r=r.nextSibling;break t}r=r.parentNode}r=void 0}r=fn(r)}}function pn(t,e){return!(!t||!e)&&(t===e||(!t||3!==t.nodeType)&&(e&&3===e.nodeType?pn(t,e.parentNode):"contains"in t?t.contains(e):!!t.compareDocumentPosition&&!!(16&t.compareDocumentPosition(e))))}function yn(){for(var t=window,e=dn();e instanceof t.HTMLIFrameElement;){try{var n="string"===typeof e.contentWindow.location.href}catch(r){n=!1}if(!n)break;e=dn((t=e.contentWindow).document)}return e}function bn(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&("input"===e&&("text"===t.type||"search"===t.type||"tel"===t.type||"url"===t.type||"password"===t.type)||"textarea"===e||"true"===t.contentEditable)}var mn="$",gn="/$",vn="$?",wn="$!",_n=null,Sn=null;function Tn(t,e){switch(t){case"button":case"input":case"select":case"textarea":return!!e.autoFocus}return!1}function In(t,e){return"textarea"===t||"option"===t||"noscript"===t||"string"===typeof e.children||"number"===typeof e.children||"object"===typeof e.dangerouslySetInnerHTML&&null!==e.dangerouslySetInnerHTML&&null!=e.dangerouslySetInnerHTML.__html}var xn="function"===typeof setTimeout?setTimeout:void 0,En="function"===typeof clearTimeout?clearTimeout:void 0;function kn(t){for(;null!=t;t=t.nextSibling){var e=t.nodeType;if(1===e||3===e)break}return t}function On(t){t=t.previousSibling;for(var e=0;t;){if(8===t.nodeType){var n=t.data;if(n===mn||n===wn||n===vn){if(0===e)return t;e--}else n===gn&&e++}t=t.previousSibling}return null}var An=Math.random().toString(36).slice(2),Cn="__reactInternalInstance$"+An,Dn="__reactEventHandlers$"+An,Bn="__reactContainere$"+An;function Nn(t){var e=t[Cn];if(e)return e;for(var n=t.parentNode;n;){if(e=n[Bn]||n[Cn]){if(n=e.alternate,null!==e.child||null!==n&&null!==n.child)for(t=On(t);null!==t;){if(n=t[Cn])return n;t=On(t)}return e}n=(t=n).parentNode}return null}function Mn(t){return!(t=t[Cn]||t[Bn])||5!==t.tag&&6!==t.tag&&13!==t.tag&&3!==t.tag?null:t}function Fn(t){if(5===t.tag||6===t.tag)return t.stateNode;throw Error(s(33))}function Pn(t){return t[Dn]||null}function Ln(t){do{t=t.return}while(t&&5!==t.tag);return t||null}function Un(t,e){var n=t.stateNode;if(!n)return null;var r=p(n);if(!r)return null;n=r[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(t=t.type)||"input"===t||"select"===t||"textarea"===t)),t=!r;break t;default:t=!1}if(t)return null;if(n&&"function"!==typeof n)throw Error(s(231,e,typeof n));return n}function Rn(t,e,n){(e=Un(t,n.dispatchConfig.phasedRegistrationNames[e]))&&(n._dispatchListeners=ie(n._dispatchListeners,e),n._dispatchInstances=ie(n._dispatchInstances,t))}function zn(t){if(t&&t.dispatchConfig.phasedRegistrationNames){for(var e=t._targetInst,n=[];e;)n.push(e),e=Ln(e);for(e=n.length;0<e--;)Rn(n[e],"captured",t);for(e=0;e<n.length;e++)Rn(n[e],"bubbled",t)}}function Vn(t,e,n){t&&n&&n.dispatchConfig.registrationName&&(e=Un(t,n.dispatchConfig.registrationName))&&(n._dispatchListeners=ie(n._dispatchListeners,e),n._dispatchInstances=ie(n._dispatchInstances,t))}function jn(t){t&&t.dispatchConfig.registrationName&&Vn(t._targetInst,null,t)}function Wn(t){oe(t,zn)}var Hn=null,Yn=null,$n=null;function Qn(){if($n)return $n;var t,e,n=Yn,r=n.length,i="value"in Hn?Hn.value:Hn.textContent,o=i.length;for(t=0;t<r&&n[t]===i[t];t++);var s=r-t;for(e=1;e<=s&&n[r-e]===i[o-e];e++);return $n=i.slice(t,1<e?1-e:void 0)}function Kn(){return!0}function qn(){return!1}function Gn(t,e,n,r){for(var i in this.dispatchConfig=t,this._targetInst=e,this.nativeEvent=n,t=this.constructor.Interface)t.hasOwnProperty(i)&&((e=t[i])?this[i]=e(n):"target"===i?this.target=r:this[i]=n[i]);return this.isDefaultPrevented=(null!=n.defaultPrevented?n.defaultPrevented:!1===n.returnValue)?Kn:qn,this.isPropagationStopped=qn,this}function Jn(t,e,n,r){if(this.eventPool.length){var i=this.eventPool.pop();return this.call(i,t,e,n,r),i}return new this(t,e,n,r)}function Xn(t){if(!(t instanceof this))throw Error(s(279));t.destructor(),10>this.eventPool.length&&this.eventPool.push(t)}function Zn(t){t.eventPool=[],t.getPooled=Jn,t.release=Xn}i(Gn.prototype,{preventDefault:function(){this.defaultPrevented=!0;var t=this.nativeEvent;t&&(t.preventDefault?t.preventDefault():"unknown"!==typeof t.returnValue&&(t.returnValue=!1),this.isDefaultPrevented=Kn)},stopPropagation:function(){var t=this.nativeEvent;t&&(t.stopPropagation?t.stopPropagation():"unknown"!==typeof t.cancelBubble&&(t.cancelBubble=!0),this.isPropagationStopped=Kn)},persist:function(){this.isPersistent=Kn},isPersistent:qn,destructor:function(){var t,e=this.constructor.Interface;for(t in e)this[t]=null;this.nativeEvent=this._targetInst=this.dispatchConfig=null,this.isPropagationStopped=this.isDefaultPrevented=qn,this._dispatchInstances=this._dispatchListeners=null}}),Gn.Interface={type:null,target:null,currentTarget:function(){return null},eventPhase:null,bubbles:null,cancelable:null,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:null,isTrusted:null},Gn.extend=function(t){function e(){}function n(){return r.apply(this,arguments)}var r=this;e.prototype=r.prototype;var o=new e;return i(o,n.prototype),n.prototype=o,n.prototype.constructor=n,n.Interface=i({},r.Interface,t),n.extend=r.extend,Zn(n),n},Zn(Gn);var tr=Gn.extend({data:null}),er=Gn.extend({data:null}),nr=[9,13,27,32],rr=k&&"CompositionEvent"in window,ir=null;k&&"documentMode"in document&&(ir=document.documentMode);var or=k&&"TextEvent"in window&&!ir,sr=k&&(!rr||ir&&8<ir&&11>=ir),ar=String.fromCharCode(32),lr={beforeInput:{phasedRegistrationNames:{bubbled:"onBeforeInput",captured:"onBeforeInputCapture"},dependencies:["compositionend","keypress","textInput","paste"]},compositionEnd:{phasedRegistrationNames:{bubbled:"onCompositionEnd",captured:"onCompositionEndCapture"},dependencies:"blur compositionend keydown keypress keyup mousedown".split(" ")},compositionStart:{phasedRegistrationNames:{bubbled:"onCompositionStart",captured:"onCompositionStartCapture"},dependencies:"blur compositionstart keydown keypress keyup mousedown".split(" ")},compositionUpdate:{phasedRegistrationNames:{bubbled:"onCompositionUpdate",captured:"onCompositionUpdateCapture"},dependencies:"blur compositionupdate keydown keypress keyup mousedown".split(" ")}},ur=!1;function cr(t,e){switch(t){case"keyup":return-1!==nr.indexOf(e.keyCode);case"keydown":return 229!==e.keyCode;case"keypress":case"mousedown":case"blur":return!0;default:return!1}}function dr(t){return"object"===typeof(t=t.detail)&&"data"in t?t.data:null}var fr=!1;var hr={eventTypes:lr,extractEvents:function(t,e,n,r){var i;if(rr)t:{switch(t){case"compositionstart":var o=lr.compositionStart;break t;case"compositionend":o=lr.compositionEnd;break t;case"compositionupdate":o=lr.compositionUpdate;break t}o=void 0}else fr?cr(t,n)&&(o=lr.compositionEnd):"keydown"===t&&229===n.keyCode&&(o=lr.compositionStart);return o?(sr&&"ko"!==n.locale&&(fr||o!==lr.compositionStart?o===lr.compositionEnd&&fr&&(i=Qn()):(Yn="value"in(Hn=r)?Hn.value:Hn.textContent,fr=!0)),o=tr.getPooled(o,e,n,r),i?o.data=i:null!==(i=dr(n))&&(o.data=i),Wn(o),i=o):i=null,(t=or?function(t,e){switch(t){case"compositionend":return dr(e);case"keypress":return 32!==e.which?null:(ur=!0,ar);case"textInput":return(t=e.data)===ar&&ur?null:t;default:return null}}(t,n):function(t,e){if(fr)return"compositionend"===t||!rr&&cr(t,e)?(t=Qn(),$n=Yn=Hn=null,fr=!1,t):null;switch(t){case"paste":default:return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return sr&&"ko"!==e.locale?null:e.data}}(t,n))?((e=er.getPooled(lr.beforeInput,e,n,r)).data=t,Wn(e)):e=null,null===i?e:null===e?i:[i,e]}},pr={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function yr(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return"input"===e?!!pr[t.type]:"textarea"===e}var br={change:{phasedRegistrationNames:{bubbled:"onChange",captured:"onChangeCapture"},dependencies:"blur change click focus input keydown keyup selectionchange".split(" ")}};function mr(t,e,n){return(t=Gn.getPooled(br.change,t,e,n)).type="change",B(n),Wn(t),t}var gr=null,vr=null;function wr(t){le(t)}function _r(t){if(_t(Fn(t)))return t}function Sr(t,e){if("change"===t)return e}var Tr=!1;function Ir(){gr&&(gr.detachEvent("onpropertychange",xr),vr=gr=null)}function xr(t){if("value"===t.propertyName&&_r(vr))if(t=mr(vr,t,ue(t)),U)le(t);else{U=!0;try{M(wr,t)}finally{U=!1,z()}}}function Er(t,e,n){"focus"===t?(Ir(),vr=n,(gr=e).attachEvent("onpropertychange",xr)):"blur"===t&&Ir()}function kr(t){if("selectionchange"===t||"keyup"===t||"keydown"===t)return _r(vr)}function Or(t,e){if("click"===t)return _r(e)}function Ar(t,e){if("input"===t||"change"===t)return _r(e)}k&&(Tr=ce("input")&&(!document.documentMode||9<document.documentMode));var Cr={eventTypes:br,_isInputEventSupported:Tr,extractEvents:function(t,e,n,r){var i=e?Fn(e):window,o=i.nodeName&&i.nodeName.toLowerCase();if("select"===o||"input"===o&&"file"===i.type)var s=Sr;else if(yr(i))if(Tr)s=Ar;else{s=kr;var a=Er}else(o=i.nodeName)&&"input"===o.toLowerCase()&&("checkbox"===i.type||"radio"===i.type)&&(s=Or);if(s&&(s=s(t,e)))return mr(s,n,r);a&&a(t,i,e),"blur"===t&&(t=i._wrapperState)&&t.controlled&&"number"===i.type&&kt(i,"number",i.value)}},Dr=Gn.extend({view:null,detail:null}),Br={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Nr(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):!!(t=Br[t])&&!!e[t]}function Mr(){return Nr}var Fr=0,Pr=0,Lr=!1,Ur=!1,Rr=Dr.extend({screenX:null,screenY:null,clientX:null,clientY:null,pageX:null,pageY:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,getModifierState:Mr,button:null,buttons:null,relatedTarget:function(t){return t.relatedTarget||(t.fromElement===t.srcElement?t.toElement:t.fromElement)},movementX:function(t){if("movementX"in t)return t.movementX;var e=Fr;return Fr=t.screenX,Lr?"mousemove"===t.type?t.screenX-e:0:(Lr=!0,0)},movementY:function(t){if("movementY"in t)return t.movementY;var e=Pr;return Pr=t.screenY,Ur?"mousemove"===t.type?t.screenY-e:0:(Ur=!0,0)}}),zr=Rr.extend({pointerId:null,width:null,height:null,pressure:null,tangentialPressure:null,tiltX:null,tiltY:null,twist:null,pointerType:null,isPrimary:null}),Vr={mouseEnter:{registrationName:"onMouseEnter",dependencies:["mouseout","mouseover"]},mouseLeave:{registrationName:"onMouseLeave",dependencies:["mouseout","mouseover"]},pointerEnter:{registrationName:"onPointerEnter",dependencies:["pointerout","pointerover"]},pointerLeave:{registrationName:"onPointerLeave",dependencies:["pointerout","pointerover"]}},jr={eventTypes:Vr,extractEvents:function(t,e,n,r,i){var o="mouseover"===t||"pointerover"===t,s="mouseout"===t||"pointerout"===t;if(o&&0===(32&i)&&(n.relatedTarget||n.fromElement)||!s&&!o)return null;(o=r.window===r?r:(o=r.ownerDocument)?o.defaultView||o.parentWindow:window,s)?(s=e,null!==(e=(e=n.relatedTarget||n.toElement)?Nn(e):null)&&(e!==te(e)||5!==e.tag&&6!==e.tag)&&(e=null)):s=null;if(s===e)return null;if("mouseout"===t||"mouseover"===t)var a=Rr,l=Vr.mouseLeave,u=Vr.mouseEnter,c="mouse";else"pointerout"!==t&&"pointerover"!==t||(a=zr,l=Vr.pointerLeave,u=Vr.pointerEnter,c="pointer");if(t=null==s?o:Fn(s),o=null==e?o:Fn(e),(l=a.getPooled(l,s,n,r)).type=c+"leave",l.target=t,l.relatedTarget=o,(n=a.getPooled(u,e,n,r)).type=c+"enter",n.target=o,n.relatedTarget=t,c=e,(r=s)&&c)t:{for(u=c,s=0,t=a=r;t;t=Ln(t))s++;for(t=0,e=u;e;e=Ln(e))t++;for(;0<s-t;)a=Ln(a),s--;for(;0<t-s;)u=Ln(u),t--;for(;s--;){if(a===u||a===u.alternate)break t;a=Ln(a),u=Ln(u)}a=null}else a=null;for(u=a,a=[];r&&r!==u&&(null===(s=r.alternate)||s!==u);)a.push(r),r=Ln(r);for(r=[];c&&c!==u&&(null===(s=c.alternate)||s!==u);)r.push(c),c=Ln(c);for(c=0;c<a.length;c++)Vn(a[c],"bubbled",l);for(c=r.length;0<c--;)Vn(r[c],"captured",n);return 0===(64&i)?[l]:[l,n]}};var Wr="function"===typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t===1/e)||t!==t&&e!==e},Hr=Object.prototype.hasOwnProperty;function Yr(t,e){if(Wr(t,e))return!0;if("object"!==typeof t||null===t||"object"!==typeof e||null===e)return!1;var n=Object.keys(t),r=Object.keys(e);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++)if(!Hr.call(e,n[r])||!Wr(t[n[r]],e[n[r]]))return!1;return!0}var $r=k&&"documentMode"in document&&11>=document.documentMode,Qr={select:{phasedRegistrationNames:{bubbled:"onSelect",captured:"onSelectCapture"},dependencies:"blur contextmenu dragend focus keydown keyup mousedown mouseup selectionchange".split(" ")}},Kr=null,qr=null,Gr=null,Jr=!1;function Xr(t,e){var n=e.window===e?e.document:9===e.nodeType?e:e.ownerDocument;return Jr||null==Kr||Kr!==dn(n)?null:("selectionStart"in(n=Kr)&&bn(n)?n={start:n.selectionStart,end:n.selectionEnd}:n={anchorNode:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset},Gr&&Yr(Gr,n)?null:(Gr=n,(t=Gn.getPooled(Qr.select,qr,t,e)).type="select",t.target=Kr,Wn(t),t))}var Zr={eventTypes:Qr,extractEvents:function(t,e,n,r,i,o){if(!(o=!(i=o||(r.window===r?r.document:9===r.nodeType?r:r.ownerDocument)))){t:{i=Zt(i),o=x.onSelect;for(var s=0;s<o.length;s++)if(!i.has(o[s])){i=!1;break t}i=!0}o=!i}if(o)return null;switch(i=e?Fn(e):window,t){case"focus":(yr(i)||"true"===i.contentEditable)&&(Kr=i,qr=e,Gr=null);break;case"blur":Gr=qr=Kr=null;break;case"mousedown":Jr=!0;break;case"contextmenu":case"mouseup":case"dragend":return Jr=!1,Xr(n,r);case"selectionchange":if($r)break;case"keydown":case"keyup":return Xr(n,r)}return null}},ti=Gn.extend({animationName:null,elapsedTime:null,pseudoElement:null}),ei=Gn.extend({clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),ni=Dr.extend({relatedTarget:null});function ri(t){var e=t.keyCode;return"charCode"in t?0===(t=t.charCode)&&13===e&&(t=13):t=e,10===t&&(t=13),32<=t||13===t?t:0}var ii={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},oi={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},si=Dr.extend({key:function(t){if(t.key){var e=ii[t.key]||t.key;if("Unidentified"!==e)return e}return"keypress"===t.type?13===(t=ri(t))?"Enter":String.fromCharCode(t):"keydown"===t.type||"keyup"===t.type?oi[t.keyCode]||"Unidentified":""},location:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,repeat:null,locale:null,getModifierState:Mr,charCode:function(t){return"keypress"===t.type?ri(t):0},keyCode:function(t){return"keydown"===t.type||"keyup"===t.type?t.keyCode:0},which:function(t){return"keypress"===t.type?ri(t):"keydown"===t.type||"keyup"===t.type?t.keyCode:0}}),ai=Rr.extend({dataTransfer:null}),li=Dr.extend({touches:null,targetTouches:null,changedTouches:null,altKey:null,metaKey:null,ctrlKey:null,shiftKey:null,getModifierState:Mr}),ui=Gn.extend({propertyName:null,elapsedTime:null,pseudoElement:null}),ci=Rr.extend({deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:null,deltaMode:null}),di={eventTypes:Ue,extractEvents:function(t,e,n,r){var i=Re.get(t);if(!i)return null;switch(t){case"keypress":if(0===ri(n))return null;case"keydown":case"keyup":t=si;break;case"blur":case"focus":t=ni;break;case"click":if(2===n.button)return null;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":t=Rr;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":t=ai;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":t=li;break;case Qt:case Kt:case qt:t=ti;break;case Gt:t=ui;break;case"scroll":t=Dr;break;case"wheel":t=ci;break;case"copy":case"cut":case"paste":t=ei;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":t=zr;break;default:t=Gn}return Wn(e=t.getPooled(i,e,n,r)),e}};if(g)throw Error(s(101));g=Array.prototype.slice.call("ResponderEventPlugin SimpleEventPlugin EnterLeaveEventPlugin ChangeEventPlugin SelectEventPlugin BeforeInputEventPlugin".split(" ")),w(),p=Pn,y=Mn,b=Fn,E({SimpleEventPlugin:di,EnterLeaveEventPlugin:jr,ChangeEventPlugin:Cr,SelectEventPlugin:Zr,BeforeInputEventPlugin:hr});var fi=[],hi=-1;function pi(t){0>hi||(t.current=fi[hi],fi[hi]=null,hi--)}function yi(t,e){hi++,fi[hi]=t.current,t.current=e}var bi={},mi={current:bi},gi={current:!1},vi=bi;function wi(t,e){var n=t.type.contextTypes;if(!n)return bi;var r=t.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===e)return r.__reactInternalMemoizedMaskedChildContext;var i,o={};for(i in n)o[i]=e[i];return r&&((t=t.stateNode).__reactInternalMemoizedUnmaskedChildContext=e,t.__reactInternalMemoizedMaskedChildContext=o),o}function _i(t){return null!==(t=t.childContextTypes)&&void 0!==t}function Si(){pi(gi),pi(mi)}function Ti(t,e,n){if(mi.current!==bi)throw Error(s(168));yi(mi,e),yi(gi,n)}function Ii(t,e,n){var r=t.stateNode;if(t=e.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var o in r=r.getChildContext())if(!(o in t))throw Error(s(108,bt(e)||"Unknown",o));return i({},n,{},r)}function xi(t){return t=(t=t.stateNode)&&t.__reactInternalMemoizedMergedChildContext||bi,vi=mi.current,yi(mi,t),yi(gi,gi.current),!0}function Ei(t,e,n){var r=t.stateNode;if(!r)throw Error(s(169));n?(t=Ii(t,e,vi),r.__reactInternalMemoizedMergedChildContext=t,pi(gi),pi(mi),yi(mi,t)):pi(gi),yi(gi,n)}var ki=o.unstable_runWithPriority,Oi=o.unstable_scheduleCallback,Ai=o.unstable_cancelCallback,Ci=o.unstable_requestPaint,Di=o.unstable_now,Bi=o.unstable_getCurrentPriorityLevel,Ni=o.unstable_ImmediatePriority,Mi=o.unstable_UserBlockingPriority,Fi=o.unstable_NormalPriority,Pi=o.unstable_LowPriority,Li=o.unstable_IdlePriority,Ui={},Ri=o.unstable_shouldYield,zi=void 0!==Ci?Ci:function(){},Vi=null,ji=null,Wi=!1,Hi=Di(),Yi=1e4>Hi?Di:function(){return Di()-Hi};function $i(){switch(Bi()){case Ni:return 99;case Mi:return 98;case Fi:return 97;case Pi:return 96;case Li:return 95;default:throw Error(s(332))}}function Qi(t){switch(t){case 99:return Ni;case 98:return Mi;case 97:return Fi;case 96:return Pi;case 95:return Li;default:throw Error(s(332))}}function Ki(t,e){return t=Qi(t),ki(t,e)}function qi(t,e,n){return t=Qi(t),Oi(t,e,n)}function Gi(t){return null===Vi?(Vi=[t],ji=Oi(Ni,Xi)):Vi.push(t),Ui}function Ji(){if(null!==ji){var t=ji;ji=null,Ai(t)}Xi()}function Xi(){if(!Wi&&null!==Vi){Wi=!0;var t=0;try{var e=Vi;Ki(99,(function(){for(;t<e.length;t++){var n=e[t];do{n=n(!0)}while(null!==n)}})),Vi=null}catch(n){throw null!==Vi&&(Vi=Vi.slice(t+1)),Oi(Ni,Ji),n}finally{Wi=!1}}}function Zi(t,e,n){return 1073741821-(1+((1073741821-t+e/10)/(n/=10)|0))*n}function to(t,e){if(t&&t.defaultProps)for(var n in e=i({},e),t=t.defaultProps)void 0===e[n]&&(e[n]=t[n]);return e}var eo={current:null},no=null,ro=null,io=null;function oo(){io=ro=no=null}function so(t){var e=eo.current;pi(eo),t.type._context._currentValue=e}function ao(t,e){for(;null!==t;){var n=t.alternate;if(t.childExpirationTime<e)t.childExpirationTime=e,null!==n&&n.childExpirationTime<e&&(n.childExpirationTime=e);else{if(!(null!==n&&n.childExpirationTime<e))break;n.childExpirationTime=e}t=t.return}}function lo(t,e){no=t,io=ro=null,null!==(t=t.dependencies)&&null!==t.firstContext&&(t.expirationTime>=e&&(Ps=!0),t.firstContext=null)}function uo(t,e){if(io!==t&&!1!==e&&0!==e)if("number"===typeof e&&1073741823!==e||(io=t,e=1073741823),e={context:t,observedBits:e,next:null},null===ro){if(null===no)throw Error(s(308));ro=e,no.dependencies={expirationTime:0,firstContext:e,responders:null}}else ro=ro.next=e;return t._currentValue}var co=!1;function fo(t){t.updateQueue={baseState:t.memoizedState,baseQueue:null,shared:{pending:null},effects:null}}function ho(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,baseQueue:t.baseQueue,shared:t.shared,effects:t.effects})}function po(t,e){return(t={expirationTime:t,suspenseConfig:e,tag:0,payload:null,callback:null,next:null}).next=t}function yo(t,e){if(null!==(t=t.updateQueue)){var n=(t=t.shared).pending;null===n?e.next=e:(e.next=n.next,n.next=e),t.pending=e}}function bo(t,e){var n=t.alternate;null!==n&&ho(n,t),null===(n=(t=t.updateQueue).baseQueue)?(t.baseQueue=e.next=e,e.next=e):(e.next=n.next,n.next=e)}function mo(t,e,n,r){var o=t.updateQueue;co=!1;var s=o.baseQueue,a=o.shared.pending;if(null!==a){if(null!==s){var l=s.next;s.next=a.next,a.next=l}s=a,o.shared.pending=null,null!==(l=t.alternate)&&(null!==(l=l.updateQueue)&&(l.baseQueue=a))}if(null!==s){l=s.next;var u=o.baseState,c=0,d=null,f=null,h=null;if(null!==l)for(var p=l;;){if((a=p.expirationTime)<r){var y={expirationTime:p.expirationTime,suspenseConfig:p.suspenseConfig,tag:p.tag,payload:p.payload,callback:p.callback,next:null};null===h?(f=h=y,d=u):h=h.next=y,a>c&&(c=a)}else{null!==h&&(h=h.next={expirationTime:1073741823,suspenseConfig:p.suspenseConfig,tag:p.tag,payload:p.payload,callback:p.callback,next:null}),Sl(a,p.suspenseConfig);t:{var b=t,m=p;switch(a=e,y=n,m.tag){case 1:if("function"===typeof(b=m.payload)){u=b.call(y,u,a);break t}u=b;break t;case 3:b.effectTag=-4097&b.effectTag|64;case 0:if(null===(a="function"===typeof(b=m.payload)?b.call(y,u,a):b)||void 0===a)break t;u=i({},u,a);break t;case 2:co=!0}}null!==p.callback&&(t.effectTag|=32,null===(a=o.effects)?o.effects=[p]:a.push(p))}if(null===(p=p.next)||p===l){if(null===(a=o.shared.pending))break;p=s.next=a.next,a.next=l,o.baseQueue=s=a,o.shared.pending=null}}null===h?d=u:h.next=f,o.baseState=d,o.baseQueue=h,Tl(c),t.expirationTime=c,t.memoizedState=u}}function go(t,e,n){if(t=e.effects,e.effects=null,null!==t)for(e=0;e<t.length;e++){var r=t[e],i=r.callback;if(null!==i){if(r.callback=null,r=i,i=n,"function"!==typeof r)throw Error(s(191,r));r.call(i)}}}var vo=G.ReactCurrentBatchConfig,wo=(new r.Component).refs;function _o(t,e,n,r){n=null===(n=n(r,e=t.memoizedState))||void 0===n?e:i({},e,n),t.memoizedState=n,0===t.expirationTime&&(t.updateQueue.baseState=n)}var So={isMounted:function(t){return!!(t=t._reactInternalFiber)&&te(t)===t},enqueueSetState:function(t,e,n){t=t._reactInternalFiber;var r=ul(),i=vo.suspense;(i=po(r=cl(r,t,i),i)).payload=e,void 0!==n&&null!==n&&(i.callback=n),yo(t,i),dl(t,r)},enqueueReplaceState:function(t,e,n){t=t._reactInternalFiber;var r=ul(),i=vo.suspense;(i=po(r=cl(r,t,i),i)).tag=1,i.payload=e,void 0!==n&&null!==n&&(i.callback=n),yo(t,i),dl(t,r)},enqueueForceUpdate:function(t,e){t=t._reactInternalFiber;var n=ul(),r=vo.suspense;(r=po(n=cl(n,t,r),r)).tag=2,void 0!==e&&null!==e&&(r.callback=e),yo(t,r),dl(t,n)}};function To(t,e,n,r,i,o,s){return"function"===typeof(t=t.stateNode).shouldComponentUpdate?t.shouldComponentUpdate(r,o,s):!e.prototype||!e.prototype.isPureReactComponent||(!Yr(n,r)||!Yr(i,o))}function Io(t,e,n){var r=!1,i=bi,o=e.contextType;return"object"===typeof o&&null!==o?o=uo(o):(i=_i(e)?vi:mi.current,o=(r=null!==(r=e.contextTypes)&&void 0!==r)?wi(t,i):bi),e=new e(n,o),t.memoizedState=null!==e.state&&void 0!==e.state?e.state:null,e.updater=So,t.stateNode=e,e._reactInternalFiber=t,r&&((t=t.stateNode).__reactInternalMemoizedUnmaskedChildContext=i,t.__reactInternalMemoizedMaskedChildContext=o),e}function xo(t,e,n,r){t=e.state,"function"===typeof e.componentWillReceiveProps&&e.componentWillReceiveProps(n,r),"function"===typeof e.UNSAFE_componentWillReceiveProps&&e.UNSAFE_componentWillReceiveProps(n,r),e.state!==t&&So.enqueueReplaceState(e,e.state,null)}function Eo(t,e,n,r){var i=t.stateNode;i.props=n,i.state=t.memoizedState,i.refs=wo,fo(t);var o=e.contextType;"object"===typeof o&&null!==o?i.context=uo(o):(o=_i(e)?vi:mi.current,i.context=wi(t,o)),mo(t,n,i,r),i.state=t.memoizedState,"function"===typeof(o=e.getDerivedStateFromProps)&&(_o(t,e,o,n),i.state=t.memoizedState),"function"===typeof e.getDerivedStateFromProps||"function"===typeof i.getSnapshotBeforeUpdate||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||(e=i.state,"function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount(),e!==i.state&&So.enqueueReplaceState(i,i.state,null),mo(t,n,i,r),i.state=t.memoizedState),"function"===typeof i.componentDidMount&&(t.effectTag|=4)}var ko=Array.isArray;function Oo(t,e,n){if(null!==(t=n.ref)&&"function"!==typeof t&&"object"!==typeof t){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(s(309));var r=n.stateNode}if(!r)throw Error(s(147,t));var i=""+t;return null!==e&&null!==e.ref&&"function"===typeof e.ref&&e.ref._stringRef===i?e.ref:(e=function(t){var e=r.refs;e===wo&&(e=r.refs={}),null===t?delete e[i]:e[i]=t},e._stringRef=i,e)}if("string"!==typeof t)throw Error(s(284));if(!n._owner)throw Error(s(290,t))}return t}function Ao(t,e){if("textarea"!==t.type)throw Error(s(31,"[object Object]"===Object.prototype.toString.call(e)?"object with keys {"+Object.keys(e).join(", ")+"}":e,""))}function Co(t){function e(e,n){if(t){var r=e.lastEffect;null!==r?(r.nextEffect=n,e.lastEffect=n):e.firstEffect=e.lastEffect=n,n.nextEffect=null,n.effectTag=8}}function n(n,r){if(!t)return null;for(;null!==r;)e(n,r),r=r.sibling;return null}function r(t,e){for(t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function i(t,e){return(t=Wl(t,e)).index=0,t.sibling=null,t}function o(e,n,r){return e.index=r,t?null!==(r=e.alternate)?(r=r.index)<n?(e.effectTag=2,n):r:(e.effectTag=2,n):n}function a(e){return t&&null===e.alternate&&(e.effectTag=2),e}function l(t,e,n,r){return null===e||6!==e.tag?((e=$l(n,t.mode,r)).return=t,e):((e=i(e,n)).return=t,e)}function u(t,e,n,r){return null!==e&&e.elementType===n.type?((r=i(e,n.props)).ref=Oo(t,e,n),r.return=t,r):((r=Hl(n.type,n.key,n.props,null,t.mode,r)).ref=Oo(t,e,n),r.return=t,r)}function c(t,e,n,r){return null===e||4!==e.tag||e.stateNode.containerInfo!==n.containerInfo||e.stateNode.implementation!==n.implementation?((e=Ql(n,t.mode,r)).return=t,e):((e=i(e,n.children||[])).return=t,e)}function d(t,e,n,r,o){return null===e||7!==e.tag?((e=Yl(n,t.mode,r,o)).return=t,e):((e=i(e,n)).return=t,e)}function f(t,e,n){if("string"===typeof e||"number"===typeof e)return(e=$l(""+e,t.mode,n)).return=t,e;if("object"===typeof e&&null!==e){switch(e.$$typeof){case tt:return(n=Hl(e.type,e.key,e.props,null,t.mode,n)).ref=Oo(t,null,e),n.return=t,n;case et:return(e=Ql(e,t.mode,n)).return=t,e}if(ko(e)||yt(e))return(e=Yl(e,t.mode,n,null)).return=t,e;Ao(t,e)}return null}function h(t,e,n,r){var i=null!==e?e.key:null;if("string"===typeof n||"number"===typeof n)return null!==i?null:l(t,e,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case tt:return n.key===i?n.type===nt?d(t,e,n.props.children,r,i):u(t,e,n,r):null;case et:return n.key===i?c(t,e,n,r):null}if(ko(n)||yt(n))return null!==i?null:d(t,e,n,r,null);Ao(t,n)}return null}function p(t,e,n,r,i){if("string"===typeof r||"number"===typeof r)return l(e,t=t.get(n)||null,""+r,i);if("object"===typeof r&&null!==r){switch(r.$$typeof){case tt:return t=t.get(null===r.key?n:r.key)||null,r.type===nt?d(e,t,r.props.children,i,r.key):u(e,t,r,i);case et:return c(e,t=t.get(null===r.key?n:r.key)||null,r,i)}if(ko(r)||yt(r))return d(e,t=t.get(n)||null,r,i,null);Ao(e,r)}return null}function y(i,s,a,l){for(var u=null,c=null,d=s,y=s=0,b=null;null!==d&&y<a.length;y++){d.index>y?(b=d,d=null):b=d.sibling;var m=h(i,d,a[y],l);if(null===m){null===d&&(d=b);break}t&&d&&null===m.alternate&&e(i,d),s=o(m,s,y),null===c?u=m:c.sibling=m,c=m,d=b}if(y===a.length)return n(i,d),u;if(null===d){for(;y<a.length;y++)null!==(d=f(i,a[y],l))&&(s=o(d,s,y),null===c?u=d:c.sibling=d,c=d);return u}for(d=r(i,d);y<a.length;y++)null!==(b=p(d,i,y,a[y],l))&&(t&&null!==b.alternate&&d.delete(null===b.key?y:b.key),s=o(b,s,y),null===c?u=b:c.sibling=b,c=b);return t&&d.forEach((function(t){return e(i,t)})),u}function b(i,a,l,u){var c=yt(l);if("function"!==typeof c)throw Error(s(150));if(null==(l=c.call(l)))throw Error(s(151));for(var d=c=null,y=a,b=a=0,m=null,g=l.next();null!==y&&!g.done;b++,g=l.next()){y.index>b?(m=y,y=null):m=y.sibling;var v=h(i,y,g.value,u);if(null===v){null===y&&(y=m);break}t&&y&&null===v.alternate&&e(i,y),a=o(v,a,b),null===d?c=v:d.sibling=v,d=v,y=m}if(g.done)return n(i,y),c;if(null===y){for(;!g.done;b++,g=l.next())null!==(g=f(i,g.value,u))&&(a=o(g,a,b),null===d?c=g:d.sibling=g,d=g);return c}for(y=r(i,y);!g.done;b++,g=l.next())null!==(g=p(y,i,b,g.value,u))&&(t&&null!==g.alternate&&y.delete(null===g.key?b:g.key),a=o(g,a,b),null===d?c=g:d.sibling=g,d=g);return t&&y.forEach((function(t){return e(i,t)})),c}return function(t,r,o,l){var u="object"===typeof o&&null!==o&&o.type===nt&&null===o.key;u&&(o=o.props.children);var c="object"===typeof o&&null!==o;if(c)switch(o.$$typeof){case tt:t:{for(c=o.key,u=r;null!==u;){if(u.key===c){if(7===u.tag){if(o.type===nt){n(t,u.sibling),(r=i(u,o.props.children)).return=t,t=r;break t}}else if(u.elementType===o.type){n(t,u.sibling),(r=i(u,o.props)).ref=Oo(t,u,o),r.return=t,t=r;break t}n(t,u);break}e(t,u),u=u.sibling}o.type===nt?((r=Yl(o.props.children,t.mode,l,o.key)).return=t,t=r):((l=Hl(o.type,o.key,o.props,null,t.mode,l)).ref=Oo(t,r,o),l.return=t,t=l)}return a(t);case et:t:{for(u=o.key;null!==r;){if(r.key===u){if(4===r.tag&&r.stateNode.containerInfo===o.containerInfo&&r.stateNode.implementation===o.implementation){n(t,r.sibling),(r=i(r,o.children||[])).return=t,t=r;break t}n(t,r);break}e(t,r),r=r.sibling}(r=Ql(o,t.mode,l)).return=t,t=r}return a(t)}if("string"===typeof o||"number"===typeof o)return o=""+o,null!==r&&6===r.tag?(n(t,r.sibling),(r=i(r,o)).return=t,t=r):(n(t,r),(r=$l(o,t.mode,l)).return=t,t=r),a(t);if(ko(o))return y(t,r,o,l);if(yt(o))return b(t,r,o,l);if(c&&Ao(t,o),"undefined"===typeof o&&!u)switch(t.tag){case 1:case 0:throw t=t.type,Error(s(152,t.displayName||t.name||"Component"))}return n(t,r)}}var Do=Co(!0),Bo=Co(!1),No={},Mo={current:No},Fo={current:No},Po={current:No};function Lo(t){if(t===No)throw Error(s(174));return t}function Uo(t,e){switch(yi(Po,e),yi(Fo,t),yi(Mo,No),t=e.nodeType){case 9:case 11:e=(e=e.documentElement)?e.namespaceURI:Lt(null,"");break;default:e=Lt(e=(t=8===t?e.parentNode:e).namespaceURI||null,t=t.tagName)}pi(Mo),yi(Mo,e)}function Ro(){pi(Mo),pi(Fo),pi(Po)}function zo(t){Lo(Po.current);var e=Lo(Mo.current),n=Lt(e,t.type);e!==n&&(yi(Fo,t),yi(Mo,n))}function Vo(t){Fo.current===t&&(pi(Mo),pi(Fo))}var jo={current:0};function Wo(t){for(var e=t;null!==e;){if(13===e.tag){var n=e.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||n.data===vn||n.data===wn))return e}else if(19===e.tag&&void 0!==e.memoizedProps.revealOrder){if(0!==(64&e.effectTag))return e}else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break;for(;null===e.sibling;){if(null===e.return||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function Ho(t,e){return{responder:t,props:e}}var Yo=G.ReactCurrentDispatcher,$o=G.ReactCurrentBatchConfig,Qo=0,Ko=null,qo=null,Go=null,Jo=!1;function Xo(){throw Error(s(321))}function Zo(t,e){if(null===e)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!Wr(t[n],e[n]))return!1;return!0}function ts(t,e,n,r,i,o){if(Qo=o,Ko=e,e.memoizedState=null,e.updateQueue=null,e.expirationTime=0,Yo.current=null===t||null===t.memoizedState?Ts:Is,t=n(r,i),e.expirationTime===Qo){o=0;do{if(e.expirationTime=0,!(25>o))throw Error(s(301));o+=1,Go=qo=null,e.updateQueue=null,Yo.current=xs,t=n(r,i)}while(e.expirationTime===Qo)}if(Yo.current=Ss,e=null!==qo&&null!==qo.next,Qo=0,Go=qo=Ko=null,Jo=!1,e)throw Error(s(300));return t}function es(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===Go?Ko.memoizedState=Go=t:Go=Go.next=t,Go}function ns(){if(null===qo){var t=Ko.alternate;t=null!==t?t.memoizedState:null}else t=qo.next;var e=null===Go?Ko.memoizedState:Go.next;if(null!==e)Go=e,qo=t;else{if(null===t)throw Error(s(310));t={memoizedState:(qo=t).memoizedState,baseState:qo.baseState,baseQueue:qo.baseQueue,queue:qo.queue,next:null},null===Go?Ko.memoizedState=Go=t:Go=Go.next=t}return Go}function rs(t,e){return"function"===typeof e?e(t):e}function is(t){var e=ns(),n=e.queue;if(null===n)throw Error(s(311));n.lastRenderedReducer=t;var r=qo,i=r.baseQueue,o=n.pending;if(null!==o){if(null!==i){var a=i.next;i.next=o.next,o.next=a}r.baseQueue=i=o,n.pending=null}if(null!==i){i=i.next,r=r.baseState;var l=a=o=null,u=i;do{var c=u.expirationTime;if(c<Qo){var d={expirationTime:u.expirationTime,suspenseConfig:u.suspenseConfig,action:u.action,eagerReducer:u.eagerReducer,eagerState:u.eagerState,next:null};null===l?(a=l=d,o=r):l=l.next=d,c>Ko.expirationTime&&(Ko.expirationTime=c,Tl(c))}else null!==l&&(l=l.next={expirationTime:1073741823,suspenseConfig:u.suspenseConfig,action:u.action,eagerReducer:u.eagerReducer,eagerState:u.eagerState,next:null}),Sl(c,u.suspenseConfig),r=u.eagerReducer===t?u.eagerState:t(r,u.action);u=u.next}while(null!==u&&u!==i);null===l?o=r:l.next=a,Wr(r,e.memoizedState)||(Ps=!0),e.memoizedState=r,e.baseState=o,e.baseQueue=l,n.lastRenderedState=r}return[e.memoizedState,n.dispatch]}function os(t){var e=ns(),n=e.queue;if(null===n)throw Error(s(311));n.lastRenderedReducer=t;var r=n.dispatch,i=n.pending,o=e.memoizedState;if(null!==i){n.pending=null;var a=i=i.next;do{o=t(o,a.action),a=a.next}while(a!==i);Wr(o,e.memoizedState)||(Ps=!0),e.memoizedState=o,null===e.baseQueue&&(e.baseState=o),n.lastRenderedState=o}return[o,r]}function ss(t){var e=es();return"function"===typeof t&&(t=t()),e.memoizedState=e.baseState=t,t=(t=e.queue={pending:null,dispatch:null,lastRenderedReducer:rs,lastRenderedState:t}).dispatch=_s.bind(null,Ko,t),[e.memoizedState,t]}function as(t,e,n,r){return t={tag:t,create:e,destroy:n,deps:r,next:null},null===(e=Ko.updateQueue)?(e={lastEffect:null},Ko.updateQueue=e,e.lastEffect=t.next=t):null===(n=e.lastEffect)?e.lastEffect=t.next=t:(r=n.next,n.next=t,t.next=r,e.lastEffect=t),t}function ls(){return ns().memoizedState}function us(t,e,n,r){var i=es();Ko.effectTag|=t,i.memoizedState=as(1|e,n,void 0,void 0===r?null:r)}function cs(t,e,n,r){var i=ns();r=void 0===r?null:r;var o=void 0;if(null!==qo){var s=qo.memoizedState;if(o=s.destroy,null!==r&&Zo(r,s.deps))return void as(e,n,o,r)}Ko.effectTag|=t,i.memoizedState=as(1|e,n,o,r)}function ds(t,e){return us(516,4,t,e)}function fs(t,e){return cs(516,4,t,e)}function hs(t,e){return cs(4,2,t,e)}function ps(t,e){return"function"===typeof e?(t=t(),e(t),function(){e(null)}):null!==e&&void 0!==e?(t=t(),e.current=t,function(){e.current=null}):void 0}function ys(t,e,n){return n=null!==n&&void 0!==n?n.concat([t]):null,cs(4,2,ps.bind(null,e,t),n)}function bs(){}function ms(t,e){return es().memoizedState=[t,void 0===e?null:e],t}function gs(t,e){var n=ns();e=void 0===e?null:e;var r=n.memoizedState;return null!==r&&null!==e&&Zo(e,r[1])?r[0]:(n.memoizedState=[t,e],t)}function vs(t,e){var n=ns();e=void 0===e?null:e;var r=n.memoizedState;return null!==r&&null!==e&&Zo(e,r[1])?r[0]:(t=t(),n.memoizedState=[t,e],t)}function ws(t,e,n){var r=$i();Ki(98>r?98:r,(function(){t(!0)})),Ki(97<r?97:r,(function(){var r=$o.suspense;$o.suspense=void 0===e?null:e;try{t(!1),n()}finally{$o.suspense=r}}))}function _s(t,e,n){var r=ul(),i=vo.suspense;i={expirationTime:r=cl(r,t,i),suspenseConfig:i,action:n,eagerReducer:null,eagerState:null,next:null};var o=e.pending;if(null===o?i.next=i:(i.next=o.next,o.next=i),e.pending=i,o=t.alternate,t===Ko||null!==o&&o===Ko)Jo=!0,i.expirationTime=Qo,Ko.expirationTime=Qo;else{if(0===t.expirationTime&&(null===o||0===o.expirationTime)&&null!==(o=e.lastRenderedReducer))try{var s=e.lastRenderedState,a=o(s,n);if(i.eagerReducer=o,i.eagerState=a,Wr(a,s))return}catch(l){}dl(t,r)}}var Ss={readContext:uo,useCallback:Xo,useContext:Xo,useEffect:Xo,useImperativeHandle:Xo,useLayoutEffect:Xo,useMemo:Xo,useReducer:Xo,useRef:Xo,useState:Xo,useDebugValue:Xo,useResponder:Xo,useDeferredValue:Xo,useTransition:Xo},Ts={readContext:uo,useCallback:ms,useContext:uo,useEffect:ds,useImperativeHandle:function(t,e,n){return n=null!==n&&void 0!==n?n.concat([t]):null,us(4,2,ps.bind(null,e,t),n)},useLayoutEffect:function(t,e){return us(4,2,t,e)},useMemo:function(t,e){var n=es();return e=void 0===e?null:e,t=t(),n.memoizedState=[t,e],t},useReducer:function(t,e,n){var r=es();return e=void 0!==n?n(e):e,r.memoizedState=r.baseState=e,t=(t=r.queue={pending:null,dispatch:null,lastRenderedReducer:t,lastRenderedState:e}).dispatch=_s.bind(null,Ko,t),[r.memoizedState,t]},useRef:function(t){return t={current:t},es().memoizedState=t},useState:ss,useDebugValue:bs,useResponder:Ho,useDeferredValue:function(t,e){var n=ss(t),r=n[0],i=n[1];return ds((function(){var n=$o.suspense;$o.suspense=void 0===e?null:e;try{i(t)}finally{$o.suspense=n}}),[t,e]),r},useTransition:function(t){var e=ss(!1),n=e[0];return e=e[1],[ms(ws.bind(null,e,t),[e,t]),n]}},Is={readContext:uo,useCallback:gs,useContext:uo,useEffect:fs,useImperativeHandle:ys,useLayoutEffect:hs,useMemo:vs,useReducer:is,useRef:ls,useState:function(){return is(rs)},useDebugValue:bs,useResponder:Ho,useDeferredValue:function(t,e){var n=is(rs),r=n[0],i=n[1];return fs((function(){var n=$o.suspense;$o.suspense=void 0===e?null:e;try{i(t)}finally{$o.suspense=n}}),[t,e]),r},useTransition:function(t){var e=is(rs),n=e[0];return e=e[1],[gs(ws.bind(null,e,t),[e,t]),n]}},xs={readContext:uo,useCallback:gs,useContext:uo,useEffect:fs,useImperativeHandle:ys,useLayoutEffect:hs,useMemo:vs,useReducer:os,useRef:ls,useState:function(){return os(rs)},useDebugValue:bs,useResponder:Ho,useDeferredValue:function(t,e){var n=os(rs),r=n[0],i=n[1];return fs((function(){var n=$o.suspense;$o.suspense=void 0===e?null:e;try{i(t)}finally{$o.suspense=n}}),[t,e]),r},useTransition:function(t){var e=os(rs),n=e[0];return e=e[1],[gs(ws.bind(null,e,t),[e,t]),n]}},Es=null,ks=null,Os=!1;function As(t,e){var n=Vl(5,null,null,0);n.elementType="DELETED",n.type="DELETED",n.stateNode=e,n.return=t,n.effectTag=8,null!==t.lastEffect?(t.lastEffect.nextEffect=n,t.lastEffect=n):t.firstEffect=t.lastEffect=n}function Cs(t,e){switch(t.tag){case 5:var n=t.type;return null!==(e=1!==e.nodeType||n.toLowerCase()!==e.nodeName.toLowerCase()?null:e)&&(t.stateNode=e,!0);case 6:return null!==(e=""===t.pendingProps||3!==e.nodeType?null:e)&&(t.stateNode=e,!0);default:return!1}}function Ds(t){if(Os){var e=ks;if(e){var n=e;if(!Cs(t,e)){if(!(e=kn(n.nextSibling))||!Cs(t,e))return t.effectTag=-1025&t.effectTag|2,Os=!1,void(Es=t);As(Es,n)}Es=t,ks=kn(e.firstChild)}else t.effectTag=-1025&t.effectTag|2,Os=!1,Es=t}}function Bs(t){for(t=t.return;null!==t&&5!==t.tag&&3!==t.tag&&13!==t.tag;)t=t.return;Es=t}function Ns(t){if(t!==Es)return!1;if(!Os)return Bs(t),Os=!0,!1;var e=t.type;if(5!==t.tag||"head"!==e&&"body"!==e&&!In(e,t.memoizedProps))for(e=ks;e;)As(t,e),e=kn(e.nextSibling);if(Bs(t),13===t.tag){if(!(t=null!==(t=t.memoizedState)?t.dehydrated:null))throw Error(s(317));t:{for(t=t.nextSibling,e=0;t;){if(8===t.nodeType){var n=t.data;if(n===gn){if(0===e){ks=kn(t.nextSibling);break t}e--}else n!==mn&&n!==wn&&n!==vn||e++}t=t.nextSibling}ks=null}}else ks=Es?kn(t.stateNode.nextSibling):null;return!0}function Ms(){ks=Es=null,Os=!1}var Fs=G.ReactCurrentOwner,Ps=!1;function Ls(t,e,n,r){e.child=null===t?Bo(e,null,n,r):Do(e,t.child,n,r)}function Us(t,e,n,r,i){n=n.render;var o=e.ref;return lo(e,i),r=ts(t,e,n,r,o,i),null===t||Ps?(e.effectTag|=1,Ls(t,e,r,i),e.child):(e.updateQueue=t.updateQueue,e.effectTag&=-517,t.expirationTime<=i&&(t.expirationTime=0),ea(t,e,i))}function Rs(t,e,n,r,i,o){if(null===t){var s=n.type;return"function"!==typeof s||jl(s)||void 0!==s.defaultProps||null!==n.compare||void 0!==n.defaultProps?((t=Hl(n.type,null,r,null,e.mode,o)).ref=e.ref,t.return=e,e.child=t):(e.tag=15,e.type=s,zs(t,e,s,r,i,o))}return s=t.child,i<o&&(i=s.memoizedProps,(n=null!==(n=n.compare)?n:Yr)(i,r)&&t.ref===e.ref)?ea(t,e,o):(e.effectTag|=1,(t=Wl(s,r)).ref=e.ref,t.return=e,e.child=t)}function zs(t,e,n,r,i,o){return null!==t&&Yr(t.memoizedProps,r)&&t.ref===e.ref&&(Ps=!1,i<o)?(e.expirationTime=t.expirationTime,ea(t,e,o)):js(t,e,n,r,o)}function Vs(t,e){var n=e.ref;(null===t&&null!==n||null!==t&&t.ref!==n)&&(e.effectTag|=128)}function js(t,e,n,r,i){var o=_i(n)?vi:mi.current;return o=wi(e,o),lo(e,i),n=ts(t,e,n,r,o,i),null===t||Ps?(e.effectTag|=1,Ls(t,e,n,i),e.child):(e.updateQueue=t.updateQueue,e.effectTag&=-517,t.expirationTime<=i&&(t.expirationTime=0),ea(t,e,i))}function Ws(t,e,n,r,i){if(_i(n)){var o=!0;xi(e)}else o=!1;if(lo(e,i),null===e.stateNode)null!==t&&(t.alternate=null,e.alternate=null,e.effectTag|=2),Io(e,n,r),Eo(e,n,r,i),r=!0;else if(null===t){var s=e.stateNode,a=e.memoizedProps;s.props=a;var l=s.context,u=n.contextType;"object"===typeof u&&null!==u?u=uo(u):u=wi(e,u=_i(n)?vi:mi.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof s.getSnapshotBeforeUpdate;d||"function"!==typeof s.UNSAFE_componentWillReceiveProps&&"function"!==typeof s.componentWillReceiveProps||(a!==r||l!==u)&&xo(e,s,r,u),co=!1;var f=e.memoizedState;s.state=f,mo(e,r,s,i),l=e.memoizedState,a!==r||f!==l||gi.current||co?("function"===typeof c&&(_o(e,n,c,r),l=e.memoizedState),(a=co||To(e,n,a,r,f,l,u))?(d||"function"!==typeof s.UNSAFE_componentWillMount&&"function"!==typeof s.componentWillMount||("function"===typeof s.componentWillMount&&s.componentWillMount(),"function"===typeof s.UNSAFE_componentWillMount&&s.UNSAFE_componentWillMount()),"function"===typeof s.componentDidMount&&(e.effectTag|=4)):("function"===typeof s.componentDidMount&&(e.effectTag|=4),e.memoizedProps=r,e.memoizedState=l),s.props=r,s.state=l,s.context=u,r=a):("function"===typeof s.componentDidMount&&(e.effectTag|=4),r=!1)}else s=e.stateNode,ho(t,e),a=e.memoizedProps,s.props=e.type===e.elementType?a:to(e.type,a),l=s.context,"object"===typeof(u=n.contextType)&&null!==u?u=uo(u):u=wi(e,u=_i(n)?vi:mi.current),(d="function"===typeof(c=n.getDerivedStateFromProps)||"function"===typeof s.getSnapshotBeforeUpdate)||"function"!==typeof s.UNSAFE_componentWillReceiveProps&&"function"!==typeof s.componentWillReceiveProps||(a!==r||l!==u)&&xo(e,s,r,u),co=!1,l=e.memoizedState,s.state=l,mo(e,r,s,i),f=e.memoizedState,a!==r||l!==f||gi.current||co?("function"===typeof c&&(_o(e,n,c,r),f=e.memoizedState),(c=co||To(e,n,a,r,l,f,u))?(d||"function"!==typeof s.UNSAFE_componentWillUpdate&&"function"!==typeof s.componentWillUpdate||("function"===typeof s.componentWillUpdate&&s.componentWillUpdate(r,f,u),"function"===typeof s.UNSAFE_componentWillUpdate&&s.UNSAFE_componentWillUpdate(r,f,u)),"function"===typeof s.componentDidUpdate&&(e.effectTag|=4),"function"===typeof s.getSnapshotBeforeUpdate&&(e.effectTag|=256)):("function"!==typeof s.componentDidUpdate||a===t.memoizedProps&&l===t.memoizedState||(e.effectTag|=4),"function"!==typeof s.getSnapshotBeforeUpdate||a===t.memoizedProps&&l===t.memoizedState||(e.effectTag|=256),e.memoizedProps=r,e.memoizedState=f),s.props=r,s.state=f,s.context=u,r=c):("function"!==typeof s.componentDidUpdate||a===t.memoizedProps&&l===t.memoizedState||(e.effectTag|=4),"function"!==typeof s.getSnapshotBeforeUpdate||a===t.memoizedProps&&l===t.memoizedState||(e.effectTag|=256),r=!1);return Hs(t,e,n,r,o,i)}function Hs(t,e,n,r,i,o){Vs(t,e);var s=0!==(64&e.effectTag);if(!r&&!s)return i&&Ei(e,n,!1),ea(t,e,o);r=e.stateNode,Fs.current=e;var a=s&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return e.effectTag|=1,null!==t&&s?(e.child=Do(e,t.child,null,o),e.child=Do(e,null,a,o)):Ls(t,e,a,o),e.memoizedState=r.state,i&&Ei(e,n,!0),e.child}function Ys(t){var e=t.stateNode;e.pendingContext?Ti(0,e.pendingContext,e.pendingContext!==e.context):e.context&&Ti(0,e.context,!1),Uo(t,e.containerInfo)}var $s,Qs,Ks,qs,Gs={dehydrated:null,retryTime:0};function Js(t,e,n){var r,i=e.mode,o=e.pendingProps,s=jo.current,a=!1;if((r=0!==(64&e.effectTag))||(r=0!==(2&s)&&(null===t||null!==t.memoizedState)),r?(a=!0,e.effectTag&=-65):null!==t&&null===t.memoizedState||void 0===o.fallback||!0===o.unstable_avoidThisFallback||(s|=1),yi(jo,1&s),null===t){if(void 0!==o.fallback&&Ds(e),a){if(a=o.fallback,(o=Yl(null,i,0,null)).return=e,0===(2&e.mode))for(t=null!==e.memoizedState?e.child.child:e.child,o.child=t;null!==t;)t.return=o,t=t.sibling;return(n=Yl(a,i,n,null)).return=e,o.sibling=n,e.memoizedState=Gs,e.child=o,n}return i=o.children,e.memoizedState=null,e.child=Bo(e,null,i,n)}if(null!==t.memoizedState){if(i=(t=t.child).sibling,a){if(o=o.fallback,(n=Wl(t,t.pendingProps)).return=e,0===(2&e.mode)&&(a=null!==e.memoizedState?e.child.child:e.child)!==t.child)for(n.child=a;null!==a;)a.return=n,a=a.sibling;return(i=Wl(i,o)).return=e,n.sibling=i,n.childExpirationTime=0,e.memoizedState=Gs,e.child=n,i}return n=Do(e,t.child,o.children,n),e.memoizedState=null,e.child=n}if(t=t.child,a){if(a=o.fallback,(o=Yl(null,i,0,null)).return=e,o.child=t,null!==t&&(t.return=o),0===(2&e.mode))for(t=null!==e.memoizedState?e.child.child:e.child,o.child=t;null!==t;)t.return=o,t=t.sibling;return(n=Yl(a,i,n,null)).return=e,o.sibling=n,n.effectTag|=2,o.childExpirationTime=0,e.memoizedState=Gs,e.child=o,n}return e.memoizedState=null,e.child=Do(e,t,o.children,n)}function Xs(t,e){t.expirationTime<e&&(t.expirationTime=e);var n=t.alternate;null!==n&&n.expirationTime<e&&(n.expirationTime=e),ao(t.return,e)}function Zs(t,e,n,r,i,o){var s=t.memoizedState;null===s?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:r,tail:n,tailExpiration:0,tailMode:i,lastEffect:o}:(s.isBackwards=e,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailExpiration=0,s.tailMode=i,s.lastEffect=o)}function ta(t,e,n){var r=e.pendingProps,i=r.revealOrder,o=r.tail;if(Ls(t,e,r.children,n),0!==(2&(r=jo.current)))r=1&r|2,e.effectTag|=64;else{if(null!==t&&0!==(64&t.effectTag))t:for(t=e.child;null!==t;){if(13===t.tag)null!==t.memoizedState&&Xs(t,n);else if(19===t.tag)Xs(t,n);else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;null===t.sibling;){if(null===t.return||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}r&=1}if(yi(jo,r),0===(2&e.mode))e.memoizedState=null;else switch(i){case"forwards":for(n=e.child,i=null;null!==n;)null!==(t=n.alternate)&&null===Wo(t)&&(i=n),n=n.sibling;null===(n=i)?(i=e.child,e.child=null):(i=n.sibling,n.sibling=null),Zs(e,!1,i,n,o,e.lastEffect);break;case"backwards":for(n=null,i=e.child,e.child=null;null!==i;){if(null!==(t=i.alternate)&&null===Wo(t)){e.child=i;break}t=i.sibling,i.sibling=n,n=i,i=t}Zs(e,!0,n,null,o,e.lastEffect);break;case"together":Zs(e,!1,null,null,void 0,e.lastEffect);break;default:e.memoizedState=null}return e.child}function ea(t,e,n){null!==t&&(e.dependencies=t.dependencies);var r=e.expirationTime;if(0!==r&&Tl(r),e.childExpirationTime<n)return null;if(null!==t&&e.child!==t.child)throw Error(s(153));if(null!==e.child){for(n=Wl(t=e.child,t.pendingProps),e.child=n,n.return=e;null!==t.sibling;)t=t.sibling,(n=n.sibling=Wl(t,t.pendingProps)).return=e;n.sibling=null}return e.child}function na(t,e){switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;null!==e;)null!==e.alternate&&(n=e),e=e.sibling;null===n?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?e||null===t.tail?t.tail=null:t.tail.sibling=null:r.sibling=null}}function ra(t,e,n){var r=e.pendingProps;switch(e.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return null;case 1:case 17:return _i(e.type)&&Si(),null;case 3:return Ro(),pi(gi),pi(mi),(n=e.stateNode).pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==t&&null!==t.child||!Ns(e)||(e.effectTag|=4),Qs(e),null;case 5:Vo(e),n=Lo(Po.current);var o=e.type;if(null!==t&&null!=e.stateNode)Ks(t,e,o,r,n),t.ref!==e.ref&&(e.effectTag|=128);else{if(!r){if(null===e.stateNode)throw Error(s(166));return null}if(t=Lo(Mo.current),Ns(e)){r=e.stateNode,o=e.type;var a=e.memoizedProps;switch(r[Cn]=e,r[Dn]=a,o){case"iframe":case"object":case"embed":Ke("load",r);break;case"video":case"audio":for(t=0;t<Jt.length;t++)Ke(Jt[t],r);break;case"source":Ke("error",r);break;case"img":case"image":case"link":Ke("error",r),Ke("load",r);break;case"form":Ke("reset",r),Ke("submit",r);break;case"details":Ke("toggle",r);break;case"input":Tt(r,a),Ke("invalid",r),un(n,"onChange");break;case"select":r._wrapperState={wasMultiple:!!a.multiple},Ke("invalid",r),un(n,"onChange");break;case"textarea":Dt(r,a),Ke("invalid",r),un(n,"onChange")}for(var l in sn(o,a),t=null,a)if(a.hasOwnProperty(l)){var u=a[l];"children"===l?"string"===typeof u?r.textContent!==u&&(t=["children",u]):"number"===typeof u&&r.textContent!==""+u&&(t=["children",""+u]):I.hasOwnProperty(l)&&null!=u&&un(n,l)}switch(o){case"input":wt(r),Et(r,a,!0);break;case"textarea":wt(r),Nt(r);break;case"select":case"option":break;default:"function"===typeof a.onClick&&(r.onclick=cn)}n=t,e.updateQueue=n,null!==n&&(e.effectTag|=4)}else{switch(l=9===n.nodeType?n:n.ownerDocument,t===ln&&(t=Pt(o)),t===ln?"script"===o?((t=l.createElement("div")).innerHTML="<script><\/script>",t=t.removeChild(t.firstChild)):"string"===typeof r.is?t=l.createElement(o,{is:r.is}):(t=l.createElement(o),"select"===o&&(l=t,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):t=l.createElementNS(t,o),t[Cn]=e,t[Dn]=r,$s(t,e,!1,!1),e.stateNode=t,l=an(o,r),o){case"iframe":case"object":case"embed":Ke("load",t),u=r;break;case"video":case"audio":for(u=0;u<Jt.length;u++)Ke(Jt[u],t);u=r;break;case"source":Ke("error",t),u=r;break;case"img":case"image":case"link":Ke("error",t),Ke("load",t),u=r;break;case"form":Ke("reset",t),Ke("submit",t),u=r;break;case"details":Ke("toggle",t),u=r;break;case"input":Tt(t,r),u=St(t,r),Ke("invalid",t),un(n,"onChange");break;case"option":u=Ot(t,r);break;case"select":t._wrapperState={wasMultiple:!!r.multiple},u=i({},r,{value:void 0}),Ke("invalid",t),un(n,"onChange");break;case"textarea":Dt(t,r),u=Ct(t,r),Ke("invalid",t),un(n,"onChange");break;default:u=r}sn(o,u);var c=u;for(a in c)if(c.hasOwnProperty(a)){var d=c[a];"style"===a?rn(t,d):"dangerouslySetInnerHTML"===a?null!=(d=d?d.__html:void 0)&&zt(t,d):"children"===a?"string"===typeof d?("textarea"!==o||""!==d)&&Vt(t,d):"number"===typeof d&&Vt(t,""+d):"suppressContentEditableWarning"!==a&&"suppressHydrationWarning"!==a&&"autoFocus"!==a&&(I.hasOwnProperty(a)?null!=d&&un(n,a):null!=d&&J(t,a,d,l))}switch(o){case"input":wt(t),Et(t,r,!1);break;case"textarea":wt(t),Nt(t);break;case"option":null!=r.value&&t.setAttribute("value",""+gt(r.value));break;case"select":t.multiple=!!r.multiple,null!=(n=r.value)?At(t,!!r.multiple,n,!1):null!=r.defaultValue&&At(t,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof u.onClick&&(t.onclick=cn)}Tn(o,r)&&(e.effectTag|=4)}null!==e.ref&&(e.effectTag|=128)}return null;case 6:if(t&&null!=e.stateNode)qs(t,e,t.memoizedProps,r);else{if("string"!==typeof r&&null===e.stateNode)throw Error(s(166));n=Lo(Po.current),Lo(Mo.current),Ns(e)?(n=e.stateNode,r=e.memoizedProps,n[Cn]=e,n.nodeValue!==r&&(e.effectTag|=4)):((n=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[Cn]=e,e.stateNode=n)}return null;case 13:return pi(jo),r=e.memoizedState,0!==(64&e.effectTag)?(e.expirationTime=n,e):(n=null!==r,r=!1,null===t?void 0!==e.memoizedProps.fallback&&Ns(e):(r=null!==(o=t.memoizedState),n||null===o||null!==(o=t.child.sibling)&&(null!==(a=e.firstEffect)?(e.firstEffect=o,o.nextEffect=a):(e.firstEffect=e.lastEffect=o,o.nextEffect=null),o.effectTag=8)),n&&!r&&0!==(2&e.mode)&&(null===t&&!0!==e.memoizedProps.unstable_avoidThisFallback||0!==(1&jo.current)?Wa===Na&&(Wa=Pa):(Wa!==Na&&Wa!==Pa||(Wa=La),0!==Ka&&null!==za&&(Gl(za,ja),Jl(za,Ka)))),(n||r)&&(e.effectTag|=4),null);case 4:return Ro(),Qs(e),null;case 10:return so(e),null;case 19:if(pi(jo),null===(r=e.memoizedState))return null;if(o=0!==(64&e.effectTag),null===(a=r.rendering)){if(o)na(r,!1);else if(Wa!==Na||null!==t&&0!==(64&t.effectTag))for(a=e.child;null!==a;){if(null!==(t=Wo(a))){for(e.effectTag|=64,na(r,!1),null!==(o=t.updateQueue)&&(e.updateQueue=o,e.effectTag|=4),null===r.lastEffect&&(e.firstEffect=null),e.lastEffect=r.lastEffect,r=e.child;null!==r;)a=n,(o=r).effectTag&=2,o.nextEffect=null,o.firstEffect=null,o.lastEffect=null,null===(t=o.alternate)?(o.childExpirationTime=0,o.expirationTime=a,o.child=null,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null):(o.childExpirationTime=t.childExpirationTime,o.expirationTime=t.expirationTime,o.child=t.child,o.memoizedProps=t.memoizedProps,o.memoizedState=t.memoizedState,o.updateQueue=t.updateQueue,a=t.dependencies,o.dependencies=null===a?null:{expirationTime:a.expirationTime,firstContext:a.firstContext,responders:a.responders}),r=r.sibling;return yi(jo,1&jo.current|2),e.child}a=a.sibling}}else{if(!o)if(null!==(t=Wo(a))){if(e.effectTag|=64,o=!0,null!==(n=t.updateQueue)&&(e.updateQueue=n,e.effectTag|=4),na(r,!0),null===r.tail&&"hidden"===r.tailMode&&!a.alternate)return null!==(e=e.lastEffect=r.lastEffect)&&(e.nextEffect=null),null}else 2*Yi()-r.renderingStartTime>r.tailExpiration&&1<n&&(e.effectTag|=64,o=!0,na(r,!1),e.expirationTime=e.childExpirationTime=n-1);r.isBackwards?(a.sibling=e.child,e.child=a):(null!==(n=r.last)?n.sibling=a:e.child=a,r.last=a)}return null!==r.tail?(0===r.tailExpiration&&(r.tailExpiration=Yi()+500),n=r.tail,r.rendering=n,r.tail=n.sibling,r.lastEffect=e.lastEffect,r.renderingStartTime=Yi(),n.sibling=null,e=jo.current,yi(jo,o?1&e|2:1&e),n):null}throw Error(s(156,e.tag))}function ia(t){switch(t.tag){case 1:_i(t.type)&&Si();var e=t.effectTag;return 4096&e?(t.effectTag=-4097&e|64,t):null;case 3:if(Ro(),pi(gi),pi(mi),0!==(64&(e=t.effectTag)))throw Error(s(285));return t.effectTag=-4097&e|64,t;case 5:return Vo(t),null;case 13:return pi(jo),4096&(e=t.effectTag)?(t.effectTag=-4097&e|64,t):null;case 19:return pi(jo),null;case 4:return Ro(),null;case 10:return so(t),null;default:return null}}function oa(t,e){return{value:t,source:e,stack:mt(e)}}$s=function(t,e){for(var n=e.child;null!==n;){if(5===n.tag||6===n.tag)t.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===e)break;for(;null===n.sibling;){if(null===n.return||n.return===e)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Qs=function(){},Ks=function(t,e,n,r,o){var s=t.memoizedProps;if(s!==r){var a,l,u=e.stateNode;switch(Lo(Mo.current),t=null,n){case"input":s=St(u,s),r=St(u,r),t=[];break;case"option":s=Ot(u,s),r=Ot(u,r),t=[];break;case"select":s=i({},s,{value:void 0}),r=i({},r,{value:void 0}),t=[];break;case"textarea":s=Ct(u,s),r=Ct(u,r),t=[];break;default:"function"!==typeof s.onClick&&"function"===typeof r.onClick&&(u.onclick=cn)}for(a in sn(n,r),n=null,s)if(!r.hasOwnProperty(a)&&s.hasOwnProperty(a)&&null!=s[a])if("style"===a)for(l in u=s[a])u.hasOwnProperty(l)&&(n||(n={}),n[l]="");else"dangerouslySetInnerHTML"!==a&&"children"!==a&&"suppressContentEditableWarning"!==a&&"suppressHydrationWarning"!==a&&"autoFocus"!==a&&(I.hasOwnProperty(a)?t||(t=[]):(t=t||[]).push(a,null));for(a in r){var c=r[a];if(u=null!=s?s[a]:void 0,r.hasOwnProperty(a)&&c!==u&&(null!=c||null!=u))if("style"===a)if(u){for(l in u)!u.hasOwnProperty(l)||c&&c.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in c)c.hasOwnProperty(l)&&u[l]!==c[l]&&(n||(n={}),n[l]=c[l])}else n||(t||(t=[]),t.push(a,n)),n=c;else"dangerouslySetInnerHTML"===a?(c=c?c.__html:void 0,u=u?u.__html:void 0,null!=c&&u!==c&&(t=t||[]).push(a,c)):"children"===a?u===c||"string"!==typeof c&&"number"!==typeof c||(t=t||[]).push(a,""+c):"suppressContentEditableWarning"!==a&&"suppressHydrationWarning"!==a&&(I.hasOwnProperty(a)?(null!=c&&un(o,a),t||u===c||(t=[])):(t=t||[]).push(a,c))}n&&(t=t||[]).push("style",n),o=t,(e.updateQueue=o)&&(e.effectTag|=4)}},qs=function(t,e,n,r){n!==r&&(e.effectTag|=4)};var sa="function"===typeof WeakSet?WeakSet:Set;function aa(t,e){var n=e.source,r=e.stack;null===r&&null!==n&&(r=mt(n)),null!==n&&bt(n.type),e=e.value,null!==t&&1===t.tag&&bt(t.type);try{console.error(e)}catch(i){setTimeout((function(){throw i}))}}function la(t){var e=t.ref;if(null!==e)if("function"===typeof e)try{e(null)}catch(n){Fl(t,n)}else e.current=null}function ua(t,e){switch(e.tag){case 0:case 11:case 15:case 22:case 3:case 5:case 6:case 4:case 17:return;case 1:if(256&e.effectTag&&null!==t){var n=t.memoizedProps,r=t.memoizedState;e=(t=e.stateNode).getSnapshotBeforeUpdate(e.elementType===e.type?n:to(e.type,n),r),t.__reactInternalSnapshotBeforeUpdate=e}return}throw Error(s(163))}function ca(t,e){if(null!==(e=null!==(e=e.updateQueue)?e.lastEffect:null)){var n=e=e.next;do{if((n.tag&t)===t){var r=n.destroy;n.destroy=void 0,void 0!==r&&r()}n=n.next}while(n!==e)}}function da(t,e){if(null!==(e=null!==(e=e.updateQueue)?e.lastEffect:null)){var n=e=e.next;do{if((n.tag&t)===t){var r=n.create;n.destroy=r()}n=n.next}while(n!==e)}}function fa(t,e,n){switch(n.tag){case 0:case 11:case 15:case 22:return void da(3,n);case 1:if(t=n.stateNode,4&n.effectTag)if(null===e)t.componentDidMount();else{var r=n.elementType===n.type?e.memoizedProps:to(n.type,e.memoizedProps);t.componentDidUpdate(r,e.memoizedState,t.__reactInternalSnapshotBeforeUpdate)}return void(null!==(e=n.updateQueue)&&go(n,e,t));case 3:if(null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 5:case 1:t=n.child.stateNode}go(n,e,t)}return;case 5:return t=n.stateNode,void(null===e&&4&n.effectTag&&Tn(n.type,n.memoizedProps)&&t.focus());case 6:case 4:case 12:case 19:case 17:case 20:case 21:return;case 13:return void(null===n.memoizedState&&(n=n.alternate,null!==n&&(n=n.memoizedState,null!==n&&(n=n.dehydrated,null!==n&&Le(n)))))}throw Error(s(163))}function ha(t,e,n){switch("function"===typeof Rl&&Rl(e),e.tag){case 0:case 11:case 14:case 15:case 22:if(null!==(t=e.updateQueue)&&null!==(t=t.lastEffect)){var r=t.next;Ki(97<n?97:n,(function(){var t=r;do{var n=t.destroy;if(void 0!==n){var i=e;try{n()}catch(o){Fl(i,o)}}t=t.next}while(t!==r)}))}break;case 1:la(e),"function"===typeof(n=e.stateNode).componentWillUnmount&&function(t,e){try{e.props=t.memoizedProps,e.state=t.memoizedState,e.componentWillUnmount()}catch(n){Fl(t,n)}}(e,n);break;case 5:la(e);break;case 4:va(t,e,n)}}function pa(t){var e=t.alternate;t.return=null,t.child=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.alternate=null,t.firstEffect=null,t.lastEffect=null,t.pendingProps=null,t.memoizedProps=null,t.stateNode=null,null!==e&&pa(e)}function ya(t){return 5===t.tag||3===t.tag||4===t.tag}function ba(t){t:{for(var e=t.return;null!==e;){if(ya(e)){var n=e;break t}e=e.return}throw Error(s(160))}switch(e=n.stateNode,n.tag){case 5:var r=!1;break;case 3:case 4:e=e.containerInfo,r=!0;break;default:throw Error(s(161))}16&n.effectTag&&(Vt(e,""),n.effectTag&=-17);t:e:for(n=t;;){for(;null===n.sibling;){if(null===n.return||ya(n.return)){n=null;break t}n=n.return}for(n.sibling.return=n.return,n=n.sibling;5!==n.tag&&6!==n.tag&&18!==n.tag;){if(2&n.effectTag)continue e;if(null===n.child||4===n.tag)continue e;n.child.return=n,n=n.child}if(!(2&n.effectTag)){n=n.stateNode;break t}}r?ma(t,n,e):ga(t,n,e)}function ma(t,e,n){var r=t.tag,i=5===r||6===r;if(i)t=i?t.stateNode:t.stateNode.instance,e?8===n.nodeType?n.parentNode.insertBefore(t,e):n.insertBefore(t,e):(8===n.nodeType?(e=n.parentNode).insertBefore(t,n):(e=n).appendChild(t),null!==(n=n._reactRootContainer)&&void 0!==n||null!==e.onclick||(e.onclick=cn));else if(4!==r&&null!==(t=t.child))for(ma(t,e,n),t=t.sibling;null!==t;)ma(t,e,n),t=t.sibling}function ga(t,e,n){var r=t.tag,i=5===r||6===r;if(i)t=i?t.stateNode:t.stateNode.instance,e?n.insertBefore(t,e):n.appendChild(t);else if(4!==r&&null!==(t=t.child))for(ga(t,e,n),t=t.sibling;null!==t;)ga(t,e,n),t=t.sibling}function va(t,e,n){for(var r,i,o=e,a=!1;;){if(!a){a=o.return;t:for(;;){if(null===a)throw Error(s(160));switch(r=a.stateNode,a.tag){case 5:i=!1;break t;case 3:case 4:r=r.containerInfo,i=!0;break t}a=a.return}a=!0}if(5===o.tag||6===o.tag){t:for(var l=t,u=o,c=n,d=u;;)if(ha(l,d,c),null!==d.child&&4!==d.tag)d.child.return=d,d=d.child;else{if(d===u)break t;for(;null===d.sibling;){if(null===d.return||d.return===u)break t;d=d.return}d.sibling.return=d.return,d=d.sibling}i?(l=r,u=o.stateNode,8===l.nodeType?l.parentNode.removeChild(u):l.removeChild(u)):r.removeChild(o.stateNode)}else if(4===o.tag){if(null!==o.child){r=o.stateNode.containerInfo,i=!0,o.child.return=o,o=o.child;continue}}else if(ha(t,o,n),null!==o.child){o.child.return=o,o=o.child;continue}if(o===e)break;for(;null===o.sibling;){if(null===o.return||o.return===e)return;4===(o=o.return).tag&&(a=!1)}o.sibling.return=o.return,o=o.sibling}}function wa(t,e){switch(e.tag){case 0:case 11:case 14:case 15:case 22:return void ca(3,e);case 1:case 12:case 17:return;case 5:var n=e.stateNode;if(null!=n){var r=e.memoizedProps,i=null!==t?t.memoizedProps:r;t=e.type;var o=e.updateQueue;if(e.updateQueue=null,null!==o){for(n[Dn]=r,"input"===t&&"radio"===r.type&&null!=r.name&&It(n,r),an(t,i),e=an(t,r),i=0;i<o.length;i+=2){var a=o[i],l=o[i+1];"style"===a?rn(n,l):"dangerouslySetInnerHTML"===a?zt(n,l):"children"===a?Vt(n,l):J(n,a,l,e)}switch(t){case"input":xt(n,r);break;case"textarea":Bt(n,r);break;case"select":e=n._wrapperState.wasMultiple,n._wrapperState.wasMultiple=!!r.multiple,null!=(t=r.value)?At(n,!!r.multiple,t,!1):e!==!!r.multiple&&(null!=r.defaultValue?At(n,!!r.multiple,r.defaultValue,!0):At(n,!!r.multiple,r.multiple?[]:"",!1))}}}return;case 6:if(null===e.stateNode)throw Error(s(162));return void(e.stateNode.nodeValue=e.memoizedProps);case 3:return void((e=e.stateNode).hydrate&&(e.hydrate=!1,Le(e.containerInfo)));case 13:if(n=e,null===e.memoizedState?r=!1:(r=!0,n=e.child,Ga=Yi()),null!==n)t:for(t=n;;){if(5===t.tag)o=t.stateNode,r?"function"===typeof(o=o.style).setProperty?o.setProperty("display","none","important"):o.display="none":(o=t.stateNode,i=void 0!==(i=t.memoizedProps.style)&&null!==i&&i.hasOwnProperty("display")?i.display:null,o.style.display=nn("display",i));else if(6===t.tag)t.stateNode.nodeValue=r?"":t.memoizedProps;else{if(13===t.tag&&null!==t.memoizedState&&null===t.memoizedState.dehydrated){(o=t.child.sibling).return=t,t=o;continue}if(null!==t.child){t.child.return=t,t=t.child;continue}}if(t===n)break;for(;null===t.sibling;){if(null===t.return||t.return===n)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}return void _a(e);case 19:return void _a(e)}throw Error(s(163))}function _a(t){var e=t.updateQueue;if(null!==e){t.updateQueue=null;var n=t.stateNode;null===n&&(n=t.stateNode=new sa),e.forEach((function(e){var r=Ll.bind(null,t,e);n.has(e)||(n.add(e),e.then(r,r))}))}}var Sa="function"===typeof WeakMap?WeakMap:Map;function Ta(t,e,n){(n=po(n,null)).tag=3,n.payload={element:null};var r=e.value;return n.callback=function(){Za||(Za=!0,tl=r),aa(t,e)},n}function Ia(t,e,n){(n=po(n,null)).tag=3;var r=t.type.getDerivedStateFromError;if("function"===typeof r){var i=e.value;n.payload=function(){return aa(t,e),r(i)}}var o=t.stateNode;return null!==o&&"function"===typeof o.componentDidCatch&&(n.callback=function(){"function"!==typeof r&&(null===el?el=new Set([this]):el.add(this),aa(t,e));var n=e.stack;this.componentDidCatch(e.value,{componentStack:null!==n?n:""})}),n}var xa,Ea=Math.ceil,ka=G.ReactCurrentDispatcher,Oa=G.ReactCurrentOwner,Aa=0,Ca=8,Da=16,Ba=32,Na=0,Ma=1,Fa=2,Pa=3,La=4,Ua=5,Ra=Aa,za=null,Va=null,ja=0,Wa=Na,Ha=null,Ya=1073741823,$a=1073741823,Qa=null,Ka=0,qa=!1,Ga=0,Ja=500,Xa=null,Za=!1,tl=null,el=null,nl=!1,rl=null,il=90,ol=null,sl=0,al=null,ll=0;function ul(){return(Ra&(Da|Ba))!==Aa?1073741821-(Yi()/10|0):0!==ll?ll:ll=1073741821-(Yi()/10|0)}function cl(t,e,n){if(0===(2&(e=e.mode)))return 1073741823;var r=$i();if(0===(4&e))return 99===r?1073741823:1073741822;if((Ra&Da)!==Aa)return ja;if(null!==n)t=Zi(t,0|n.timeoutMs||5e3,250);else switch(r){case 99:t=1073741823;break;case 98:t=Zi(t,150,100);break;case 97:case 96:t=Zi(t,5e3,250);break;case 95:t=2;break;default:throw Error(s(326))}return null!==za&&t===ja&&--t,t}function dl(t,e){if(50<sl)throw sl=0,al=null,Error(s(185));if(null!==(t=fl(t,e))){var n=$i();1073741823===e?(Ra&Ca)!==Aa&&(Ra&(Da|Ba))===Aa?bl(t):(pl(t),Ra===Aa&&Ji()):pl(t),(4&Ra)===Aa||98!==n&&99!==n||(null===ol?ol=new Map([[t,e]]):(void 0===(n=ol.get(t))||n>e)&&ol.set(t,e))}}function fl(t,e){t.expirationTime<e&&(t.expirationTime=e);var n=t.alternate;null!==n&&n.expirationTime<e&&(n.expirationTime=e);var r=t.return,i=null;if(null===r&&3===t.tag)i=t.stateNode;else for(;null!==r;){if(n=r.alternate,r.childExpirationTime<e&&(r.childExpirationTime=e),null!==n&&n.childExpirationTime<e&&(n.childExpirationTime=e),null===r.return&&3===r.tag){i=r.stateNode;break}r=r.return}return null!==i&&(za===i&&(Tl(e),Wa===La&&Gl(i,ja)),Jl(i,e)),i}function hl(t){var e=t.lastExpiredTime;if(0!==e)return e;if(!ql(t,e=t.firstPendingTime))return e;var n=t.lastPingedTime;return 2>=(t=n>(t=t.nextKnownPendingLevel)?n:t)&&e!==t?0:t}function pl(t){if(0!==t.lastExpiredTime)t.callbackExpirationTime=1073741823,t.callbackPriority=99,t.callbackNode=Gi(bl.bind(null,t));else{var e=hl(t),n=t.callbackNode;if(0===e)null!==n&&(t.callbackNode=null,t.callbackExpirationTime=0,t.callbackPriority=90);else{var r=ul();if(1073741823===e?r=99:1===e||2===e?r=95:r=0>=(r=10*(1073741821-e)-10*(1073741821-r))?99:250>=r?98:5250>=r?97:95,null!==n){var i=t.callbackPriority;if(t.callbackExpirationTime===e&&i>=r)return;n!==Ui&&Ai(n)}t.callbackExpirationTime=e,t.callbackPriority=r,e=1073741823===e?Gi(bl.bind(null,t)):qi(r,yl.bind(null,t),{timeout:10*(1073741821-e)-Yi()}),t.callbackNode=e}}}function yl(t,e){if(ll=0,e)return Xl(t,e=ul()),pl(t),null;var n=hl(t);if(0!==n){if(e=t.callbackNode,(Ra&(Da|Ba))!==Aa)throw Error(s(327));if(Bl(),t===za&&n===ja||vl(t,n),null!==Va){var r=Ra;Ra|=Da;for(var i=_l();;)try{xl();break}catch(l){wl(t,l)}if(oo(),Ra=r,ka.current=i,Wa===Ma)throw e=Ha,vl(t,n),Gl(t,n),pl(t),e;if(null===Va)switch(i=t.finishedWork=t.current.alternate,t.finishedExpirationTime=n,r=Wa,za=null,r){case Na:case Ma:throw Error(s(345));case Fa:Xl(t,2<n?2:n);break;case Pa:if(Gl(t,n),n===(r=t.lastSuspendedTime)&&(t.nextKnownPendingLevel=Ol(i)),1073741823===Ya&&10<(i=Ga+Ja-Yi())){if(qa){var o=t.lastPingedTime;if(0===o||o>=n){t.lastPingedTime=n,vl(t,n);break}}if(0!==(o=hl(t))&&o!==n)break;if(0!==r&&r!==n){t.lastPingedTime=r;break}t.timeoutHandle=xn(Al.bind(null,t),i);break}Al(t);break;case La:if(Gl(t,n),n===(r=t.lastSuspendedTime)&&(t.nextKnownPendingLevel=Ol(i)),qa&&(0===(i=t.lastPingedTime)||i>=n)){t.lastPingedTime=n,vl(t,n);break}if(0!==(i=hl(t))&&i!==n)break;if(0!==r&&r!==n){t.lastPingedTime=r;break}if(1073741823!==$a?r=10*(1073741821-$a)-Yi():1073741823===Ya?r=0:(r=10*(1073741821-Ya)-5e3,0>(r=(i=Yi())-r)&&(r=0),(n=10*(1073741821-n)-i)<(r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Ea(r/1960))-r)&&(r=n)),10<r){t.timeoutHandle=xn(Al.bind(null,t),r);break}Al(t);break;case Ua:if(1073741823!==Ya&&null!==Qa){o=Ya;var a=Qa;if(0>=(r=0|a.busyMinDurationMs)?r=0:(i=0|a.busyDelayMs,r=(o=Yi()-(10*(1073741821-o)-(0|a.timeoutMs||5e3)))<=i?0:i+r-o),10<r){Gl(t,n),t.timeoutHandle=xn(Al.bind(null,t),r);break}}Al(t);break;default:throw Error(s(329))}if(pl(t),t.callbackNode===e)return yl.bind(null,t)}}return null}function bl(t){var e=t.lastExpiredTime;if(e=0!==e?e:1073741823,(Ra&(Da|Ba))!==Aa)throw Error(s(327));if(Bl(),t===za&&e===ja||vl(t,e),null!==Va){var n=Ra;Ra|=Da;for(var r=_l();;)try{Il();break}catch(i){wl(t,i)}if(oo(),Ra=n,ka.current=r,Wa===Ma)throw n=Ha,vl(t,e),Gl(t,e),pl(t),n;if(null!==Va)throw Error(s(261));t.finishedWork=t.current.alternate,t.finishedExpirationTime=e,za=null,Al(t),pl(t)}return null}function ml(t,e){var n=Ra;Ra|=1;try{return t(e)}finally{(Ra=n)===Aa&&Ji()}}function gl(t,e){var n=Ra;Ra&=-2,Ra|=Ca;try{return t(e)}finally{(Ra=n)===Aa&&Ji()}}function vl(t,e){t.finishedWork=null,t.finishedExpirationTime=0;var n=t.timeoutHandle;if(-1!==n&&(t.timeoutHandle=-1,En(n)),null!==Va)for(n=Va.return;null!==n;){var r=n;switch(r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Si();break;case 3:Ro(),pi(gi),pi(mi);break;case 5:Vo(r);break;case 4:Ro();break;case 13:case 19:pi(jo);break;case 10:so(r)}n=n.return}za=t,Va=Wl(t.current,null),ja=e,Wa=Na,Ha=null,$a=Ya=1073741823,Qa=null,Ka=0,qa=!1}function wl(t,e){for(;;){try{if(oo(),Yo.current=Ss,Jo)for(var n=Ko.memoizedState;null!==n;){var r=n.queue;null!==r&&(r.pending=null),n=n.next}if(Qo=0,Go=qo=Ko=null,Jo=!1,null===Va||null===Va.return)return Wa=Ma,Ha=e,Va=null;t:{var i=t,o=Va.return,s=Va,a=e;if(e=ja,s.effectTag|=2048,s.firstEffect=s.lastEffect=null,null!==a&&"object"===typeof a&&"function"===typeof a.then){var l=a;if(0===(2&s.mode)){var u=s.alternate;u?(s.updateQueue=u.updateQueue,s.memoizedState=u.memoizedState,s.expirationTime=u.expirationTime):(s.updateQueue=null,s.memoizedState=null)}var c=0!==(1&jo.current),d=o;do{var f;if(f=13===d.tag){var h=d.memoizedState;if(null!==h)f=null!==h.dehydrated;else{var p=d.memoizedProps;f=void 0!==p.fallback&&(!0!==p.unstable_avoidThisFallback||!c)}}if(f){var y=d.updateQueue;if(null===y){var b=new Set;b.add(l),d.updateQueue=b}else y.add(l);if(0===(2&d.mode)){if(d.effectTag|=64,s.effectTag&=-2981,1===s.tag)if(null===s.alternate)s.tag=17;else{var m=po(1073741823,null);m.tag=2,yo(s,m)}s.expirationTime=1073741823;break t}a=void 0,s=e;var g=i.pingCache;if(null===g?(g=i.pingCache=new Sa,a=new Set,g.set(l,a)):void 0===(a=g.get(l))&&(a=new Set,g.set(l,a)),!a.has(s)){a.add(s);var v=Pl.bind(null,i,l,s);l.then(v,v)}d.effectTag|=4096,d.expirationTime=e;break t}d=d.return}while(null!==d);a=Error((bt(s.type)||"A React component")+" suspended while rendering, but no fallback UI was specified.\n\nAdd a <Suspense fallback=...> component higher in the tree to provide a loading indicator or placeholder to display."+mt(s))}Wa!==Ua&&(Wa=Fa),a=oa(a,s),d=o;do{switch(d.tag){case 3:l=a,d.effectTag|=4096,d.expirationTime=e,bo(d,Ta(d,l,e));break t;case 1:l=a;var w=d.type,_=d.stateNode;if(0===(64&d.effectTag)&&("function"===typeof w.getDerivedStateFromError||null!==_&&"function"===typeof _.componentDidCatch&&(null===el||!el.has(_)))){d.effectTag|=4096,d.expirationTime=e,bo(d,Ia(d,l,e));break t}}d=d.return}while(null!==d)}Va=kl(Va)}catch(S){e=S;continue}break}}function _l(){var t=ka.current;return ka.current=Ss,null===t?Ss:t}function Sl(t,e){t<Ya&&2<t&&(Ya=t),null!==e&&t<$a&&2<t&&($a=t,Qa=e)}function Tl(t){t>Ka&&(Ka=t)}function Il(){for(;null!==Va;)Va=El(Va)}function xl(){for(;null!==Va&&!Ri();)Va=El(Va)}function El(t){var e=xa(t.alternate,t,ja);return t.memoizedProps=t.pendingProps,null===e&&(e=kl(t)),Oa.current=null,e}function kl(t){Va=t;do{var e=Va.alternate;if(t=Va.return,0===(2048&Va.effectTag)){if(e=ra(e,Va,ja),1===ja||1!==Va.childExpirationTime){for(var n=0,r=Va.child;null!==r;){var i=r.expirationTime,o=r.childExpirationTime;i>n&&(n=i),o>n&&(n=o),r=r.sibling}Va.childExpirationTime=n}if(null!==e)return e;null!==t&&0===(2048&t.effectTag)&&(null===t.firstEffect&&(t.firstEffect=Va.firstEffect),null!==Va.lastEffect&&(null!==t.lastEffect&&(t.lastEffect.nextEffect=Va.firstEffect),t.lastEffect=Va.lastEffect),1<Va.effectTag&&(null!==t.lastEffect?t.lastEffect.nextEffect=Va:t.firstEffect=Va,t.lastEffect=Va))}else{if(null!==(e=ia(Va)))return e.effectTag&=2047,e;null!==t&&(t.firstEffect=t.lastEffect=null,t.effectTag|=2048)}if(null!==(e=Va.sibling))return e;Va=t}while(null!==Va);return Wa===Na&&(Wa=Ua),null}function Ol(t){var e=t.expirationTime;return e>(t=t.childExpirationTime)?e:t}function Al(t){var e=$i();return Ki(99,Cl.bind(null,t,e)),null}function Cl(t,e){do{Bl()}while(null!==rl);if((Ra&(Da|Ba))!==Aa)throw Error(s(327));var n=t.finishedWork,r=t.finishedExpirationTime;if(null===n)return null;if(t.finishedWork=null,t.finishedExpirationTime=0,n===t.current)throw Error(s(177));t.callbackNode=null,t.callbackExpirationTime=0,t.callbackPriority=90,t.nextKnownPendingLevel=0;var i=Ol(n);if(t.firstPendingTime=i,r<=t.lastSuspendedTime?t.firstSuspendedTime=t.lastSuspendedTime=t.nextKnownPendingLevel=0:r<=t.firstSuspendedTime&&(t.firstSuspendedTime=r-1),r<=t.lastPingedTime&&(t.lastPingedTime=0),r<=t.lastExpiredTime&&(t.lastExpiredTime=0),t===za&&(Va=za=null,ja=0),1<n.effectTag?null!==n.lastEffect?(n.lastEffect.nextEffect=n,i=n.firstEffect):i=n:i=n.firstEffect,null!==i){var o=Ra;Ra|=Ba,Oa.current=null,_n=Qe;var a=yn();if(bn(a)){if("selectionStart"in a)var l={start:a.selectionStart,end:a.selectionEnd};else t:{var u=(l=(l=a.ownerDocument)&&l.defaultView||window).getSelection&&l.getSelection();if(u&&0!==u.rangeCount){l=u.anchorNode;var c=u.anchorOffset,d=u.focusNode;u=u.focusOffset;try{l.nodeType,d.nodeType}catch(E){l=null;break t}var f=0,h=-1,p=-1,y=0,b=0,m=a,g=null;e:for(;;){for(var v;m!==l||0!==c&&3!==m.nodeType||(h=f+c),m!==d||0!==u&&3!==m.nodeType||(p=f+u),3===m.nodeType&&(f+=m.nodeValue.length),null!==(v=m.firstChild);)g=m,m=v;for(;;){if(m===a)break e;if(g===l&&++y===c&&(h=f),g===d&&++b===u&&(p=f),null!==(v=m.nextSibling))break;g=(m=g).parentNode}m=v}l=-1===h||-1===p?null:{start:h,end:p}}else l=null}l=l||{start:0,end:0}}else l=null;Sn={activeElementDetached:null,focusedElem:a,selectionRange:l},Qe=!1,Xa=i;do{try{Dl()}catch(E){if(null===Xa)throw Error(s(330));Fl(Xa,E),Xa=Xa.nextEffect}}while(null!==Xa);Xa=i;do{try{for(a=t,l=e;null!==Xa;){var w=Xa.effectTag;if(16&w&&Vt(Xa.stateNode,""),128&w){var _=Xa.alternate;if(null!==_){var S=_.ref;null!==S&&("function"===typeof S?S(null):S.current=null)}}switch(1038&w){case 2:ba(Xa),Xa.effectTag&=-3;break;case 6:ba(Xa),Xa.effectTag&=-3,wa(Xa.alternate,Xa);break;case 1024:Xa.effectTag&=-1025;break;case 1028:Xa.effectTag&=-1025,wa(Xa.alternate,Xa);break;case 4:wa(Xa.alternate,Xa);break;case 8:va(a,c=Xa,l),pa(c)}Xa=Xa.nextEffect}}catch(E){if(null===Xa)throw Error(s(330));Fl(Xa,E),Xa=Xa.nextEffect}}while(null!==Xa);if(S=Sn,_=yn(),w=S.focusedElem,l=S.selectionRange,_!==w&&w&&w.ownerDocument&&pn(w.ownerDocument.documentElement,w)){null!==l&&bn(w)&&(_=l.start,void 0===(S=l.end)&&(S=_),"selectionStart"in w?(w.selectionStart=_,w.selectionEnd=Math.min(S,w.value.length)):(S=(_=w.ownerDocument||document)&&_.defaultView||window).getSelection&&(S=S.getSelection(),c=w.textContent.length,a=Math.min(l.start,c),l=void 0===l.end?a:Math.min(l.end,c),!S.extend&&a>l&&(c=l,l=a,a=c),c=hn(w,a),d=hn(w,l),c&&d&&(1!==S.rangeCount||S.anchorNode!==c.node||S.anchorOffset!==c.offset||S.focusNode!==d.node||S.focusOffset!==d.offset)&&((_=_.createRange()).setStart(c.node,c.offset),S.removeAllRanges(),a>l?(S.addRange(_),S.extend(d.node,d.offset)):(_.setEnd(d.node,d.offset),S.addRange(_))))),_=[];for(S=w;S=S.parentNode;)1===S.nodeType&&_.push({element:S,left:S.scrollLeft,top:S.scrollTop});for("function"===typeof w.focus&&w.focus(),w=0;w<_.length;w++)(S=_[w]).element.scrollLeft=S.left,S.element.scrollTop=S.top}Qe=!!_n,Sn=_n=null,t.current=n,Xa=i;do{try{for(w=t;null!==Xa;){var T=Xa.effectTag;if(36&T&&fa(w,Xa.alternate,Xa),128&T){_=void 0;var I=Xa.ref;if(null!==I){var x=Xa.stateNode;Xa.tag,_=x,"function"===typeof I?I(_):I.current=_}}Xa=Xa.nextEffect}}catch(E){if(null===Xa)throw Error(s(330));Fl(Xa,E),Xa=Xa.nextEffect}}while(null!==Xa);Xa=null,zi(),Ra=o}else t.current=n;if(nl)nl=!1,rl=t,il=e;else for(Xa=i;null!==Xa;)e=Xa.nextEffect,Xa.nextEffect=null,Xa=e;if(0===(e=t.firstPendingTime)&&(el=null),1073741823===e?t===al?sl++:(sl=0,al=t):sl=0,"function"===typeof Ul&&Ul(n.stateNode,r),pl(t),Za)throw Za=!1,t=tl,tl=null,t;return(Ra&Ca)!==Aa||Ji(),null}function Dl(){for(;null!==Xa;){var t=Xa.effectTag;0!==(256&t)&&ua(Xa.alternate,Xa),0===(512&t)||nl||(nl=!0,qi(97,(function(){return Bl(),null}))),Xa=Xa.nextEffect}}function Bl(){if(90!==il){var t=97<il?97:il;return il=90,Ki(t,Nl)}}function Nl(){if(null===rl)return!1;var t=rl;if(rl=null,(Ra&(Da|Ba))!==Aa)throw Error(s(331));var e=Ra;for(Ra|=Ba,t=t.current.firstEffect;null!==t;){try{var n=t;if(0!==(512&n.effectTag))switch(n.tag){case 0:case 11:case 15:case 22:ca(5,n),da(5,n)}}catch(r){if(null===t)throw Error(s(330));Fl(t,r)}n=t.nextEffect,t.nextEffect=null,t=n}return Ra=e,Ji(),!0}function Ml(t,e,n){yo(t,e=Ta(t,e=oa(n,e),1073741823)),null!==(t=fl(t,1073741823))&&pl(t)}function Fl(t,e){if(3===t.tag)Ml(t,t,e);else for(var n=t.return;null!==n;){if(3===n.tag){Ml(n,t,e);break}if(1===n.tag){var r=n.stateNode;if("function"===typeof n.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===el||!el.has(r))){yo(n,t=Ia(n,t=oa(e,t),1073741823)),null!==(n=fl(n,1073741823))&&pl(n);break}}n=n.return}}function Pl(t,e,n){var r=t.pingCache;null!==r&&r.delete(e),za===t&&ja===n?Wa===La||Wa===Pa&&1073741823===Ya&&Yi()-Ga<Ja?vl(t,ja):qa=!0:ql(t,n)&&(0!==(e=t.lastPingedTime)&&e<n||(t.lastPingedTime=n,pl(t)))}function Ll(t,e){var n=t.stateNode;null!==n&&n.delete(e),0===(e=0)&&(e=cl(e=ul(),t,null)),null!==(t=fl(t,e))&&pl(t)}xa=function(t,e,n){var r=e.expirationTime;if(null!==t){var i=e.pendingProps;if(t.memoizedProps!==i||gi.current)Ps=!0;else{if(r<n){switch(Ps=!1,e.tag){case 3:Ys(e),Ms();break;case 5:if(zo(e),4&e.mode&&1!==n&&i.hidden)return e.expirationTime=e.childExpirationTime=1,null;break;case 1:_i(e.type)&&xi(e);break;case 4:Uo(e,e.stateNode.containerInfo);break;case 10:r=e.memoizedProps.value,i=e.type._context,yi(eo,i._currentValue),i._currentValue=r;break;case 13:if(null!==e.memoizedState)return 0!==(r=e.child.childExpirationTime)&&r>=n?Js(t,e,n):(yi(jo,1&jo.current),null!==(e=ea(t,e,n))?e.sibling:null);yi(jo,1&jo.current);break;case 19:if(r=e.childExpirationTime>=n,0!==(64&t.effectTag)){if(r)return ta(t,e,n);e.effectTag|=64}if(null!==(i=e.memoizedState)&&(i.rendering=null,i.tail=null),yi(jo,jo.current),!r)return null}return ea(t,e,n)}Ps=!1}}else Ps=!1;switch(e.expirationTime=0,e.tag){case 2:if(r=e.type,null!==t&&(t.alternate=null,e.alternate=null,e.effectTag|=2),t=e.pendingProps,i=wi(e,mi.current),lo(e,n),i=ts(null,e,r,t,i,n),e.effectTag|=1,"object"===typeof i&&null!==i&&"function"===typeof i.render&&void 0===i.$$typeof){if(e.tag=1,e.memoizedState=null,e.updateQueue=null,_i(r)){var o=!0;xi(e)}else o=!1;e.memoizedState=null!==i.state&&void 0!==i.state?i.state:null,fo(e);var a=r.getDerivedStateFromProps;"function"===typeof a&&_o(e,r,a,t),i.updater=So,e.stateNode=i,i._reactInternalFiber=e,Eo(e,r,t,n),e=Hs(null,e,r,!0,o,n)}else e.tag=0,Ls(null,e,i,n),e=e.child;return e;case 16:t:{if(i=e.elementType,null!==t&&(t.alternate=null,e.alternate=null,e.effectTag|=2),t=e.pendingProps,function(t){if(-1===t._status){t._status=0;var e=t._ctor;e=e(),t._result=e,e.then((function(e){0===t._status&&(e=e.default,t._status=1,t._result=e)}),(function(e){0===t._status&&(t._status=2,t._result=e)}))}}(i),1!==i._status)throw i._result;switch(i=i._result,e.type=i,o=e.tag=function(t){if("function"===typeof t)return jl(t)?1:0;if(void 0!==t&&null!==t){if((t=t.$$typeof)===lt)return 11;if(t===dt)return 14}return 2}(i),t=to(i,t),o){case 0:e=js(null,e,i,t,n);break t;case 1:e=Ws(null,e,i,t,n);break t;case 11:e=Us(null,e,i,t,n);break t;case 14:e=Rs(null,e,i,to(i.type,t),r,n);break t}throw Error(s(306,i,""))}return e;case 0:return r=e.type,i=e.pendingProps,js(t,e,r,i=e.elementType===r?i:to(r,i),n);case 1:return r=e.type,i=e.pendingProps,Ws(t,e,r,i=e.elementType===r?i:to(r,i),n);case 3:if(Ys(e),r=e.updateQueue,null===t||null===r)throw Error(s(282));if(r=e.pendingProps,i=null!==(i=e.memoizedState)?i.element:null,ho(t,e),mo(e,r,null,n),(r=e.memoizedState.element)===i)Ms(),e=ea(t,e,n);else{if((i=e.stateNode.hydrate)&&(ks=kn(e.stateNode.containerInfo.firstChild),Es=e,i=Os=!0),i)for(n=Bo(e,null,r,n),e.child=n;n;)n.effectTag=-3&n.effectTag|1024,n=n.sibling;else Ls(t,e,r,n),Ms();e=e.child}return e;case 5:return zo(e),null===t&&Ds(e),r=e.type,i=e.pendingProps,o=null!==t?t.memoizedProps:null,a=i.children,In(r,i)?a=null:null!==o&&In(r,o)&&(e.effectTag|=16),Vs(t,e),4&e.mode&&1!==n&&i.hidden?(e.expirationTime=e.childExpirationTime=1,e=null):(Ls(t,e,a,n),e=e.child),e;case 6:return null===t&&Ds(e),null;case 13:return Js(t,e,n);case 4:return Uo(e,e.stateNode.containerInfo),r=e.pendingProps,null===t?e.child=Do(e,null,r,n):Ls(t,e,r,n),e.child;case 11:return r=e.type,i=e.pendingProps,Us(t,e,r,i=e.elementType===r?i:to(r,i),n);case 7:return Ls(t,e,e.pendingProps,n),e.child;case 8:case 12:return Ls(t,e,e.pendingProps.children,n),e.child;case 10:t:{r=e.type._context,i=e.pendingProps,a=e.memoizedProps,o=i.value;var l=e.type._context;if(yi(eo,l._currentValue),l._currentValue=o,null!==a)if(l=a.value,0===(o=Wr(l,o)?0:0|("function"===typeof r._calculateChangedBits?r._calculateChangedBits(l,o):1073741823))){if(a.children===i.children&&!gi.current){e=ea(t,e,n);break t}}else for(null!==(l=e.child)&&(l.return=e);null!==l;){var u=l.dependencies;if(null!==u){a=l.child;for(var c=u.firstContext;null!==c;){if(c.context===r&&0!==(c.observedBits&o)){1===l.tag&&((c=po(n,null)).tag=2,yo(l,c)),l.expirationTime<n&&(l.expirationTime=n),null!==(c=l.alternate)&&c.expirationTime<n&&(c.expirationTime=n),ao(l.return,n),u.expirationTime<n&&(u.expirationTime=n);break}c=c.next}}else a=10===l.tag&&l.type===e.type?null:l.child;if(null!==a)a.return=l;else for(a=l;null!==a;){if(a===e){a=null;break}if(null!==(l=a.sibling)){l.return=a.return,a=l;break}a=a.return}l=a}Ls(t,e,i.children,n),e=e.child}return e;case 9:return i=e.type,r=(o=e.pendingProps).children,lo(e,n),r=r(i=uo(i,o.unstable_observedBits)),e.effectTag|=1,Ls(t,e,r,n),e.child;case 14:return o=to(i=e.type,e.pendingProps),Rs(t,e,i,o=to(i.type,o),r,n);case 15:return zs(t,e,e.type,e.pendingProps,r,n);case 17:return r=e.type,i=e.pendingProps,i=e.elementType===r?i:to(r,i),null!==t&&(t.alternate=null,e.alternate=null,e.effectTag|=2),e.tag=1,_i(r)?(t=!0,xi(e)):t=!1,lo(e,n),Io(e,r,i),Eo(e,r,i,n),Hs(null,e,r,!0,t,n);case 19:return ta(t,e,n)}throw Error(s(156,e.tag))};var Ul=null,Rl=null;function zl(t,e,n,r){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.effectTag=0,this.lastEffect=this.firstEffect=this.nextEffect=null,this.childExpirationTime=this.expirationTime=0,this.alternate=null}function Vl(t,e,n,r){return new zl(t,e,n,r)}function jl(t){return!(!(t=t.prototype)||!t.isReactComponent)}function Wl(t,e){var n=t.alternate;return null===n?((n=Vl(t.tag,e,t.key,t.mode)).elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.effectTag=0,n.nextEffect=null,n.firstEffect=null,n.lastEffect=null),n.childExpirationTime=t.childExpirationTime,n.expirationTime=t.expirationTime,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=null===e?null:{expirationTime:e.expirationTime,firstContext:e.firstContext,responders:e.responders},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n}function Hl(t,e,n,r,i,o){var a=2;if(r=t,"function"===typeof t)jl(t)&&(a=1);else if("string"===typeof t)a=5;else t:switch(t){case nt:return Yl(n.children,i,o,e);case at:a=8,i|=7;break;case rt:a=8,i|=1;break;case it:return(t=Vl(12,n,e,8|i)).elementType=it,t.type=it,t.expirationTime=o,t;case ut:return(t=Vl(13,n,e,i)).type=ut,t.elementType=ut,t.expirationTime=o,t;case ct:return(t=Vl(19,n,e,i)).elementType=ct,t.expirationTime=o,t;default:if("object"===typeof t&&null!==t)switch(t.$$typeof){case ot:a=10;break t;case st:a=9;break t;case lt:a=11;break t;case dt:a=14;break t;case ft:a=16,r=null;break t;case ht:a=22;break t}throw Error(s(130,null==t?t:typeof t,""))}return(e=Vl(a,n,e,i)).elementType=t,e.type=r,e.expirationTime=o,e}function Yl(t,e,n,r){return(t=Vl(7,t,r,e)).expirationTime=n,t}function $l(t,e,n){return(t=Vl(6,t,null,e)).expirationTime=n,t}function Ql(t,e,n){return(e=Vl(4,null!==t.children?t.children:[],t.key,e)).expirationTime=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}function Kl(t,e,n){this.tag=e,this.current=null,this.containerInfo=t,this.pingCache=this.pendingChildren=null,this.finishedExpirationTime=0,this.finishedWork=null,this.timeoutHandle=-1,this.pendingContext=this.context=null,this.hydrate=n,this.callbackNode=null,this.callbackPriority=90,this.lastExpiredTime=this.lastPingedTime=this.nextKnownPendingLevel=this.lastSuspendedTime=this.firstSuspendedTime=this.firstPendingTime=0}function ql(t,e){var n=t.firstSuspendedTime;return t=t.lastSuspendedTime,0!==n&&n>=e&&t<=e}function Gl(t,e){var n=t.firstSuspendedTime,r=t.lastSuspendedTime;n<e&&(t.firstSuspendedTime=e),(r>e||0===n)&&(t.lastSuspendedTime=e),e<=t.lastPingedTime&&(t.lastPingedTime=0),e<=t.lastExpiredTime&&(t.lastExpiredTime=0)}function Jl(t,e){e>t.firstPendingTime&&(t.firstPendingTime=e);var n=t.firstSuspendedTime;0!==n&&(e>=n?t.firstSuspendedTime=t.lastSuspendedTime=t.nextKnownPendingLevel=0:e>=t.lastSuspendedTime&&(t.lastSuspendedTime=e+1),e>t.nextKnownPendingLevel&&(t.nextKnownPendingLevel=e))}function Xl(t,e){var n=t.lastExpiredTime;(0===n||n>e)&&(t.lastExpiredTime=e)}function Zl(t,e,n,r){var i=e.current,o=ul(),a=vo.suspense;o=cl(o,i,a);t:if(n){e:{if(te(n=n._reactInternalFiber)!==n||1!==n.tag)throw Error(s(170));var l=n;do{switch(l.tag){case 3:l=l.stateNode.context;break e;case 1:if(_i(l.type)){l=l.stateNode.__reactInternalMemoizedMergedChildContext;break e}}l=l.return}while(null!==l);throw Error(s(171))}if(1===n.tag){var u=n.type;if(_i(u)){n=Ii(n,u,l);break t}}n=l}else n=bi;return null===e.context?e.context=n:e.pendingContext=n,(e=po(o,a)).payload={element:t},null!==(r=void 0===r?null:r)&&(e.callback=r),yo(i,e),dl(i,o),o}function tu(t){return(t=t.current).child?(t.child.tag,t.child.stateNode):null}function eu(t,e){null!==(t=t.memoizedState)&&null!==t.dehydrated&&t.retryTime<e&&(t.retryTime=e)}function nu(t,e){eu(t,e),(t=t.alternate)&&eu(t,e)}function ru(t,e,n){var r=new Kl(t,e,n=null!=n&&!0===n.hydrate),i=Vl(3,null,null,2===e?7:1===e?3:0);r.current=i,i.stateNode=r,fo(i),t[Bn]=r.current,n&&0!==e&&function(t,e){var n=Zt(e);ke.forEach((function(t){ye(t,e,n)})),Oe.forEach((function(t){ye(t,e,n)}))}(0,9===t.nodeType?t:t.ownerDocument),this._internalRoot=r}function iu(t){return!(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType&&(8!==t.nodeType||" react-mount-point-unstable "!==t.nodeValue))}function ou(t,e,n,r,i){var o=n._reactRootContainer;if(o){var s=o._internalRoot;if("function"===typeof i){var a=i;i=function(){var t=tu(s);a.call(t)}}Zl(e,s,t,i)}else{if(o=n._reactRootContainer=function(t,e){if(e||(e=!(!(e=t?9===t.nodeType?t.documentElement:t.firstChild:null)||1!==e.nodeType||!e.hasAttribute("data-reactroot"))),!e)for(var n;n=t.lastChild;)t.removeChild(n);return new ru(t,0,e?{hydrate:!0}:void 0)}(n,r),s=o._internalRoot,"function"===typeof i){var l=i;i=function(){var t=tu(s);l.call(t)}}gl((function(){Zl(e,s,t,i)}))}return tu(s)}function su(t,e){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!iu(e))throw Error(s(200));return function(t,e,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:et,key:null==r?null:""+r,children:t,containerInfo:e,implementation:n}}(t,e,null,n)}ru.prototype.render=function(t){Zl(t,this._internalRoot,null,null)},ru.prototype.unmount=function(){var t=this._internalRoot,e=t.containerInfo;Zl(null,t,null,(function(){e[Bn]=null}))},be=function(t){if(13===t.tag){var e=Zi(ul(),150,100);dl(t,e),nu(t,e)}},me=function(t){13===t.tag&&(dl(t,3),nu(t,3))},ge=function(t){if(13===t.tag){var e=ul();dl(t,e=cl(e,t,null)),nu(t,e)}},O=function(t,e,n){switch(e){case"input":if(xt(t,n),e=n.name,"radio"===n.type&&null!=e){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+e)+'][type="radio"]'),e=0;e<n.length;e++){var r=n[e];if(r!==t&&r.form===t.form){var i=Pn(r);if(!i)throw Error(s(90));_t(r),xt(r,i)}}}break;case"textarea":Bt(t,n);break;case"select":null!=(e=n.value)&&At(t,!!n.multiple,e,!1)}},M=ml,F=function(t,e,n,r,i){var o=Ra;Ra|=4;try{return Ki(98,t.bind(null,e,n,r,i))}finally{(Ra=o)===Aa&&Ji()}},P=function(){(Ra&(1|Da|Ba))===Aa&&(function(){if(null!==ol){var t=ol;ol=null,t.forEach((function(t,e){Xl(e,t),pl(e)})),Ji()}}(),Bl())},L=function(t,e){var n=Ra;Ra|=2;try{return t(e)}finally{(Ra=n)===Aa&&Ji()}};var au={Events:[Mn,Fn,Pn,E,T,Wn,function(t){oe(t,jn)},B,N,Xe,le,Bl,{current:!1}]};!function(t){var e=t.findFiberByHostInstance;(function(t){if("undefined"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__)return!1;var e=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(e.isDisabled||!e.supportsFiber)return!0;try{var n=e.inject(t);Ul=function(t){try{e.onCommitFiberRoot(n,t,void 0,64===(64&t.current.effectTag))}catch(r){}},Rl=function(t){try{e.onCommitFiberUnmount(n,t)}catch(r){}}}catch(r){}})(i({},t,{overrideHookState:null,overrideProps:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:G.ReactCurrentDispatcher,findHostInstanceByFiber:function(t){return null===(t=re(t))?null:t.stateNode},findFiberByHostInstance:function(t){return e?e(t):null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null}))}({findFiberByHostInstance:Nn,bundleType:0,version:"16.14.0",rendererPackageName:"react-dom"}),e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=au,e.createPortal=su,e.findDOMNode=function(t){if(null==t)return null;if(1===t.nodeType)return t;var e=t._reactInternalFiber;if(void 0===e){if("function"===typeof t.render)throw Error(s(188));throw Error(s(268,Object.keys(t)))}return t=null===(t=re(e))?null:t.stateNode},e.flushSync=function(t,e){if((Ra&(Da|Ba))!==Aa)throw Error(s(187));var n=Ra;Ra|=1;try{return Ki(99,t.bind(null,e))}finally{Ra=n,Ji()}},e.hydrate=function(t,e,n){if(!iu(e))throw Error(s(200));return ou(null,t,e,!0,n)},e.render=function(t,e,n){if(!iu(e))throw Error(s(200));return ou(null,t,e,!1,n)},e.unmountComponentAtNode=function(t){if(!iu(t))throw Error(s(40));return!!t._reactRootContainer&&(gl((function(){ou(null,null,t,!1,(function(){t._reactRootContainer=null,t[Bn]=null}))})),!0)},e.unstable_batchedUpdates=ml,e.unstable_createPortal=function(t,e){return su(t,e,2<arguments.length&&void 0!==arguments[2]?arguments[2]:null)},e.unstable_renderSubtreeIntoContainer=function(t,e,n,r){if(!iu(n))throw Error(s(200));if(null==t||void 0===t._reactInternalFiber)throw Error(s(38));return ou(t,e,n,!1,r)},e.version="16.14.0"},962:(t,e,n)=>{!function t(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t)}catch(e){console.error(e)}}(),t.exports=n(678)},512:(t,e)=>{var n="function"===typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,i=n?Symbol.for("react.portal"):60106,o=n?Symbol.for("react.fragment"):60107,s=n?Symbol.for("react.strict_mode"):60108,a=n?Symbol.for("react.profiler"):60114,l=n?Symbol.for("react.provider"):60109,u=n?Symbol.for("react.context"):60110,c=n?Symbol.for("react.async_mode"):60111,d=n?Symbol.for("react.concurrent_mode"):60111,f=n?Symbol.for("react.forward_ref"):60112,h=n?Symbol.for("react.suspense"):60113,p=n?Symbol.for("react.suspense_list"):60120,y=n?Symbol.for("react.memo"):60115,b=n?Symbol.for("react.lazy"):60116,m=n?Symbol.for("react.block"):60121,g=n?Symbol.for("react.fundamental"):60117,v=n?Symbol.for("react.responder"):60118,w=n?Symbol.for("react.scope"):60119;function _(t){if("object"===typeof t&&null!==t){var e=t.$$typeof;switch(e){case r:switch(t=t.type){case c:case d:case o:case a:case s:case h:return t;default:switch(t=t&&t.$$typeof){case u:case f:case b:case y:case l:return t;default:return e}}case i:return e}}}function S(t){return _(t)===d}e.AsyncMode=c,e.ConcurrentMode=d,e.ContextConsumer=u,e.ContextProvider=l,e.Element=r,e.ForwardRef=f,e.Fragment=o,e.Lazy=b,e.Memo=y,e.Portal=i,e.Profiler=a,e.StrictMode=s,e.Suspense=h,e.isAsyncMode=function(t){return S(t)||_(t)===c},e.isConcurrentMode=S,e.isContextConsumer=function(t){return _(t)===u},e.isContextProvider=function(t){return _(t)===l},e.isElement=function(t){return"object"===typeof t&&null!==t&&t.$$typeof===r},e.isForwardRef=function(t){return _(t)===f},e.isFragment=function(t){return _(t)===o},e.isLazy=function(t){return _(t)===b},e.isMemo=function(t){return _(t)===y},e.isPortal=function(t){return _(t)===i},e.isProfiler=function(t){return _(t)===a},e.isStrictMode=function(t){return _(t)===s},e.isSuspense=function(t){return _(t)===h},e.isValidElementType=function(t){return"string"===typeof t||"function"===typeof t||t===o||t===d||t===a||t===s||t===h||t===p||"object"===typeof t&&null!==t&&(t.$$typeof===b||t.$$typeof===y||t.$$typeof===l||t.$$typeof===u||t.$$typeof===f||t.$$typeof===g||t.$$typeof===v||t.$$typeof===w||t.$$typeof===m)},e.typeOf=_},956:(t,e,n)=>{t.exports=n(512)},332:(t,e,n)=>{var r=n(108),i=60103;if(60107,"function"===typeof Symbol&&Symbol.for){var o=Symbol.for;i=o("react.element"),o("react.fragment")}var s=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,a=Object.prototype.hasOwnProperty,l={key:!0,ref:!0,__self:!0,__source:!0};function u(t,e,n){var r,o={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==e.key&&(u=""+e.key),void 0!==e.ref&&(c=e.ref),e)a.call(e,r)&&!l.hasOwnProperty(r)&&(o[r]=e[r]);if(t&&t.defaultProps)for(r in e=t.defaultProps)void 0===o[r]&&(o[r]=e[r]);return{$$typeof:i,type:t,key:u,ref:c,props:o,_owner:s.current}}e.jsx=u,e.jsxs=u},863:(t,e,n)=>{var r=n(4),i="function"===typeof Symbol&&Symbol.for,o=i?Symbol.for("react.element"):60103,s=i?Symbol.for("react.portal"):60106,a=i?Symbol.for("react.fragment"):60107,l=i?Symbol.for("react.strict_mode"):60108,u=i?Symbol.for("react.profiler"):60114,c=i?Symbol.for("react.provider"):60109,d=i?Symbol.for("react.context"):60110,f=i?Symbol.for("react.forward_ref"):60112,h=i?Symbol.for("react.suspense"):60113,p=i?Symbol.for("react.memo"):60115,y=i?Symbol.for("react.lazy"):60116,b="function"===typeof Symbol&&Symbol.iterator;function m(t){for(var e="https://reactjs.org/docs/error-decoder.html?invariant="+t,n=1;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var g={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},v={};function w(t,e,n){this.props=t,this.context=e,this.refs=v,this.updater=n||g}function _(){}function S(t,e,n){this.props=t,this.context=e,this.refs=v,this.updater=n||g}w.prototype.isReactComponent={},w.prototype.setState=function(t,e){if("object"!==typeof t&&"function"!==typeof t&&null!=t)throw Error(m(85));this.updater.enqueueSetState(this,t,e,"setState")},w.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")},_.prototype=w.prototype;var T=S.prototype=new _;T.constructor=S,r(T,w.prototype),T.isPureReactComponent=!0;var I={current:null},x=Object.prototype.hasOwnProperty,E={key:!0,ref:!0,__self:!0,__source:!0};function k(t,e,n){var r,i={},s=null,a=null;if(null!=e)for(r in void 0!==e.ref&&(a=e.ref),void 0!==e.key&&(s=""+e.key),e)x.call(e,r)&&!E.hasOwnProperty(r)&&(i[r]=e[r]);var l=arguments.length-2;if(1===l)i.children=n;else if(1<l){for(var u=Array(l),c=0;c<l;c++)u[c]=arguments[c+2];i.children=u}if(t&&t.defaultProps)for(r in l=t.defaultProps)void 0===i[r]&&(i[r]=l[r]);return{$$typeof:o,type:t,key:s,ref:a,props:i,_owner:I.current}}function O(t){return"object"===typeof t&&null!==t&&t.$$typeof===o}var A=/\/+/g,C=[];function D(t,e,n,r){if(C.length){var i=C.pop();return i.result=t,i.keyPrefix=e,i.func=n,i.context=r,i.count=0,i}return{result:t,keyPrefix:e,func:n,context:r,count:0}}function B(t){t.result=null,t.keyPrefix=null,t.func=null,t.context=null,t.count=0,10>C.length&&C.push(t)}function N(t,e,n,r){var i=typeof t;"undefined"!==i&&"boolean"!==i||(t=null);var a=!1;if(null===t)a=!0;else switch(i){case"string":case"number":a=!0;break;case"object":switch(t.$$typeof){case o:case s:a=!0}}if(a)return n(r,t,""===e?"."+F(t,0):e),1;if(a=0,e=""===e?".":e+":",Array.isArray(t))for(var l=0;l<t.length;l++){var u=e+F(i=t[l],l);a+=N(i,u,n,r)}else if(null===t||"object"!==typeof t?u=null:u="function"===typeof(u=b&&t[b]||t["@@iterator"])?u:null,"function"===typeof u)for(t=u.call(t),l=0;!(i=t.next()).done;)a+=N(i=i.value,u=e+F(i,l++),n,r);else if("object"===i)throw n=""+t,Error(m(31,"[object Object]"===n?"object with keys {"+Object.keys(t).join(", ")+"}":n,""));return a}function M(t,e,n){return null==t?0:N(t,"",e,n)}function F(t,e){return"object"===typeof t&&null!==t&&null!=t.key?function(t){var e={"=":"=0",":":"=2"};return"$"+(""+t).replace(/[=:]/g,(function(t){return e[t]}))}(t.key):e.toString(36)}function P(t,e){t.func.call(t.context,e,t.count++)}function L(t,e,n){var r=t.result,i=t.keyPrefix;t=t.func.call(t.context,e,t.count++),Array.isArray(t)?U(t,r,n,(function(t){return t})):null!=t&&(O(t)&&(t=function(t,e){return{$$typeof:o,type:t.type,key:e,ref:t.ref,props:t.props,_owner:t._owner}}(t,i+(!t.key||e&&e.key===t.key?"":(""+t.key).replace(A,"$&/")+"/")+n)),r.push(t))}function U(t,e,n,r,i){var o="";null!=n&&(o=(""+n).replace(A,"$&/")+"/"),M(t,L,e=D(e,o,r,i)),B(e)}var R={current:null};function z(){var t=R.current;if(null===t)throw Error(m(321));return t}var V={ReactCurrentDispatcher:R,ReactCurrentBatchConfig:{suspense:null},ReactCurrentOwner:I,IsSomeRendererActing:{current:!1},assign:r};e.Children={map:function(t,e,n){if(null==t)return t;var r=[];return U(t,r,null,e,n),r},forEach:function(t,e,n){if(null==t)return t;M(t,P,e=D(null,null,e,n)),B(e)},count:function(t){return M(t,(function(){return null}),null)},toArray:function(t){var e=[];return U(t,e,null,(function(t){return t})),e},only:function(t){if(!O(t))throw Error(m(143));return t}},e.Component=w,e.Fragment=a,e.Profiler=u,e.PureComponent=S,e.StrictMode=l,e.Suspense=h,e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=V,e.cloneElement=function(t,e,n){if(null===t||void 0===t)throw Error(m(267,t));var i=r({},t.props),s=t.key,a=t.ref,l=t._owner;if(null!=e){if(void 0!==e.ref&&(a=e.ref,l=I.current),void 0!==e.key&&(s=""+e.key),t.type&&t.type.defaultProps)var u=t.type.defaultProps;for(c in e)x.call(e,c)&&!E.hasOwnProperty(c)&&(i[c]=void 0===e[c]&&void 0!==u?u[c]:e[c])}var c=arguments.length-2;if(1===c)i.children=n;else if(1<c){u=Array(c);for(var d=0;d<c;d++)u[d]=arguments[d+2];i.children=u}return{$$typeof:o,type:t.type,key:s,ref:a,props:i,_owner:l}},e.createContext=function(t,e){return void 0===e&&(e=null),(t={$$typeof:d,_calculateChangedBits:e,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null}).Provider={$$typeof:c,_context:t},t.Consumer=t},e.createElement=k,e.createFactory=function(t){var e=k.bind(null,t);return e.type=t,e},e.createRef=function(){return{current:null}},e.forwardRef=function(t){return{$$typeof:f,render:t}},e.isValidElement=O,e.lazy=function(t){return{$$typeof:y,_ctor:t,_status:-1,_result:null}},e.memo=function(t,e){return{$$typeof:p,type:t,compare:void 0===e?null:e}},e.useCallback=function(t,e){return z().useCallback(t,e)},e.useContext=function(t,e){return z().useContext(t,e)},e.useDebugValue=function(){},e.useEffect=function(t,e){return z().useEffect(t,e)},e.useImperativeHandle=function(t,e,n){return z().useImperativeHandle(t,e,n)},e.useLayoutEffect=function(t,e){return z().useLayoutEffect(t,e)},e.useMemo=function(t,e){return z().useMemo(t,e)},e.useReducer=function(t,e,n){return z().useReducer(t,e,n)},e.useRef=function(t){return z().useRef(t)},e.useState=function(t){return z().useState(t)},e.version="16.14.0"},108:(t,e,n)=>{t.exports=n(863)},88:(t,e,n)=>{t.exports=n(332)},526:(t,e)=>{var n,r,i,o,s;if("undefined"===typeof window||"function"!==typeof MessageChannel){var a=null,l=null,u=function(){if(null!==a)try{var t=e.unstable_now();a(!0,t),a=null}catch(n){throw setTimeout(u,0),n}},c=Date.now();e.unstable_now=function(){return Date.now()-c},n=function(t){null!==a?setTimeout(n,0,t):(a=t,setTimeout(u,0))},r=function(t,e){l=setTimeout(t,e)},i=function(){clearTimeout(l)},o=function(){return!1},s=e.unstable_forceFrameRate=function(){}}else{var d=window.performance,f=window.Date,h=window.setTimeout,p=window.clearTimeout;if("undefined"!==typeof console){var y=window.cancelAnimationFrame;"function"!==typeof window.requestAnimationFrame&&console.error("This browser doesn't support requestAnimationFrame. Make sure that you load a polyfill in older browsers. https://fb.me/react-polyfills"),"function"!==typeof y&&console.error("This browser doesn't support cancelAnimationFrame. Make sure that you load a polyfill in older browsers. https://fb.me/react-polyfills")}if("object"===typeof d&&"function"===typeof d.now)e.unstable_now=function(){return d.now()};else{var b=f.now();e.unstable_now=function(){return f.now()-b}}var m=!1,g=null,v=-1,w=5,_=0;o=function(){return e.unstable_now()>=_},s=function(){},e.unstable_forceFrameRate=function(t){0>t||125<t?console.error("forceFrameRate takes a positive int between 0 and 125, forcing framerates higher than 125 fps is not unsupported"):w=0<t?Math.floor(1e3/t):5};var S=new MessageChannel,T=S.port2;S.port1.onmessage=function(){if(null!==g){var t=e.unstable_now();_=t+w;try{g(!0,t)?T.postMessage(null):(m=!1,g=null)}catch(n){throw T.postMessage(null),n}}else m=!1},n=function(t){g=t,m||(m=!0,T.postMessage(null))},r=function(t,n){v=h((function(){t(e.unstable_now())}),n)},i=function(){p(v),v=-1}}function I(t,e){var n=t.length;t.push(e);t:for(;;){var r=n-1>>>1,i=t[r];if(!(void 0!==i&&0<k(i,e)))break t;t[r]=e,t[n]=i,n=r}}function x(t){return void 0===(t=t[0])?null:t}function E(t){var e=t[0];if(void 0!==e){var n=t.pop();if(n!==e){t[0]=n;t:for(var r=0,i=t.length;r<i;){var o=2*(r+1)-1,s=t[o],a=o+1,l=t[a];if(void 0!==s&&0>k(s,n))void 0!==l&&0>k(l,s)?(t[r]=l,t[a]=n,r=a):(t[r]=s,t[o]=n,r=o);else{if(!(void 0!==l&&0>k(l,n)))break t;t[r]=l,t[a]=n,r=a}}}return e}return null}function k(t,e){var n=t.sortIndex-e.sortIndex;return 0!==n?n:t.id-e.id}var O=[],A=[],C=1,D=null,B=3,N=!1,M=!1,F=!1;function P(t){for(var e=x(A);null!==e;){if(null===e.callback)E(A);else{if(!(e.startTime<=t))break;E(A),e.sortIndex=e.expirationTime,I(O,e)}e=x(A)}}function L(t){if(F=!1,P(t),!M)if(null!==x(O))M=!0,n(U);else{var e=x(A);null!==e&&r(L,e.startTime-t)}}function U(t,n){M=!1,F&&(F=!1,i()),N=!0;var s=B;try{for(P(n),D=x(O);null!==D&&(!(D.expirationTime>n)||t&&!o());){var a=D.callback;if(null!==a){D.callback=null,B=D.priorityLevel;var l=a(D.expirationTime<=n);n=e.unstable_now(),"function"===typeof l?D.callback=l:D===x(O)&&E(O),P(n)}else E(O);D=x(O)}if(null!==D)var u=!0;else{var c=x(A);null!==c&&r(L,c.startTime-n),u=!1}return u}finally{D=null,B=s,N=!1}}function R(t){switch(t){case 1:return-1;case 2:return 250;case 5:return 1073741823;case 4:return 1e4;default:return 5e3}}var z=s;e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(t){t.callback=null},e.unstable_continueExecution=function(){M||N||(M=!0,n(U))},e.unstable_getCurrentPriorityLevel=function(){return B},e.unstable_getFirstCallbackNode=function(){return x(O)},e.unstable_next=function(t){switch(B){case 1:case 2:case 3:var e=3;break;default:e=B}var n=B;B=e;try{return t()}finally{B=n}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=z,e.unstable_runWithPriority=function(t,e){switch(t){case 1:case 2:case 3:case 4:case 5:break;default:t=3}var n=B;B=t;try{return e()}finally{B=n}},e.unstable_scheduleCallback=function(t,o,s){var a=e.unstable_now();if("object"===typeof s&&null!==s){var l=s.delay;l="number"===typeof l&&0<l?a+l:a,s="number"===typeof s.timeout?s.timeout:R(t)}else s=R(t),l=a;return t={id:C++,callback:o,priorityLevel:t,startTime:l,expirationTime:s=l+s,sortIndex:-1},l>a?(t.sortIndex=l,I(A,t),null===x(O)&&t===x(A)&&(F?i():F=!0,r(L,l-a))):(t.sortIndex=s,I(O,t),M||N||(M=!0,n(U))),t},e.unstable_shouldYield=function(){var t=e.unstable_now();P(t);var n=x(O);return n!==D&&null!==D&&null!==n&&null!==n.callback&&n.startTime<=t&&n.expirationTime<D.expirationTime||o()},e.unstable_wrapCallback=function(t){var e=B;return function(){var n=B;B=e;try{return t.apply(this,arguments)}finally{B=n}}}},841:(t,e,n)=>{t.exports=n(526)}},e={};function n(r){var i=e[r];if(void 0!==i)return i.exports;var o=e[r]={exports:{}};return t[r](o,o.exports,n),o.exports}n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),(()=>{var t,e,r,i,o,s,a,l,u,c=n(108),d=n(962),f=n(394),h=n.n(f);!function(t){t[t.V1=0]="V1",t[t.V2=1]="V2",t[t.V3=2]="V3",t[t.V4=3]="V4",t[t.V5=4]="V5"}(t||(t={})),function(t){t[t.Sparse=0]="Sparse",t[t.Dense=1]="Dense"}(e||(e={})),function(t){t[t.HALF=0]="HALF",t[t.SINGLE=1]="SINGLE",t[t.DOUBLE=2]="DOUBLE"}(r||(r={})),function(t){t[t.DAY=0]="DAY",t[t.MILLISECOND=1]="MILLISECOND"}(i||(i={})),function(t){t[t.SECOND=0]="SECOND",t[t.MILLISECOND=1]="MILLISECOND",t[t.MICROSECOND=2]="MICROSECOND",t[t.NANOSECOND=3]="NANOSECOND"}(o||(o={})),function(t){t[t.YEAR_MONTH=0]="YEAR_MONTH",t[t.DAY_TIME=1]="DAY_TIME",t[t.MONTH_DAY_NANO=2]="MONTH_DAY_NANO"}(s||(s={})),function(t){t[t.NONE=0]="NONE",t[t.Schema=1]="Schema",t[t.DictionaryBatch=2]="DictionaryBatch",t[t.RecordBatch=3]="RecordBatch",t[t.Tensor=4]="Tensor",t[t.SparseTensor=5]="SparseTensor"}(a||(a={})),function(t){t[t.NONE=0]="NONE",t[t.Null=1]="Null",t[t.Int=2]="Int",t[t.Float=3]="Float",t[t.Binary=4]="Binary",t[t.Utf8=5]="Utf8",t[t.Bool=6]="Bool",t[t.Decimal=7]="Decimal",t[t.Date=8]="Date",t[t.Time=9]="Time",t[t.Timestamp=10]="Timestamp",t[t.Interval=11]="Interval",t[t.List=12]="List",t[t.Struct=13]="Struct",t[t.Union=14]="Union",t[t.FixedSizeBinary=15]="FixedSizeBinary",t[t.FixedSizeList=16]="FixedSizeList",t[t.Map=17]="Map",t[t.Dictionary=-1]="Dictionary",t[t.Int8=-2]="Int8",t[t.Int16=-3]="Int16",t[t.Int32=-4]="Int32",t[t.Int64=-5]="Int64",t[t.Uint8=-6]="Uint8",t[t.Uint16=-7]="Uint16",t[t.Uint32=-8]="Uint32",t[t.Uint64=-9]="Uint64",t[t.Float16=-10]="Float16",t[t.Float32=-11]="Float32",t[t.Float64=-12]="Float64",t[t.DateDay=-13]="DateDay",t[t.DateMillisecond=-14]="DateMillisecond",t[t.TimestampSecond=-15]="TimestampSecond",t[t.TimestampMillisecond=-16]="TimestampMillisecond",t[t.TimestampMicrosecond=-17]="TimestampMicrosecond",t[t.TimestampNanosecond=-18]="TimestampNanosecond",t[t.TimeSecond=-19]="TimeSecond",t[t.TimeMillisecond=-20]="TimeMillisecond",t[t.TimeMicrosecond=-21]="TimeMicrosecond",t[t.TimeNanosecond=-22]="TimeNanosecond",t[t.DenseUnion=-23]="DenseUnion",t[t.SparseUnion=-24]="SparseUnion",t[t.IntervalDayTime=-25]="IntervalDayTime",t[t.IntervalYearMonth=-26]="IntervalYearMonth"}(l||(l={})),function(t){t[t.OFFSET=0]="OFFSET",t[t.DATA=1]="DATA",t[t.VALIDITY=2]="VALIDITY",t[t.TYPE=3]="TYPE"}(u||(u={}));const[p,y]=(()=>{const t=()=>{throw new Error("BigInt is not available in this environment")};function e(){throw t()}return e.asIntN=()=>{throw t()},e.asUintN=()=>{throw t()},"undefined"!==typeof BigInt?[BigInt,!0]:[e,!1]})(),[b,m]=(()=>{const t=()=>{throw new Error("BigInt64Array is not available in this environment")};return"undefined"!==typeof BigInt64Array?[BigInt64Array,!0]:[class{static get BYTES_PER_ELEMENT(){return 8}static of(){throw t()}static from(){throw t()}constructor(){throw t()}},!1]})(),[g,v]=(()=>{const t=()=>{throw new Error("BigUint64Array is not available in this environment")};return"undefined"!==typeof BigUint64Array?[BigUint64Array,!0]:[class{static get BYTES_PER_ELEMENT(){return 8}static of(){throw t()}static from(){throw t()}constructor(){throw t()}},!1]})(),w=t=>"number"===typeof t,_=t=>"boolean"===typeof t,S=t=>"function"===typeof t,T=t=>null!=t&&Object(t)===t,I=t=>T(t)&&S(t.then),x=t=>T(t)&&S(t[Symbol.iterator]),E=t=>T(t)&&S(t[Symbol.asyncIterator]),k=t=>T(t)&&T(t.schema),O=t=>T(t)&&"done"in t&&"value"in t,A=t=>T(t)&&S(t.stat)&&w(t.fd),C=t=>T(t)&&B(t.body),D=t=>"_getDOMStream"in t&&"_getNodeStream"in t,B=t=>T(t)&&S(t.cancel)&&S(t.getReader)&&!D(t),N=t=>T(t)&&S(t.read)&&S(t.pipe)&&_(t.readable)&&!D(t),M=t=>T(t)&&S(t.clear)&&S(t.bytes)&&S(t.position)&&S(t.setPosition)&&S(t.capacity)&&S(t.getBufferIdentifier)&&S(t.createLong);function F(t){if(null===t)return"null";if(undefined===t)return"undefined";switch(typeof t){case"number":case"bigint":return"".concat(t);case"string":return'"'.concat(t,'"')}return"function"===typeof t[Symbol.toPrimitive]?t[Symbol.toPrimitive]("string"):ArrayBuffer.isView(t)?"[".concat(t instanceof b||t instanceof g?[...t].map((t=>F(t))):t,"]"):ArrayBuffer.isView(t)?"[".concat(t,"]"):JSON.stringify(t,((t,e)=>"bigint"===typeof e?"".concat(e):e))}function P(t,e,n,r){return new(n||(n=Promise))((function(i,o){function s(t){try{l(r.next(t))}catch(e){o(e)}}function a(t){try{l(r.throw(t))}catch(e){o(e)}}function l(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(s,a)}l((r=r.apply(t,e||[])).next())}))}Object.create;function L(t){var e="function"===typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"===typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function U(t){return this instanceof U?(this.v=t,this):new U(t)}function R(t,e,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,i=n.apply(t,e||[]),o=[];return r={},s("next"),s("throw"),s("return"),r[Symbol.asyncIterator]=function(){return this},r;function s(t){i[t]&&(r[t]=function(e){return new Promise((function(n,r){o.push([t,e,n,r])>1||a(t,e)}))})}function a(t,e){try{(n=i[t](e)).value instanceof U?Promise.resolve(n.value.v).then(l,u):c(o[0][2],n)}catch(r){c(o[0][3],r)}var n}function l(t){a("next",t)}function u(t){a("throw",t)}function c(t,e){t(e),o.shift(),o.length&&a(o[0][0],o[0][1])}}function z(t){var e,n;return e={},r("next"),r("throw",(function(t){throw t})),r("return"),e[Symbol.iterator]=function(){return this},e;function r(r,i){e[r]=t[r]?function(e){return(n=!n)?{value:U(t[r](e)),done:!1}:i?i(e):e}:i}}function V(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,n=t[Symbol.asyncIterator];return n?n.call(t):(t=L(t),e={},r("next"),r("throw"),r("return"),e[Symbol.asyncIterator]=function(){return this},e);function r(n){e[n]=t[n]&&function(e){return new Promise((function(r,i){(function(t,e,n,r){Promise.resolve(r).then((function(e){t({value:e,done:n})}),e)})(r,i,(e=t[n](e)).done,e.value)}))}}}Object.create;"function"===typeof SuppressedError&&SuppressedError;const j=new TextDecoder("utf-8"),W=t=>j.decode(t),H=new TextEncoder,Y=t=>H.encode(t),$="undefined"!==typeof SharedArrayBuffer?SharedArrayBuffer:ArrayBuffer;function Q(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e.byteLength;const i=t.byteLength,o=new Uint8Array(t.buffer,t.byteOffset,i),s=new Uint8Array(e.buffer,e.byteOffset,Math.min(r,i));return o.set(s,n),t}function K(t,e){const n=function(t){const e=t[0]?[t[0]]:[];let n,r,i,o;for(let s,a,l=0,u=0,c=t.length;++l<c;)s=e[u],a=t[l],!s||!a||s.buffer!==a.buffer||a.byteOffset<s.byteOffset?a&&(e[++u]=a):(({byteOffset:n,byteLength:i}=s),({byteOffset:r,byteLength:o}=a),n+i<r||r+o<n?a&&(e[++u]=a):e[u]=new Uint8Array(s.buffer,n,r-n+o));return e}(t),r=n.reduce(((t,e)=>t+e.byteLength),0);let i,o,s,a=0,l=-1;const u=Math.min(e||Number.POSITIVE_INFINITY,r);for(const c=n.length;++l<c;){if(i=n[l],o=i.subarray(0,Math.min(i.length,u-a)),u<=a+o.length){o.length<i.length?n[l]=i.subarray(o.length):o.length===i.length&&l++,s?Q(s,o,a):s=o;break}Q(s||(s=new Uint8Array(u)),o,a),a+=o.length}return[s||new Uint8Array(0),n.slice(l),r-(s?s.byteLength:0)]}function q(t,e){let n=O(e)?e.value:e;return n instanceof t?t===Uint8Array?new t(n.buffer,n.byteOffset,n.byteLength):n:n?("string"===typeof n&&(n=Y(n)),n instanceof ArrayBuffer||n instanceof $?new t(n):M(n)?q(t,n.bytes()):ArrayBuffer.isView(n)?n.byteLength<=0?new t(0):new t(n.buffer,n.byteOffset,n.byteLength/t.BYTES_PER_ELEMENT):t.from(n)):new t(0)}const G=t=>q(Int32Array,t),J=t=>q(Uint8Array,t),X=t=>(t.next(),t);function*Z(t,e){const n=function*(t){yield t},r="string"===typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof $?n(e):x(e)?e:n(e);return yield*X(function*(e){let n=null;do{n=e.next(yield q(t,n))}while(!n.done)}(r[Symbol.iterator]())),new t}function tt(t,e){return R(this,arguments,(function*(){if(I(e))return yield U(yield U(yield*z(V(tt(t,yield U(e))))));const n=function(t){return R(this,arguments,(function*(){yield yield U(yield U(t))}))},r="string"===typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof $?n(e):x(e)?function(t){return R(this,arguments,(function*(){yield U(yield*z(V(X(function*(t){let e=null;do{e=t.next(yield null===e||void 0===e?void 0:e.value)}while(!e.done)}(t[Symbol.iterator]())))))}))}(e):E(e)?e:n(e);return yield U(yield*z(V(X(function(e){return R(this,arguments,(function*(){let n=null;do{n=yield U(e.next(yield yield U(q(t,n))))}while(!n.done)}))}(r[Symbol.asyncIterator]()))))),yield U(new t)}))}function et(t,e,n){if(0!==t){n=n.slice(0,e+1);for(let r=-1;++r<=e;)n[r]+=t}return n}const nt=Symbol.for("isArrowBigNum");function rt(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return 0===n.length?Object.setPrototypeOf(q(this.TypedArray,t),this.constructor.prototype):Object.setPrototypeOf(new this.TypedArray(t,...n),this.constructor.prototype)}function it(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return rt.apply(this,e)}function ot(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return rt.apply(this,e)}function st(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return rt.apply(this,e)}function at(t){const{buffer:e,byteOffset:n,length:r,signed:i}=t,o=new g(e,n,r),s=i&&o[o.length-1]&BigInt(1)<<BigInt(63);let a=s?BigInt(1):BigInt(0),l=BigInt(0);if(s){for(const t of o)a+=~t*(BigInt(1)<<BigInt(32)*l++);a*=BigInt(-1)}else for(const u of o)a+=u*(BigInt(1)<<BigInt(32)*l++);return a}let lt,ut;function ct(t){let e="";const n=new Uint32Array(2);let r=new Uint16Array(t.buffer,t.byteOffset,t.byteLength/2);const i=new Uint32Array((r=new Uint16Array(r).reverse()).buffer);let o=-1;const s=r.length-1;do{for(n[0]=r[o=0];o<s;)r[o++]=n[1]=n[0]/10,n[0]=(n[0]-10*n[1]<<16)+r[o];r[o]=n[1]=n[0]/10,n[0]=n[0]-10*n[1],e="".concat(n[0]).concat(e)}while(i[0]||i[1]||i[2]||i[3]);return null!==e&&void 0!==e?e:"0"}rt.prototype[nt]=!0,rt.prototype.toJSON=function(){return'"'.concat(lt(this),'"')},rt.prototype.valueOf=function(){return at(this)},rt.prototype.toString=function(){return lt(this)},rt.prototype[Symbol.toPrimitive]=function(){switch(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default"){case"number":return at(this);case"string":return lt(this);case"default":return ut(this)}return lt(this)},Object.setPrototypeOf(it.prototype,Object.create(Int32Array.prototype)),Object.setPrototypeOf(ot.prototype,Object.create(Uint32Array.prototype)),Object.setPrototypeOf(st.prototype,Object.create(Uint32Array.prototype)),Object.assign(it.prototype,rt.prototype,{constructor:it,signed:!0,TypedArray:Int32Array,BigIntArray:b}),Object.assign(ot.prototype,rt.prototype,{constructor:ot,signed:!1,TypedArray:Uint32Array,BigIntArray:g}),Object.assign(st.prototype,rt.prototype,{constructor:st,signed:!0,TypedArray:Uint32Array,BigIntArray:g}),y?(ut=t=>8===t.byteLength?new t.BigIntArray(t.buffer,t.byteOffset,1)[0]:ct(t),lt=t=>8===t.byteLength?"".concat(new t.BigIntArray(t.buffer,t.byteOffset,1)[0]):ct(t)):(lt=ct,ut=lt);class dt{static new(t,e){switch(e){case!0:return new it(t);case!1:return new ot(t)}switch(t.constructor){case Int8Array:case Int16Array:case Int32Array:case b:return new it(t)}return 16===t.byteLength?new st(t):new ot(t)}static signed(t){return new it(t)}static unsigned(t){return new ot(t)}static decimal(t){return new st(t)}constructor(t,e){return dt.new(t,e)}}var ft,ht,pt,yt,bt,mt,gt,vt,wt,_t,St,Tt,It,xt,Et,kt,Ot,At,Ct,Dt;class Bt{static isNull(t){return(null===t||void 0===t?void 0:t.typeId)===l.Null}static isInt(t){return(null===t||void 0===t?void 0:t.typeId)===l.Int}static isFloat(t){return(null===t||void 0===t?void 0:t.typeId)===l.Float}static isBinary(t){return(null===t||void 0===t?void 0:t.typeId)===l.Binary}static isUtf8(t){return(null===t||void 0===t?void 0:t.typeId)===l.Utf8}static isBool(t){return(null===t||void 0===t?void 0:t.typeId)===l.Bool}static isDecimal(t){return(null===t||void 0===t?void 0:t.typeId)===l.Decimal}static isDate(t){return(null===t||void 0===t?void 0:t.typeId)===l.Date}static isTime(t){return(null===t||void 0===t?void 0:t.typeId)===l.Time}static isTimestamp(t){return(null===t||void 0===t?void 0:t.typeId)===l.Timestamp}static isInterval(t){return(null===t||void 0===t?void 0:t.typeId)===l.Interval}static isList(t){return(null===t||void 0===t?void 0:t.typeId)===l.List}static isStruct(t){return(null===t||void 0===t?void 0:t.typeId)===l.Struct}static isUnion(t){return(null===t||void 0===t?void 0:t.typeId)===l.Union}static isFixedSizeBinary(t){return(null===t||void 0===t?void 0:t.typeId)===l.FixedSizeBinary}static isFixedSizeList(t){return(null===t||void 0===t?void 0:t.typeId)===l.FixedSizeList}static isMap(t){return(null===t||void 0===t?void 0:t.typeId)===l.Map}static isDictionary(t){return(null===t||void 0===t?void 0:t.typeId)===l.Dictionary}static isDenseUnion(t){return Bt.isUnion(t)&&t.mode===e.Dense}static isSparseUnion(t){return Bt.isUnion(t)&&t.mode===e.Sparse}get typeId(){return l.NONE}}ft=Symbol.toStringTag,Bt[ft]=((Dt=Bt.prototype).children=null,Dt.ArrayType=Array,Dt[Symbol.toStringTag]="DataType");class Nt extends Bt{toString(){return"Null"}get typeId(){return l.Null}}ht=Symbol.toStringTag,Nt[ht]=(t=>t[Symbol.toStringTag]="Null")(Nt.prototype);class Mt extends Bt{constructor(t,e){super(),this.isSigned=t,this.bitWidth=e}get typeId(){return l.Int}get ArrayType(){switch(this.bitWidth){case 8:return this.isSigned?Int8Array:Uint8Array;case 16:return this.isSigned?Int16Array:Uint16Array;case 32:return this.isSigned?Int32Array:Uint32Array;case 64:return this.isSigned?b:g}throw new Error("Unrecognized ".concat(this[Symbol.toStringTag]," type"))}toString(){return"".concat(this.isSigned?"I":"Ui","nt").concat(this.bitWidth)}}pt=Symbol.toStringTag,Mt[pt]=(t=>(t.isSigned=null,t.bitWidth=null,t[Symbol.toStringTag]="Int"))(Mt.prototype);class Ft extends Mt{constructor(){super(!0,32)}get ArrayType(){return Int32Array}}Object.defineProperty(class extends Mt{constructor(){super(!0,8)}get ArrayType(){return Int8Array}}.prototype,"ArrayType",{value:Int8Array}),Object.defineProperty(class extends Mt{constructor(){super(!0,16)}get ArrayType(){return Int16Array}}.prototype,"ArrayType",{value:Int16Array}),Object.defineProperty(Ft.prototype,"ArrayType",{value:Int32Array}),Object.defineProperty(class extends Mt{constructor(){super(!0,64)}get ArrayType(){return b}}.prototype,"ArrayType",{value:b}),Object.defineProperty(class extends Mt{constructor(){super(!1,8)}get ArrayType(){return Uint8Array}}.prototype,"ArrayType",{value:Uint8Array}),Object.defineProperty(class extends Mt{constructor(){super(!1,16)}get ArrayType(){return Uint16Array}}.prototype,"ArrayType",{value:Uint16Array}),Object.defineProperty(class extends Mt{constructor(){super(!1,32)}get ArrayType(){return Uint32Array}}.prototype,"ArrayType",{value:Uint32Array}),Object.defineProperty(class extends Mt{constructor(){super(!1,64)}get ArrayType(){return g}}.prototype,"ArrayType",{value:g});class Pt extends Bt{constructor(t){super(),this.precision=t}get typeId(){return l.Float}get ArrayType(){switch(this.precision){case r.HALF:return Uint16Array;case r.SINGLE:return Float32Array;case r.DOUBLE:return Float64Array}throw new Error("Unrecognized ".concat(this[Symbol.toStringTag]," type"))}toString(){return"Float".concat(this.precision<<5||16)}}yt=Symbol.toStringTag,Pt[yt]=(t=>(t.precision=null,t[Symbol.toStringTag]="Float"))(Pt.prototype);Object.defineProperty(class extends Pt{constructor(){super(r.HALF)}}.prototype,"ArrayType",{value:Uint16Array}),Object.defineProperty(class extends Pt{constructor(){super(r.SINGLE)}}.prototype,"ArrayType",{value:Float32Array}),Object.defineProperty(class extends Pt{constructor(){super(r.DOUBLE)}}.prototype,"ArrayType",{value:Float64Array});class Lt extends Bt{constructor(){super()}get typeId(){return l.Binary}toString(){return"Binary"}}bt=Symbol.toStringTag,Lt[bt]=(t=>(t.ArrayType=Uint8Array,t[Symbol.toStringTag]="Binary"))(Lt.prototype);class Ut extends Bt{constructor(){super()}get typeId(){return l.Utf8}toString(){return"Utf8"}}mt=Symbol.toStringTag,Ut[mt]=(t=>(t.ArrayType=Uint8Array,t[Symbol.toStringTag]="Utf8"))(Ut.prototype);class Rt extends Bt{constructor(){super()}get typeId(){return l.Bool}toString(){return"Bool"}}gt=Symbol.toStringTag,Rt[gt]=(t=>(t.ArrayType=Uint8Array,t[Symbol.toStringTag]="Bool"))(Rt.prototype);class zt extends Bt{constructor(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:128;super(),this.scale=t,this.precision=e,this.bitWidth=n}get typeId(){return l.Decimal}toString(){return"Decimal[".concat(this.precision,"e").concat(this.scale>0?"+":"").concat(this.scale,"]")}}vt=Symbol.toStringTag,zt[vt]=(t=>(t.scale=null,t.precision=null,t.ArrayType=Uint32Array,t[Symbol.toStringTag]="Decimal"))(zt.prototype);class Vt extends Bt{constructor(t){super(),this.unit=t}get typeId(){return l.Date}toString(){return"Date".concat(32*(this.unit+1),"<").concat(i[this.unit],">")}}wt=Symbol.toStringTag,Vt[wt]=(t=>(t.unit=null,t.ArrayType=Int32Array,t[Symbol.toStringTag]="Date"))(Vt.prototype);class jt extends Bt{constructor(t,e){super(),this.unit=t,this.bitWidth=e}get typeId(){return l.Time}toString(){return"Time".concat(this.bitWidth,"<").concat(o[this.unit],">")}get ArrayType(){switch(this.bitWidth){case 32:return Int32Array;case 64:return b}throw new Error("Unrecognized ".concat(this[Symbol.toStringTag]," type"))}}_t=Symbol.toStringTag,jt[_t]=(t=>(t.unit=null,t.bitWidth=null,t[Symbol.toStringTag]="Time"))(jt.prototype);class Wt extends Bt{constructor(t,e){super(),this.unit=t,this.timezone=e}get typeId(){return l.Timestamp}toString(){return"Timestamp<".concat(o[this.unit]).concat(this.timezone?", ".concat(this.timezone):"",">")}}St=Symbol.toStringTag,Wt[St]=(t=>(t.unit=null,t.timezone=null,t.ArrayType=Int32Array,t[Symbol.toStringTag]="Timestamp"))(Wt.prototype);class Ht extends Bt{constructor(t){super(),this.unit=t}get typeId(){return l.Interval}toString(){return"Interval<".concat(s[this.unit],">")}}Tt=Symbol.toStringTag,Ht[Tt]=(t=>(t.unit=null,t.ArrayType=Int32Array,t[Symbol.toStringTag]="Interval"))(Ht.prototype);class Yt extends Bt{constructor(t){super(),this.children=[t]}get typeId(){return l.List}toString(){return"List<".concat(this.valueType,">")}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}}It=Symbol.toStringTag,Yt[It]=(t=>(t.children=null,t[Symbol.toStringTag]="List"))(Yt.prototype);class $t extends Bt{constructor(t){super(),this.children=t}get typeId(){return l.Struct}toString(){return"Struct<{".concat(this.children.map((t=>"".concat(t.name,":").concat(t.type))).join(", "),"}>")}}xt=Symbol.toStringTag,$t[xt]=(t=>(t.children=null,t[Symbol.toStringTag]="Struct"))($t.prototype);class Qt extends Bt{constructor(t,e,n){super(),this.mode=t,this.children=n,this.typeIds=e=Int32Array.from(e),this.typeIdToChildIndex=e.reduce(((t,e,n)=>(t[e]=n)&&t||t),Object.create(null))}get typeId(){return l.Union}toString(){return"".concat(this[Symbol.toStringTag],"<").concat(this.children.map((t=>"".concat(t.type))).join(" | "),">")}}Et=Symbol.toStringTag,Qt[Et]=(t=>(t.mode=null,t.typeIds=null,t.children=null,t.typeIdToChildIndex=null,t.ArrayType=Int8Array,t[Symbol.toStringTag]="Union"))(Qt.prototype);class Kt extends Bt{constructor(t){super(),this.byteWidth=t}get typeId(){return l.FixedSizeBinary}toString(){return"FixedSizeBinary[".concat(this.byteWidth,"]")}}kt=Symbol.toStringTag,Kt[kt]=(t=>(t.byteWidth=null,t.ArrayType=Uint8Array,t[Symbol.toStringTag]="FixedSizeBinary"))(Kt.prototype);class qt extends Bt{constructor(t,e){super(),this.listSize=t,this.children=[e]}get typeId(){return l.FixedSizeList}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}toString(){return"FixedSizeList[".concat(this.listSize,"]<").concat(this.valueType,">")}}Ot=Symbol.toStringTag,qt[Ot]=(t=>(t.children=null,t.listSize=null,t[Symbol.toStringTag]="FixedSizeList"))(qt.prototype);class Gt extends Bt{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];super(),this.children=[t],this.keysSorted=e}get typeId(){return l.Map}get keyType(){return this.children[0].type.children[0].type}get valueType(){return this.children[0].type.children[1].type}get childType(){return this.children[0].type}toString(){return"Map<{".concat(this.children[0].type.children.map((t=>"".concat(t.name,":").concat(t.type))).join(", "),"}>")}}At=Symbol.toStringTag,Gt[At]=(t=>(t.children=null,t.keysSorted=null,t[Symbol.toStringTag]="Map_"))(Gt.prototype);const Jt=(Xt=-1,()=>++Xt);var Xt;class Zt extends Bt{constructor(t,e,n,r){super(),this.indices=e,this.dictionary=t,this.isOrdered=r||!1,this.id=null==n?Jt():"number"===typeof n?n:n.low}get typeId(){return l.Dictionary}get children(){return this.dictionary.children}get valueType(){return this.dictionary}get ArrayType(){return this.dictionary.ArrayType}toString(){return"Dictionary<".concat(this.indices,", ").concat(this.dictionary,">")}}function te(t){const e=t;switch(t.typeId){case l.Decimal:return t.bitWidth/32;case l.Timestamp:return 2;case l.Date:case l.Interval:return 1+e.unit;case l.FixedSizeList:return e.listSize;case l.FixedSizeBinary:return e.byteWidth;default:return 1}}Ct=Symbol.toStringTag,Zt[Ct]=(t=>(t.id=null,t.indices=null,t.isOrdered=null,t.dictionary=null,t[Symbol.toStringTag]="Dictionary"))(Zt.prototype);class ee{visitMany(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return t.map(((t,e)=>this.visit(t,...n.map((t=>t[e])))))}visit(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return this.getVisitFn(e[0],!1).apply(this,e)}getVisitFn(t){return function(t,e){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if("number"===typeof e)return ne(t,e,n);if("string"===typeof e&&e in l)return ne(t,l[e],n);if(e&&e instanceof Bt)return ne(t,re(e),n);if((null===e||void 0===e?void 0:e.type)&&e.type instanceof Bt)return ne(t,re(e.type),n);return ne(t,l.NONE,n)}(this,t,!(arguments.length>1&&void 0!==arguments[1])||arguments[1])}getVisitFnByTypeId(t){return ne(this,t,!(arguments.length>1&&void 0!==arguments[1])||arguments[1])}visitNull(t){return null}visitBool(t){return null}visitInt(t){return null}visitFloat(t){return null}visitUtf8(t){return null}visitBinary(t){return null}visitFixedSizeBinary(t){return null}visitDate(t){return null}visitTimestamp(t){return null}visitTime(t){return null}visitDecimal(t){return null}visitList(t){return null}visitStruct(t){return null}visitUnion(t){return null}visitDictionary(t){return null}visitInterval(t){return null}visitFixedSizeList(t){return null}visitMap(t){return null}}function ne(t,e){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=null;switch(e){case l.Null:r=t.visitNull;break;case l.Bool:r=t.visitBool;break;case l.Int:r=t.visitInt;break;case l.Int8:r=t.visitInt8||t.visitInt;break;case l.Int16:r=t.visitInt16||t.visitInt;break;case l.Int32:r=t.visitInt32||t.visitInt;break;case l.Int64:r=t.visitInt64||t.visitInt;break;case l.Uint8:r=t.visitUint8||t.visitInt;break;case l.Uint16:r=t.visitUint16||t.visitInt;break;case l.Uint32:r=t.visitUint32||t.visitInt;break;case l.Uint64:r=t.visitUint64||t.visitInt;break;case l.Float:r=t.visitFloat;break;case l.Float16:r=t.visitFloat16||t.visitFloat;break;case l.Float32:r=t.visitFloat32||t.visitFloat;break;case l.Float64:r=t.visitFloat64||t.visitFloat;break;case l.Utf8:r=t.visitUtf8;break;case l.Binary:r=t.visitBinary;break;case l.FixedSizeBinary:r=t.visitFixedSizeBinary;break;case l.Date:r=t.visitDate;break;case l.DateDay:r=t.visitDateDay||t.visitDate;break;case l.DateMillisecond:r=t.visitDateMillisecond||t.visitDate;break;case l.Timestamp:r=t.visitTimestamp;break;case l.TimestampSecond:r=t.visitTimestampSecond||t.visitTimestamp;break;case l.TimestampMillisecond:r=t.visitTimestampMillisecond||t.visitTimestamp;break;case l.TimestampMicrosecond:r=t.visitTimestampMicrosecond||t.visitTimestamp;break;case l.TimestampNanosecond:r=t.visitTimestampNanosecond||t.visitTimestamp;break;case l.Time:r=t.visitTime;break;case l.TimeSecond:r=t.visitTimeSecond||t.visitTime;break;case l.TimeMillisecond:r=t.visitTimeMillisecond||t.visitTime;break;case l.TimeMicrosecond:r=t.visitTimeMicrosecond||t.visitTime;break;case l.TimeNanosecond:r=t.visitTimeNanosecond||t.visitTime;break;case l.Decimal:r=t.visitDecimal;break;case l.List:r=t.visitList;break;case l.Struct:r=t.visitStruct;break;case l.Union:r=t.visitUnion;break;case l.DenseUnion:r=t.visitDenseUnion||t.visitUnion;break;case l.SparseUnion:r=t.visitSparseUnion||t.visitUnion;break;case l.Dictionary:r=t.visitDictionary;break;case l.Interval:r=t.visitInterval;break;case l.IntervalDayTime:r=t.visitIntervalDayTime||t.visitInterval;break;case l.IntervalYearMonth:r=t.visitIntervalYearMonth||t.visitInterval;break;case l.FixedSizeList:r=t.visitFixedSizeList;break;case l.Map:r=t.visitMap}if("function"===typeof r)return r;if(!n)return()=>null;throw new Error("Unrecognized type '".concat(l[e],"'"))}function re(t){switch(t.typeId){case l.Null:return l.Null;case l.Int:{const{bitWidth:e,isSigned:n}=t;switch(e){case 8:return n?l.Int8:l.Uint8;case 16:return n?l.Int16:l.Uint16;case 32:return n?l.Int32:l.Uint32;case 64:return n?l.Int64:l.Uint64}return l.Int}case l.Float:switch(t.precision){case r.HALF:return l.Float16;case r.SINGLE:return l.Float32;case r.DOUBLE:return l.Float64}return l.Float;case l.Binary:return l.Binary;case l.Utf8:return l.Utf8;case l.Bool:return l.Bool;case l.Decimal:return l.Decimal;case l.Time:switch(t.unit){case o.SECOND:return l.TimeSecond;case o.MILLISECOND:return l.TimeMillisecond;case o.MICROSECOND:return l.TimeMicrosecond;case o.NANOSECOND:return l.TimeNanosecond}return l.Time;case l.Timestamp:switch(t.unit){case o.SECOND:return l.TimestampSecond;case o.MILLISECOND:return l.TimestampMillisecond;case o.MICROSECOND:return l.TimestampMicrosecond;case o.NANOSECOND:return l.TimestampNanosecond}return l.Timestamp;case l.Date:switch(t.unit){case i.DAY:return l.DateDay;case i.MILLISECOND:return l.DateMillisecond}return l.Date;case l.Interval:switch(t.unit){case s.DAY_TIME:return l.IntervalDayTime;case s.YEAR_MONTH:return l.IntervalYearMonth}return l.Interval;case l.Map:return l.Map;case l.List:return l.List;case l.Struct:return l.Struct;case l.Union:switch(t.mode){case e.Dense:return l.DenseUnion;case e.Sparse:return l.SparseUnion}return l.Union;case l.FixedSizeBinary:return l.FixedSizeBinary;case l.FixedSizeList:return l.FixedSizeList;case l.Dictionary:return l.Dictionary}throw new Error("Unrecognized type '".concat(l[t.typeId],"'"))}ee.prototype.visitInt8=null,ee.prototype.visitInt16=null,ee.prototype.visitInt32=null,ee.prototype.visitInt64=null,ee.prototype.visitUint8=null,ee.prototype.visitUint16=null,ee.prototype.visitUint32=null,ee.prototype.visitUint64=null,ee.prototype.visitFloat16=null,ee.prototype.visitFloat32=null,ee.prototype.visitFloat64=null,ee.prototype.visitDateDay=null,ee.prototype.visitDateMillisecond=null,ee.prototype.visitTimestampSecond=null,ee.prototype.visitTimestampMillisecond=null,ee.prototype.visitTimestampMicrosecond=null,ee.prototype.visitTimestampNanosecond=null,ee.prototype.visitTimeSecond=null,ee.prototype.visitTimeMillisecond=null,ee.prototype.visitTimeMicrosecond=null,ee.prototype.visitTimeNanosecond=null,ee.prototype.visitDenseUnion=null,ee.prototype.visitSparseUnion=null,ee.prototype.visitIntervalDayTime=null,ee.prototype.visitIntervalYearMonth=null;const ie=new Float64Array(1),oe=new Uint32Array(ie.buffer);function se(t){const e=(31744&t)>>10,n=(1023&t)/1024,r=Math.pow(-1,(32768&t)>>15);switch(e){case 31:return r*(n?Number.NaN:1/0);case 0:return r*(n?6103515625e-14*n:0)}return r*Math.pow(2,e-15)*(1+n)}class ae extends ee{}function le(t){return(e,n,r)=>{if(e.setValid(n,null!=r))return t(e,n,r)}}const ue=(t,e,n)=>{t[e]=Math.trunc(n%4294967296),t[e+1]=Math.trunc(n/4294967296)},ce=(t,e,n,r)=>{if(n+1<e.length){const{[n]:i,[n+1]:o}=e;t.set(r.subarray(0,o-i),i)}},de=(t,e,n)=>{let{values:r}=t;r[e]=n},fe=(t,e,n)=>{let{values:r}=t;r[e]=n},he=(t,e,n)=>{let{values:r}=t;r[e]=function(t){if(t!==t)return 32256;ie[0]=t;const e=(2147483648&oe[1])>>16&65535;let n=2146435072&oe[1],r=0;return n>=1089470464?oe[0]>0?n=31744:(n=(2080374784&n)>>16,r=(1048575&oe[1])>>10):n<=1056964608?(r=1048576+(1048575&oe[1]),r=1048576+(r<<(n>>20)-998)>>21,n=0):(n=n-1056964608>>10,r=512+(1048575&oe[1])>>10),e|n|65535&r}(n)},pe=(t,e,n)=>{let{values:r}=t;((t,e,n)=>{t[e]=Math.trunc(n/864e5)})(r,e,n.valueOf())},ye=(t,e,n)=>{let{values:r}=t;ue(r,2*e,n.valueOf())},be=(t,e,n)=>{let{values:r}=t;return ue(r,2*e,n/1e3)},me=(t,e,n)=>{let{values:r}=t;return ue(r,2*e,n)},ge=(t,e,n)=>{let{values:r}=t;return((t,e,n)=>{t[e]=Math.trunc(1e3*n%4294967296),t[e+1]=Math.trunc(1e3*n/4294967296)})(r,2*e,n)},ve=(t,e,n)=>{let{values:r}=t;return((t,e,n)=>{t[e]=Math.trunc(1e6*n%4294967296),t[e+1]=Math.trunc(1e6*n/4294967296)})(r,2*e,n)},we=(t,e,n)=>{let{values:r}=t;r[e]=n},_e=(t,e,n)=>{let{values:r}=t;r[e]=n},Se=(t,e,n)=>{let{values:r}=t;r[e]=n},Te=(t,e,n)=>{let{values:r}=t;r[e]=n},Ie=(t,e,n)=>{const r=t.type.typeIdToChildIndex[t.typeIds[e]],i=t.children[r];Oe.visit(i,t.valueOffsets[e],n)},xe=(t,e,n)=>{const r=t.type.typeIdToChildIndex[t.typeIds[e]],i=t.children[r];Oe.visit(i,e,n)},Ee=(t,e,n)=>{let{values:r}=t;r.set(n.subarray(0,2),2*e)},ke=(t,e,n)=>{let{values:r}=t;r[e]=12*n[0]+n[1]%12};ae.prototype.visitBool=le(((t,e,n)=>{let{offset:r,values:i}=t;const o=r+e;n?i[o>>3]|=1<<o%8:i[o>>3]&=~(1<<o%8)})),ae.prototype.visitInt=le(de),ae.prototype.visitInt8=le(de),ae.prototype.visitInt16=le(de),ae.prototype.visitInt32=le(de),ae.prototype.visitInt64=le(de),ae.prototype.visitUint8=le(de),ae.prototype.visitUint16=le(de),ae.prototype.visitUint32=le(de),ae.prototype.visitUint64=le(de),ae.prototype.visitFloat=le(((t,e,n)=>{switch(t.type.precision){case r.HALF:return he(t,e,n);case r.SINGLE:case r.DOUBLE:return fe(t,e,n)}})),ae.prototype.visitFloat16=le(he),ae.prototype.visitFloat32=le(fe),ae.prototype.visitFloat64=le(fe),ae.prototype.visitUtf8=le(((t,e,n)=>{let{values:r,valueOffsets:i}=t;ce(r,i,e,Y(n))})),ae.prototype.visitBinary=le(((t,e,n)=>{let{values:r,valueOffsets:i}=t;return ce(r,i,e,n)})),ae.prototype.visitFixedSizeBinary=le(((t,e,n)=>{let{stride:r,values:i}=t;i.set(n.subarray(0,r),r*e)})),ae.prototype.visitDate=le(((t,e,n)=>{t.type.unit===i.DAY?pe(t,e,n):ye(t,e,n)})),ae.prototype.visitDateDay=le(pe),ae.prototype.visitDateMillisecond=le(ye),ae.prototype.visitTimestamp=le(((t,e,n)=>{switch(t.type.unit){case o.SECOND:return be(t,e,n);case o.MILLISECOND:return me(t,e,n);case o.MICROSECOND:return ge(t,e,n);case o.NANOSECOND:return ve(t,e,n)}})),ae.prototype.visitTimestampSecond=le(be),ae.prototype.visitTimestampMillisecond=le(me),ae.prototype.visitTimestampMicrosecond=le(ge),ae.prototype.visitTimestampNanosecond=le(ve),ae.prototype.visitTime=le(((t,e,n)=>{switch(t.type.unit){case o.SECOND:return we(t,e,n);case o.MILLISECOND:return _e(t,e,n);case o.MICROSECOND:return Se(t,e,n);case o.NANOSECOND:return Te(t,e,n)}})),ae.prototype.visitTimeSecond=le(we),ae.prototype.visitTimeMillisecond=le(_e),ae.prototype.visitTimeMicrosecond=le(Se),ae.prototype.visitTimeNanosecond=le(Te),ae.prototype.visitDecimal=le(((t,e,n)=>{let{values:r,stride:i}=t;r.set(n.subarray(0,i),i*e)})),ae.prototype.visitList=le(((t,e,n)=>{const r=t.children[0],i=t.valueOffsets,o=Oe.getVisitFn(r);if(Array.isArray(n))for(let s=-1,a=i[e],l=i[e+1];a<l;)o(r,a++,n[++s]);else for(let s=-1,a=i[e],l=i[e+1];a<l;)o(r,a++,n.get(++s))})),ae.prototype.visitStruct=le(((t,e,n)=>{const r=t.type.children.map((t=>Oe.getVisitFn(t.type))),i=n instanceof Map?(o=e,s=n,(t,e,n,r)=>e&&t(e,o,s.get(n.name))):n instanceof $n?((t,e)=>(n,r,i,o)=>r&&n(r,t,e.get(o)))(e,n):Array.isArray(n)?((t,e)=>(n,r,i,o)=>r&&n(r,t,e[o]))(e,n):((t,e)=>(n,r,i,o)=>r&&n(r,t,e[i.name]))(e,n);var o,s;t.type.children.forEach(((e,n)=>i(r[n],t.children[n],e,n)))})),ae.prototype.visitUnion=le(((t,n,r)=>{t.type.mode===e.Dense?Ie(t,n,r):xe(t,n,r)})),ae.prototype.visitDenseUnion=le(Ie),ae.prototype.visitSparseUnion=le(xe),ae.prototype.visitDictionary=le(((t,e,n)=>{var r;null===(r=t.dictionary)||void 0===r||r.set(t.values[e],n)})),ae.prototype.visitInterval=le(((t,e,n)=>{t.type.unit===s.DAY_TIME?Ee(t,e,n):ke(t,e,n)})),ae.prototype.visitIntervalDayTime=le(Ee),ae.prototype.visitIntervalYearMonth=le(ke),ae.prototype.visitFixedSizeList=le(((t,e,n)=>{const{stride:r}=t,i=t.children[0],o=Oe.getVisitFn(i);if(Array.isArray(n))for(let s=-1,a=e*r;++s<r;)o(i,a+s,n[s]);else for(let s=-1,a=e*r;++s<r;)o(i,a+s,n.get(s))})),ae.prototype.visitMap=le(((t,e,n)=>{const r=t.children[0],{valueOffsets:i}=t,o=Oe.getVisitFn(r);let{[e]:s,[e+1]:a}=i;const l=n instanceof Map?n.entries():Object.entries(n);for(const u of l)if(o(r,s,u),++s>=a)break}));const Oe=new ae,Ae=Symbol.for("parent"),Ce=Symbol.for("rowIndex");class De{constructor(t,e){return this[Ae]=t,this[Ce]=e,new Proxy(this,new Ne)}toArray(){return Object.values(this.toJSON())}toJSON(){const t=this[Ce],e=this[Ae],n=e.type.children,r={};for(let i=-1,o=n.length;++i<o;)r[n[i].name]=en.visit(e.children[i],t);return r}toString(){return"{".concat([...this].map((t=>{let[e,n]=t;return"".concat(F(e),": ").concat(F(n))})).join(", "),"}")}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}[Symbol.iterator](){return new Be(this[Ae],this[Ce])}}class Be{constructor(t,e){this.childIndex=0,this.children=t.children,this.rowIndex=e,this.childFields=t.type.children,this.numChildren=this.childFields.length}[Symbol.iterator](){return this}next(){const t=this.childIndex;return t<this.numChildren?(this.childIndex=t+1,{done:!1,value:[this.childFields[t].name,en.visit(this.children[t],this.rowIndex)]}):{done:!0,value:null}}}Object.defineProperties(De.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[Ae]:{writable:!0,enumerable:!1,configurable:!1,value:null},[Ce]:{writable:!0,enumerable:!1,configurable:!1,value:-1}});class Ne{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(t){return t[Ae].type.children.map((t=>t.name))}has(t,e){return-1!==t[Ae].type.children.findIndex((t=>t.name===e))}getOwnPropertyDescriptor(t,e){if(-1!==t[Ae].type.children.findIndex((t=>t.name===e)))return{writable:!0,enumerable:!0,configurable:!0}}get(t,e){if(Reflect.has(t,e))return t[e];const n=t[Ae].type.children.findIndex((t=>t.name===e));if(-1!==n){const r=en.visit(t[Ae].children[n],t[Ce]);return Reflect.set(t,e,r),r}}set(t,e,n){const r=t[Ae].type.children.findIndex((t=>t.name===e));return-1!==r?(Oe.visit(t[Ae].children[r],t[Ce],n),Reflect.set(t,e,n)):!(!Reflect.has(t,e)&&"symbol"!==typeof e)&&Reflect.set(t,e,n)}}class Me extends ee{}function Fe(t){return(e,n)=>e.getValid(n)?t(e,n):null}const Pe=(t,e)=>4294967296*t[e+1]+(t[e]>>>0),Le=t=>new Date(t),Ue=(t,e,n)=>{if(n+1>=e.length)return null;const r=e[n],i=e[n+1];return t.subarray(r,i)},Re=(t,e)=>{let{values:n}=t;return((t,e)=>Le(((t,e)=>864e5*t[e])(t,e)))(n,e)},ze=(t,e)=>{let{values:n}=t;return((t,e)=>Le(Pe(t,e)))(n,2*e)},Ve=(t,e)=>{let{stride:n,values:r}=t;return r[n*e]},je=(t,e)=>{let{values:n}=t;return n[e]},We=(t,e)=>{let{values:n}=t;return 1e3*Pe(n,2*e)},He=(t,e)=>{let{values:n}=t;return Pe(n,2*e)},Ye=(t,e)=>{let{values:n}=t;return((t,e)=>t[e+1]/1e3*4294967296+(t[e]>>>0)/1e3)(n,2*e)},$e=(t,e)=>{let{values:n}=t;return((t,e)=>t[e+1]/1e6*4294967296+(t[e]>>>0)/1e6)(n,2*e)},Qe=(t,e)=>{let{values:n}=t;return n[e]},Ke=(t,e)=>{let{values:n}=t;return n[e]},qe=(t,e)=>{let{values:n}=t;return n[e]},Ge=(t,e)=>{let{values:n}=t;return n[e]},Je=(t,e)=>{const n=t.type.typeIdToChildIndex[t.typeIds[e]],r=t.children[n];return en.visit(r,t.valueOffsets[e])},Xe=(t,e)=>{const n=t.type.typeIdToChildIndex[t.typeIds[e]],r=t.children[n];return en.visit(r,e)},Ze=(t,e)=>{let{values:n}=t;return n.subarray(2*e,2*(e+1))},tn=(t,e)=>{let{values:n}=t;const r=n[e],i=new Int32Array(2);return i[0]=Math.trunc(r/12),i[1]=Math.trunc(r%12),i};Me.prototype.visitNull=Fe(((t,e)=>null)),Me.prototype.visitBool=Fe(((t,e)=>{let{offset:n,values:r}=t;const i=n+e;return 0!==(r[i>>3]&1<<i%8)})),Me.prototype.visitInt=Fe(((t,e)=>{let{values:n}=t;return n[e]})),Me.prototype.visitInt8=Fe(Ve),Me.prototype.visitInt16=Fe(Ve),Me.prototype.visitInt32=Fe(Ve),Me.prototype.visitInt64=Fe(je),Me.prototype.visitUint8=Fe(Ve),Me.prototype.visitUint16=Fe(Ve),Me.prototype.visitUint32=Fe(Ve),Me.prototype.visitUint64=Fe(je),Me.prototype.visitFloat=Fe(((t,e)=>{let{type:n,values:i}=t;return n.precision!==r.HALF?i[e]:se(i[e])})),Me.prototype.visitFloat16=Fe(((t,e)=>{let{stride:n,values:r}=t;return se(r[n*e])})),Me.prototype.visitFloat32=Fe(Ve),Me.prototype.visitFloat64=Fe(Ve),Me.prototype.visitUtf8=Fe(((t,e)=>{let{values:n,valueOffsets:r}=t;const i=Ue(n,r,e);return null!==i?W(i):null})),Me.prototype.visitBinary=Fe(((t,e)=>{let{values:n,valueOffsets:r}=t;return Ue(n,r,e)})),Me.prototype.visitFixedSizeBinary=Fe(((t,e)=>{let{stride:n,values:r}=t;return r.subarray(n*e,n*(e+1))})),Me.prototype.visitDate=Fe(((t,e)=>t.type.unit===i.DAY?Re(t,e):ze(t,e))),Me.prototype.visitDateDay=Fe(Re),Me.prototype.visitDateMillisecond=Fe(ze),Me.prototype.visitTimestamp=Fe(((t,e)=>{switch(t.type.unit){case o.SECOND:return We(t,e);case o.MILLISECOND:return He(t,e);case o.MICROSECOND:return Ye(t,e);case o.NANOSECOND:return $e(t,e)}})),Me.prototype.visitTimestampSecond=Fe(We),Me.prototype.visitTimestampMillisecond=Fe(He),Me.prototype.visitTimestampMicrosecond=Fe(Ye),Me.prototype.visitTimestampNanosecond=Fe($e),Me.prototype.visitTime=Fe(((t,e)=>{switch(t.type.unit){case o.SECOND:return Qe(t,e);case o.MILLISECOND:return Ke(t,e);case o.MICROSECOND:return qe(t,e);case o.NANOSECOND:return Ge(t,e)}})),Me.prototype.visitTimeSecond=Fe(Qe),Me.prototype.visitTimeMillisecond=Fe(Ke),Me.prototype.visitTimeMicrosecond=Fe(qe),Me.prototype.visitTimeNanosecond=Fe(Ge),Me.prototype.visitDecimal=Fe(((t,e)=>{let{values:n,stride:r}=t;return dt.decimal(n.subarray(r*e,r*(e+1)))})),Me.prototype.visitList=Fe(((t,e)=>{const{valueOffsets:n,stride:r,children:i}=t,{[e*r]:o,[e*r+1]:s}=n,a=i[0].slice(o,s-o);return new $n([a])})),Me.prototype.visitStruct=Fe(((t,e)=>new De(t,e))),Me.prototype.visitUnion=Fe(((t,n)=>t.type.mode===e.Dense?Je(t,n):Xe(t,n))),Me.prototype.visitDenseUnion=Fe(Je),Me.prototype.visitSparseUnion=Fe(Xe),Me.prototype.visitDictionary=Fe(((t,e)=>{var n;return null===(n=t.dictionary)||void 0===n?void 0:n.get(t.values[e])})),Me.prototype.visitInterval=Fe(((t,e)=>t.type.unit===s.DAY_TIME?Ze(t,e):tn(t,e))),Me.prototype.visitIntervalDayTime=Fe(Ze),Me.prototype.visitIntervalYearMonth=Fe(tn),Me.prototype.visitFixedSizeList=Fe(((t,e)=>{const{stride:n,children:r}=t,i=r[0].slice(e*n,n);return new $n([i])})),Me.prototype.visitMap=Fe(((t,e)=>{const{valueOffsets:n,children:r}=t,{[e]:i,[e+1]:o}=n,s=r[0];return new on(s.slice(i,o-i))}));const en=new Me,nn=Symbol.for("keys"),rn=Symbol.for("vals");class on{constructor(t){return this[nn]=new $n([t.children[0]]).memoize(),this[rn]=t.children[1],new Proxy(this,new an)}[Symbol.iterator](){return new sn(this[nn],this[rn])}get size(){return this[nn].length}toArray(){return Object.values(this.toJSON())}toJSON(){const t=this[nn],e=this[rn],n={};for(let r=-1,i=t.length;++r<i;)n[t.get(r)]=en.visit(e,r);return n}toString(){return"{".concat([...this].map((t=>{let[e,n]=t;return"".concat(F(e),": ").concat(F(n))})).join(", "),"}")}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}}class sn{constructor(t,e){this.keys=t,this.vals=e,this.keyIndex=0,this.numKeys=t.length}[Symbol.iterator](){return this}next(){const t=this.keyIndex;return t===this.numKeys?{done:!0,value:null}:(this.keyIndex++,{done:!1,value:[this.keys.get(t),en.visit(this.vals,t)]})}}class an{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(t){return t[nn].toArray().map(String)}has(t,e){return t[nn].includes(e)}getOwnPropertyDescriptor(t,e){if(-1!==t[nn].indexOf(e))return{writable:!0,enumerable:!0,configurable:!0}}get(t,e){if(Reflect.has(t,e))return t[e];const n=t[nn].indexOf(e);if(-1!==n){const r=en.visit(Reflect.get(t,rn),n);return Reflect.set(t,e,r),r}}set(t,e,n){const r=t[nn].indexOf(e);return-1!==r?(Oe.visit(Reflect.get(t,rn),r,n),Reflect.set(t,e,n)):!!Reflect.has(t,e)&&Reflect.set(t,e,n)}}let ln;function un(t,e,n,r){const{length:i=0}=t;let o="number"!==typeof e?0:e,s="number"!==typeof n?i:n;return o<0&&(o=(o%i+i)%i),s<0&&(s=(s%i+i)%i),s<o&&(ln=o,o=s,s=ln),s>i&&(s=i),r?r(t,o,s):[o,s]}Object.defineProperties(on.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[nn]:{writable:!0,enumerable:!1,configurable:!1,value:null},[rn]:{writable:!0,enumerable:!1,configurable:!1,value:null}});const cn=t=>t!==t;function dn(t){if("object"!==typeof t||null===t)return cn(t)?cn:e=>e===t;if(t instanceof Date){const e=t.valueOf();return t=>t instanceof Date&&t.valueOf()===e}return ArrayBuffer.isView(t)?e=>!!e&&function(t,e){let n=0;const r=t.length;if(r!==e.length)return!1;if(r>0)do{if(t[n]!==e[n])return!1}while(++n<r);return!0}(t,e):t instanceof Map?function(t){let e=-1;const n=[];for(const r of t.values())n[++e]=dn(r);return fn(n)}(t):Array.isArray(t)?function(t){const e=[];for(let n=-1,r=t.length;++n<r;)e[n]=dn(t[n]);return fn(e)}(t):t instanceof $n?function(t){const e=[];for(let n=-1,r=t.length;++n<r;)e[n]=dn(t.get(n));return fn(e)}(t):function(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=Object.keys(t);if(!e&&0===n.length)return()=>!1;const r=[];for(let i=-1,o=n.length;++i<o;)r[i]=dn(t[n[i]]);return fn(r,n)}(t,!0)}function fn(t,e){return n=>{if(!n||"object"!==typeof n)return!1;switch(n.constructor){case Array:return function(t,e){const n=t.length;if(e.length!==n)return!1;for(let r=-1;++r<n;)if(!t[r](e[r]))return!1;return!0}(t,n);case Map:return hn(t,n,n.keys());case on:case De:case Object:case void 0:return hn(t,n,e||Object.keys(n))}return n instanceof $n&&function(t,e){const n=t.length;if(e.length!==n)return!1;for(let r=-1;++r<n;)if(!t[r](e.get(r)))return!1;return!0}(t,n)}}function hn(t,e,n){const r=n[Symbol.iterator](),i=e instanceof Map?e.keys():Object.keys(e)[Symbol.iterator](),o=e instanceof Map?e.values():Object.values(e)[Symbol.iterator]();let s=0;const a=t.length;let l=o.next(),u=r.next(),c=i.next();for(;s<a&&!u.done&&!c.done&&!l.done&&(u.value===c.value&&t[s](l.value));++s,u=r.next(),c=i.next(),l=o.next());return!!(s===a&&u.done&&c.done&&l.done)||(r.return&&r.return(),i.return&&i.return(),o.return&&o.return(),!1)}class pn{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1?arguments[1]:void 0;this.numChunks=t,this.getChunkIterator=e,this.chunkIndex=0,this.chunkIterator=this.getChunkIterator(0)}next(){for(;this.chunkIndex<this.numChunks;){const t=this.chunkIterator.next();if(!t.done)return t;++this.chunkIndex<this.numChunks&&(this.chunkIterator=this.getChunkIterator(this.chunkIndex))}return{done:!0,value:null}}[Symbol.iterator](){return this}}function yn(t){return t.reduce(((t,e)=>t+e.nullCount),0)}function bn(t){return t.reduce(((t,e,n)=>(t[n+1]=t[n]+e.length,t)),new Uint32Array(t.length+1))}function mn(t,e,n,r){const i=[];for(let o=-1,s=t.length;++o<s;){const s=t[o],a=e[o],{length:l}=s;if(a>=r)break;if(n>=a+l)continue;if(a>=n&&a+l<=r){i.push(s);continue}const u=Math.max(0,n-a),c=Math.min(r-a,l);i.push(s.slice(u,c-u))}return 0===i.length&&i.push(t[0].slice(0,0)),i}function gn(t,e,n,r){let i=0,o=0,s=e.length-1;do{if(i>=s-1)return n<e[s]?r(t,i,n-e[i]):null;o=i+Math.trunc(.5*(s-i)),n<e[o]?s=o:i=o}while(i<s)}function vn(t,e){return t.getValid(e)}function wn(t){function e(e,n,r){return t(e[n],r)}return function(t){return gn(this.data,this._offsets,t,e)}}function _n(t){let e;function n(n,r,i){return t(n[r],i,e)}return function(t,r){const i=this.data;e=r;const o=gn(i,this._offsets,t,n);return e=void 0,o}}function Sn(t){let e;function n(n,r,i){let o=i,s=0,a=0;for(let l=r-1,u=n.length;++l<u;){const r=n[l];if(~(s=t(r,e,o)))return a+s;o=0,a+=r.length}return-1}return function(t,r){e=t;const i=this.data,o="number"!==typeof r?n(i,0,0):gn(i,this._offsets,r,n);return e=void 0,o}}function Tn(t,e,n,r){return 0!==(n&1<<r)}function In(t,e,n,r){return(n&1<<r)>>r}function xn(t,e,n){const r=n.byteLength+7&-8;if(t>0||n.byteLength<r){const i=new Uint8Array(r);return i.set(t%8===0?n.subarray(t>>3):En(new kn(n,t,e,null,Tn)).subarray(0,r)),i}return n}function En(t){const e=[];let n=0,r=0,i=0;for(const s of t)s&&(i|=1<<r),8===++r&&(e[n++]=i,i=r=0);(0===n||r>0)&&(e[n++]=i);const o=new Uint8Array(e.length+7&-8);return o.set(e),o}class kn{constructor(t,e,n,r,i){this.bytes=t,this.length=n,this.context=r,this.get=i,this.bit=e%8,this.byteIndex=e>>3,this.byte=t[this.byteIndex++],this.index=0}next(){return this.index<this.length?(8===this.bit&&(this.bit=0,this.byte=this.bytes[this.byteIndex++]),{value:this.get(this.context,this.index++,this.byte,this.bit++)}):{done:!0,value:null}}[Symbol.iterator](){return this}}function On(t,e,n){if(n-e<=0)return 0;if(n-e<8){let r=0;for(const i of new kn(t,e,n-e,t,In))r+=i;return r}const r=n>>3<<3,i=e+(e%8===0?0:8-e%8);return On(t,e,i)+On(t,r,n)+function(t,e,n){let r=0,i=Math.trunc(e);const o=new DataView(t.buffer,t.byteOffset,t.byteLength),s=void 0===n?t.byteLength:i+n;for(;s-i>=4;)r+=An(o.getUint32(i)),i+=4;for(;s-i>=2;)r+=An(o.getUint16(i)),i+=2;for(;s-i>=1;)r+=An(o.getUint8(i)),i+=1;return r}(t,i>>3,r-i>>3)}function An(t){let e=Math.trunc(t);return e-=e>>>1&1431655765,e=(*********&e)+(e>>>2&*********),16843009*(e+(e>>>4)&*********)>>>24}class Cn extends ee{}function Dn(t,e,n){if(void 0===e)return-1;if(null===e)return function(t,e){const{nullBitmap:n}=t;if(!n||t.nullCount<=0)return-1;let r=0;for(const i of new kn(n,t.offset+(e||0),t.length,n,Tn)){if(!i)return r;++r}return-1}(t,n);const r=en.getVisitFn(t),i=dn(e);for(let o=(n||0)-1,s=t.length;++o<s;)if(i(r(t,o)))return o;return-1}function Bn(t,e,n){const r=en.getVisitFn(t),i=dn(e);for(let o=(n||0)-1,s=t.length;++o<s;)if(i(r(t,o)))return o;return-1}Cn.prototype.visitNull=function(t,e){return null===e&&t.length>0?0:-1},Cn.prototype.visitBool=Dn,Cn.prototype.visitInt=Dn,Cn.prototype.visitInt8=Dn,Cn.prototype.visitInt16=Dn,Cn.prototype.visitInt32=Dn,Cn.prototype.visitInt64=Dn,Cn.prototype.visitUint8=Dn,Cn.prototype.visitUint16=Dn,Cn.prototype.visitUint32=Dn,Cn.prototype.visitUint64=Dn,Cn.prototype.visitFloat=Dn,Cn.prototype.visitFloat16=Dn,Cn.prototype.visitFloat32=Dn,Cn.prototype.visitFloat64=Dn,Cn.prototype.visitUtf8=Dn,Cn.prototype.visitBinary=Dn,Cn.prototype.visitFixedSizeBinary=Dn,Cn.prototype.visitDate=Dn,Cn.prototype.visitDateDay=Dn,Cn.prototype.visitDateMillisecond=Dn,Cn.prototype.visitTimestamp=Dn,Cn.prototype.visitTimestampSecond=Dn,Cn.prototype.visitTimestampMillisecond=Dn,Cn.prototype.visitTimestampMicrosecond=Dn,Cn.prototype.visitTimestampNanosecond=Dn,Cn.prototype.visitTime=Dn,Cn.prototype.visitTimeSecond=Dn,Cn.prototype.visitTimeMillisecond=Dn,Cn.prototype.visitTimeMicrosecond=Dn,Cn.prototype.visitTimeNanosecond=Dn,Cn.prototype.visitDecimal=Dn,Cn.prototype.visitList=Dn,Cn.prototype.visitStruct=Dn,Cn.prototype.visitUnion=Dn,Cn.prototype.visitDenseUnion=Bn,Cn.prototype.visitSparseUnion=Bn,Cn.prototype.visitDictionary=Dn,Cn.prototype.visitInterval=Dn,Cn.prototype.visitIntervalDayTime=Dn,Cn.prototype.visitIntervalYearMonth=Dn,Cn.prototype.visitFixedSizeList=Dn,Cn.prototype.visitMap=Dn;const Nn=new Cn;class Mn extends ee{}function Fn(t){const{type:e}=t;if(0===t.nullCount&&1===t.stride&&(e.typeId===l.Timestamp||e instanceof Mt&&64!==e.bitWidth||e instanceof jt&&64!==e.bitWidth||e instanceof Pt&&e.precision!==r.HALF))return new pn(t.data.length,(e=>{const n=t.data[e];return n.values.subarray(0,n.length)[Symbol.iterator]()}));let n=0;return new pn(t.data.length,(e=>{const r=t.data[e].length,i=t.slice(n,n+r);return n+=r,new Pn(i)}))}class Pn{constructor(t){this.vector=t,this.index=0}next(){return this.index<this.vector.length?{value:this.vector.get(this.index++)}:{done:!0,value:null}}[Symbol.iterator](){return this}}Mn.prototype.visitNull=Fn,Mn.prototype.visitBool=Fn,Mn.prototype.visitInt=Fn,Mn.prototype.visitInt8=Fn,Mn.prototype.visitInt16=Fn,Mn.prototype.visitInt32=Fn,Mn.prototype.visitInt64=Fn,Mn.prototype.visitUint8=Fn,Mn.prototype.visitUint16=Fn,Mn.prototype.visitUint32=Fn,Mn.prototype.visitUint64=Fn,Mn.prototype.visitFloat=Fn,Mn.prototype.visitFloat16=Fn,Mn.prototype.visitFloat32=Fn,Mn.prototype.visitFloat64=Fn,Mn.prototype.visitUtf8=Fn,Mn.prototype.visitBinary=Fn,Mn.prototype.visitFixedSizeBinary=Fn,Mn.prototype.visitDate=Fn,Mn.prototype.visitDateDay=Fn,Mn.prototype.visitDateMillisecond=Fn,Mn.prototype.visitTimestamp=Fn,Mn.prototype.visitTimestampSecond=Fn,Mn.prototype.visitTimestampMillisecond=Fn,Mn.prototype.visitTimestampMicrosecond=Fn,Mn.prototype.visitTimestampNanosecond=Fn,Mn.prototype.visitTime=Fn,Mn.prototype.visitTimeSecond=Fn,Mn.prototype.visitTimeMillisecond=Fn,Mn.prototype.visitTimeMicrosecond=Fn,Mn.prototype.visitTimeNanosecond=Fn,Mn.prototype.visitDecimal=Fn,Mn.prototype.visitList=Fn,Mn.prototype.visitStruct=Fn,Mn.prototype.visitUnion=Fn,Mn.prototype.visitDenseUnion=Fn,Mn.prototype.visitSparseUnion=Fn,Mn.prototype.visitDictionary=Fn,Mn.prototype.visitInterval=Fn,Mn.prototype.visitIntervalDayTime=Fn,Mn.prototype.visitIntervalYearMonth=Fn,Mn.prototype.visitFixedSizeList=Fn,Mn.prototype.visitMap=Fn;const Ln=new Mn,Un=(t,e)=>t+e;class Rn extends ee{visitNull(t,e){return 0}visitInt(t,e){return t.type.bitWidth/8}visitFloat(t,e){return t.type.ArrayType.BYTES_PER_ELEMENT}visitBool(t,e){return 1/8}visitDecimal(t,e){return t.type.bitWidth/8}visitDate(t,e){return 4*(t.type.unit+1)}visitTime(t,e){return t.type.bitWidth/8}visitTimestamp(t,e){return t.type.unit===o.SECOND?4:8}visitInterval(t,e){return 4*(t.type.unit+1)}visitStruct(t,e){return t.children.reduce(((t,n)=>t+jn.visit(n,e)),0)}visitFixedSizeBinary(t,e){return t.type.byteWidth}visitMap(t,e){return 8+t.children.reduce(((t,n)=>t+jn.visit(n,e)),0)}visitDictionary(t,e){var n;return t.type.indices.bitWidth/8+((null===(n=t.dictionary)||void 0===n?void 0:n.getByteLength(t.values[e]))||0)}}const zn=(t,e)=>{let{type:n,children:r,typeIds:i,valueOffsets:o}=t;const s=n.typeIdToChildIndex[i[e]];return 8+jn.visit(r[s],o[e])},Vn=(t,e)=>{let{children:n}=t;return 4+jn.visitMany(n,n.map((()=>e))).reduce(Un,0)};Rn.prototype.visitUtf8=(t,e)=>{let{valueOffsets:n}=t;return n[e+1]-n[e]+8},Rn.prototype.visitBinary=(t,e)=>{let{valueOffsets:n}=t;return n[e+1]-n[e]+8},Rn.prototype.visitList=(t,e)=>{let{valueOffsets:n,stride:r,children:i}=t;const o=i[0],{[e*r]:s}=n,{[e*r+1]:a}=n,l=jn.getVisitFn(o.type),u=o.slice(s,a-s);let c=8;for(let d=-1,f=a-s;++d<f;)c+=l(u,d);return c},Rn.prototype.visitFixedSizeList=(t,e)=>{let{stride:n,children:r}=t;const i=r[0],o=i.slice(e*n,n),s=jn.getVisitFn(i.type);let a=0;for(let l=-1,u=o.length;++l<u;)a+=s(o,l);return a},Rn.prototype.visitUnion=(t,n)=>t.type.mode===e.Dense?zn(t,n):Vn(t,n),Rn.prototype.visitDenseUnion=zn,Rn.prototype.visitSparseUnion=Vn;const jn=new Rn;var Wn;const Hn={},Yn={};class $n{constructor(t){var e,n,r;const i=t[0]instanceof $n?t.flatMap((t=>t.data)):t;if(0===i.length||i.some((t=>!(t instanceof Kn))))throw new TypeError("Vector constructor expects an Array of Data instances.");const o=null===(e=i[0])||void 0===e?void 0:e.type;switch(i.length){case 0:this._offsets=[0];break;case 1:{const{get:t,set:e,indexOf:n,byteLength:r}=Hn[o.typeId],s=i[0];this.isValid=t=>vn(s,t),this.get=e=>t(s,e),this.set=(t,n)=>e(s,t,n),this.indexOf=t=>n(s,t),this.getByteLength=t=>r(s,t),this._offsets=[0,s.length];break}default:Object.setPrototypeOf(this,Yn[o.typeId]),this._offsets=bn(i)}this.data=i,this.type=o,this.stride=te(o),this.numChildren=null!==(r=null===(n=o.children)||void 0===n?void 0:n.length)&&void 0!==r?r:0,this.length=this._offsets[this._offsets.length-1]}get byteLength(){return-1===this._byteLength&&(this._byteLength=this.data.reduce(((t,e)=>t+e.byteLength),0)),this._byteLength}get nullCount(){return-1===this._nullCount&&(this._nullCount=yn(this.data)),this._nullCount}get ArrayType(){return this.type.ArrayType}get[Symbol.toStringTag](){return"".concat(this.VectorName,"<").concat(this.type[Symbol.toStringTag],">")}get VectorName(){return"".concat(l[this.type.typeId],"Vector")}isValid(t){return!1}get(t){return null}set(t,e){}indexOf(t,e){return-1}includes(t,e){return this.indexOf(t,e)>0}getByteLength(t){return 0}[Symbol.iterator](){return Ln.visit(this)}concat(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return new $n(this.data.concat(e.flatMap((t=>t.data)).flat(Number.POSITIVE_INFINITY)))}slice(t,e){return new $n(un(this,t,e,((t,e,n)=>{let{data:r,_offsets:i}=t;return mn(r,i,e,n)})))}toJSON(){return[...this]}toArray(){const{type:t,data:e,length:n,stride:r,ArrayType:i}=this;switch(t.typeId){case l.Int:case l.Float:case l.Decimal:case l.Time:case l.Timestamp:switch(e.length){case 0:return new i;case 1:return e[0].values.subarray(0,n*r);default:return e.reduce(((t,e)=>{let{values:n,length:i}=e;return t.array.set(n.subarray(0,i*r),t.offset),t.offset+=i*r,t}),{array:new i(n*r),offset:0}).array}}return[...this]}toString(){return"[".concat([...this].join(","),"]")}getChild(t){var e;return this.getChildAt(null===(e=this.type.children)||void 0===e?void 0:e.findIndex((e=>e.name===t)))}getChildAt(t){return t>-1&&t<this.numChildren?new $n(this.data.map((e=>{let{children:n}=e;return n[t]}))):null}get isMemoized(){return!!Bt.isDictionary(this.type)&&this.data[0].dictionary.isMemoized}memoize(){if(Bt.isDictionary(this.type)){const t=new Qn(this.data[0].dictionary),e=this.data.map((e=>{const n=e.clone();return n.dictionary=t,n}));return new $n(e)}return new Qn(this)}unmemoize(){if(Bt.isDictionary(this.type)&&this.isMemoized){const t=this.data[0].dictionary.unmemoize(),e=this.data.map((e=>{const n=e.clone();return n.dictionary=t,n}));return new $n(e)}return this}}Wn=Symbol.toStringTag,$n[Wn]=(t=>{t.type=Bt.prototype,t.data=[],t.length=0,t.stride=1,t.numChildren=0,t._nullCount=-1,t._byteLength=-1,t._offsets=new Uint32Array([0]),t[Symbol.isConcatSpreadable]=!0;const e=Object.keys(l).map((t=>l[t])).filter((t=>"number"===typeof t&&t!==l.NONE));for(const n of e){const e=en.getVisitFnByTypeId(n),r=Oe.getVisitFnByTypeId(n),i=Nn.getVisitFnByTypeId(n),o=jn.getVisitFnByTypeId(n);Hn[n]={get:e,set:r,indexOf:i,byteLength:o},Yn[n]=Object.create(t,{isValid:{value:wn(vn)},get:{value:wn(en.getVisitFnByTypeId(n))},set:{value:_n(Oe.getVisitFnByTypeId(n))},indexOf:{value:Sn(Nn.getVisitFnByTypeId(n))},getByteLength:{value:wn(jn.getVisitFnByTypeId(n))}})}return"Vector"})($n.prototype);class Qn extends $n{constructor(t){super(t.data);const e=this.get,n=this.set,r=this.slice,i=new Array(this.length);Object.defineProperty(this,"get",{value(t){const n=i[t];if(void 0!==n)return n;const r=e.call(this,t);return i[t]=r,r}}),Object.defineProperty(this,"set",{value(t,e){n.call(this,t,e),i[t]=e}}),Object.defineProperty(this,"slice",{value:(t,e)=>new Qn(r.call(this,t,e))}),Object.defineProperty(this,"isMemoized",{value:!0}),Object.defineProperty(this,"unmemoize",{value:()=>new $n(this.data)}),Object.defineProperty(this,"memoize",{value:()=>this})}}class Kn{constructor(t,e,n,r,i){let o,s=arguments.length>5&&void 0!==arguments[5]?arguments[5]:[],a=arguments.length>6?arguments[6]:void 0;this.type=t,this.children=s,this.dictionary=a,this.offset=Math.floor(Math.max(e||0,0)),this.length=Math.floor(Math.max(n||0,0)),this._nullCount=Math.floor(Math.max(r||0,-1)),i instanceof Kn?(this.stride=i.stride,this.values=i.values,this.typeIds=i.typeIds,this.nullBitmap=i.nullBitmap,this.valueOffsets=i.valueOffsets):(this.stride=te(t),i&&((o=i[0])&&(this.valueOffsets=o),(o=i[1])&&(this.values=o),(o=i[2])&&(this.nullBitmap=o),(o=i[3])&&(this.typeIds=o))),this.nullable=0!==this._nullCount&&this.nullBitmap&&this.nullBitmap.byteLength>0}get typeId(){return this.type.typeId}get ArrayType(){return this.type.ArrayType}get buffers(){return[this.valueOffsets,this.values,this.nullBitmap,this.typeIds]}get byteLength(){let t=0;const{valueOffsets:e,values:n,nullBitmap:r,typeIds:i}=this;return e&&(t+=e.byteLength),n&&(t+=n.byteLength),r&&(t+=r.byteLength),i&&(t+=i.byteLength),this.children.reduce(((t,e)=>t+e.byteLength),t)}get nullCount(){let t,e=this._nullCount;return e<=-1&&(t=this.nullBitmap)&&(this._nullCount=e=this.length-On(t,this.offset,this.offset+this.length)),e}getValid(t){if(this.nullable&&this.nullCount>0){const e=this.offset+t;return 0!==(this.nullBitmap[e>>3]&1<<e%8)}return!0}setValid(t,e){if(!this.nullable)return e;if(!this.nullBitmap||this.nullBitmap.byteLength<=t>>3){const{nullBitmap:t}=this._changeLengthAndBackfillNullBitmap(this.length);Object.assign(this,{nullBitmap:t,_nullCount:0})}const{nullBitmap:n,offset:r}=this,i=r+t>>3,o=(r+t)%8,s=n[i]>>o&1;return e?0===s&&(n[i]|=1<<o,this._nullCount=this.nullCount+1):1===s&&(n[i]&=~(1<<o),this._nullCount=this.nullCount-1),e}clone(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.type,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.offset,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.length,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:this._nullCount,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this,o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:this.children;return new Kn(t,e,n,r,i,o,this.dictionary)}slice(t,e){const{stride:n,typeId:r,children:i}=this,o=+(0===this._nullCount)-1,s=16===r?n:1,a=this._sliceBuffers(t,e,n,r);return this.clone(this.type,this.offset+t,e,o,a,0===i.length||this.valueOffsets?i:this._sliceChildren(i,s*t,s*e))}_changeLengthAndBackfillNullBitmap(t){if(this.typeId===l.Null)return this.clone(this.type,0,t,0);const{length:e,nullCount:n}=this,r=new Uint8Array((t+63&-64)>>3).fill(255,0,e>>3);r[e>>3]=(1<<e-(-8&e))-1,n>0&&r.set(xn(this.offset,e,this.nullBitmap),0);const i=this.buffers;return i[u.VALIDITY]=r,this.clone(this.type,0,t,n+(t-e),i)}_sliceBuffers(t,e,n,r){let i;const{buffers:o}=this;return(i=o[u.TYPE])&&(o[u.TYPE]=i.subarray(t,t+e)),(i=o[u.OFFSET])&&(o[u.OFFSET]=i.subarray(t,t+e+1))||(i=o[u.DATA])&&(o[u.DATA]=6===r?i:i.subarray(n*t,n*(t+e))),o}_sliceChildren(t,e,n){return t.map((t=>t.slice(e,n)))}}Kn.prototype.children=Object.freeze([]);class qn extends ee{visit(t){return this.getVisitFn(t.type).call(this,t)}visitNull(t){const{type:e,offset:n=0,length:r=0}=t;return new Kn(e,n,r,0)}visitBool(t){const{type:e,offset:n=0}=t,r=J(t.nullBitmap),i=q(e.ArrayType,t.data),{length:o=i.length>>3,nullCount:s=(t.nullBitmap?-1:0)}=t;return new Kn(e,n,o,s,[void 0,i,r])}visitInt(t){const{type:e,offset:n=0}=t,r=J(t.nullBitmap),i=q(e.ArrayType,t.data),{length:o=i.length,nullCount:s=(t.nullBitmap?-1:0)}=t;return new Kn(e,n,o,s,[void 0,i,r])}visitFloat(t){const{type:e,offset:n=0}=t,r=J(t.nullBitmap),i=q(e.ArrayType,t.data),{length:o=i.length,nullCount:s=(t.nullBitmap?-1:0)}=t;return new Kn(e,n,o,s,[void 0,i,r])}visitUtf8(t){const{type:e,offset:n=0}=t,r=J(t.data),i=J(t.nullBitmap),o=G(t.valueOffsets),{length:s=o.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new Kn(e,n,s,a,[o,r,i])}visitBinary(t){const{type:e,offset:n=0}=t,r=J(t.data),i=J(t.nullBitmap),o=G(t.valueOffsets),{length:s=o.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new Kn(e,n,s,a,[o,r,i])}visitFixedSizeBinary(t){const{type:e,offset:n=0}=t,r=J(t.nullBitmap),i=q(e.ArrayType,t.data),{length:o=i.length/te(e),nullCount:s=(t.nullBitmap?-1:0)}=t;return new Kn(e,n,o,s,[void 0,i,r])}visitDate(t){const{type:e,offset:n=0}=t,r=J(t.nullBitmap),i=q(e.ArrayType,t.data),{length:o=i.length/te(e),nullCount:s=(t.nullBitmap?-1:0)}=t;return new Kn(e,n,o,s,[void 0,i,r])}visitTimestamp(t){const{type:e,offset:n=0}=t,r=J(t.nullBitmap),i=q(e.ArrayType,t.data),{length:o=i.length/te(e),nullCount:s=(t.nullBitmap?-1:0)}=t;return new Kn(e,n,o,s,[void 0,i,r])}visitTime(t){const{type:e,offset:n=0}=t,r=J(t.nullBitmap),i=q(e.ArrayType,t.data),{length:o=i.length/te(e),nullCount:s=(t.nullBitmap?-1:0)}=t;return new Kn(e,n,o,s,[void 0,i,r])}visitDecimal(t){const{type:e,offset:n=0}=t,r=J(t.nullBitmap),i=q(e.ArrayType,t.data),{length:o=i.length/te(e),nullCount:s=(t.nullBitmap?-1:0)}=t;return new Kn(e,n,o,s,[void 0,i,r])}visitList(t){const{type:e,offset:n=0,child:r}=t,i=J(t.nullBitmap),o=G(t.valueOffsets),{length:s=o.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new Kn(e,n,s,a,[o,void 0,i],[r])}visitStruct(t){const{type:e,offset:n=0,children:r=[]}=t,i=J(t.nullBitmap),{length:o=r.reduce(((t,e)=>{let{length:n}=e;return Math.max(t,n)}),0),nullCount:s=(t.nullBitmap?-1:0)}=t;return new Kn(e,n,o,s,[void 0,void 0,i],r)}visitUnion(t){const{type:e,offset:n=0,children:r=[]}=t,i=J(t.nullBitmap),o=q(e.ArrayType,t.typeIds),{length:s=o.length,nullCount:a=(t.nullBitmap?-1:0)}=t;if(Bt.isSparseUnion(e))return new Kn(e,n,s,a,[void 0,void 0,i,o],r);const l=G(t.valueOffsets);return new Kn(e,n,s,a,[l,void 0,i,o],r)}visitDictionary(t){const{type:e,offset:n=0}=t,r=J(t.nullBitmap),i=q(e.indices.ArrayType,t.data),{dictionary:o=new $n([(new qn).visit({type:e.dictionary})])}=t,{length:s=i.length,nullCount:a=(t.nullBitmap?-1:0)}=t;return new Kn(e,n,s,a,[void 0,i,r],[],o)}visitInterval(t){const{type:e,offset:n=0}=t,r=J(t.nullBitmap),i=q(e.ArrayType,t.data),{length:o=i.length/te(e),nullCount:s=(t.nullBitmap?-1:0)}=t;return new Kn(e,n,o,s,[void 0,i,r])}visitFixedSizeList(t){const{type:e,offset:n=0,child:r=(new qn).visit({type:e.valueType})}=t,i=J(t.nullBitmap),{length:o=r.length/te(e),nullCount:s=(t.nullBitmap?-1:0)}=t;return new Kn(e,n,o,s,[void 0,void 0,i],[r])}visitMap(t){const{type:e,offset:n=0,child:r=(new qn).visit({type:e.childType})}=t,i=J(t.nullBitmap),o=G(t.valueOffsets),{length:s=o.length-1,nullCount:a=(t.nullBitmap?-1:0)}=t;return new Kn(e,n,s,a,[o,void 0,i],[r])}}function Gn(t){return(new qn).visit(t)}class Jn{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0;this.fields=t||[],this.metadata=e||new Map,n||(n=tr(t)),this.dictionaries=n}get[Symbol.toStringTag](){return"Schema"}get names(){return this.fields.map((t=>t.name))}toString(){return"Schema<{ ".concat(this.fields.map(((t,e)=>"".concat(e,": ").concat(t))).join(", ")," }>")}select(t){const e=new Set(t),n=this.fields.filter((t=>e.has(t.name)));return new Jn(n,this.metadata)}selectAt(t){const e=t.map((t=>this.fields[t])).filter(Boolean);return new Jn(e,this.metadata)}assign(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];const r=e[0]instanceof Jn?e[0]:Array.isArray(e[0])?new Jn(e[0]):new Jn(e),i=[...this.fields],o=Zn(Zn(new Map,this.metadata),r.metadata),s=r.fields.filter((t=>{const e=i.findIndex((e=>e.name===t.name));return!~e||(i[e]=t.clone({metadata:Zn(Zn(new Map,i[e].metadata),t.metadata)}))&&!1})),a=tr(s,new Map);return new Jn([...i,...s],o,new Map([...this.dictionaries,...a]))}}Jn.prototype.fields=null,Jn.prototype.metadata=null,Jn.prototype.dictionaries=null;class Xn{constructor(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3?arguments[3]:void 0;this.name=t,this.type=e,this.nullable=n,this.metadata=r||new Map}static new(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];let[r,i,o,s]=e;return e[0]&&"object"===typeof e[0]&&(({name:r}=e[0]),void 0===i&&(i=e[0].type),void 0===o&&(o=e[0].nullable),void 0===s&&(s=e[0].metadata)),new Xn("".concat(r),i,o,s)}get typeId(){return this.type.typeId}get[Symbol.toStringTag](){return"Field"}toString(){return"".concat(this.name,": ").concat(this.type)}clone(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];let[r,i,o,s]=e;return e[0]&&"object"===typeof e[0]?({name:r=this.name,type:i=this.type,nullable:o=this.nullable,metadata:s=this.metadata}=e[0]):[r=this.name,i=this.type,o=this.nullable,s=this.metadata]=e,Xn.new(r,i,o,s)}}function Zn(t,e){return new Map([...t||new Map,...e||new Map])}function tr(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Map;for(let n=-1,r=t.length;++n<r;){const r=t[n].type;if(Bt.isDictionary(r))if(e.has(r.id)){if(e.get(r.id)!==r.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}else e.set(r.id,r.dictionary);r.children&&r.children.length>0&&tr(r.children,e)}return e}Xn.prototype.type=null,Xn.prototype.name=null,Xn.prototype.nullable=null,Xn.prototype.metadata=null;class er extends ee{compareSchemas(t,e){return t===e||e instanceof t.constructor&&this.compareManyFields(t.fields,e.fields)}compareManyFields(t,e){return t===e||Array.isArray(t)&&Array.isArray(e)&&t.length===e.length&&t.every(((t,n)=>this.compareFields(t,e[n])))}compareFields(t,e){return t===e||e instanceof t.constructor&&t.name===e.name&&t.nullable===e.nullable&&this.visit(t.type,e.type)}}function nr(t,e){return e instanceof t.constructor}function rr(t,e){return t===e||nr(t,e)}function ir(t,e){return t===e||nr(t,e)&&t.bitWidth===e.bitWidth&&t.isSigned===e.isSigned}function or(t,e){return t===e||nr(t,e)&&t.precision===e.precision}function sr(t,e){return t===e||nr(t,e)&&t.unit===e.unit}function ar(t,e){return t===e||nr(t,e)&&t.unit===e.unit&&t.timezone===e.timezone}function lr(t,e){return t===e||nr(t,e)&&t.unit===e.unit&&t.bitWidth===e.bitWidth}function ur(t,e){return t===e||nr(t,e)&&t.mode===e.mode&&t.typeIds.every(((t,n)=>t===e.typeIds[n]))&&dr.compareManyFields(t.children,e.children)}function cr(t,e){return t===e||nr(t,e)&&t.unit===e.unit}er.prototype.visitNull=rr,er.prototype.visitBool=rr,er.prototype.visitInt=ir,er.prototype.visitInt8=ir,er.prototype.visitInt16=ir,er.prototype.visitInt32=ir,er.prototype.visitInt64=ir,er.prototype.visitUint8=ir,er.prototype.visitUint16=ir,er.prototype.visitUint32=ir,er.prototype.visitUint64=ir,er.prototype.visitFloat=or,er.prototype.visitFloat16=or,er.prototype.visitFloat32=or,er.prototype.visitFloat64=or,er.prototype.visitUtf8=rr,er.prototype.visitBinary=rr,er.prototype.visitFixedSizeBinary=function(t,e){return t===e||nr(t,e)&&t.byteWidth===e.byteWidth},er.prototype.visitDate=sr,er.prototype.visitDateDay=sr,er.prototype.visitDateMillisecond=sr,er.prototype.visitTimestamp=ar,er.prototype.visitTimestampSecond=ar,er.prototype.visitTimestampMillisecond=ar,er.prototype.visitTimestampMicrosecond=ar,er.prototype.visitTimestampNanosecond=ar,er.prototype.visitTime=lr,er.prototype.visitTimeSecond=lr,er.prototype.visitTimeMillisecond=lr,er.prototype.visitTimeMicrosecond=lr,er.prototype.visitTimeNanosecond=lr,er.prototype.visitDecimal=rr,er.prototype.visitList=function(t,e){return t===e||nr(t,e)&&t.children.length===e.children.length&&dr.compareManyFields(t.children,e.children)},er.prototype.visitStruct=function(t,e){return t===e||nr(t,e)&&t.children.length===e.children.length&&dr.compareManyFields(t.children,e.children)},er.prototype.visitUnion=ur,er.prototype.visitDenseUnion=ur,er.prototype.visitSparseUnion=ur,er.prototype.visitDictionary=function(t,e){return t===e||nr(t,e)&&t.id===e.id&&t.isOrdered===e.isOrdered&&dr.visit(t.indices,e.indices)&&dr.visit(t.dictionary,e.dictionary)},er.prototype.visitInterval=cr,er.prototype.visitIntervalDayTime=cr,er.prototype.visitIntervalYearMonth=cr,er.prototype.visitFixedSizeList=function(t,e){return t===e||nr(t,e)&&t.listSize===e.listSize&&t.children.length===e.children.length&&dr.compareManyFields(t.children,e.children)},er.prototype.visitMap=function(t,e){return t===e||nr(t,e)&&t.keysSorted===e.keysSorted&&t.children.length===e.children.length&&dr.compareManyFields(t.children,e.children)};const dr=new er;function fr(t,e){return dr.compareSchemas(t,e)}var hr,pr;class yr{constructor(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];switch(e.length){case 2:if([this.schema]=e,!(this.schema instanceof Jn))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");if([,this.data=Gn({nullCount:0,type:new $t(this.schema.fields),children:this.schema.fields.map((t=>Gn({type:t.type,nullCount:0})))})]=e,!(this.data instanceof Kn))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");[this.schema,this.data]=br(this.schema,this.data.children);break;case 1:{const[t]=e,{fields:n,children:r,length:i}=Object.keys(t).reduce(((e,n,r)=>(e.children[r]=t[n],e.length=Math.max(e.length,t[n].length),e.fields[r]=Xn.new({name:n,type:t[n].type,nullable:!0}),e)),{length:0,fields:new Array,children:new Array}),o=new Jn(n),s=Gn({type:new $t(n),length:i,children:r,nullCount:0});[this.schema,this.data]=br(o,s.children,i);break}default:throw new TypeError("RecordBatch constructor expects an Object mapping names to child Data, or a [Schema, Data] pair.")}}get dictionaries(){return this._dictionaries||(this._dictionaries=mr(this.schema.fields,this.data.children))}get numCols(){return this.schema.fields.length}get numRows(){return this.data.length}get nullCount(){return this.data.nullCount}isValid(t){return this.data.getValid(t)}get(t){return en.visit(this.data,t)}set(t,e){return Oe.visit(this.data,t,e)}indexOf(t,e){return Nn.visit(this.data,t,e)}getByteLength(t){return jn.visit(this.data,t)}[Symbol.iterator](){return Ln.visit(new $n([this.data]))}toArray(){return[...this]}concat(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return new _r(this.schema,[this,...e])}slice(t,e){const[n]=new $n([this.data]).slice(t,e).data;return new yr(this.schema,n)}getChild(t){var e;return this.getChildAt(null===(e=this.schema.fields)||void 0===e?void 0:e.findIndex((e=>e.name===t)))}getChildAt(t){return t>-1&&t<this.schema.fields.length?new $n([this.data.children[t]]):null}setChild(t,e){var n;return this.setChildAt(null===(n=this.schema.fields)||void 0===n?void 0:n.findIndex((e=>e.name===t)),e)}setChildAt(t,e){let n=this.schema,r=this.data;if(t>-1&&t<this.numCols){e||(e=new $n([Gn({type:new Nt,length:this.numRows})]));const i=n.fields.slice(),o=r.children.slice(),s=i[t].clone({type:e.type});[i[t],o[t]]=[s,e.data[0]],n=new Jn(i,new Map(this.schema.metadata)),r=Gn({type:new $t(i),children:o})}return new yr(n,r)}select(t){const e=this.schema.select(t),n=new $t(e.fields),r=[];for(const i of t){const t=this.schema.fields.findIndex((t=>t.name===i));~t&&(r[t]=this.data.children[t])}return new yr(e,Gn({type:n,length:this.numRows,children:r}))}selectAt(t){const e=this.schema.selectAt(t),n=t.map((t=>this.data.children[t])).filter(Boolean),r=Gn({type:new $t(e.fields),length:this.numRows,children:n});return new yr(e,r)}}function br(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e.reduce(((t,e)=>Math.max(t,e.length)),0);var r;const i=[...t.fields],o=[...e],s=(n+63&-64)>>3;for(const[a,l]of t.fields.entries()){const t=e[a];t&&t.length===n||(i[a]=l.clone({nullable:!0}),o[a]=null!==(r=null===t||void 0===t?void 0:t._changeLengthAndBackfillNullBitmap(n))&&void 0!==r?r:Gn({type:l.type,length:n,nullCount:n,nullBitmap:new Uint8Array(s)}))}return[t.assign(i),Gn({type:new $t(i),length:n,children:o})]}function mr(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:new Map;for(let r=-1,i=t.length;++r<i;){const i=t[r].type,o=e[r];if(Bt.isDictionary(i))if(n.has(i.id)){if(n.get(i.id)!==o.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}else o.dictionary&&n.set(i.id,o.dictionary);i.children&&i.children.length>0&&mr(i.children,o.children,n)}return n}hr=Symbol.toStringTag,yr[hr]=(t=>(t._nullCount=-1,t[Symbol.isConcatSpreadable]=!0,"RecordBatch"))(yr.prototype);class gr extends yr{constructor(t){const e=t.fields.map((t=>Gn({type:t.type})));super(t,Gn({type:new $t(t.fields),nullCount:0,children:e}))}}function vr(t,e){return function(t,e){const n=[...t.fields],r=[],i={numBatches:e.reduce(((t,e)=>Math.max(t,e.length)),0)};let o=0,s=0,a=-1;const l=e.length;let u,c=[];for(;i.numBatches-- >0;){for(s=Number.POSITIVE_INFINITY,a=-1;++a<l;)c[a]=u=e[a].shift(),s=Math.min(s,u?u.length:s);Number.isFinite(s)&&(c=wr(n,s,c,e,i),s>0&&(r[o++]=Gn({type:new $t(n),length:s,nullCount:0,children:c.slice()})))}return[t=t.assign(n),r.map((e=>new yr(t,e)))]}(t,e.map((t=>t.data.concat())))}function wr(t,e,n,r,i){var o;const s=(e+63&-64)>>3;for(let a=-1,l=r.length;++a<l;){const l=n[a],u=null===l||void 0===l?void 0:l.length;if(u>=e)u===e?n[a]=l:(n[a]=l.slice(0,e),i.numBatches=Math.max(i.numBatches,r[a].unshift(l.slice(e,u-e))));else{const r=t[a];t[a]=r.clone({nullable:!0}),n[a]=null!==(o=null===l||void 0===l?void 0:l._changeLengthAndBackfillNullBitmap(e))&&void 0!==o?o:Gn({type:r.type,length:e,nullCount:e,nullBitmap:new Uint8Array(s)})}}return n}class _r{constructor(){for(var t,e,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];if(0===r.length)return this.batches=[],this.schema=new Jn([]),this._offsets=[0],this;let o,s;r[0]instanceof Jn&&(o=r.shift()),r[r.length-1]instanceof Uint32Array&&(s=r.pop());const a=t=>{if(t){if(t instanceof yr)return[t];if(t instanceof _r)return t.batches;if(t instanceof Kn){if(t.type instanceof $t)return[new yr(new Jn(t.type.children),t)]}else{if(Array.isArray(t))return t.flatMap((t=>a(t)));if("function"===typeof t[Symbol.iterator])return[...t].flatMap((t=>a(t)));if("object"===typeof t){const e=Object.keys(t),n=e.map((e=>new $n([t[e]]))),r=new Jn(e.map(((t,e)=>new Xn(String(t),n[e].type)))),[,i]=vr(r,n);return 0===i.length?[new yr(t)]:i}}}return[]},l=r.flatMap((t=>a(t)));if(o=null!==(e=null!==o&&void 0!==o?o:null===(t=l[0])||void 0===t?void 0:t.schema)&&void 0!==e?e:new Jn([]),!(o instanceof Jn))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");for(const u of l){if(!(u instanceof yr))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");if(!fr(o,u.schema))throw new TypeError("Table and inner RecordBatch schemas must be equivalent.")}this.schema=o,this.batches=l,this._offsets=null!==s&&void 0!==s?s:bn(this.data)}get data(){return this.batches.map((t=>{let{data:e}=t;return e}))}get numCols(){return this.schema.fields.length}get numRows(){return this.data.reduce(((t,e)=>t+e.length),0)}get nullCount(){return-1===this._nullCount&&(this._nullCount=yn(this.data)),this._nullCount}isValid(t){return!1}get(t){return null}set(t,e){}indexOf(t,e){return-1}getByteLength(t){return 0}[Symbol.iterator](){return this.batches.length>0?Ln.visit(new $n(this.data)):new Array(0)[Symbol.iterator]()}toArray(){return[...this]}toString(){return"[\n  ".concat(this.toArray().join(",\n  "),"\n]")}concat(){const t=this.schema;for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];const i=this.data.concat(n.flatMap((t=>{let{data:e}=t;return e})));return new _r(t,i.map((e=>new yr(t,e))))}slice(t,e){const n=this.schema;[t,e]=un({length:this.numRows},t,e);const r=mn(this.data,this._offsets,t,e);return new _r(n,r.map((t=>new yr(n,t))))}getChild(t){return this.getChildAt(this.schema.fields.findIndex((e=>e.name===t)))}getChildAt(t){if(t>-1&&t<this.schema.fields.length){const e=this.data.map((e=>e.children[t]));if(0===e.length){const{type:n}=this.schema.fields[t],r=Gn({type:n,length:0,nullCount:0});e.push(r._changeLengthAndBackfillNullBitmap(this.numRows))}return new $n(e)}return null}setChild(t,e){var n;return this.setChildAt(null===(n=this.schema.fields)||void 0===n?void 0:n.findIndex((e=>e.name===t)),e)}setChildAt(t,e){let n=this.schema,r=[...this.batches];if(t>-1&&t<this.numCols){e||(e=new $n([Gn({type:new Nt,length:this.numRows})]));const i=n.fields.slice(),o=i[t].clone({type:e.type}),s=this.schema.fields.map(((t,e)=>this.getChildAt(e)));[i[t],s[t]]=[o,e],[n,r]=vr(n,s)}return new _r(n,r)}select(t){const e=this.schema.fields.reduce(((t,e,n)=>t.set(e.name,n)),new Map);return this.selectAt(t.map((t=>e.get(t))).filter((t=>t>-1)))}selectAt(t){const e=this.schema.selectAt(t),n=this.batches.map((e=>e.selectAt(t)));return new _r(e,n)}assign(t){const e=this.schema.fields,[n,r]=t.schema.fields.reduce(((t,n,r)=>{const[i,o]=t,s=e.findIndex((t=>t.name===n.name));return~s?o[s]=r:i.push(r),t}),[[],[]]),i=this.schema.assign(t.schema),o=[...e.map(((t,e)=>[e,r[e]])).map((e=>{let[n,r]=e;return void 0===r?this.getChildAt(n):t.getChildAt(r)})),...n.map((e=>t.getChildAt(e)))].filter(Boolean);return new _r(...vr(i,o))}}pr=Symbol.toStringTag,_r[pr]=(t=>(t.schema=null,t.batches=[],t._offsets=new Uint32Array([0]),t._nullCount=-1,t[Symbol.isConcatSpreadable]=!0,t.isValid=wn(vn),t.get=wn(en.getVisitFn(l.Struct)),t.set=_n(Oe.getVisitFn(l.Struct)),t.indexOf=Sn(Nn.getVisitFn(l.Struct)),t.getByteLength=wn(jn.getVisitFn(l.Struct)),"Table"))(_r.prototype);class Sr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}offset(){return this.bb.readInt64(this.bb_pos)}metaDataLength(){return this.bb.readInt32(this.bb_pos+8)}bodyLength(){return this.bb.readInt64(this.bb_pos+16)}static sizeOf(){return 24}static createBlock(t,e,n,r){return t.prep(8,24),t.writeInt64(r),t.pad(4),t.writeInt32(n),t.writeInt64(e),t.offset()}}const Tr=new Int32Array(2),Ir=new Float32Array(Tr.buffer),xr=new Float64Array(Tr.buffer),Er=1===new Uint16Array(new Uint8Array([1,0]).buffer)[0];class kr{constructor(t,e){this.low=0|t,this.high=0|e}static create(t,e){return 0==t&&0==e?kr.ZERO:new kr(t,e)}toFloat64(){return(this.low>>>0)+4294967296*this.high}equals(t){return this.low==t.low&&this.high==t.high}}var Or,Ar,Cr,Dr,Br;kr.ZERO=new kr(0,0),function(t){t[t.UTF8_BYTES=1]="UTF8_BYTES",t[t.UTF16_STRING=2]="UTF16_STRING"}(Or||(Or={}));class Nr{constructor(t){this.bytes_=t,this.position_=0}static allocate(t){return new Nr(new Uint8Array(t))}clear(){this.position_=0}bytes(){return this.bytes_}position(){return this.position_}setPosition(t){this.position_=t}capacity(){return this.bytes_.length}readInt8(t){return this.readUint8(t)<<24>>24}readUint8(t){return this.bytes_[t]}readInt16(t){return this.readUint16(t)<<16>>16}readUint16(t){return this.bytes_[t]|this.bytes_[t+1]<<8}readInt32(t){return this.bytes_[t]|this.bytes_[t+1]<<8|this.bytes_[t+2]<<16|this.bytes_[t+3]<<24}readUint32(t){return this.readInt32(t)>>>0}readInt64(t){return new kr(this.readInt32(t),this.readInt32(t+4))}readUint64(t){return new kr(this.readUint32(t),this.readUint32(t+4))}readFloat32(t){return Tr[0]=this.readInt32(t),Ir[0]}readFloat64(t){return Tr[Er?0:1]=this.readInt32(t),Tr[Er?1:0]=this.readInt32(t+4),xr[0]}writeInt8(t,e){this.bytes_[t]=e}writeUint8(t,e){this.bytes_[t]=e}writeInt16(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8}writeUint16(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8}writeInt32(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24}writeUint32(t,e){this.bytes_[t]=e,this.bytes_[t+1]=e>>8,this.bytes_[t+2]=e>>16,this.bytes_[t+3]=e>>24}writeInt64(t,e){this.writeInt32(t,e.low),this.writeInt32(t+4,e.high)}writeUint64(t,e){this.writeUint32(t,e.low),this.writeUint32(t+4,e.high)}writeFloat32(t,e){Ir[0]=e,this.writeInt32(t,Tr[0])}writeFloat64(t,e){xr[0]=e,this.writeInt32(t,Tr[Er?0:1]),this.writeInt32(t+4,Tr[Er?1:0])}getBufferIdentifier(){if(this.bytes_.length<this.position_+4+4)throw new Error("FlatBuffers: ByteBuffer is too short to contain an identifier.");let t="";for(let e=0;e<4;e++)t+=String.fromCharCode(this.readInt8(this.position_+4+e));return t}__offset(t,e){const n=t-this.readInt32(t);return e<this.readInt16(n)?this.readInt16(n+e):0}__union(t,e){return t.bb_pos=e+this.readInt32(e),t.bb=this,t}__string(t,e){t+=this.readInt32(t);const n=this.readInt32(t);let r="",i=0;if(t+=4,e===Or.UTF8_BYTES)return this.bytes_.subarray(t,t+n);for(;i<n;){let e;const n=this.readUint8(t+i++);if(n<192)e=n;else{const r=this.readUint8(t+i++);if(n<224)e=(31&n)<<6|63&r;else{const o=this.readUint8(t+i++);if(n<240)e=(15&n)<<12|(63&r)<<6|63&o;else{e=(7&n)<<18|(63&r)<<12|(63&o)<<6|63&this.readUint8(t+i++)}}}e<65536?r+=String.fromCharCode(e):(e-=65536,r+=String.fromCharCode(55296+(e>>10),56320+(1023&e)))}return r}__union_with_string(t,e){return"string"===typeof t?this.__string(e):this.__union(t,e)}__indirect(t){return t+this.readInt32(t)}__vector(t){return t+this.readInt32(t)+4}__vector_len(t){return this.readInt32(t+this.readInt32(t))}__has_identifier(t){if(4!=t.length)throw new Error("FlatBuffers: file identifier must be length 4");for(let e=0;e<4;e++)if(t.charCodeAt(e)!=this.readInt8(this.position()+4+e))return!1;return!0}createLong(t,e){return kr.create(t,e)}createScalarList(t,e){const n=[];for(let r=0;r<e;++r)null!==t(r)&&n.push(t(r));return n}createObjList(t,e){const n=[];for(let r=0;r<e;++r){const e=t(r);null!==e&&n.push(e.unpack())}return n}}class Mr{constructor(t){let e;this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null,e=t||1024,this.bb=Nr.allocate(e),this.space=e}clear(){this.bb.clear(),this.space=this.bb.capacity(),this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null}forceDefaults(t){this.force_defaults=t}dataBuffer(){return this.bb}asUint8Array(){return this.bb.bytes().subarray(this.bb.position(),this.bb.position()+this.offset())}prep(t,e){t>this.minalign&&(this.minalign=t);const n=1+~(this.bb.capacity()-this.space+e)&t-1;for(;this.space<n+t+e;){const t=this.bb.capacity();this.bb=Mr.growByteBuffer(this.bb),this.space+=this.bb.capacity()-t}this.pad(n)}pad(t){for(let e=0;e<t;e++)this.bb.writeInt8(--this.space,0)}writeInt8(t){this.bb.writeInt8(this.space-=1,t)}writeInt16(t){this.bb.writeInt16(this.space-=2,t)}writeInt32(t){this.bb.writeInt32(this.space-=4,t)}writeInt64(t){this.bb.writeInt64(this.space-=8,t)}writeFloat32(t){this.bb.writeFloat32(this.space-=4,t)}writeFloat64(t){this.bb.writeFloat64(this.space-=8,t)}addInt8(t){this.prep(1,0),this.writeInt8(t)}addInt16(t){this.prep(2,0),this.writeInt16(t)}addInt32(t){this.prep(4,0),this.writeInt32(t)}addInt64(t){this.prep(8,0),this.writeInt64(t)}addFloat32(t){this.prep(4,0),this.writeFloat32(t)}addFloat64(t){this.prep(8,0),this.writeFloat64(t)}addFieldInt8(t,e,n){(this.force_defaults||e!=n)&&(this.addInt8(e),this.slot(t))}addFieldInt16(t,e,n){(this.force_defaults||e!=n)&&(this.addInt16(e),this.slot(t))}addFieldInt32(t,e,n){(this.force_defaults||e!=n)&&(this.addInt32(e),this.slot(t))}addFieldInt64(t,e,n){!this.force_defaults&&e.equals(n)||(this.addInt64(e),this.slot(t))}addFieldFloat32(t,e,n){(this.force_defaults||e!=n)&&(this.addFloat32(e),this.slot(t))}addFieldFloat64(t,e,n){(this.force_defaults||e!=n)&&(this.addFloat64(e),this.slot(t))}addFieldOffset(t,e,n){(this.force_defaults||e!=n)&&(this.addOffset(e),this.slot(t))}addFieldStruct(t,e,n){e!=n&&(this.nested(e),this.slot(t))}nested(t){if(t!=this.offset())throw new Error("FlatBuffers: struct must be serialized inline.")}notNested(){if(this.isNested)throw new Error("FlatBuffers: object serialization must not be nested.")}slot(t){null!==this.vtable&&(this.vtable[t]=this.offset())}offset(){return this.bb.capacity()-this.space}static growByteBuffer(t){const e=t.capacity();if(3221225472&e)throw new Error("FlatBuffers: cannot grow buffer beyond 2 gigabytes.");const n=e<<1,r=Nr.allocate(n);return r.setPosition(n-e),r.bytes().set(t.bytes(),n-e),r}addOffset(t){this.prep(4,0),this.writeInt32(this.offset()-t+4)}startObject(t){this.notNested(),null==this.vtable&&(this.vtable=[]),this.vtable_in_use=t;for(let e=0;e<t;e++)this.vtable[e]=0;this.isNested=!0,this.object_start=this.offset()}endObject(){if(null==this.vtable||!this.isNested)throw new Error("FlatBuffers: endObject called without startObject");this.addInt32(0);const t=this.offset();let e=this.vtable_in_use-1;for(;e>=0&&0==this.vtable[e];e--);const n=e+1;for(;e>=0;e--)this.addInt16(0!=this.vtable[e]?t-this.vtable[e]:0);this.addInt16(t-this.object_start);const r=2*(n+2);this.addInt16(r);let i=0;const o=this.space;t:for(e=0;e<this.vtables.length;e++){const t=this.bb.capacity()-this.vtables[e];if(r==this.bb.readInt16(t)){for(let e=2;e<r;e+=2)if(this.bb.readInt16(o+e)!=this.bb.readInt16(t+e))continue t;i=this.vtables[e];break}}return i?(this.space=this.bb.capacity()-t,this.bb.writeInt32(this.space,i-t)):(this.vtables.push(this.offset()),this.bb.writeInt32(this.bb.capacity()-t,this.offset()-t)),this.isNested=!1,t}finish(t,e,n){const r=n?4:0;if(e){const t=e;if(this.prep(this.minalign,8+r),4!=t.length)throw new Error("FlatBuffers: file identifier must be length 4");for(let e=3;e>=0;e--)this.writeInt8(t.charCodeAt(e))}this.prep(this.minalign,4+r),this.addOffset(t),r&&this.addInt32(this.bb.capacity()-this.space),this.bb.setPosition(this.space)}finishSizePrefixed(t,e){this.finish(t,e,!0)}requiredField(t,e){const n=this.bb.capacity()-t,r=n-this.bb.readInt32(n);if(!(0!=this.bb.readInt16(r+e)))throw new Error("FlatBuffers: field "+e+" must be set")}startVector(t,e,n){this.notNested(),this.vector_num_elems=e,this.prep(4,t*e),this.prep(n,t*e)}endVector(){return this.writeInt32(this.vector_num_elems),this.offset()}createSharedString(t){if(!t)return 0;if(this.string_maps||(this.string_maps=new Map),this.string_maps.has(t))return this.string_maps.get(t);const e=this.createString(t);return this.string_maps.set(t,e),e}createString(t){if(!t)return 0;let e;if(t instanceof Uint8Array)e=t;else{e=[];let n=0;for(;n<t.length;){let r;const i=t.charCodeAt(n++);if(i<55296||i>=56320)r=i;else{r=(i<<10)+t.charCodeAt(n++)+-56613888}r<128?e.push(r):(r<2048?e.push(r>>6&31|192):(r<65536?e.push(r>>12&15|224):e.push(r>>18&7|240,r>>12&63|128),e.push(r>>6&63|128)),e.push(63&r|128))}}this.addInt8(0),this.startVector(1,e.length,1),this.bb.setPosition(this.space-=e.length);for(let n=0,r=this.space,i=this.bb.bytes();n<e.length;n++)i[r++]=e[n];return this.endVector()}createLong(t,e){return kr.create(t,e)}createObjectOffset(t){return null===t?0:"string"===typeof t?this.createString(t):t.pack(this)}createObjectOffsetList(t){const e=[];for(let n=0;n<t.length;++n){const r=t[n];if(null===r)throw new Error("FlatBuffers: Argument for createObjectOffsetList cannot contain null.");e.push(this.createObjectOffset(r))}return e}createStructOffsetList(t,e){return e(this,t.length),this.createObjectOffsetList(t),this.endVector()}}class Fr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsKeyValue(t,e){return(e||new Fr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsKeyValue(t,e){return t.setPosition(t.position()+4),(e||new Fr).__init(t.readInt32(t.position())+t.position(),t)}key(t){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}value(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}static startKeyValue(t){t.startObject(2)}static addKey(t,e){t.addFieldOffset(0,e,0)}static addValue(t,e){t.addFieldOffset(1,e,0)}static endKeyValue(t){return t.endObject()}static createKeyValue(t,e,n){return Fr.startKeyValue(t),Fr.addKey(t,e),Fr.addValue(t,n),Fr.endKeyValue(t)}}!function(t){t[t.V1=0]="V1",t[t.V2=1]="V2",t[t.V3=2]="V3",t[t.V4=3]="V4",t[t.V5=4]="V5"}(Ar||(Ar={})),function(t){t[t.Little=0]="Little",t[t.Big=1]="Big"}(Cr||(Cr={})),function(t){t[t.DenseArray=0]="DenseArray"}(Dr||(Dr={}));class Pr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsInt(t,e){return(e||new Pr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsInt(t,e){return t.setPosition(t.position()+4),(e||new Pr).__init(t.readInt32(t.position())+t.position(),t)}bitWidth(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}isSigned(){const t=this.bb.__offset(this.bb_pos,6);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}static startInt(t){t.startObject(2)}static addBitWidth(t,e){t.addFieldInt32(0,e,0)}static addIsSigned(t,e){t.addFieldInt8(1,+e,0)}static endInt(t){return t.endObject()}static createInt(t,e,n){return Pr.startInt(t),Pr.addBitWidth(t,e),Pr.addIsSigned(t,n),Pr.endInt(t)}}class Lr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDictionaryEncoding(t,e){return(e||new Lr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDictionaryEncoding(t,e){return t.setPosition(t.position()+4),(e||new Lr).__init(t.readInt32(t.position())+t.position(),t)}id(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):this.bb.createLong(0,0)}indexType(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new Pr).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}isOrdered(){const t=this.bb.__offset(this.bb_pos,8);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}dictionaryKind(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt16(this.bb_pos+t):Dr.DenseArray}static startDictionaryEncoding(t){t.startObject(4)}static addId(t,e){t.addFieldInt64(0,e,t.createLong(0,0))}static addIndexType(t,e){t.addFieldOffset(1,e,0)}static addIsOrdered(t,e){t.addFieldInt8(2,+e,0)}static addDictionaryKind(t,e){t.addFieldInt16(3,e,Dr.DenseArray)}static endDictionaryEncoding(t){return t.endObject()}}!function(t){t[t.NONE=0]="NONE",t[t.Null=1]="Null",t[t.Int=2]="Int",t[t.FloatingPoint=3]="FloatingPoint",t[t.Binary=4]="Binary",t[t.Utf8=5]="Utf8",t[t.Bool=6]="Bool",t[t.Decimal=7]="Decimal",t[t.Date=8]="Date",t[t.Time=9]="Time",t[t.Timestamp=10]="Timestamp",t[t.Interval=11]="Interval",t[t.List=12]="List",t[t.Struct_=13]="Struct_",t[t.Union=14]="Union",t[t.FixedSizeBinary=15]="FixedSizeBinary",t[t.FixedSizeList=16]="FixedSizeList",t[t.Map=17]="Map",t[t.Duration=18]="Duration",t[t.LargeBinary=19]="LargeBinary",t[t.LargeUtf8=20]="LargeUtf8",t[t.LargeList=21]="LargeList"}(Br||(Br={}));class Ur{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsField(t,e){return(e||new Ur).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsField(t,e){return t.setPosition(t.position()+4),(e||new Ur).__init(t.readInt32(t.position())+t.position(),t)}name(t){const e=this.bb.__offset(this.bb_pos,4);return e?this.bb.__string(this.bb_pos+e,t):null}nullable(){const t=this.bb.__offset(this.bb_pos,6);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}typeType(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readUint8(this.bb_pos+t):Br.NONE}type(t){const e=this.bb.__offset(this.bb_pos,10);return e?this.bb.__union(t,this.bb_pos+e):null}dictionary(t){const e=this.bb.__offset(this.bb_pos,12);return e?(t||new Lr).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}children(t,e){const n=this.bb.__offset(this.bb_pos,14);return n?(e||new Ur).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}childrenLength(){const t=this.bb.__offset(this.bb_pos,14);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const n=this.bb.__offset(this.bb_pos,16);return n?(e||new Fr).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,16);return t?this.bb.__vector_len(this.bb_pos+t):0}static startField(t){t.startObject(7)}static addName(t,e){t.addFieldOffset(0,e,0)}static addNullable(t,e){t.addFieldInt8(1,+e,0)}static addTypeType(t,e){t.addFieldInt8(2,e,Br.NONE)}static addType(t,e){t.addFieldOffset(3,e,0)}static addDictionary(t,e){t.addFieldOffset(4,e,0)}static addChildren(t,e){t.addFieldOffset(5,e,0)}static createChildrenVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startChildrenVector(t,e){t.startVector(4,e,4)}static addCustomMetadata(t,e){t.addFieldOffset(6,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endField(t){return t.endObject()}}class Rr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsSchema(t,e){return(e||new Rr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsSchema(t,e){return t.setPosition(t.position()+4),(e||new Rr).__init(t.readInt32(t.position())+t.position(),t)}endianness(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Cr.Little}fields(t,e){const n=this.bb.__offset(this.bb_pos,6);return n?(e||new Ur).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}fieldsLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const n=this.bb.__offset(this.bb_pos,8);return n?(e||new Fr).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}features(t){const e=this.bb.__offset(this.bb_pos,10);return e?this.bb.readInt64(this.bb.__vector(this.bb_pos+e)+8*t):this.bb.createLong(0,0)}featuresLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__vector_len(this.bb_pos+t):0}static startSchema(t){t.startObject(4)}static addEndianness(t,e){t.addFieldInt16(0,e,Cr.Little)}static addFields(t,e){t.addFieldOffset(1,e,0)}static createFieldsVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startFieldsVector(t,e){t.startVector(4,e,4)}static addCustomMetadata(t,e){t.addFieldOffset(2,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static addFeatures(t,e){t.addFieldOffset(3,e,0)}static createFeaturesVector(t,e){t.startVector(8,e.length,8);for(let n=e.length-1;n>=0;n--)t.addInt64(e[n]);return t.endVector()}static startFeaturesVector(t,e){t.startVector(8,e,8)}static endSchema(t){return t.endObject()}static finishSchemaBuffer(t,e){t.finish(e)}static finishSizePrefixedSchemaBuffer(t,e){t.finish(e,void 0,!0)}static createSchema(t,e,n,r,i){return Rr.startSchema(t),Rr.addEndianness(t,e),Rr.addFields(t,n),Rr.addCustomMetadata(t,r),Rr.addFeatures(t,i),Rr.endSchema(t)}}class zr{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFooter(t,e){return(e||new zr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFooter(t,e){return t.setPosition(t.position()+4),(e||new zr).__init(t.readInt32(t.position())+t.position(),t)}version(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Ar.V1}schema(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new Rr).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}dictionaries(t,e){const n=this.bb.__offset(this.bb_pos,8);return n?(e||new Sr).__init(this.bb.__vector(this.bb_pos+n)+24*t,this.bb):null}dictionariesLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}recordBatches(t,e){const n=this.bb.__offset(this.bb_pos,10);return n?(e||new Sr).__init(this.bb.__vector(this.bb_pos+n)+24*t,this.bb):null}recordBatchesLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,e){const n=this.bb.__offset(this.bb_pos,12);return n?(e||new Fr).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}static startFooter(t){t.startObject(5)}static addVersion(t,e){t.addFieldInt16(0,e,Ar.V1)}static addSchema(t,e){t.addFieldOffset(1,e,0)}static addDictionaries(t,e){t.addFieldOffset(2,e,0)}static startDictionariesVector(t,e){t.startVector(24,e,8)}static addRecordBatches(t,e){t.addFieldOffset(3,e,0)}static startRecordBatchesVector(t,e){t.startVector(24,e,8)}static addCustomMetadata(t,e){t.addFieldOffset(4,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endFooter(t){return t.endObject()}static finishFooterBuffer(t,e){t.finish(e)}static finishSizePrefixedFooterBuffer(t,e){t.finish(e,void 0,!0)}}var Vr=kr,jr=Mr,Wr=Nr;class Hr{constructor(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t.V4,r=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0;this.schema=e,this.version=n,r&&(this._recordBatches=r),i&&(this._dictionaryBatches=i)}static decode(t){t=new Wr(J(t));const e=zr.getRootAsFooter(t),n=Jn.decode(e.schema());return new Yr(n,e)}static encode(e){const n=new jr,r=Jn.encode(n,e.schema);zr.startRecordBatchesVector(n,e.numRecordBatches);for(const t of[...e.recordBatches()].slice().reverse())$r.encode(n,t);const i=n.endVector();zr.startDictionariesVector(n,e.numDictionaries);for(const t of[...e.dictionaryBatches()].slice().reverse())$r.encode(n,t);const o=n.endVector();return zr.startFooter(n),zr.addSchema(n,r),zr.addVersion(n,t.V4),zr.addRecordBatches(n,i),zr.addDictionaries(n,o),zr.finishFooterBuffer(n,zr.endFooter(n)),n.asUint8Array()}get numRecordBatches(){return this._recordBatches.length}get numDictionaries(){return this._dictionaryBatches.length}*recordBatches(){for(let t,e=-1,n=this.numRecordBatches;++e<n;)(t=this.getRecordBatch(e))&&(yield t)}*dictionaryBatches(){for(let t,e=-1,n=this.numDictionaries;++e<n;)(t=this.getDictionaryBatch(e))&&(yield t)}getRecordBatch(t){return t>=0&&t<this.numRecordBatches&&this._recordBatches[t]||null}getDictionaryBatch(t){return t>=0&&t<this.numDictionaries&&this._dictionaryBatches[t]||null}}class Yr extends Hr{constructor(t,e){super(t,e.version()),this._footer=e}get numRecordBatches(){return this._footer.recordBatchesLength()}get numDictionaries(){return this._footer.dictionariesLength()}getRecordBatch(t){if(t>=0&&t<this.numRecordBatches){const e=this._footer.recordBatches(t);if(e)return $r.decode(e)}return null}getDictionaryBatch(t){if(t>=0&&t<this.numDictionaries){const e=this._footer.dictionaries(t);if(e)return $r.decode(e)}return null}}class $r{constructor(t,e,n){this.metaDataLength=t,this.offset="number"===typeof n?n:n.low,this.bodyLength="number"===typeof e?e:e.low}static decode(t){return new $r(t.metaDataLength(),t.bodyLength(),t.offset())}static encode(t,e){const{metaDataLength:n}=e,r=new Vr(e.offset,0),i=new Vr(e.bodyLength,0);return Sr.createBlock(t,r,n,i)}}const Qr={fromIterable:t=>Kr(function*(t){let e,n,r,i,o=!1,s=[],a=0;function l(){return"peek"===r?K(s,i)[0]:([n,s,a]=K(s,i),n)}({cmd:r,size:i}=yield null);const u=(c=t,Z(Uint8Array,c))[Symbol.iterator]();var c;try{do{if(({done:e,value:n}=Number.isNaN(i-a)?u.next():u.next(i-a)),!e&&n.byteLength>0&&(s.push(n),a+=n.byteLength),e||i<=a)do{({cmd:r,size:i}=yield l())}while(i<a)}while(!e)}catch(d){(o=!0)&&"function"===typeof u.throw&&u.throw(d)}finally{!1===o&&"function"===typeof u.return&&u.return(null)}return null}(t)),fromAsyncIterable:t=>Kr(function(t){return R(this,arguments,(function*(){let e,n,r,i,o=!1,s=[],a=0;function l(){return"peek"===r?K(s,i)[0]:([n,s,a]=K(s,i),n)}({cmd:r,size:i}=yield yield U(null));const u=(c=t,tt(Uint8Array,c))[Symbol.asyncIterator]();var c;try{do{if(({done:e,value:n}=Number.isNaN(i-a)?yield U(u.next()):yield U(u.next(i-a))),!e&&n.byteLength>0&&(s.push(n),a+=n.byteLength),e||i<=a)do{({cmd:r,size:i}=yield yield U(l()))}while(i<a)}while(!e)}catch(d){(o=!0)&&"function"===typeof u.throw&&(yield U(u.throw(d)))}finally{!1===o&&"function"===typeof u.return&&(yield U(u.return(new Uint8Array(0))))}return yield U(null)}))}(t)),fromDOMStream:t=>Kr(function(t){return R(this,arguments,(function*(){let e,n,r,i=!1,o=!1,s=[],a=0;function l(){return"peek"===n?K(s,r)[0]:([e,s,a]=K(s,r),e)}({cmd:n,size:r}=yield yield U(null));const u=new qr(t);try{do{if(({done:i,value:e}=Number.isNaN(r-a)?yield U(u.read()):yield U(u.read(r-a))),!i&&e.byteLength>0&&(s.push(J(e)),a+=e.byteLength),i||r<=a)do{({cmd:n,size:r}=yield yield U(l()))}while(r<a)}while(!i)}catch(c){(o=!0)&&(yield U(u.cancel(c)))}finally{!1===o?yield U(u.cancel()):t.locked&&u.releaseLock()}return yield U(null)}))}(t)),fromNodeStream:t=>Kr(function(t){return R(this,arguments,(function*(){const e=[];let n,r,i,o="error",s=!1,a=null,l=0,u=[];function c(){return"peek"===n?K(u,r)[0]:([i,u,l]=K(u,r),i)}if(({cmd:n,size:r}=yield yield U(null)),t.isTTY)return yield yield U(new Uint8Array(0)),yield U(null);try{e[0]=Gr(t,"end"),e[1]=Gr(t,"error");do{if(e[2]=Gr(t,"readable"),[o,a]=yield U(Promise.race(e.map((t=>t[2])))),"error"===o)break;if((s="end"===o)||(Number.isFinite(r-l)?(i=J(t.read(r-l)),i.byteLength<r-l&&(i=J(t.read()))):i=J(t.read()),i.byteLength>0&&(u.push(i),l+=i.byteLength)),s||r<=l)do{({cmd:n,size:r}=yield yield U(c()))}while(r<l)}while(!s)}finally{yield U(d(e,"error"===o?a:null))}return yield U(null);function d(e,n){return i=u=null,new Promise(((r,i)=>{for(const[n,s]of e)t.off(n,s);try{const e=t.destroy;e&&e.call(t,n),n=void 0}catch(o){n=o||n}finally{null!=n?i(n):r()}}))}}))}(t)),toDOMStream(t,e){throw new Error('"toDOMStream" not available in this environment')},toNodeStream(t,e){throw new Error('"toNodeStream" not available in this environment')}},Kr=t=>(t.next(),t);class qr{constructor(t){this.source=t,this.reader=null,this.reader=this.source.getReader(),this.reader.closed.catch((()=>{}))}get closed(){return this.reader?this.reader.closed.catch((()=>{})):Promise.resolve()}releaseLock(){this.reader&&this.reader.releaseLock(),this.reader=null}cancel(t){return P(this,void 0,void 0,(function*(){const{reader:e,source:n}=this;e&&(yield e.cancel(t).catch((()=>{}))),n&&n.locked&&this.releaseLock()}))}read(t){return P(this,void 0,void 0,(function*(){if(0===t)return{done:null==this.reader,value:new Uint8Array(0)};const e=yield this.reader.read();return!e.done&&(e.value=J(e)),e}))}}const Gr=(t,e)=>{const n=t=>r([e,t]);let r;return[e,n,new Promise((i=>(r=i)&&t.once(e,n)))]};const Jr=Object.freeze({done:!0,value:void 0});class Xr{constructor(t){this._json=t}get schema(){return this._json.schema}get batches(){return this._json.batches||[]}get dictionaries(){return this._json.dictionaries||[]}}class Zr{tee(){return this._getDOMStream().tee()}pipe(t,e){return this._getNodeStream().pipe(t,e)}pipeTo(t,e){return this._getDOMStream().pipeTo(t,e)}pipeThrough(t,e){return this._getDOMStream().pipeThrough(t,e)}_getDOMStream(){return this._DOMStream||(this._DOMStream=this.toDOMStream())}_getNodeStream(){return this._nodeStream||(this._nodeStream=this.toNodeStream())}}class ti extends Zr{constructor(){super(),this._values=[],this.resolvers=[],this._closedPromise=new Promise((t=>this._closedPromiseResolve=t))}get closed(){return this._closedPromise}cancel(t){return P(this,void 0,void 0,(function*(){yield this.return(t)}))}write(t){this._ensureOpen()&&(this.resolvers.length<=0?this._values.push(t):this.resolvers.shift().resolve({done:!1,value:t}))}abort(t){this._closedPromiseResolve&&(this.resolvers.length<=0?this._error={error:t}:this.resolvers.shift().reject({done:!0,value:t}))}close(){if(this._closedPromiseResolve){const{resolvers:t}=this;for(;t.length>0;)t.shift().resolve(Jr);this._closedPromiseResolve(),this._closedPromiseResolve=void 0}}[Symbol.asyncIterator](){return this}toDOMStream(t){return Qr.toDOMStream(this._closedPromiseResolve||this._error?this:this._values,t)}toNodeStream(t){return Qr.toNodeStream(this._closedPromiseResolve||this._error?this:this._values,t)}throw(t){return P(this,void 0,void 0,(function*(){return yield this.abort(t),Jr}))}return(t){return P(this,void 0,void 0,(function*(){return yield this.close(),Jr}))}read(t){return P(this,void 0,void 0,(function*(){return(yield this.next(t,"read")).value}))}peek(t){return P(this,void 0,void 0,(function*(){return(yield this.next(t,"peek")).value}))}next(){return this._values.length>0?Promise.resolve({done:!1,value:this._values.shift()}):this._error?Promise.reject({done:!0,value:this._error.error}):this._closedPromiseResolve?new Promise(((t,e)=>{this.resolvers.push({resolve:t,reject:e})})):Promise.resolve(Jr)}_ensureOpen(){if(this._closedPromiseResolve)return!0;throw new Error("AsyncQueue is closed")}}class ei extends ti{write(t){if((t=J(t)).byteLength>0)return super.write(t)}toString(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]?W(this.toUint8Array(!0)):this.toUint8Array(!1).then(W)}toUint8Array(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]?K(this._values)[0]:(()=>P(this,void 0,void 0,(function*(){var t,e;const n=[];let r=0;try{for(var i,o=V(this);!(i=yield o.next()).done;){const t=i.value;n.push(t),r+=t.byteLength}}catch(s){t={error:s}}finally{try{i&&!i.done&&(e=o.return)&&(yield e.call(o))}finally{if(t)throw t.error}}return K(n,r)[0]})))()}}class ni{constructor(t){t&&(this.source=new ii(Qr.fromIterable(t)))}[Symbol.iterator](){return this}next(t){return this.source.next(t)}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}peek(t){return this.source.peek(t)}read(t){return this.source.read(t)}}class ri{constructor(t){t instanceof ri?this.source=t.source:t instanceof ei?this.source=new oi(Qr.fromAsyncIterable(t)):N(t)?this.source=new oi(Qr.fromNodeStream(t)):B(t)?this.source=new oi(Qr.fromDOMStream(t)):C(t)?this.source=new oi(Qr.fromDOMStream(t.body)):x(t)?this.source=new oi(Qr.fromIterable(t)):(I(t)||E(t))&&(this.source=new oi(Qr.fromAsyncIterable(t)))}[Symbol.asyncIterator](){return this}next(t){return this.source.next(t)}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}get closed(){return this.source.closed}cancel(t){return this.source.cancel(t)}peek(t){return this.source.peek(t)}read(t){return this.source.read(t)}}class ii{constructor(t){this.source=t}cancel(t){this.return(t)}peek(t){return this.next(t,"peek").value}read(t){return this.next(t,"read").value}next(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"read";return this.source.next({cmd:e,size:t})}throw(t){return Object.create(this.source.throw&&this.source.throw(t)||Jr)}return(t){return Object.create(this.source.return&&this.source.return(t)||Jr)}}class oi{constructor(t){this.source=t,this._closedPromise=new Promise((t=>this._closedPromiseResolve=t))}cancel(t){return P(this,void 0,void 0,(function*(){yield this.return(t)}))}get closed(){return this._closedPromise}read(t){return P(this,void 0,void 0,(function*(){return(yield this.next(t,"read")).value}))}peek(t){return P(this,void 0,void 0,(function*(){return(yield this.next(t,"peek")).value}))}next(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"read";return P(this,void 0,void 0,(function*(){return yield this.source.next({cmd:e,size:t})}))}throw(t){return P(this,void 0,void 0,(function*(){const e=this.source.throw&&(yield this.source.throw(t))||Jr;return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(e)}))}return(t){return P(this,void 0,void 0,(function*(){const e=this.source.return&&(yield this.source.return(t))||Jr;return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(e)}))}}class si extends ni{constructor(t,e){super(),this.position=0,this.buffer=J(t),this.size="undefined"===typeof e?this.buffer.byteLength:e}readInt32(t){const{buffer:e,byteOffset:n}=this.readAt(t,4);return new DataView(e,n).getInt32(0,!0)}seek(t){return this.position=Math.min(t,this.size),t<this.size}read(t){const{buffer:e,size:n,position:r}=this;return e&&r<n?("number"!==typeof t&&(t=Number.POSITIVE_INFINITY),this.position=Math.min(n,r+Math.min(n-r,t)),e.subarray(r,this.position)):null}readAt(t,e){const n=this.buffer,r=Math.min(this.size,t+e);return n?n.subarray(t,r):new Uint8Array(e)}close(){this.buffer&&(this.buffer=null)}throw(t){return this.close(),{done:!0,value:t}}return(t){return this.close(),{done:!0,value:t}}}class ai extends ri{constructor(t,e){super(),this.position=0,this._handle=t,"number"===typeof e?this.size=e:this._pending=(()=>P(this,void 0,void 0,(function*(){this.size=(yield t.stat()).size,delete this._pending})))()}readInt32(t){return P(this,void 0,void 0,(function*(){const{buffer:e,byteOffset:n}=yield this.readAt(t,4);return new DataView(e,n).getInt32(0,!0)}))}seek(t){return P(this,void 0,void 0,(function*(){return this._pending&&(yield this._pending),this.position=Math.min(t,this.size),t<this.size}))}read(t){return P(this,void 0,void 0,(function*(){this._pending&&(yield this._pending);const{_handle:e,size:n,position:r}=this;if(e&&r<n){"number"!==typeof t&&(t=Number.POSITIVE_INFINITY);let i=r,o=0,s=0;const a=Math.min(n,i+Math.min(n-i,t)),l=new Uint8Array(Math.max(0,(this.position=a)-i));for(;(i+=s)<a&&(o+=s)<l.byteLength;)({bytesRead:s}=yield e.read(l,o,l.byteLength-o,i));return l}return null}))}readAt(t,e){return P(this,void 0,void 0,(function*(){this._pending&&(yield this._pending);const{_handle:n,size:r}=this;if(n&&t+e<r){const i=Math.min(r,t+e),o=new Uint8Array(i-t);return(yield n.read(o,0,e,t)).buffer}return new Uint8Array(e)}))}close(){return P(this,void 0,void 0,(function*(){const t=this._handle;this._handle=null,t&&(yield t.close())}))}throw(t){return P(this,void 0,void 0,(function*(){return yield this.close(),{done:!0,value:t}}))}return(t){return P(this,void 0,void 0,(function*(){return yield this.close(),{done:!0,value:t}}))}}function li(t){return t<0&&(t=4294967295+t+1),"0x".concat(t.toString(16))}const ui=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8];class ci{constructor(t){this.buffer=t}high(){return this.buffer[1]}low(){return this.buffer[0]}_times(t){const e=new Uint32Array([this.buffer[1]>>>16,65535&this.buffer[1],this.buffer[0]>>>16,65535&this.buffer[0]]),n=new Uint32Array([t.buffer[1]>>>16,65535&t.buffer[1],t.buffer[0]>>>16,65535&t.buffer[0]]);let r=e[3]*n[3];this.buffer[0]=65535&r;let i=r>>>16;return r=e[2]*n[3],i+=r,r=e[3]*n[2]>>>0,i+=r,this.buffer[0]+=i<<16,this.buffer[1]=i>>>0<r?65536:0,this.buffer[1]+=i>>>16,this.buffer[1]+=e[1]*n[3]+e[2]*n[2]+e[3]*n[1],this.buffer[1]+=e[0]*n[3]+e[1]*n[2]+e[2]*n[1]+e[3]*n[0]<<16,this}_plus(t){const e=this.buffer[0]+t.buffer[0]>>>0;this.buffer[1]+=t.buffer[1],e<this.buffer[0]>>>0&&++this.buffer[1],this.buffer[0]=e}lessThan(t){return this.buffer[1]<t.buffer[1]||this.buffer[1]===t.buffer[1]&&this.buffer[0]<t.buffer[0]}equals(t){return this.buffer[1]===t.buffer[1]&&this.buffer[0]==t.buffer[0]}greaterThan(t){return t.lessThan(this)}hex(){return"".concat(li(this.buffer[1])," ").concat(li(this.buffer[0]))}}class di extends ci{times(t){return this._times(t),this}plus(t){return this._plus(t),this}static from(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);return di.fromString("string"===typeof t?t:t.toString(),e)}static fromNumber(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);return di.fromString(t.toString(),e)}static fromString(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);const n=t.length,r=new di(e);for(let i=0;i<n;){const e=8<n-i?8:n-i,o=new di(new Uint32Array([Number.parseInt(t.slice(i,i+e),10),0])),s=new di(new Uint32Array([ui[e],0]));r.times(s),r.plus(o),i+=e}return r}static convertArray(t){const e=new Uint32Array(2*t.length);for(let n=-1,r=t.length;++n<r;)di.from(t[n],new Uint32Array(e.buffer,e.byteOffset+2*n*4,2));return e}static multiply(t,e){return new di(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new di(new Uint32Array(t.buffer)).plus(e)}}class fi extends ci{negate(){return this.buffer[0]=1+~this.buffer[0],this.buffer[1]=~this.buffer[1],0==this.buffer[0]&&++this.buffer[1],this}times(t){return this._times(t),this}plus(t){return this._plus(t),this}lessThan(t){const e=this.buffer[1]|0,n=t.buffer[1]|0;return e<n||e===n&&this.buffer[0]<t.buffer[0]}static from(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);return fi.fromString("string"===typeof t?t:t.toString(),e)}static fromNumber(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);return fi.fromString(t.toString(),e)}static fromString(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(2);const n=t.startsWith("-"),r=t.length,i=new fi(e);for(let o=n?1:0;o<r;){const e=8<r-o?8:r-o,n=new fi(new Uint32Array([Number.parseInt(t.slice(o,o+e),10),0])),s=new fi(new Uint32Array([ui[e],0]));i.times(s),i.plus(n),o+=e}return n?i.negate():i}static convertArray(t){const e=new Uint32Array(2*t.length);for(let n=-1,r=t.length;++n<r;)fi.from(t[n],new Uint32Array(e.buffer,e.byteOffset+2*n*4,2));return e}static multiply(t,e){return new fi(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new fi(new Uint32Array(t.buffer)).plus(e)}}class hi{constructor(t){this.buffer=t}high(){return new fi(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2))}low(){return new fi(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset,2))}negate(){return this.buffer[0]=1+~this.buffer[0],this.buffer[1]=~this.buffer[1],this.buffer[2]=~this.buffer[2],this.buffer[3]=~this.buffer[3],0==this.buffer[0]&&++this.buffer[1],0==this.buffer[1]&&++this.buffer[2],0==this.buffer[2]&&++this.buffer[3],this}times(t){const e=new di(new Uint32Array([this.buffer[3],0])),n=new di(new Uint32Array([this.buffer[2],0])),r=new di(new Uint32Array([this.buffer[1],0])),i=new di(new Uint32Array([this.buffer[0],0])),o=new di(new Uint32Array([t.buffer[3],0])),s=new di(new Uint32Array([t.buffer[2],0])),a=new di(new Uint32Array([t.buffer[1],0])),l=new di(new Uint32Array([t.buffer[0],0]));let u=di.multiply(i,l);this.buffer[0]=u.low();const c=new di(new Uint32Array([u.high(),0]));u=di.multiply(r,l),c.plus(u),u=di.multiply(i,a),c.plus(u),this.buffer[1]=c.low(),this.buffer[3]=c.lessThan(u)?1:0,this.buffer[2]=c.high();return new di(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2)).plus(di.multiply(n,l)).plus(di.multiply(r,a)).plus(di.multiply(i,s)),this.buffer[3]+=di.multiply(e,l).plus(di.multiply(n,a)).plus(di.multiply(r,s)).plus(di.multiply(i,o)).low(),this}plus(t){const e=new Uint32Array(4);return e[3]=this.buffer[3]+t.buffer[3]>>>0,e[2]=this.buffer[2]+t.buffer[2]>>>0,e[1]=this.buffer[1]+t.buffer[1]>>>0,e[0]=this.buffer[0]+t.buffer[0]>>>0,e[0]<this.buffer[0]>>>0&&++e[1],e[1]<this.buffer[1]>>>0&&++e[2],e[2]<this.buffer[2]>>>0&&++e[3],this.buffer[3]=e[3],this.buffer[2]=e[2],this.buffer[1]=e[1],this.buffer[0]=e[0],this}hex(){return"".concat(li(this.buffer[3])," ").concat(li(this.buffer[2])," ").concat(li(this.buffer[1])," ").concat(li(this.buffer[0]))}static multiply(t,e){return new hi(new Uint32Array(t.buffer)).times(e)}static add(t,e){return new hi(new Uint32Array(t.buffer)).plus(e)}static from(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(4);return hi.fromString("string"===typeof t?t:t.toString(),e)}static fromNumber(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(4);return hi.fromString(t.toString(),e)}static fromString(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint32Array(4);const n=t.startsWith("-"),r=t.length,i=new hi(e);for(let o=n?1:0;o<r;){const e=8<r-o?8:r-o,n=new hi(new Uint32Array([Number.parseInt(t.slice(o,o+e),10),0,0,0])),s=new hi(new Uint32Array([ui[e],0,0,0]));i.times(s),i.plus(n),o+=e}return n?i.negate():i}static convertArray(t){const e=new Uint32Array(4*t.length);for(let n=-1,r=t.length;++n<r;)hi.from(t[n],new Uint32Array(e.buffer,e.byteOffset+16*n,4));return e}}class pi extends ee{constructor(t,e,n,r){super(),this.nodesIndex=-1,this.buffersIndex=-1,this.bytes=t,this.nodes=e,this.buffers=n,this.dictionaries=r}visit(t){return super.visit(t instanceof Xn?t.type:t)}visitNull(t){let{length:e}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e})}visitBool(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitInt(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitFloat(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitUtf8(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitBinary(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitFixedSizeBinary(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitDate(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitTimestamp(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitTime(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitDecimal(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitList(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),valueOffsets:this.readOffsets(t),child:this.visit(t.children[0])})}visitStruct(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),children:this.visitMany(t.children)})}visitUnion(t){return t.mode===e.Sparse?this.visitSparseUnion(t):this.visitDenseUnion(t)}visitDenseUnion(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),typeIds:this.readTypeIds(t),valueOffsets:this.readOffsets(t),children:this.visitMany(t.children)})}visitSparseUnion(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),typeIds:this.readTypeIds(t),children:this.visitMany(t.children)})}visitDictionary(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t.indices),dictionary:this.readDictionary(t)})}visitInterval(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),data:this.readData(t)})}visitFixedSizeList(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),child:this.visit(t.children[0])})}visitMap(t){let{length:e,nullCount:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextFieldNode();return Gn({type:t,length:e,nullCount:n,nullBitmap:this.readNullBitmap(t,n),valueOffsets:this.readOffsets(t),child:this.visit(t.children[0])})}nextFieldNode(){return this.nodes[++this.nodesIndex]}nextBufferRange(){return this.buffers[++this.buffersIndex]}readNullBitmap(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.nextBufferRange();return e>0&&this.readData(t,n)||new Uint8Array(0)}readOffsets(t,e){return this.readData(t,e)}readTypeIds(t,e){return this.readData(t,e)}readData(t){let{length:e,offset:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextBufferRange();return this.bytes.subarray(n,n+e)}readDictionary(t){return this.dictionaries.get(t.id)}}class yi extends pi{constructor(t,e,n,r){super(new Uint8Array(0),e,n,r),this.sources=t}readNullBitmap(t,e){let{offset:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.nextBufferRange();return e<=0?new Uint8Array(0):En(this.sources[n])}readOffsets(t){let{offset:e}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextBufferRange();return q(Uint8Array,q(Int32Array,this.sources[e]))}readTypeIds(t){let{offset:e}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextBufferRange();return q(Uint8Array,q(t.ArrayType,this.sources[e]))}readData(t){let{offset:e}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.nextBufferRange();const{sources:n}=this;return Bt.isTimestamp(t)||(Bt.isInt(t)||Bt.isTime(t))&&64===t.bitWidth||Bt.isDate(t)&&t.unit===i.MILLISECOND?q(Uint8Array,fi.convertArray(n[e])):Bt.isDecimal(t)?q(Uint8Array,hi.convertArray(n[e])):Bt.isBinary(t)||Bt.isFixedSizeBinary(t)?function(t){const e=t.join(""),n=new Uint8Array(e.length/2);for(let r=0;r<e.length;r+=2)n[r>>1]=Number.parseInt(e.slice(r,r+2),16);return n}(n[e]):Bt.isBool(t)?En(n[e]):Bt.isUtf8(t)?Y(n[e].join("")):q(Uint8Array,q(t.ArrayType,n[e].map((t=>+t))))}}var bi,mi,gi,vi,wi,_i,Si,Ti;!function(t){t[t.BUFFER=0]="BUFFER"}(bi||(bi={})),function(t){t[t.LZ4_FRAME=0]="LZ4_FRAME",t[t.ZSTD=1]="ZSTD"}(mi||(mi={}));class Ii{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBodyCompression(t,e){return(e||new Ii).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsBodyCompression(t,e){return t.setPosition(t.position()+4),(e||new Ii).__init(t.readInt32(t.position())+t.position(),t)}codec(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt8(this.bb_pos+t):mi.LZ4_FRAME}method(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt8(this.bb_pos+t):bi.BUFFER}static startBodyCompression(t){t.startObject(2)}static addCodec(t,e){t.addFieldInt8(0,e,mi.LZ4_FRAME)}static addMethod(t,e){t.addFieldInt8(1,e,bi.BUFFER)}static endBodyCompression(t){return t.endObject()}static createBodyCompression(t,e,n){return Ii.startBodyCompression(t),Ii.addCodec(t,e),Ii.addMethod(t,n),Ii.endBodyCompression(t)}}class xi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}offset(){return this.bb.readInt64(this.bb_pos)}length(){return this.bb.readInt64(this.bb_pos+8)}static sizeOf(){return 16}static createBuffer(t,e,n){return t.prep(8,16),t.writeInt64(n),t.writeInt64(e),t.offset()}}class Ei{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}length(){return this.bb.readInt64(this.bb_pos)}nullCount(){return this.bb.readInt64(this.bb_pos+8)}static sizeOf(){return 16}static createFieldNode(t,e,n){return t.prep(8,16),t.writeInt64(n),t.writeInt64(e),t.offset()}}class ki{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsRecordBatch(t,e){return(e||new ki).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsRecordBatch(t,e){return t.setPosition(t.position()+4),(e||new ki).__init(t.readInt32(t.position())+t.position(),t)}length(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):this.bb.createLong(0,0)}nodes(t,e){const n=this.bb.__offset(this.bb_pos,6);return n?(e||new Ei).__init(this.bb.__vector(this.bb_pos+n)+16*t,this.bb):null}nodesLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}buffers(t,e){const n=this.bb.__offset(this.bb_pos,8);return n?(e||new xi).__init(this.bb.__vector(this.bb_pos+n)+16*t,this.bb):null}buffersLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}compression(t){const e=this.bb.__offset(this.bb_pos,10);return e?(t||new Ii).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}static startRecordBatch(t){t.startObject(4)}static addLength(t,e){t.addFieldInt64(0,e,t.createLong(0,0))}static addNodes(t,e){t.addFieldOffset(1,e,0)}static startNodesVector(t,e){t.startVector(16,e,8)}static addBuffers(t,e){t.addFieldOffset(2,e,0)}static startBuffersVector(t,e){t.startVector(16,e,8)}static addCompression(t,e){t.addFieldOffset(3,e,0)}static endRecordBatch(t){return t.endObject()}}class Oi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDictionaryBatch(t,e){return(e||new Oi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDictionaryBatch(t,e){return t.setPosition(t.position()+4),(e||new Oi).__init(t.readInt32(t.position())+t.position(),t)}id(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):this.bb.createLong(0,0)}data(t){const e=this.bb.__offset(this.bb_pos,6);return e?(t||new ki).__init(this.bb.__indirect(this.bb_pos+e),this.bb):null}isDelta(){const t=this.bb.__offset(this.bb_pos,8);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}static startDictionaryBatch(t){t.startObject(3)}static addId(t,e){t.addFieldInt64(0,e,t.createLong(0,0))}static addData(t,e){t.addFieldOffset(1,e,0)}static addIsDelta(t,e){t.addFieldInt8(2,+e,0)}static endDictionaryBatch(t){return t.endObject()}}!function(t){t[t.HALF=0]="HALF",t[t.SINGLE=1]="SINGLE",t[t.DOUBLE=2]="DOUBLE"}(gi||(gi={}));class Ai{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFloatingPoint(t,e){return(e||new Ai).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFloatingPoint(t,e){return t.setPosition(t.position()+4),(e||new Ai).__init(t.readInt32(t.position())+t.position(),t)}precision(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):gi.HALF}static startFloatingPoint(t){t.startObject(1)}static addPrecision(t,e){t.addFieldInt16(0,e,gi.HALF)}static endFloatingPoint(t){return t.endObject()}static createFloatingPoint(t,e){return Ai.startFloatingPoint(t),Ai.addPrecision(t,e),Ai.endFloatingPoint(t)}}class Ci{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDecimal(t,e){return(e||new Ci).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDecimal(t,e){return t.setPosition(t.position()+4),(e||new Ci).__init(t.readInt32(t.position())+t.position(),t)}precision(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}scale(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb_pos+t):0}bitWidth(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readInt32(this.bb_pos+t):128}static startDecimal(t){t.startObject(3)}static addPrecision(t,e){t.addFieldInt32(0,e,0)}static addScale(t,e){t.addFieldInt32(1,e,0)}static addBitWidth(t,e){t.addFieldInt32(2,e,128)}static endDecimal(t){return t.endObject()}static createDecimal(t,e,n,r){return Ci.startDecimal(t),Ci.addPrecision(t,e),Ci.addScale(t,n),Ci.addBitWidth(t,r),Ci.endDecimal(t)}}!function(t){t[t.DAY=0]="DAY",t[t.MILLISECOND=1]="MILLISECOND"}(vi||(vi={}));class Di{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsDate(t,e){return(e||new Di).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDate(t,e){return t.setPosition(t.position()+4),(e||new Di).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):vi.MILLISECOND}static startDate(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,vi.MILLISECOND)}static endDate(t){return t.endObject()}static createDate(t,e){return Di.startDate(t),Di.addUnit(t,e),Di.endDate(t)}}!function(t){t[t.SECOND=0]="SECOND",t[t.MILLISECOND=1]="MILLISECOND",t[t.MICROSECOND=2]="MICROSECOND",t[t.NANOSECOND=3]="NANOSECOND"}(wi||(wi={}));class Bi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsTime(t,e){return(e||new Bi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTime(t,e){return t.setPosition(t.position()+4),(e||new Bi).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):wi.MILLISECOND}bitWidth(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb_pos+t):32}static startTime(t){t.startObject(2)}static addUnit(t,e){t.addFieldInt16(0,e,wi.MILLISECOND)}static addBitWidth(t,e){t.addFieldInt32(1,e,32)}static endTime(t){return t.endObject()}static createTime(t,e,n){return Bi.startTime(t),Bi.addUnit(t,e),Bi.addBitWidth(t,n),Bi.endTime(t)}}class Ni{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsTimestamp(t,e){return(e||new Ni).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTimestamp(t,e){return t.setPosition(t.position()+4),(e||new Ni).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):wi.SECOND}timezone(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.__string(this.bb_pos+e,t):null}static startTimestamp(t){t.startObject(2)}static addUnit(t,e){t.addFieldInt16(0,e,wi.SECOND)}static addTimezone(t,e){t.addFieldOffset(1,e,0)}static endTimestamp(t){return t.endObject()}static createTimestamp(t,e,n){return Ni.startTimestamp(t),Ni.addUnit(t,e),Ni.addTimezone(t,n),Ni.endTimestamp(t)}}!function(t){t[t.YEAR_MONTH=0]="YEAR_MONTH",t[t.DAY_TIME=1]="DAY_TIME",t[t.MONTH_DAY_NANO=2]="MONTH_DAY_NANO"}(_i||(_i={}));class Mi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsInterval(t,e){return(e||new Mi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsInterval(t,e){return t.setPosition(t.position()+4),(e||new Mi).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):_i.YEAR_MONTH}static startInterval(t){t.startObject(1)}static addUnit(t,e){t.addFieldInt16(0,e,_i.YEAR_MONTH)}static endInterval(t){return t.endObject()}static createInterval(t,e){return Mi.startInterval(t),Mi.addUnit(t,e),Mi.endInterval(t)}}!function(t){t[t.Sparse=0]="Sparse",t[t.Dense=1]="Dense"}(Si||(Si={}));class Fi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsUnion(t,e){return(e||new Fi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsUnion(t,e){return t.setPosition(t.position()+4),(e||new Fi).__init(t.readInt32(t.position())+t.position(),t)}mode(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Si.Sparse}typeIds(t){const e=this.bb.__offset(this.bb_pos,6);return e?this.bb.readInt32(this.bb.__vector(this.bb_pos+e)+4*t):0}typeIdsLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}typeIdsArray(){const t=this.bb.__offset(this.bb_pos,6);return t?new Int32Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+t),this.bb.__vector_len(this.bb_pos+t)):null}static startUnion(t){t.startObject(2)}static addMode(t,e){t.addFieldInt16(0,e,Si.Sparse)}static addTypeIds(t,e){t.addFieldOffset(1,e,0)}static createTypeIdsVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addInt32(e[n]);return t.endVector()}static startTypeIdsVector(t,e){t.startVector(4,e,4)}static endUnion(t){return t.endObject()}static createUnion(t,e,n){return Fi.startUnion(t),Fi.addMode(t,e),Fi.addTypeIds(t,n),Fi.endUnion(t)}}class Pi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFixedSizeBinary(t,e){return(e||new Pi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFixedSizeBinary(t,e){return t.setPosition(t.position()+4),(e||new Pi).__init(t.readInt32(t.position())+t.position(),t)}byteWidth(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}static startFixedSizeBinary(t){t.startObject(1)}static addByteWidth(t,e){t.addFieldInt32(0,e,0)}static endFixedSizeBinary(t){return t.endObject()}static createFixedSizeBinary(t,e){return Pi.startFixedSizeBinary(t),Pi.addByteWidth(t,e),Pi.endFixedSizeBinary(t)}}class Li{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsFixedSizeList(t,e){return(e||new Li).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFixedSizeList(t,e){return t.setPosition(t.position()+4),(e||new Li).__init(t.readInt32(t.position())+t.position(),t)}listSize(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}static startFixedSizeList(t){t.startObject(1)}static addListSize(t,e){t.addFieldInt32(0,e,0)}static endFixedSizeList(t){return t.endObject()}static createFixedSizeList(t,e){return Li.startFixedSizeList(t),Li.addListSize(t,e),Li.endFixedSizeList(t)}}class Ui{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsMap(t,e){return(e||new Ui).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsMap(t,e){return t.setPosition(t.position()+4),(e||new Ui).__init(t.readInt32(t.position())+t.position(),t)}keysSorted(){const t=this.bb.__offset(this.bb_pos,4);return!!t&&!!this.bb.readInt8(this.bb_pos+t)}static startMap(t){t.startObject(1)}static addKeysSorted(t,e){t.addFieldInt8(0,+e,0)}static endMap(t){return t.endObject()}static createMap(t,e){return Ui.startMap(t),Ui.addKeysSorted(t,e),Ui.endMap(t)}}!function(t){t[t.NONE=0]="NONE",t[t.Schema=1]="Schema",t[t.DictionaryBatch=2]="DictionaryBatch",t[t.RecordBatch=3]="RecordBatch",t[t.Tensor=4]="Tensor",t[t.SparseTensor=5]="SparseTensor"}(Ti||(Ti={}));class Ri{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsMessage(t,e){return(e||new Ri).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsMessage(t,e){return t.setPosition(t.position()+4),(e||new Ri).__init(t.readInt32(t.position())+t.position(),t)}version(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):Ar.V1}headerType(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readUint8(this.bb_pos+t):Ti.NONE}header(t){const e=this.bb.__offset(this.bb_pos,8);return e?this.bb.__union(t,this.bb_pos+e):null}bodyLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt64(this.bb_pos+t):this.bb.createLong(0,0)}customMetadata(t,e){const n=this.bb.__offset(this.bb_pos,12);return n?(e||new Fr).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+n)+4*t),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}static startMessage(t){t.startObject(5)}static addVersion(t,e){t.addFieldInt16(0,e,Ar.V1)}static addHeaderType(t,e){t.addFieldInt8(1,e,Ti.NONE)}static addHeader(t,e){t.addFieldOffset(2,e,0)}static addBodyLength(t,e){t.addFieldInt64(3,e,t.createLong(0,0))}static addCustomMetadata(t,e){t.addFieldOffset(4,e,0)}static createCustomMetadataVector(t,e){t.startVector(4,e.length,4);for(let n=e.length-1;n>=0;n--)t.addOffset(e[n]);return t.endVector()}static startCustomMetadataVector(t,e){t.startVector(4,e,4)}static endMessage(t){return t.endObject()}static finishMessageBuffer(t,e){t.finish(e)}static finishSizePrefixedMessageBuffer(t,e){t.finish(e,void 0,!0)}static createMessage(t,e,n,r,i,o){return Ri.startMessage(t),Ri.addVersion(t,e),Ri.addHeaderType(t,n),Ri.addHeader(t,r),Ri.addBodyLength(t,i),Ri.addCustomMetadata(t,o),Ri.endMessage(t)}}class zi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsNull(t,e){return(e||new zi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsNull(t,e){return t.setPosition(t.position()+4),(e||new zi).__init(t.readInt32(t.position())+t.position(),t)}static startNull(t){t.startObject(0)}static endNull(t){return t.endObject()}static createNull(t){return zi.startNull(t),zi.endNull(t)}}class Vi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBinary(t,e){return(e||new Vi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsBinary(t,e){return t.setPosition(t.position()+4),(e||new Vi).__init(t.readInt32(t.position())+t.position(),t)}static startBinary(t){t.startObject(0)}static endBinary(t){return t.endObject()}static createBinary(t){return Vi.startBinary(t),Vi.endBinary(t)}}class ji{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsBool(t,e){return(e||new ji).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsBool(t,e){return t.setPosition(t.position()+4),(e||new ji).__init(t.readInt32(t.position())+t.position(),t)}static startBool(t){t.startObject(0)}static endBool(t){return t.endObject()}static createBool(t){return ji.startBool(t),ji.endBool(t)}}class Wi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsUtf8(t,e){return(e||new Wi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsUtf8(t,e){return t.setPosition(t.position()+4),(e||new Wi).__init(t.readInt32(t.position())+t.position(),t)}static startUtf8(t){t.startObject(0)}static endUtf8(t){return t.endObject()}static createUtf8(t){return Wi.startUtf8(t),Wi.endUtf8(t)}}class Hi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsList(t,e){return(e||new Hi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsList(t,e){return t.setPosition(t.position()+4),(e||new Hi).__init(t.readInt32(t.position())+t.position(),t)}static startList(t){t.startObject(0)}static endList(t){return t.endObject()}static createList(t){return Hi.startList(t),Hi.endList(t)}}class Yi{constructor(){this.bb=null,this.bb_pos=0}__init(t,e){return this.bb_pos=t,this.bb=e,this}static getRootAsStruct_(t,e){return(e||new Yi).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsStruct_(t,e){return t.setPosition(t.position()+4),(e||new Yi).__init(t.readInt32(t.position())+t.position(),t)}static startStruct_(t){t.startObject(0)}static endStruct_(t){return t.endObject()}static createStruct_(t){return Yi.startStruct_(t),Yi.endStruct_(t)}}var $i=kr;const Qi=new class extends ee{visit(t,e){return null==t||null==e?void 0:super.visit(t,e)}visitNull(t,e){return zi.startNull(e),zi.endNull(e)}visitInt(t,e){return Pr.startInt(e),Pr.addBitWidth(e,t.bitWidth),Pr.addIsSigned(e,t.isSigned),Pr.endInt(e)}visitFloat(t,e){return Ai.startFloatingPoint(e),Ai.addPrecision(e,t.precision),Ai.endFloatingPoint(e)}visitBinary(t,e){return Vi.startBinary(e),Vi.endBinary(e)}visitBool(t,e){return ji.startBool(e),ji.endBool(e)}visitUtf8(t,e){return Wi.startUtf8(e),Wi.endUtf8(e)}visitDecimal(t,e){return Ci.startDecimal(e),Ci.addScale(e,t.scale),Ci.addPrecision(e,t.precision),Ci.addBitWidth(e,t.bitWidth),Ci.endDecimal(e)}visitDate(t,e){return Di.startDate(e),Di.addUnit(e,t.unit),Di.endDate(e)}visitTime(t,e){return Bi.startTime(e),Bi.addUnit(e,t.unit),Bi.addBitWidth(e,t.bitWidth),Bi.endTime(e)}visitTimestamp(t,e){const n=t.timezone&&e.createString(t.timezone)||void 0;return Ni.startTimestamp(e),Ni.addUnit(e,t.unit),void 0!==n&&Ni.addTimezone(e,n),Ni.endTimestamp(e)}visitInterval(t,e){return Mi.startInterval(e),Mi.addUnit(e,t.unit),Mi.endInterval(e)}visitList(t,e){return Hi.startList(e),Hi.endList(e)}visitStruct(t,e){return Yi.startStruct_(e),Yi.endStruct_(e)}visitUnion(t,e){Fi.startTypeIdsVector(e,t.typeIds.length);const n=Fi.createTypeIdsVector(e,t.typeIds);return Fi.startUnion(e),Fi.addMode(e,t.mode),Fi.addTypeIds(e,n),Fi.endUnion(e)}visitDictionary(t,e){const n=this.visit(t.indices,e);return Lr.startDictionaryEncoding(e),Lr.addId(e,new $i(t.id,0)),Lr.addIsOrdered(e,t.isOrdered),void 0!==n&&Lr.addIndexType(e,n),Lr.endDictionaryEncoding(e)}visitFixedSizeBinary(t,e){return Pi.startFixedSizeBinary(e),Pi.addByteWidth(e,t.byteWidth),Pi.endFixedSizeBinary(e)}visitFixedSizeList(t,e){return Li.startFixedSizeList(e),Li.addListSize(e,t.listSize),Li.endFixedSizeList(e)}visitMap(t,e){return Ui.startMap(e),Ui.addKeysSorted(e,t.keysSorted),Ui.endMap(e)}};function Ki(t){return new oo(t.count,Gi(t.columns),Ji(t.columns))}function qi(t,e){return(t.children||[]).filter(Boolean).map((t=>Xn.fromJSON(t,e)))}function Gi(t){return(t||[]).reduce(((t,e)=>{return[...t,new lo(e.count,(n=e.VALIDITY,(n||[]).reduce(((t,e)=>t+ +(0===e)),0))),...Gi(e.children)];var n}),[])}function Ji(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];for(let n=-1,r=(t||[]).length;++n<r;){const r=t[n];r.VALIDITY&&e.push(new ao(e.length,r.VALIDITY.length)),r.TYPE&&e.push(new ao(e.length,r.TYPE.length)),r.OFFSET&&e.push(new ao(e.length,r.OFFSET.length)),r.DATA&&e.push(new ao(e.length,r.DATA.length)),e=Ji(r.children,e)}return e}function Xi(t){return new Map(Object.entries(t||{}))}function Zi(t){return new Mt(t.isSigned,t.bitWidth)}function to(t,n){const a=t.type.name;switch(a){case"NONE":case"null":return new Nt;case"binary":return new Lt;case"utf8":return new Ut;case"bool":return new Rt;case"list":return new Yt((n||[])[0]);case"struct":case"struct_":return new $t(n||[])}switch(a){case"int":{const e=t.type;return new Mt(e.isSigned,e.bitWidth)}case"floatingpoint":{const e=t.type;return new Pt(r[e.precision])}case"decimal":{const e=t.type;return new zt(e.scale,e.precision,e.bitWidth)}case"date":{const e=t.type;return new Vt(i[e.unit])}case"time":{const e=t.type;return new jt(o[e.unit],e.bitWidth)}case"timestamp":{const e=t.type;return new Wt(o[e.unit],e.timezone)}case"interval":{const e=t.type;return new Ht(s[e.unit])}case"union":{const r=t.type;return new Qt(e[r.mode],r.typeIds||[],n||[])}case"fixedsizebinary":{const e=t.type;return new Kt(e.byteWidth)}case"fixedsizelist":{const e=t.type;return new qt(e.listSize,(n||[])[0])}case"map":{const e=t.type;return new Gt((n||[])[0],e.keysSorted)}}throw new Error('Unrecognized type: "'.concat(a,'"'))}var eo=kr,no=Mr,ro=Nr;class io{constructor(t,e,n,r){this._version=e,this._headerType=n,this.body=new Uint8Array(0),r&&(this._createHeader=()=>r),this._bodyLength="number"===typeof t?t:t.low}static fromJSON(e,n){const r=new io(0,t.V4,n);return r._createHeader=function(t,e){return()=>{switch(e){case a.Schema:return Jn.fromJSON(t);case a.RecordBatch:return oo.fromJSON(t);case a.DictionaryBatch:return so.fromJSON(t)}throw new Error("Unrecognized Message type: { name: ".concat(a[e],", type: ").concat(e," }"))}}(e,n),r}static decode(t){t=new ro(J(t));const e=Ri.getRootAsMessage(t),n=e.bodyLength(),r=e.version(),i=e.headerType(),o=new io(n,r,i);return o._createHeader=function(t,e){return()=>{switch(e){case a.Schema:return Jn.decode(t.header(new Rr));case a.RecordBatch:return oo.decode(t.header(new ki),t.version());case a.DictionaryBatch:return so.decode(t.header(new Oi),t.version())}throw new Error("Unrecognized Message type: { name: ".concat(a[e],", type: ").concat(e," }"))}}(e,i),o}static encode(e){const n=new no;let r=-1;return e.isSchema()?r=Jn.encode(n,e.header()):e.isRecordBatch()?r=oo.encode(n,e.header()):e.isDictionaryBatch()&&(r=so.encode(n,e.header())),Ri.startMessage(n),Ri.addVersion(n,t.V4),Ri.addHeader(n,r),Ri.addHeaderType(n,e.headerType),Ri.addBodyLength(n,new eo(e.bodyLength,0)),Ri.finishMessageBuffer(n,Ri.endMessage(n)),n.asUint8Array()}static from(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(e instanceof Jn)return new io(0,t.V4,a.Schema,e);if(e instanceof oo)return new io(n,t.V4,a.RecordBatch,e);if(e instanceof so)return new io(n,t.V4,a.DictionaryBatch,e);throw new Error("Unrecognized Message header: ".concat(e))}get type(){return this.headerType}get version(){return this._version}get headerType(){return this._headerType}get bodyLength(){return this._bodyLength}header(){return this._createHeader()}isSchema(){return this.headerType===a.Schema}isRecordBatch(){return this.headerType===a.RecordBatch}isDictionaryBatch(){return this.headerType===a.DictionaryBatch}}class oo{constructor(t,e,n){this._nodes=e,this._buffers=n,this._length="number"===typeof t?t:t.low}get nodes(){return this._nodes}get length(){return this._length}get buffers(){return this._buffers}}class so{constructor(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this._data=t,this._isDelta=n,this._id="number"===typeof e?e:e.low}get id(){return this._id}get data(){return this._data}get isDelta(){return this._isDelta}get length(){return this.data.length}get nodes(){return this.data.nodes}get buffers(){return this.data.buffers}}class ao{constructor(t,e){this.offset="number"===typeof t?t:t.low,this.length="number"===typeof e?e:e.low}}class lo{constructor(t,e){this.length="number"===typeof t?t:t.low,this.nullCount="number"===typeof e?e:e.low}}function uo(t,e){const n=[];for(let r,i=-1,o=-1,s=t.childrenLength();++i<s;)(r=t.children(i))&&(n[++o]=Xn.decode(r,e));return n}function co(t){const e=new Map;if(t)for(let n,r,i=-1,o=Math.trunc(t.customMetadataLength());++i<o;)(n=t.customMetadata(i))&&null!=(r=n.key())&&e.set(r,n.value());return e}function fo(t){return new Mt(t.isSigned(),t.bitWidth())}function ho(t,e){const n=t.typeType();switch(n){case Br.NONE:case Br.Null:return new Nt;case Br.Binary:return new Lt;case Br.Utf8:return new Ut;case Br.Bool:return new Rt;case Br.List:return new Yt((e||[])[0]);case Br.Struct_:return new $t(e||[])}switch(n){case Br.Int:{const e=t.type(new Pr);return new Mt(e.isSigned(),e.bitWidth())}case Br.FloatingPoint:{const e=t.type(new Ai);return new Pt(e.precision())}case Br.Decimal:{const e=t.type(new Ci);return new zt(e.scale(),e.precision(),e.bitWidth())}case Br.Date:{const e=t.type(new Di);return new Vt(e.unit())}case Br.Time:{const e=t.type(new Bi);return new jt(e.unit(),e.bitWidth())}case Br.Timestamp:{const e=t.type(new Ni);return new Wt(e.unit(),e.timezone())}case Br.Interval:{const e=t.type(new Mi);return new Ht(e.unit())}case Br.Union:{const n=t.type(new Fi);return new Qt(n.mode(),n.typeIdsArray()||[],e||[])}case Br.FixedSizeBinary:{const e=t.type(new Pi);return new Kt(e.byteWidth())}case Br.FixedSizeList:{const n=t.type(new Li);return new qt(n.listSize(),(e||[])[0])}case Br.Map:{const n=t.type(new Ui);return new Gt((e||[])[0],n.keysSorted())}}throw new Error('Unrecognized type: "'.concat(Br[n],'" (').concat(n,")"))}Xn.encode=function(t,e){let n=-1,r=-1,i=-1;const o=e.type;let s=e.typeId;Bt.isDictionary(o)?(s=o.dictionary.typeId,i=Qi.visit(o,t),r=Qi.visit(o.dictionary,t)):r=Qi.visit(o,t);const a=(o.children||[]).map((e=>Xn.encode(t,e))),l=Ur.createChildrenVector(t,a),u=e.metadata&&e.metadata.size>0?Ur.createCustomMetadataVector(t,[...e.metadata].map((e=>{let[n,r]=e;const i=t.createString("".concat(n)),o=t.createString("".concat(r));return Fr.startKeyValue(t),Fr.addKey(t,i),Fr.addValue(t,o),Fr.endKeyValue(t)}))):-1;e.name&&(n=t.createString(e.name));Ur.startField(t),Ur.addType(t,r),Ur.addTypeType(t,s),Ur.addChildren(t,l),Ur.addNullable(t,!!e.nullable),-1!==n&&Ur.addName(t,n);-1!==i&&Ur.addDictionary(t,i);-1!==u&&Ur.addCustomMetadata(t,u);return Ur.endField(t)},Xn.decode=function(t,e){let n,r,i,o,s,a;e&&(a=t.dictionary())?e.has(n=a.id().low)?(o=(o=a.indexType())?fo(o):new Ft,s=new Zt(e.get(n),o,n,a.isOrdered()),r=new Xn(t.name(),s,t.nullable(),co(t))):(o=(o=a.indexType())?fo(o):new Ft,e.set(n,i=ho(t,uo(t,e))),s=new Zt(i,o,n,a.isOrdered()),r=new Xn(t.name(),s,t.nullable(),co(t))):(i=ho(t,uo(t,e)),r=new Xn(t.name(),i,t.nullable(),co(t)));return r||null},Xn.fromJSON=function(t,e){let n,r,i,o,s,a;return e&&(o=t.dictionary)?e.has(n=o.id)?(r=(r=o.indexType)?Zi(r):new Ft,a=new Zt(e.get(n),r,n,o.isOrdered),i=new Xn(t.name,a,t.nullable,Xi(t.customMetadata))):(r=(r=o.indexType)?Zi(r):new Ft,e.set(n,s=to(t,qi(t,e))),a=new Zt(s,r,n,o.isOrdered),i=new Xn(t.name,a,t.nullable,Xi(t.customMetadata))):(s=to(t,qi(t,e)),i=new Xn(t.name,s,t.nullable,Xi(t.customMetadata))),i||null},Jn.encode=function(t,e){const n=e.fields.map((e=>Xn.encode(t,e)));Rr.startFieldsVector(t,n.length);const r=Rr.createFieldsVector(t,n),i=e.metadata&&e.metadata.size>0?Rr.createCustomMetadataVector(t,[...e.metadata].map((e=>{let[n,r]=e;const i=t.createString("".concat(n)),o=t.createString("".concat(r));return Fr.startKeyValue(t),Fr.addKey(t,i),Fr.addValue(t,o),Fr.endKeyValue(t)}))):-1;Rr.startSchema(t),Rr.addFields(t,r),Rr.addEndianness(t,po?Cr.Little:Cr.Big),-1!==i&&Rr.addCustomMetadata(t,i);return Rr.endSchema(t)},Jn.decode=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Map;const n=function(t,e){const n=[];for(let r,i=-1,o=-1,s=t.fieldsLength();++i<s;)(r=t.fields(i))&&(n[++o]=Xn.decode(r,e));return n}(t,e);return new Jn(n,co(t),e)},Jn.fromJSON=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Map;return new Jn(function(t,e){return(t.fields||[]).filter(Boolean).map((t=>Xn.fromJSON(t,e)))}(t,e),Xi(t.customMetadata),e)},oo.encode=function(t,e){const n=e.nodes||[],r=e.buffers||[];ki.startNodesVector(t,n.length);for(const s of n.slice().reverse())lo.encode(t,s);const i=t.endVector();ki.startBuffersVector(t,r.length);for(const s of r.slice().reverse())ao.encode(t,s);const o=t.endVector();return ki.startRecordBatch(t),ki.addLength(t,new eo(e.length,0)),ki.addNodes(t,i),ki.addBuffers(t,o),ki.endRecordBatch(t)},oo.decode=function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t.V4;if(null!==e.compression())throw new Error("Record batch compression not implemented");return new oo(e.length(),function(t){const e=[];for(let n,r=-1,i=-1,o=t.nodesLength();++r<o;)(n=t.nodes(r))&&(e[++i]=lo.decode(n));return e}(e),function(e,n){const r=[];for(let i,o=-1,s=-1,a=e.buffersLength();++o<a;)(i=e.buffers(o))&&(n<t.V4&&(i.bb_pos+=8*(o+1)),r[++s]=ao.decode(i));return r}(e,n))},oo.fromJSON=Ki,so.encode=function(t,e){const n=oo.encode(t,e.data);return Oi.startDictionaryBatch(t),Oi.addId(t,new eo(e.id,0)),Oi.addIsDelta(t,e.isDelta),Oi.addData(t,n),Oi.endDictionaryBatch(t)},so.decode=function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t.V4;return new so(oo.decode(e.data(),n),e.id(),e.isDelta())},so.fromJSON=function(t){return new so(Ki(t.data),t.id,t.isDelta)},lo.encode=function(t,e){return Ei.createFieldNode(t,new eo(e.length,0),new eo(e.nullCount,0))},lo.decode=function(t){return new lo(t.length(),t.nullCount())},ao.encode=function(t,e){return xi.createBuffer(t,new eo(e.offset,0),new eo(e.length,0))},ao.decode=function(t){return new ao(t.offset(),t.length())};const po=(()=>{const t=new ArrayBuffer(2);return new DataView(t).setInt16(0,256,!0),256===new Int16Array(t)[0]})(),yo=t=>"Expected ".concat(a[t]," Message in stream, but was null or length 0."),bo=t=>"Header pointer of flatbuffer-encoded ".concat(a[t]," Message is null or length 0."),mo=(t,e)=>"Expected to read ".concat(t," metadata bytes, but only read ").concat(e,"."),go=(t,e)=>"Expected to read ".concat(t," bytes for message body, but only read ").concat(e,".");class vo{constructor(t){this.source=t instanceof ni?t:new ni(t)}[Symbol.iterator](){return this}next(){let t;return(t=this.readMetadataLength()).done||-1===t.value&&(t=this.readMetadataLength()).done||(t=this.readMetadata(t.value)).done?Jr:t}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}readMessage(t){let e;if((e=this.next()).done)return null;if(null!=t&&e.value.headerType!==t)throw new Error(yo(t));return e.value}readMessageBody(t){if(t<=0)return new Uint8Array(0);const e=J(this.source.read(t));if(e.byteLength<t)throw new Error(go(t,e.byteLength));return e.byteOffset%8===0&&e.byteOffset+e.byteLength<=e.buffer.byteLength?e:e.slice()}readSchema(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const e=a.Schema,n=this.readMessage(e),r=null===n||void 0===n?void 0:n.header();if(t&&!r)throw new Error(bo(e));return r}readMetadataLength(){const t=this.source.read(So),e=t&&new Nr(t),n=(null===e||void 0===e?void 0:e.readInt32(0))||0;return{done:0===n,value:n}}readMetadata(t){const e=this.source.read(t);if(!e)return Jr;if(e.byteLength<t)throw new Error(mo(t,e.byteLength));return{done:!1,value:io.decode(e)}}}class wo{constructor(t,e){this.source=t instanceof ri?t:A(t)?new ai(t,e):new ri(t)}[Symbol.asyncIterator](){return this}next(){return P(this,void 0,void 0,(function*(){let t;return(t=yield this.readMetadataLength()).done||-1===t.value&&(t=yield this.readMetadataLength()).done||(t=yield this.readMetadata(t.value)).done?Jr:t}))}throw(t){return P(this,void 0,void 0,(function*(){return yield this.source.throw(t)}))}return(t){return P(this,void 0,void 0,(function*(){return yield this.source.return(t)}))}readMessage(t){return P(this,void 0,void 0,(function*(){let e;if((e=yield this.next()).done)return null;if(null!=t&&e.value.headerType!==t)throw new Error(yo(t));return e.value}))}readMessageBody(t){return P(this,void 0,void 0,(function*(){if(t<=0)return new Uint8Array(0);const e=J(yield this.source.read(t));if(e.byteLength<t)throw new Error(go(t,e.byteLength));return e.byteOffset%8===0&&e.byteOffset+e.byteLength<=e.buffer.byteLength?e:e.slice()}))}readSchema(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return P(this,void 0,void 0,(function*(){const e=a.Schema,n=yield this.readMessage(e),r=null===n||void 0===n?void 0:n.header();if(t&&!r)throw new Error(bo(e));return r}))}readMetadataLength(){return P(this,void 0,void 0,(function*(){const t=yield this.source.read(So),e=t&&new Nr(t),n=(null===e||void 0===e?void 0:e.readInt32(0))||0;return{done:0===n,value:n}}))}readMetadata(t){return P(this,void 0,void 0,(function*(){const e=yield this.source.read(t);if(!e)return Jr;if(e.byteLength<t)throw new Error(mo(t,e.byteLength));return{done:!1,value:io.decode(e)}}))}}class _o extends vo{constructor(t){super(new Uint8Array(0)),this._schema=!1,this._body=[],this._batchIndex=0,this._dictionaryIndex=0,this._json=t instanceof Xr?t:new Xr(t)}next(){const{_json:t}=this;if(!this._schema){this._schema=!0;return{done:!1,value:io.fromJSON(t.schema,a.Schema)}}if(this._dictionaryIndex<t.dictionaries.length){const e=t.dictionaries[this._dictionaryIndex++];this._body=e.data.columns;return{done:!1,value:io.fromJSON(e,a.DictionaryBatch)}}if(this._batchIndex<t.batches.length){const e=t.batches[this._batchIndex++];this._body=e.columns;return{done:!1,value:io.fromJSON(e,a.RecordBatch)}}return this._body=[],Jr}readMessageBody(t){return function t(e){return(e||[]).reduce(((e,n)=>[...e,...n.VALIDITY&&[n.VALIDITY]||[],...n.TYPE&&[n.TYPE]||[],...n.OFFSET&&[n.OFFSET]||[],...n.DATA&&[n.DATA]||[],...t(n.children)]),[])}(this._body)}readMessage(t){let e;if((e=this.next()).done)return null;if(null!=t&&e.value.headerType!==t)throw new Error(yo(t));return e.value}readSchema(){const t=a.Schema,e=this.readMessage(t),n=null===e||void 0===e?void 0:e.header();if(!e||!n)throw new Error(bo(t));return n}}const So=4,To="ARROW1",Io=new Uint8Array(6);for(let n=0;n<6;n+=1)Io[n]=To.codePointAt(n);function xo(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;for(let n=-1,r=Io.length;++n<r;)if(Io[n]!==t[e+n])return!1;return!0}const Eo=Io.length,ko=Eo+So,Oo=2*Eo+So;class Ao extends Zr{constructor(t){super(),this._impl=t}get closed(){return this._impl.closed}get schema(){return this._impl.schema}get autoDestroy(){return this._impl.autoDestroy}get dictionaries(){return this._impl.dictionaries}get numDictionaries(){return this._impl.numDictionaries}get numRecordBatches(){return this._impl.numRecordBatches}get footer(){return this._impl.isFile()?this._impl.footer:null}isSync(){return this._impl.isSync()}isAsync(){return this._impl.isAsync()}isFile(){return this._impl.isFile()}isStream(){return this._impl.isStream()}next(){return this._impl.next()}throw(t){return this._impl.throw(t)}return(t){return this._impl.return(t)}cancel(){return this._impl.cancel()}reset(t){return this._impl.reset(t),this._DOMStream=void 0,this._nodeStream=void 0,this}open(t){const e=this._impl.open(t);return I(e)?e.then((()=>this)):this}readRecordBatch(t){return this._impl.isFile()?this._impl.readRecordBatch(t):null}[Symbol.iterator](){return this._impl[Symbol.iterator]()}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}toDOMStream(){return Qr.toDOMStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this})}toNodeStream(){return Qr.toNodeStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this},{objectMode:!0})}static throughNode(t){throw new Error('"throughNode" not available in this environment')}static throughDOM(t,e){throw new Error('"throughDOM" not available in this environment')}static from(t){return t instanceof Ao?t:k(t)?function(t){return new Co(new Ro(t))}(t):A(t)?function(t){return P(this,void 0,void 0,(function*(){const{size:e}=yield t.stat(),n=new ai(t,e);return e>=Oo&&xo(yield n.readAt(0,Eo+7&-8))?new No(new Uo(n)):new Do(new Po(n))}))}(t):I(t)?(()=>P(this,void 0,void 0,(function*(){return yield Ao.from(yield t)})))():C(t)||B(t)||N(t)||E(t)?function(t){return P(this,void 0,void 0,(function*(){const e=yield t.peek(Eo+7&-8);return e&&e.byteLength>=4?xo(e)?new Bo(new Lo(yield t.read())):new Do(new Po(t)):new Do(new Po(function(){return R(this,arguments,(function*(){}))}()))}))}(new ri(t)):function(t){const e=t.peek(Eo+7&-8);return e&&e.byteLength>=4?xo(e)?new Bo(new Lo(t.read())):new Co(new Fo(t)):new Co(new Fo(function*(){}()))}(new ni(t))}static readAll(t){return t instanceof Ao?t.isSync()?Vo(t):jo(t):k(t)||ArrayBuffer.isView(t)||x(t)||O(t)?Vo(t):jo(t)}}class Co extends Ao{constructor(t){super(t),this._impl=t}readAll(){return[...this]}[Symbol.iterator](){return this._impl[Symbol.iterator]()}[Symbol.asyncIterator](){return R(this,arguments,(function*(){yield U(yield*z(V(this[Symbol.iterator]())))}))}}class Do extends Ao{constructor(t){super(t),this._impl=t}readAll(){var t,e;return P(this,void 0,void 0,(function*(){const n=new Array;try{for(var r,i=V(this);!(r=yield i.next()).done;){const t=r.value;n.push(t)}}catch(o){t={error:o}}finally{try{r&&!r.done&&(e=i.return)&&(yield e.call(i))}finally{if(t)throw t.error}}return n}))}[Symbol.iterator](){throw new Error("AsyncRecordBatchStreamReader is not Iterable")}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}}class Bo extends Co{constructor(t){super(t),this._impl=t}}class No extends Do{constructor(t){super(t),this._impl=t}}class Mo{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Map;this.closed=!1,this.autoDestroy=!0,this._dictionaryIndex=0,this._recordBatchIndex=0,this.dictionaries=t}get numDictionaries(){return this._dictionaryIndex}get numRecordBatches(){return this._recordBatchIndex}isSync(){return!1}isAsync(){return!1}isFile(){return!1}isStream(){return!1}reset(t){return this._dictionaryIndex=0,this._recordBatchIndex=0,this.schema=t,this.dictionaries=new Map,this}_loadRecordBatch(t,e){const n=this._loadVectors(t,e,this.schema.fields),r=Gn({type:new $t(this.schema.fields),length:t.length,children:n});return new yr(this.schema,r)}_loadDictionaryBatch(t,e){const{id:n,isDelta:r}=t,{dictionaries:i,schema:o}=this,s=i.get(n);if(r||!s){const i=o.dictionaries.get(n),a=this._loadVectors(t.data,e,[i]);return(s&&r?s.concat(new $n(a)):new $n(a)).memoize()}return s.memoize()}_loadVectors(t,e,n){return new pi(e,t.nodes,t.buffers,this.dictionaries).visitMany(n)}}class Fo extends Mo{constructor(t,e){super(e),this._reader=k(t)?new _o(this._handle=t):new vo(this._handle=t)}isSync(){return!0}isStream(){return!0}[Symbol.iterator](){return this}cancel(){!this.closed&&(this.closed=!0)&&(this.reset()._reader.return(),this._reader=null,this.dictionaries=null)}open(t){return this.closed||(this.autoDestroy=zo(this,t),this.schema||(this.schema=this._reader.readSchema())||this.cancel()),this}throw(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.throw(t):Jr}return(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.return(t):Jr}next(){if(this.closed)return Jr;let t;const{_reader:e}=this;for(;t=this._readNextMessageAndValidate();)if(t.isSchema())this.reset(t.header());else{if(t.isRecordBatch()){this._recordBatchIndex++;const n=t.header(),r=e.readMessageBody(t.bodyLength);return{done:!1,value:this._loadRecordBatch(n,r)}}if(t.isDictionaryBatch()){this._dictionaryIndex++;const n=t.header(),r=e.readMessageBody(t.bodyLength),i=this._loadDictionaryBatch(n,r);this.dictionaries.set(n.id,i)}}return this.schema&&0===this._recordBatchIndex?(this._recordBatchIndex++,{done:!1,value:new gr(this.schema)}):this.return()}_readNextMessageAndValidate(t){return this._reader.readMessage(t)}}class Po extends Mo{constructor(t,e){super(e),this._reader=new wo(this._handle=t)}isAsync(){return!0}isStream(){return!0}[Symbol.asyncIterator](){return this}cancel(){return P(this,void 0,void 0,(function*(){!this.closed&&(this.closed=!0)&&(yield this.reset()._reader.return(),this._reader=null,this.dictionaries=null)}))}open(t){return P(this,void 0,void 0,(function*(){return this.closed||(this.autoDestroy=zo(this,t),this.schema||(this.schema=yield this._reader.readSchema())||(yield this.cancel())),this}))}throw(t){return P(this,void 0,void 0,(function*(){return!this.closed&&this.autoDestroy&&(this.closed=!0)?yield this.reset()._reader.throw(t):Jr}))}return(t){return P(this,void 0,void 0,(function*(){return!this.closed&&this.autoDestroy&&(this.closed=!0)?yield this.reset()._reader.return(t):Jr}))}next(){return P(this,void 0,void 0,(function*(){if(this.closed)return Jr;let t;const{_reader:e}=this;for(;t=yield this._readNextMessageAndValidate();)if(t.isSchema())yield this.reset(t.header());else{if(t.isRecordBatch()){this._recordBatchIndex++;const n=t.header(),r=yield e.readMessageBody(t.bodyLength);return{done:!1,value:this._loadRecordBatch(n,r)}}if(t.isDictionaryBatch()){this._dictionaryIndex++;const n=t.header(),r=yield e.readMessageBody(t.bodyLength),i=this._loadDictionaryBatch(n,r);this.dictionaries.set(n.id,i)}}return this.schema&&0===this._recordBatchIndex?(this._recordBatchIndex++,{done:!1,value:new gr(this.schema)}):yield this.return()}))}_readNextMessageAndValidate(t){return P(this,void 0,void 0,(function*(){return yield this._reader.readMessage(t)}))}}class Lo extends Fo{constructor(t,e){super(t instanceof si?t:new si(t),e)}get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}isSync(){return!0}isFile(){return!0}open(t){if(!this.closed&&!this._footer){this.schema=(this._footer=this._readFooter()).schema;for(const t of this._footer.dictionaryBatches())t&&this._readDictionaryBatch(this._dictionaryIndex++)}return super.open(t)}readRecordBatch(t){var e;if(this.closed)return null;this._footer||this.open();const n=null===(e=this._footer)||void 0===e?void 0:e.getRecordBatch(t);if(n&&this._handle.seek(n.offset)){const t=this._reader.readMessage(a.RecordBatch);if(null===t||void 0===t?void 0:t.isRecordBatch()){const e=t.header(),n=this._reader.readMessageBody(t.bodyLength);return this._loadRecordBatch(e,n)}}return null}_readDictionaryBatch(t){var e;const n=null===(e=this._footer)||void 0===e?void 0:e.getDictionaryBatch(t);if(n&&this._handle.seek(n.offset)){const t=this._reader.readMessage(a.DictionaryBatch);if(null===t||void 0===t?void 0:t.isDictionaryBatch()){const e=t.header(),n=this._reader.readMessageBody(t.bodyLength),r=this._loadDictionaryBatch(e,n);this.dictionaries.set(e.id,r)}}}_readFooter(){const{_handle:t}=this,e=t.size-ko,n=t.readInt32(e),r=t.readAt(e-n,n);return Hr.decode(r)}_readNextMessageAndValidate(t){var e;if(this._footer||this.open(),this._footer&&this._recordBatchIndex<this.numRecordBatches){const n=null===(e=this._footer)||void 0===e?void 0:e.getRecordBatch(this._recordBatchIndex);if(n&&this._handle.seek(n.offset))return this._reader.readMessage(t)}return null}}class Uo extends Po{constructor(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];const i="number"!==typeof n[0]?n.shift():void 0,o=n[0]instanceof Map?n.shift():void 0;super(t instanceof ai?t:new ai(t,i),o)}get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}isFile(){return!0}isAsync(){return!0}open(t){const e=Object.create(null,{open:{get:()=>super.open}});return P(this,void 0,void 0,(function*(){if(!this.closed&&!this._footer){this.schema=(this._footer=yield this._readFooter()).schema;for(const t of this._footer.dictionaryBatches())t&&(yield this._readDictionaryBatch(this._dictionaryIndex++))}return yield e.open.call(this,t)}))}readRecordBatch(t){var e;return P(this,void 0,void 0,(function*(){if(this.closed)return null;this._footer||(yield this.open());const n=null===(e=this._footer)||void 0===e?void 0:e.getRecordBatch(t);if(n&&(yield this._handle.seek(n.offset))){const t=yield this._reader.readMessage(a.RecordBatch);if(null===t||void 0===t?void 0:t.isRecordBatch()){const e=t.header(),n=yield this._reader.readMessageBody(t.bodyLength);return this._loadRecordBatch(e,n)}}return null}))}_readDictionaryBatch(t){var e;return P(this,void 0,void 0,(function*(){const n=null===(e=this._footer)||void 0===e?void 0:e.getDictionaryBatch(t);if(n&&(yield this._handle.seek(n.offset))){const t=yield this._reader.readMessage(a.DictionaryBatch);if(null===t||void 0===t?void 0:t.isDictionaryBatch()){const e=t.header(),n=yield this._reader.readMessageBody(t.bodyLength),r=this._loadDictionaryBatch(e,n);this.dictionaries.set(e.id,r)}}}))}_readFooter(){return P(this,void 0,void 0,(function*(){const{_handle:t}=this;t._pending&&(yield t._pending);const e=t.size-ko,n=yield t.readInt32(e),r=yield t.readAt(e-n,n);return Hr.decode(r)}))}_readNextMessageAndValidate(t){return P(this,void 0,void 0,(function*(){if(this._footer||(yield this.open()),this._footer&&this._recordBatchIndex<this.numRecordBatches){const e=this._footer.getRecordBatch(this._recordBatchIndex);if(e&&(yield this._handle.seek(e.offset)))return yield this._reader.readMessage(t)}return null}))}}class Ro extends Fo{constructor(t,e){super(t,e)}_loadVectors(t,e,n){return new yi(e,t.nodes,t.buffers,this.dictionaries).visitMany(n)}}function zo(t,e){return e&&"boolean"===typeof e.autoDestroy?e.autoDestroy:t.autoDestroy}function*Vo(t){const e=Ao.from(t);try{if(!e.open({autoDestroy:!1}).closed)do{yield e}while(!e.reset().open().closed)}finally{e.cancel()}}function jo(t){return R(this,arguments,(function*(){const e=yield U(Ao.from(t));try{if(!(yield U(e.open({autoDestroy:!1}))).closed)do{yield yield U(e)}while(!(yield U(e.reset().open())).closed)}finally{yield U(e.cancel())}}))}class Wo extends ee{constructor(){super(),this._byteLength=0,this._nodes=[],this._buffers=[],this._bufferRegions=[]}static assemble(){const t=e=>e.flatMap((e=>Array.isArray(e)?t(e):e instanceof yr?e.data.children:e.data)),e=new Wo;for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return e.visitMany(t(r)),e}visit(t){if(t instanceof $n)return this.visitMany(t.data),this;const{type:e}=t;if(!Bt.isDictionary(e)){const{length:n,nullCount:r}=t;if(n>2147483647)throw new RangeError("Cannot write arrays larger than 2^31 - 1 in length");Bt.isNull(e)||Ho.call(this,r<=0?new Uint8Array(0):xn(t.offset,n,t.nullBitmap)),this.nodes.push(new lo(n,r))}return super.visit(t)}visitNull(t){return this}visitDictionary(t){return this.visit(t.clone(t.type.indices))}get nodes(){return this._nodes}get buffers(){return this._buffers}get byteLength(){return this._byteLength}get bufferRegions(){return this._bufferRegions}}function Ho(t){const e=t.byteLength+7&-8;return this.buffers.push(t),this.bufferRegions.push(new ao(this._byteLength,e)),this._byteLength+=e,this}function Yo(t){return Ho.call(this,t.values.subarray(0,t.length*t.stride))}function $o(t){const{length:e,values:n,valueOffsets:r}=t,i=r[0],o=r[e],s=Math.min(o-i,n.byteLength-i);return Ho.call(this,et(-r[0],e,r)),Ho.call(this,n.subarray(i,i+s)),this}function Qo(t){const{length:e,valueOffsets:n}=t;return n&&Ho.call(this,et(n[0],e,n)),this.visit(t.children[0])}function Ko(t){return this.visitMany(t.type.children.map(((e,n)=>t.children[n])).filter(Boolean))[0]}Wo.prototype.visitBool=function(t){let e;return t.nullCount>=t.length?Ho.call(this,new Uint8Array(0)):(e=t.values)instanceof Uint8Array?Ho.call(this,xn(t.offset,t.length,e)):Ho.call(this,En(t.values))},Wo.prototype.visitInt=Yo,Wo.prototype.visitFloat=Yo,Wo.prototype.visitUtf8=$o,Wo.prototype.visitBinary=$o,Wo.prototype.visitFixedSizeBinary=Yo,Wo.prototype.visitDate=Yo,Wo.prototype.visitTimestamp=Yo,Wo.prototype.visitTime=Yo,Wo.prototype.visitDecimal=Yo,Wo.prototype.visitList=Qo,Wo.prototype.visitStruct=Ko,Wo.prototype.visitUnion=function(t){const{type:n,length:r,typeIds:i,valueOffsets:o}=t;if(Ho.call(this,i),n.mode===e.Sparse)return Ko.call(this,t);if(n.mode===e.Dense){if(t.offset<=0)return Ho.call(this,o),Ko.call(this,t);{const e=i.reduce(((t,e)=>Math.max(t,e)),i[0]),s=new Int32Array(e+1),a=new Int32Array(e+1).fill(-1),l=new Int32Array(r),u=et(-o[0],r,o);for(let t,n,o=-1;++o<r;)-1===(n=a[t=i[o]])&&(n=a[t]=u[t]),l[o]=u[o]-n,++s[t];Ho.call(this,l);for(let i,o=-1,c=n.children.length;++o<c;)if(i=t.children[o]){const t=n.typeIds[o],e=Math.min(r,s[t]);this.visit(i.slice(a[t],e))}}}return this},Wo.prototype.visitInterval=Yo,Wo.prototype.visitFixedSizeList=Qo,Wo.prototype.visitMap=Qo;class qo extends Zr{constructor(t){super(),this._position=0,this._started=!1,this._sink=new ei,this._schema=null,this._dictionaryBlocks=[],this._recordBatchBlocks=[],this._dictionaryDeltaOffsets=new Map,T(t)||(t={autoDestroy:!0,writeLegacyIpcFormat:!1}),this._autoDestroy="boolean"!==typeof t.autoDestroy||t.autoDestroy,this._writeLegacyIpcFormat="boolean"===typeof t.writeLegacyIpcFormat&&t.writeLegacyIpcFormat}static throughNode(t){throw new Error('"throughNode" not available in this environment')}static throughDOM(t,e){throw new Error('"throughDOM" not available in this environment')}toString(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this._sink.toString(t)}toUint8Array(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this._sink.toUint8Array(t)}writeAll(t){return I(t)?t.then((t=>this.writeAll(t))):E(t)?Zo(this,t):Xo(this,t)}get closed(){return this._sink.closed}[Symbol.asyncIterator](){return this._sink[Symbol.asyncIterator]()}toDOMStream(t){return this._sink.toDOMStream(t)}toNodeStream(t){return this._sink.toNodeStream(t)}close(){return this.reset()._sink.close()}abort(t){return this.reset()._sink.abort(t)}finish(){return this._autoDestroy?this.close():this.reset(this._sink,this._schema),this}reset(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this._sink,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;var n;return t===this._sink||t instanceof ei?this._sink=t:(this._sink=new ei,t&&(T(n=t)&&S(n.abort)&&S(n.getWriter)&&!D(n))?this.toDOMStream({type:"bytes"}).pipeTo(t):t&&(t=>T(t)&&S(t.end)&&S(t.write)&&_(t.writable)&&!D(t))(t)&&this.toNodeStream({objectMode:!1}).pipe(t)),this._started&&this._schema&&this._writeFooter(this._schema),this._started=!1,this._dictionaryBlocks=[],this._recordBatchBlocks=[],this._dictionaryDeltaOffsets=new Map,e&&fr(e,this._schema)||(null==e?(this._position=0,this._schema=null):(this._started=!0,this._schema=e,this._writeSchema(e))),this}write(t){let e=null;if(!this._sink)throw new Error("RecordBatchWriter is closed");if(null==t)return this.finish()&&void 0;if(t instanceof _r&&!(e=t.schema))return this.finish()&&void 0;if(t instanceof yr&&!(e=t.schema))return this.finish()&&void 0;if(e&&!fr(e,this._schema)){if(this._started&&this._autoDestroy)return this.close();this.reset(this._sink,e)}t instanceof yr?t instanceof gr||this._writeRecordBatch(t):t instanceof _r?this.writeAll(t.batches):x(t)&&this.writeAll(t)}_writeMessage(t){const e=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:8)-1,n=io.encode(t),r=n.byteLength,i=this._writeLegacyIpcFormat?4:8,o=r+i+e&~e,s=o-r-i;return t.headerType===a.RecordBatch?this._recordBatchBlocks.push(new $r(o,t.bodyLength,this._position)):t.headerType===a.DictionaryBatch&&this._dictionaryBlocks.push(new $r(o,t.bodyLength,this._position)),this._writeLegacyIpcFormat||this._write(Int32Array.of(-1)),this._write(Int32Array.of(o-i)),r>0&&this._write(n),this._writePadding(s)}_write(t){if(this._started){const e=J(t);e&&e.byteLength>0&&(this._sink.write(e),this._position+=e.byteLength)}return this}_writeSchema(t){return this._writeMessage(io.from(t))}_writeFooter(t){return this._writeLegacyIpcFormat?this._write(Int32Array.of(0)):this._write(Int32Array.of(-1,0))}_writeMagic(){return this._write(Io)}_writePadding(t){return t>0?this._write(new Uint8Array(t)):this}_writeRecordBatch(t){const{byteLength:e,nodes:n,bufferRegions:r,buffers:i}=Wo.assemble(t),o=new oo(t.numRows,n,r),s=io.from(o,e);return this._writeDictionaries(t)._writeMessage(s)._writeBodyBuffers(i)}_writeDictionaryBatch(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this._dictionaryDeltaOffsets.set(e,t.length+(this._dictionaryDeltaOffsets.get(e)||0));const{byteLength:r,nodes:i,bufferRegions:o,buffers:s}=Wo.assemble(new $n([t])),a=new oo(t.length,i,o),l=new so(a,e,n),u=io.from(l,r);return this._writeMessage(u)._writeBodyBuffers(s)}_writeBodyBuffers(t){let e,n,r;for(let i=-1,o=t.length;++i<o;)(e=t[i])&&(n=e.byteLength)>0&&(this._write(e),(r=(n+7&-8)-n)>0&&this._writePadding(r));return this}_writeDictionaries(t){for(let[e,n]of t.dictionaries){let t=this._dictionaryDeltaOffsets.get(e)||0;if(0===t||(n=null===n||void 0===n?void 0:n.slice(t)).length>0)for(const r of n.data)this._writeDictionaryBatch(r,e,t>0),t+=r.length}return this}}class Go extends qo{static writeAll(t,e){const n=new Go(e);return I(t)?t.then((t=>n.writeAll(t))):E(t)?Zo(n,t):Xo(n,t)}}class Jo extends qo{static writeAll(t){const e=new Jo;return I(t)?t.then((t=>e.writeAll(t))):E(t)?Zo(e,t):Xo(e,t)}constructor(){super(),this._autoDestroy=!0}_writeSchema(t){return this._writeMagic()._writePadding(2)}_writeFooter(e){const n=Hr.encode(new Hr(e,t.V4,this._recordBatchBlocks,this._dictionaryBlocks));return super._writeFooter(e)._write(n)._write(Int32Array.of(n.byteLength))._writeMagic()}}function Xo(t,e){let n=e;e instanceof _r&&(n=e.batches,t.reset(void 0,e.schema));for(const r of n)t.write(r);return t.finish()}function Zo(t,e){var n,r,i,o;return P(this,void 0,void 0,(function*(){try{for(n=V(e);!(r=yield n.next()).done;){const e=r.value;t.write(e)}}catch(s){i={error:s}}finally{try{r&&!r.done&&(o=n.return)&&(yield o.call(n))}finally{if(i)throw i.error}}return t.finish()}))}function ts(t){const e=Ao.from(t);return I(e)?e.then((t=>ts(t))):e.isAsync()?e.readAll().then((t=>new _r(t))):new _r(e.readAll())}function es(t){return("stream"===(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"stream")?Go:Jo).writeAll(t).toUint8Array(!0)}var ns,rs=function(){function t(t,e,n,r){var i=this;this.getCell=function(t,e){var n=t<i.headerRows&&e<i.headerColumns,r=t>=i.headerRows&&e<i.headerColumns,o=t<i.headerRows&&e>=i.headerColumns;if(n){var s=["blank"];return e>0&&s.push("level"+t),{type:"blank",classNames:s.join(" "),content:""}}if(o)return{type:"columns",classNames:(s=["col_heading","level"+t,"col"+(l=e-i.headerColumns)]).join(" "),content:i.getContent(i.columnsTable,l,t)};if(r){s=["row_heading","level"+e,"row"+(a=t-i.headerRows)];return{type:"index",id:"T_".concat(i.uuid,"level").concat(e,"_row").concat(a),classNames:s.join(" "),content:i.getContent(i.indexTable,a,e)}}s=["data","row"+(a=t-i.headerRows),"col"+(l=e-i.headerColumns)];var a,l,u=i.styler?i.getContent(i.styler.displayValuesTable,a,l):i.getContent(i.dataTable,a,l);return{type:"data",id:"T_".concat(i.uuid,"row").concat(a,"_col").concat(l),classNames:s.join(" "),content:u}},this.getContent=function(t,e,n){var r=t.getChildAt(n);return null===r?"":i.getColumnTypeId(t,n)===l.Timestamp?i.nanosToDate(r.get(e)):r.get(e)},this.dataTable=ts(t),this.indexTable=ts(e),this.columnsTable=ts(n),this.styler=r?{caption:r.caption,displayValuesTable:ts(r.displayValues),styles:r.styles,uuid:r.uuid}:void 0}return Object.defineProperty(t.prototype,"rows",{get:function(){return this.indexTable.numRows+this.columnsTable.numCols},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"columns",{get:function(){return this.indexTable.numCols+this.columnsTable.numRows},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"headerRows",{get:function(){return this.rows-this.dataRows},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"headerColumns",{get:function(){return this.columns-this.dataColumns},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dataRows",{get:function(){return this.dataTable.numRows},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dataColumns",{get:function(){return this.dataTable.numCols},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"uuid",{get:function(){return this.styler&&this.styler.uuid},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"caption",{get:function(){return this.styler&&this.styler.caption},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"styles",{get:function(){return this.styler&&this.styler.styles},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"table",{get:function(){return this.dataTable},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"index",{get:function(){return this.indexTable},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"columnTable",{get:function(){return this.columnsTable},enumerable:!1,configurable:!0}),t.prototype.serialize=function(){return{data:es(this.dataTable),index:es(this.indexTable),columns:es(this.columnsTable)}},t.prototype.getColumnTypeId=function(t,e){return t.schema.fields[e].type.typeId},t.prototype.nanosToDate=function(t){return new Date(t/1e6)},t}(),is=function(){return is=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},is.apply(this,arguments)};!function(t){t.COMPONENT_READY="streamlit:componentReady",t.SET_COMPONENT_VALUE="streamlit:setComponentValue",t.SET_FRAME_HEIGHT="streamlit:setFrameHeight"}(ns||(ns={}));var os=function(){function t(){}return t.API_VERSION=1,t.RENDER_EVENT="streamlit:render",t.events=new EventTarget,t.registeredMessageListener=!1,t.setComponentReady=function(){t.registeredMessageListener||(window.addEventListener("message",t.onMessageEvent),t.registeredMessageListener=!0),t.sendBackMsg(ns.COMPONENT_READY,{apiVersion:t.API_VERSION})},t.setFrameHeight=function(e){void 0===e&&(e=document.body.scrollHeight),e!==t.lastFrameHeight&&(t.lastFrameHeight=e,t.sendBackMsg(ns.SET_FRAME_HEIGHT,{height:e}))},t.setComponentValue=function(e){var n;e instanceof rs?(n="dataframe",e=e.serialize()):!function(t){var e=!1;try{e=t instanceof BigInt64Array||t instanceof BigUint64Array}catch(n){}return t instanceof Int8Array||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array||e}(e)?e instanceof ArrayBuffer?(n="bytes",e=new Uint8Array(e)):n="json":(n="bytes",e=new Uint8Array(e.buffer)),t.sendBackMsg(ns.SET_COMPONENT_VALUE,{value:e,dataType:n})},t.onMessageEvent=function(e){if(e.data.type===t.RENDER_EVENT)t.onRenderMessage(e.data)},t.onRenderMessage=function(e){var n=e.args;null==n&&(console.error("Got null args in onRenderMessage. This should never happen"),n={});var r=e.dfs&&e.dfs.length>0?t.argsDataframeToObject(e.dfs):{};n=is(is({},n),r);var i=Boolean(e.disabled),o=e.theme;o&&ss(o);var s={disabled:i,args:n,theme:o},a=new CustomEvent(t.RENDER_EVENT,{detail:s});t.events.dispatchEvent(a)},t.argsDataframeToObject=function(e){var n=e.map((function(e){var n=e.key,r=e.value;return[n,t.toArrowTable(r)]}));return Object.fromEntries(n)},t.toArrowTable=function(t){var e,n=(e=t.data).data,r=e.index,i=e.columns,o=e.styler;return new rs(n,r,i,o)},t.sendBackMsg=function(t,e){window.parent.postMessage(is({isStreamlitMessage:!0,type:t},e),"*")},t}(),ss=function(t){var e=document.createElement("style");document.head.appendChild(e),e.innerHTML="\n    :root {\n      --primary-color: ".concat(t.primaryColor,";\n      --background-color: ").concat(t.backgroundColor,";\n      --secondary-background-color: ").concat(t.secondaryBackgroundColor,";\n      --text-color: ").concat(t.textColor,";\n      --font: ").concat(t.font,";\n    }\n\n    body {\n      background-color: var(--background-color);\n      color: var(--text-color);\n    }\n  ")};var as=function(){var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},t(e,n)};return function(e,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),ls=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return as(e,t),e.prototype.componentDidMount=function(){os.setFrameHeight()},e.prototype.componentDidUpdate=function(){os.setFrameHeight()},e}(c.PureComponent);var us=n(88);class cs extends c.Component{render(){const{data:t,onClicked:e}=this.props,n={width:t.size,height:t.size,borderRadius:"50%",objectFit:"cover",marginRight:"10px"};return(0,us.jsxs)("div",{style:{display:"flex",alignItems:"center",margin:"0.5rem"},onClick:()=>e(t.title,t.caption,t.key),children:[(0,us.jsx)("img",{src:t.url,alt:"avatar",style:n}),(0,us.jsxs)("div",{style:{display:"flex",flexDirection:"column"},children:[(0,us.jsx)("div",{style:{fontWeight:"bold"},children:t.title}),(0,us.jsx)("p",{style:{fontSize:"12px",color:"gray",marginBottom:0},children:t.caption})]})]})}}const ds=function(t){var e=function(e){function n(t){var n=e.call(this,t)||this;return n.componentDidMount=function(){os.events.addEventListener(os.RENDER_EVENT,n.onRenderEvent),os.setComponentReady()},n.componentDidUpdate=function(){null!=n.state.componentError&&os.setFrameHeight()},n.componentWillUnmount=function(){os.events.removeEventListener(os.RENDER_EVENT,n.onRenderEvent)},n.onRenderEvent=function(t){n.setState({renderData:t.detail})},n.state={renderData:void 0,componentError:void 0},n}return as(n,e),n.prototype.render=function(){return null!=this.state.componentError?c.createElement("div",null,c.createElement("h1",null,"Component Error"),c.createElement("span",null,this.state.componentError.message)):null==this.state.renderData?null:c.createElement(t,{width:window.innerWidth,disabled:this.state.renderData.disabled,args:this.state.renderData.args,theme:this.state.renderData.theme})},n.getDerivedStateFromError=function(t){return{componentError:t}},n}(c.PureComponent);return h()(e,t)}(class extends ls{constructor(){super(...arguments),this.render=()=>{const t=this.props.args.data_list||[];return(0,us.jsx)("div",{style:{display:"flex",flexDirection:"column"},children:t.map(((t,e)=>(0,us.jsx)(cs,{data:t,onClicked:this.onClicked},e)))})},this.onClicked=(t,e,n)=>{os.setComponentValue({title:t,cption:e,key:n})}}});d.render((0,us.jsx)(c.StrictMode,{children:(0,us.jsx)(ds,{})}),document.getElementById("root"))})()})();
//# sourceMappingURL=main.f4b71242.js.map