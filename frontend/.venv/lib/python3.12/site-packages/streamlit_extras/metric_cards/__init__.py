import streamlit as st

from streamlit_extras import extra
from streamlit_extras.theme import st_theme

theme = st_theme()

DEFAULT_BACKGROUND_COLOR_LIGHT = "#FFF"
DEFAULT_BORDER_COLOR_LIGHT = "#CCC"

DEFAULT_BACKGROUND_COLOR_DARK = "#1B1C24"
DEFAULT_BORDER_COLOR_DARK = "#292D34"

if theme:
    DEFAULT_BACKGROUND_COLOR = (
        DEFAULT_BACKGROUND_COLOR_DARK
        if theme.get("base") == "dark"
        else DEFAULT_BACKGROUND_COLOR_LIGHT
    )
    DEFAULT_BORDER_COLOR = (
        DEFAULT_BORDER_COLOR_DARK
        if theme.get("base") == "dark"
        else DEFAULT_BORDER_COLOR_LIGHT
    )

else:
    DEFAULT_BACKGROUND_COLOR = DEFAULT_BACKGROUND_COLOR_LIGHT
    DEFAULT_BORDER_COLOR = DEFAULT_BORDER_COLOR_LIGHT


@extra
def style_metric_cards(
    background_color: str = DEFAULT_BACKGROUND_COLOR,
    border_size_px: int = 1,
    border_color: str = DEFAULT_BORDER_COLOR,
    border_radius_px: int = 5,
    border_left_color: str = "#9AD8E1",
    box_shadow: bool = True,
) -> None:
    """
    Applies a custom style to st.metrics in the page

    Args:
        background_color (str, optional): Background color. Defaults to "#FFF" or "#292D34" in dark mode.
        border_size_px (int, optional): Border size in pixels. Defaults to 1.
        border_color (str, optional): Border color. Defaults to "#CCC" or "#292D34" in dark mode.
        border_radius_px (int, optional): Border radius in pixels. Defaults to 5.
        border_left_color (str, optional): Borfer left color. Defaults to "#9AD8E1".
        box_shadow (bool, optional): Whether a box shadow is applied. Defaults to True.
    """

    box_shadow_str = (
        "box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;"
        if box_shadow
        else "box-shadow: none !important;"
    )
    st.markdown(
        f"""
        <style>
            div[data-testid="stMetric"],
            div[data-testid="metric-container"] {{
                background-color: {background_color};
                border: {border_size_px}px solid {border_color};
                padding: 5% 5% 5% 10%;
                border-radius: {border_radius_px}px;
                border-left: 0.5rem solid {border_left_color} !important;
                {box_shadow_str}
            }}
        </style>
        """,
        unsafe_allow_html=True,
    )


def example():
    col1, col2, col3 = st.columns(3)

    col1.metric(label="Gain", value=5000, delta=1000)
    col2.metric(label="Loss", value=5000, delta=-1000)
    col3.metric(label="No Change", value=5000, delta=0)

    style_metric_cards()


def _add_metric_card_cards():
    """For playground"""
    col1, col2, col3 = st.columns(3)

    col1.metric(label="Gain", value=5000, delta=1000)
    col2.metric(label="Loss", value=5000, delta=-1000)
    col3.metric(label="No Change", value=5000, delta=0)


__title__ = "Metric Cards"
__desc__ = """Restyle metrics as cards with custom styles. **Note: you can now also
use the native st.container(border=True) to make nice cards with a border.**"""
__icon__ = "♠️"
__examples__ = [example]
__author__ = "Chanin Nantasenamat"
__playground__ = True
__experimental_playground_funcs__ = [_add_metric_card_cards]
