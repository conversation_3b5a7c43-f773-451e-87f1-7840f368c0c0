Metadata-Version: 2.1
Name: favicon
Version: 0.7.0
Summary: Get a website's favicon.
Home-page: https://github.com/scottwernervt/favicon
Author: <PERSON>
Author-email: <EMAIL>
License: MIT
Keywords: favicon icon
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: Operating System :: Unix
Classifier: Operating System :: POSIX
Classifier: Operating System :: Microsoft :: Windows
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Topic :: Utilities
Classifier: Topic :: Internet :: WWW/HTTP
Requires-Dist: requests (>=2.21.0)
Requires-Dist: beautifulsoup4 (>=4.7.0)

========
favicon
========



``favicon`` is a Python library to find a website's favicon.

Installation
============

.. code-block:: bash

   pip install favicon

Usage
=====

Get all icons:

.. code-block:: python

   >>> import favicon
   >>> icons = favicon.get('https://www.python.org/')
   Icon(url='https://www.python.org/static/apple-touch-icon-144x144-precomposed.png', width=144, height=144, format='png')
   Icon(url='https://www.python.org/static/apple-touch-icon-114x114-precomposed.png', width=114, height=114, format='png')
   Icon(url='https://www.python.org/static/apple-touch-icon-72x72-precomposed.png', width=72, height=72, format='png')
   Icon(url='https://www.python.org/static/apple-touch-icon-precomposed.png', width=0, height=0, format='png')
   Icon(url='https://www.python.org/static/favicon.ico', width=0, height=0, format='ico')

Download largest icon:

.. code-block:: python

   import requests
   import favicon

   icons = favicon.get('https://www.python.org/')
   icon = icons[0]

   response = requests.get(icon.url, stream=True)
   with open('/tmp/python-favicon.{}'.format(icon.format), 'wb') as image:
       for chunk in response.iter_content(1024):
           image.write(chunk)

   # /tmp/python-favicon.png

`Request library <https://2.python-requests.org/>`_ parameters can be passed to `favicon.get()` as keyword
arguments:

.. code-block:: python

   import favicon

   user_agent = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_9_3) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/35.0.1916.47 Safari/537.36'
   headers = {'User-Agent': user_agent}
   favicon.get('https://www.python.org/', headers=headers, timeout=2)

Requirements
============

* `requests <http://docs.python-requests.org/>`_
* `beautifulsoup4 <https://www.crummy.com/software/BeautifulSoup/bs4/doc/>`_

Inspiration
===========

* `pyfav <https://github.com/phillipsm/pyfav>`_
* `besticon <https://github.com/mat/besticon/>`_
* `How to get high resolution website logo (favicon) for a given URL <https://stackoverflow.com/questions/21991044/how-to-get-high-resolution-website-logo-favicon-for-a-given-url>`_

Changelog
=========

0.7.0 (2019-08-31)
------------------

* Handle empty `href` and `content` attribute values (`#22 <https://github.com/scottwernervt/favicon/issues/22>`_).
* Support passing request library parameters to `favicon.get()` (`#21 <https://github.com/scottwernervt/favicon/issues/21>`_).

  * Deprecate `headers` argument. Use keyword arguments: `favicon.get(url, headers={'User-Agent'; 'my-agent'}`.

0.6.0 (2019-08-10)
------------------

* Upgrade ``beautifulsoup4`` and ``requests`` package dependencies.

0.5.1 (2018-11-05)
------------------

* Fix 'NoneType' object has no attribute 'lower' for meta tags (`#16 <https://github.com/scottwernervt/favicon/issues/16>`_).

0.5.0 (2018-11-05)
------------------

* Add support for meta tags (`#15 <https://github.com/scottwernervt/favicon/pull/15>`_).
* Set bs4 parser to ``html.parser`` (`#13 <https://github.com/scottwernervt/favicon/issues/13>`_).
* Use ``src`` package structure (`#11 <https://github.com/scottwernervt/favicon/issues/11>`_).

0.4.1 (2018-10-01)
------------------

* Update ``requirements.txt`` and ``dev-requirements.txt``.

0.4.0 (2018-07-19)
------------------

* Add support for Python 2.7 and PyPy.
* Get icon size for New York Times (`#9 <https://github.com/scottwernervt/favicon/issues/9>`_).

0.3.0 (2018-05-18)
------------------

* Fav icon not found for microsoft.com (`#7 <https://github.com/scottwernervt/favicon/issues/7>`_).

0.2.0 (2018-05-17)
------------------

* Handle poor html values in links (`#5 <https://github.com/scottwernervt/favicon/issues/5>`_).
* Use given website for icon url scheme (`#6 <https://github.com/scottwernervt/favicon/issues/6>`_).

0.1.0 (2018-05-07)
------------------

* First release.


