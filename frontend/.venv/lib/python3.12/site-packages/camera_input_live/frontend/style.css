button {
  text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-font-smoothing: auto;
  box-sizing: border-box;
  font-size: inherit;
  font-family: inherit;
  overflow: visible;
  text-transform: none;
  appearance: button;
  display: inline-flex;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: center;
  justify-content: center;
  font-weight: 400;
  padding: 0.25rem 0.75rem;
  border-radius: 0.25rem;
  margin: 0px;
  line-height: 1.6;
  color: inherit;
  width: auto;
  user-select: none;
  background-color: rgb(255, 255, 255);
  border: 1px solid rgba(49, 51, 63, 0.2);
  cursor: pointer;
  position: absolute;
  left: 0;
}

button:hover {
  border-color: rgb(255, 75, 75);
  color: rgb(255, 75, 75);
}
button:active {
  color: rgb(255, 255, 255);
  border-color: rgb(255, 75, 75);
  background-color: rgb(255, 75, 75);
}

.padding {
  height: 100px;
}
