"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""

import builtins
from . import cloudevent_pb2
import collections.abc
import google.protobuf.any_pb2
import google.protobuf.descriptor
import google.protobuf.internal.containers
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class AgentId(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TYPE_FIELD_NUMBER: builtins.int
    KEY_FIELD_NUMBER: builtins.int
    type: builtins.str
    key: builtins.str
    def __init__(
        self,
        *,
        type: builtins.str = ...,
        key: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["key", b"key", "type", b"type"]) -> None: ...

global___AgentId = AgentId

@typing.final
class Payload(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    DATA_TYPE_FIELD_NUMBER: builtins.int
    DATA_CONTENT_TYPE_FIELD_NUMBER: builtins.int
    DATA_FIELD_NUMBER: builtins.int
    data_type: builtins.str
    data_content_type: builtins.str
    data: builtins.bytes
    def __init__(
        self,
        *,
        data_type: builtins.str = ...,
        data_content_type: builtins.str = ...,
        data: builtins.bytes = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["data", b"data", "data_content_type", b"data_content_type", "data_type", b"data_type"]) -> None: ...

global___Payload = Payload

@typing.final
class RpcRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class MetadataEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    REQUEST_ID_FIELD_NUMBER: builtins.int
    SOURCE_FIELD_NUMBER: builtins.int
    TARGET_FIELD_NUMBER: builtins.int
    METHOD_FIELD_NUMBER: builtins.int
    PAYLOAD_FIELD_NUMBER: builtins.int
    METADATA_FIELD_NUMBER: builtins.int
    request_id: builtins.str
    method: builtins.str
    @property
    def source(self) -> global___AgentId: ...
    @property
    def target(self) -> global___AgentId: ...
    @property
    def payload(self) -> global___Payload: ...
    @property
    def metadata(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]: ...
    def __init__(
        self,
        *,
        request_id: builtins.str = ...,
        source: global___AgentId | None = ...,
        target: global___AgentId | None = ...,
        method: builtins.str = ...,
        payload: global___Payload | None = ...,
        metadata: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_source", b"_source", "payload", b"payload", "source", b"source", "target", b"target"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_source", b"_source", "metadata", b"metadata", "method", b"method", "payload", b"payload", "request_id", b"request_id", "source", b"source", "target", b"target"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["_source", b"_source"]) -> typing.Literal["source"] | None: ...

global___RpcRequest = RpcRequest

@typing.final
class RpcResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    @typing.final
    class MetadataEntry(google.protobuf.message.Message):
        DESCRIPTOR: google.protobuf.descriptor.Descriptor

        KEY_FIELD_NUMBER: builtins.int
        VALUE_FIELD_NUMBER: builtins.int
        key: builtins.str
        value: builtins.str
        def __init__(
            self,
            *,
            key: builtins.str = ...,
            value: builtins.str = ...,
        ) -> None: ...
        def ClearField(self, field_name: typing.Literal["key", b"key", "value", b"value"]) -> None: ...

    REQUEST_ID_FIELD_NUMBER: builtins.int
    PAYLOAD_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    METADATA_FIELD_NUMBER: builtins.int
    request_id: builtins.str
    error: builtins.str
    @property
    def payload(self) -> global___Payload: ...
    @property
    def metadata(self) -> google.protobuf.internal.containers.ScalarMap[builtins.str, builtins.str]: ...
    def __init__(
        self,
        *,
        request_id: builtins.str = ...,
        payload: global___Payload | None = ...,
        error: builtins.str = ...,
        metadata: collections.abc.Mapping[builtins.str, builtins.str] | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["payload", b"payload"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["error", b"error", "metadata", b"metadata", "payload", b"payload", "request_id", b"request_id"]) -> None: ...

global___RpcResponse = RpcResponse

@typing.final
class RegisterAgentTypeRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TYPE_FIELD_NUMBER: builtins.int
    type: builtins.str
    def __init__(
        self,
        *,
        type: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["type", b"type"]) -> None: ...

global___RegisterAgentTypeRequest = RegisterAgentTypeRequest

@typing.final
class RegisterAgentTypeResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___RegisterAgentTypeResponse = RegisterAgentTypeResponse

@typing.final
class TypeSubscription(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TOPIC_TYPE_FIELD_NUMBER: builtins.int
    AGENT_TYPE_FIELD_NUMBER: builtins.int
    topic_type: builtins.str
    agent_type: builtins.str
    def __init__(
        self,
        *,
        topic_type: builtins.str = ...,
        agent_type: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["agent_type", b"agent_type", "topic_type", b"topic_type"]) -> None: ...

global___TypeSubscription = TypeSubscription

@typing.final
class TypePrefixSubscription(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    TOPIC_TYPE_PREFIX_FIELD_NUMBER: builtins.int
    AGENT_TYPE_FIELD_NUMBER: builtins.int
    topic_type_prefix: builtins.str
    agent_type: builtins.str
    def __init__(
        self,
        *,
        topic_type_prefix: builtins.str = ...,
        agent_type: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["agent_type", b"agent_type", "topic_type_prefix", b"topic_type_prefix"]) -> None: ...

global___TypePrefixSubscription = TypePrefixSubscription

@typing.final
class Subscription(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    TYPESUBSCRIPTION_FIELD_NUMBER: builtins.int
    TYPEPREFIXSUBSCRIPTION_FIELD_NUMBER: builtins.int
    id: builtins.str
    @property
    def typeSubscription(self) -> global___TypeSubscription: ...
    @property
    def typePrefixSubscription(self) -> global___TypePrefixSubscription: ...
    def __init__(
        self,
        *,
        id: builtins.str = ...,
        typeSubscription: global___TypeSubscription | None = ...,
        typePrefixSubscription: global___TypePrefixSubscription | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["subscription", b"subscription", "typePrefixSubscription", b"typePrefixSubscription", "typeSubscription", b"typeSubscription"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["id", b"id", "subscription", b"subscription", "typePrefixSubscription", b"typePrefixSubscription", "typeSubscription", b"typeSubscription"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["subscription", b"subscription"]) -> typing.Literal["typeSubscription", "typePrefixSubscription"] | None: ...

global___Subscription = Subscription

@typing.final
class AddSubscriptionRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SUBSCRIPTION_FIELD_NUMBER: builtins.int
    @property
    def subscription(self) -> global___Subscription: ...
    def __init__(
        self,
        *,
        subscription: global___Subscription | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["subscription", b"subscription"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["subscription", b"subscription"]) -> None: ...

global___AddSubscriptionRequest = AddSubscriptionRequest

@typing.final
class AddSubscriptionResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___AddSubscriptionResponse = AddSubscriptionResponse

@typing.final
class RemoveSubscriptionRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ID_FIELD_NUMBER: builtins.int
    id: builtins.str
    def __init__(
        self,
        *,
        id: builtins.str = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["id", b"id"]) -> None: ...

global___RemoveSubscriptionRequest = RemoveSubscriptionRequest

@typing.final
class RemoveSubscriptionResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___RemoveSubscriptionResponse = RemoveSubscriptionResponse

@typing.final
class GetSubscriptionsRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    def __init__(
        self,
    ) -> None: ...

global___GetSubscriptionsRequest = GetSubscriptionsRequest

@typing.final
class GetSubscriptionsResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    SUBSCRIPTIONS_FIELD_NUMBER: builtins.int
    @property
    def subscriptions(self) -> google.protobuf.internal.containers.RepeatedCompositeFieldContainer[global___Subscription]: ...
    def __init__(
        self,
        *,
        subscriptions: collections.abc.Iterable[global___Subscription] | None = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["subscriptions", b"subscriptions"]) -> None: ...

global___GetSubscriptionsResponse = GetSubscriptionsResponse

@typing.final
class Message(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    REQUEST_FIELD_NUMBER: builtins.int
    RESPONSE_FIELD_NUMBER: builtins.int
    CLOUDEVENT_FIELD_NUMBER: builtins.int
    @property
    def request(self) -> global___RpcRequest: ...
    @property
    def response(self) -> global___RpcResponse: ...
    @property
    def cloudEvent(self) -> cloudevent_pb2.CloudEvent: ...
    def __init__(
        self,
        *,
        request: global___RpcRequest | None = ...,
        response: global___RpcResponse | None = ...,
        cloudEvent: cloudevent_pb2.CloudEvent | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["cloudEvent", b"cloudEvent", "message", b"message", "request", b"request", "response", b"response"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["cloudEvent", b"cloudEvent", "message", b"message", "request", b"request", "response", b"response"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["message", b"message"]) -> typing.Literal["request", "response", "cloudEvent"] | None: ...

global___Message = Message

@typing.final
class SaveStateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    AGENTID_FIELD_NUMBER: builtins.int
    @property
    def agentId(self) -> global___AgentId: ...
    def __init__(
        self,
        *,
        agentId: global___AgentId | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["agentId", b"agentId"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["agentId", b"agentId"]) -> None: ...

global___SaveStateRequest = SaveStateRequest

@typing.final
class SaveStateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    STATE_FIELD_NUMBER: builtins.int
    ERROR_FIELD_NUMBER: builtins.int
    state: builtins.str
    error: builtins.str
    def __init__(
        self,
        *,
        state: builtins.str = ...,
        error: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_error", b"_error", "error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_error", b"_error", "error", b"error", "state", b"state"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["_error", b"_error"]) -> typing.Literal["error"] | None: ...

global___SaveStateResponse = SaveStateResponse

@typing.final
class LoadStateRequest(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    AGENTID_FIELD_NUMBER: builtins.int
    STATE_FIELD_NUMBER: builtins.int
    state: builtins.str
    @property
    def agentId(self) -> global___AgentId: ...
    def __init__(
        self,
        *,
        agentId: global___AgentId | None = ...,
        state: builtins.str = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["agentId", b"agentId"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["agentId", b"agentId", "state", b"state"]) -> None: ...

global___LoadStateRequest = LoadStateRequest

@typing.final
class LoadStateResponse(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    ERROR_FIELD_NUMBER: builtins.int
    error: builtins.str
    def __init__(
        self,
        *,
        error: builtins.str | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_error", b"_error", "error", b"error"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_error", b"_error", "error", b"error"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["_error", b"_error"]) -> typing.Literal["error"] | None: ...

global___LoadStateResponse = LoadStateResponse

@typing.final
class ControlMessage(google.protobuf.message.Message):
    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RPC_ID_FIELD_NUMBER: builtins.int
    DESTINATION_FIELD_NUMBER: builtins.int
    RESPOND_TO_FIELD_NUMBER: builtins.int
    RPCMESSAGE_FIELD_NUMBER: builtins.int
    rpc_id: builtins.str
    """A response message should have the same id as the request message"""
    destination: builtins.str
    """This is either:
    agentid=AGENT_ID
    clientid=CLIENT_ID
    """
    respond_to: builtins.str
    """This is either:
    agentid=AGENT_ID
    clientid=CLIENT_ID
    Empty string means the message is a response
    """
    @property
    def rpcMessage(self) -> google.protobuf.any_pb2.Any:
        """One of:
            SaveStateRequest saveStateRequest = 2;
            SaveStateResponse saveStateResponse = 3;
            LoadStateRequest loadStateRequest = 4;
            LoadStateResponse loadStateResponse = 5;
        """

    def __init__(
        self,
        *,
        rpc_id: builtins.str = ...,
        destination: builtins.str = ...,
        respond_to: builtins.str | None = ...,
        rpcMessage: google.protobuf.any_pb2.Any | None = ...,
    ) -> None: ...
    def HasField(self, field_name: typing.Literal["_respond_to", b"_respond_to", "respond_to", b"respond_to", "rpcMessage", b"rpcMessage"]) -> builtins.bool: ...
    def ClearField(self, field_name: typing.Literal["_respond_to", b"_respond_to", "destination", b"destination", "respond_to", b"respond_to", "rpcMessage", b"rpcMessage", "rpc_id", b"rpc_id"]) -> None: ...
    def WhichOneof(self, oneof_group: typing.Literal["_respond_to", b"_respond_to"]) -> typing.Literal["respond_to"] | None: ...

global___ControlMessage = ControlMessage
