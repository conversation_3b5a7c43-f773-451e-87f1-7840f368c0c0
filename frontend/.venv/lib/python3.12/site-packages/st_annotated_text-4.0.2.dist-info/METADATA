Metadata-Version: 2.2
Name: st-annotated-text
Version: 4.0.2
Summary: A simple component to display annotated text in Streamlit apps.
Home-page: https://github.com/tvst/st-annotated-text
Author: <PERSON><PERSON><PERSON>
Author-email: <EMAIL>
License: Apache 2
Classifier: Programming Language :: Python :: 3
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Operating System :: OS Independent
Requires-Python: >=3.5
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: htbuilder
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: home-page
Dynamic: license
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

# Annotated Text Component for Streamlit

A simple component to display annotated text in Streamlit apps. For example:

![Example image](https://github.com/tvst/st-annotated-text/raw/master/example.png)

Or, even better, check out our demo app here:

[![Open in Streamlit](https://static.streamlit.io/badges/streamlit_badge_black_white.svg)](https://st-annotated-text.streamlit.app/)


## Installation

First install Streamlit (of course!) then pip-install this library:

```bash
pip install streamlit
pip install st-annotated-text
```


## Example

```python
import streamlit as st
from annotated_text import annotated_text

annotated_text(
    "This ",
    ("is", "verb"),
    " some ",
    ("annotated", "adj"),
    ("text", "noun"),
    " for those of ",
    ("you", "pronoun"),
    " who ",
    ("like", "verb"),
    " this sort of ",
    ("thing", "noun"),
    "."
)
```

For more examples, including :sparkles:**customization options**:sparkles:, see
[the demo app](https://st-annotated-text.streamlit.app).


## Parameters

The `annotated_text()` function accepts any number of the following arguments:
- strings, to draw the string as-is on the screen.
- tuples of the form (main_text, annotation_text, background, color) where
  background and foreground colors are optional and should be an CSS-valid string such as
  "#aabbcc" or "rgb(10, 20, 30)"
- [htbuilder.HtmlElement](https://github.com/tvst/htbuilder) objects in case you want to customize
  the annotations further. In particular, you can import the `annotation()` function from this
  module to easily produce annotations whose CSS you can customize via keyword arguments.
- lists of any of the above -- or of lists!
