{"version": 3, "sources": ["streamlit_vertical_slider.tsx", "index.tsx"], "names": ["withStreamlitConnection", "props", "args", "min_value", "max_value", "default_value", "step", "track_color", "slider_color", "thumb_color", "useEffect", "Streamlit", "setFrameHeight", "snowflakeTheme", "createTheme", "overrides", "<PERSON>i<PERSON><PERSON><PERSON>", "root", "height", "fontSize", "marginBottom", "<PERSON><PERSON><PERSON><PERSON>", "color", "paddingLeft", "thumb", "marginLeft", "track", "width", "borderRadius", "borderWidth", "rail", "Box", "sx", "marginRight", "paddingTop", "ThemeProvider", "theme", "Slide<PERSON>", "min", "max", "defaultValue", "onChange", "event", "newValue", "setComponentValue", "valueLabelDisplay", "orientation", "aria-<PERSON>by", "ThumbComponent", "marks", "value", "Number", "label", "String", "ReactDOM", "render", "StrictMode", "document", "getElementById"], "mappings": "+PA8EeA,eAjEQ,SAACC,GAEtB,MAA8FA,EAAMC,KAA5FC,EAAS,EAATA,UAAWC,EAAS,EAATA,UAAWC,EAAa,EAAbA,cAAeC,EAAI,EAAJA,KAAMC,EAAW,EAAXA,YAAaC,EAAY,EAAZA,aAAcC,EAAW,EAAXA,YAC9EC,qBAAU,kBAAMC,IAAUC,oBAC1B,IAKMC,EAAiBC,YAAY,CACjCC,UAAW,CACTC,UAAW,CACTC,KAAM,CACJC,OAAQ,IACRC,SAAU,GACVC,aAAc,GAEhBC,UAAW,CACTC,MAAO,QACPC,YAAa,IAEfC,MAAO,CACLF,MAAOb,EACPgB,WAAY,kBAEdC,MAAO,CACLJ,MAAOd,EACPmB,MAAO,kBACPF,WAAY,iBACZG,aAAc,EACdR,aAAc,EACdS,YAAa,GAEfC,KAAM,CACJR,MAAOf,EACPoB,MAAO,kBACPF,WAAY,iBACZG,aAAc,EACdR,aAAc,OAMtB,OACE,kBAACW,EAAA,EAAG,CAACC,GAAI,CAAEd,OAAQ,IAAKe,YAAa,GAAIR,WAAY,GAAIS,WAAY,KACnE,kBAACC,EAAA,EAAa,CAACC,MAAOvB,GACpB,kBAACwB,EAAA,EAAM,CACLC,IAAKnC,EACLG,KAAMA,EACNiC,IAAKnC,EACLoC,aAAcnC,EACdoC,SAhDa,SAACC,EAAYC,GAChChC,IAAUiC,kBAAkBD,IAgDtBE,kBAAkB,KAClBC,YAAY,WACZC,kBAAgB,oBAChBC,eAAe,OACfC,MAAO,CAAC,CAAEC,MAAOC,OAAOhD,GAAYiD,MAAOC,OAAOlD,IAAc,CAAE+C,MAAOC,OAAO/C,GAAYgD,MAAOC,OAAOjD,YCjEpHkD,IAASC,OACP,kBAAC,IAAMC,WAAU,KACf,kBAAC,EAAc,OAEjBC,SAASC,eAAe,W", "file": "static/js/main.9f60d05d.chunk.js", "sourcesContent": ["import {\n  ComponentProps,\n  Streamlit,\n  withStreamlitConnection,\n} from \"streamlit-component-lib\"\nimport React, { useEffect } from \"react\"\nimport Box from '@material-ui/core/Box'\nimport { createTheme } from '@material-ui/core/styles';\nimport { Slider } from \"@material-ui/core\";\nimport { ThemeProvider } from '@material-ui/styles';\n\n\n\nconst VerticalSlider = (props: ComponentProps) => {\n\n  const { min_value, max_value, default_value, step, track_color, slider_color, thumb_color } = props.args;\n  useEffect(() => Streamlit.setFrameHeight());\n  const handleChange = (event: any, newValue: number | number[]) => {\n    Streamlit.setComponentValue(newValue);\n  };\n\n\n  const snowflakeTheme = createTheme({\n    overrides: {\n      MuiSlider: {\n        root: {\n          height: 200,\n          fontSize: 10,\n          marginBottom: 0\n        },\n        markLabel: {\n          color: \"black\",\n          paddingLeft: 15\n        },\n        thumb: {\n          color: thumb_color,\n          marginLeft: \"4px !important\"\n        },\n        track: {\n          color: slider_color,\n          width: \"10px !important\",\n          marginLeft: \"5px !important\",\n          borderRadius: 2,\n          marginBottom: 0,\n          borderWidth: 1\n        },\n        rail: {\n          color: track_color,\n          width: \"10px !important\",\n          marginLeft: \"5px !important\",\n          borderRadius: 2,\n          marginBottom: 0\n        }\n      }\n    }\n  });\n\n  return (\n    <Box sx={{ height: 200, marginRight: 10, marginLeft: 10, paddingTop: 50 }}>\n      <ThemeProvider theme={snowflakeTheme}>\n        <Slider\n          min={min_value}\n          step={step}\n          max={max_value}\n          defaultValue={default_value}\n          onChange={handleChange}\n          valueLabelDisplay=\"on\"\n          orientation=\"vertical\"\n          aria-labelledby=\"continuous-slider\"\n          ThumbComponent=\"span\"\n          marks={[{ value: Number(min_value), label: String(min_value) }, { value: Number(max_value), label: String(max_value) }]}\n        />\n      </ThemeProvider>\n    </Box>\n\n  );\n}\n\nexport default withStreamlitConnection(VerticalSlider);\n", "import React from \"react\"\nimport ReactD<PERSON> from \"react-dom\"\nimport VerticalSlider from \"./streamlit_vertical_slider\"\n\n\nReactDOM.render(\n  <React.StrictMode>\n    <VerticalSlider />\n  </React.StrictMode>,\n  document.getElementById(\"root\")\n)\n"], "sourceRoot": ""}