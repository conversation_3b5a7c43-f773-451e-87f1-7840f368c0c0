(this.webpackJsonpstreamlit_vertical_slider=this.webpackJsonpstreamlit_vertical_slider||[]).push([[0],{34:function(e,t,a){e.exports=a(41)},41:function(e,t,a){"use strict";a.r(t);var r=a(2),n=a.n(r),i=a(18),o=a.n(i),l=a(23),m=a(56),c=a(30),u=a(57),s=a(54),d=Object(l.b)((function(e){var t=e.args,a=t.min_value,i=t.max_value,o=t.default_value,d=t.step,p=t.track_color,b=t.slider_color,g=t.thumb_color;Object(r.useEffect)((function(){return l.a.setFrameHeight()}));var h=Object(c.a)({overrides:{MuiSlider:{root:{height:200,fontSize:10,marginBottom:0},mark<PERSON>abel:{color:"black",paddingLeft:15},thumb:{color:g,marginLeft:"4px !important"},track:{color:b,width:"10px !important",marginLeft:"5px !important",borderRadius:2,marginBottom:0,borderWidth:1},rail:{color:p,width:"10px !important",marginLeft:"5px !important",borderRadius:2,marginBottom:0}}}});return n.a.createElement(m.a,{sx:{height:200,marginRight:10,marginLeft:10,paddingTop:50}},n.a.createElement(s.a,{theme:h},n.a.createElement(u.a,{min:a,step:d,max:i,defaultValue:o,onChange:function(e,t){l.a.setComponentValue(t)},valueLabelDisplay:"on",orientation:"vertical","aria-labelledby":"continuous-slider",ThumbComponent:"span",marks:[{value:Number(a),label:String(a)},{value:Number(i),label:String(i)}]})))}));o.a.render(n.a.createElement(n.a.StrictMode,null,n.a.createElement(d,null)),document.getElementById("root"))}},[[34,1,2]]]);
//# sourceMappingURL=main.9f60d05d.chunk.js.map