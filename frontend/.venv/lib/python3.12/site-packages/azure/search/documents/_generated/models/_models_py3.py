# coding=utf-8
# pylint: disable=too-many-lines
# --------------------------------------------------------------------------
# Code generated by Microsoft (R) AutoRest Code Generator (autorest: 3.10.2, generator: @autorest/python@6.15.0)
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from typing import Any, Dict, List, Optional, TYPE_CHECKING, Union

from .. import _serialization

if TYPE_CHECKING:
    # pylint: disable=unused-import,ungrouped-imports
    from .. import models as _models


class AutocompleteItem(_serialization.Model):
    """The result of Autocomplete requests.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar text: The completed term. Required.
    :vartype text: str
    :ivar query_plus_text: The query along with the completed term. Required.
    :vartype query_plus_text: str
    """

    _validation = {
        "text": {"required": True, "readonly": True},
        "query_plus_text": {"required": True, "readonly": True},
    }

    _attribute_map = {
        "text": {"key": "text", "type": "str"},
        "query_plus_text": {"key": "queryPlusText", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.text = None
        self.query_plus_text = None


class AutocompleteOptions(_serialization.Model):
    """Parameter group.

    :ivar autocomplete_mode: Specifies the mode for Autocomplete. The default is 'oneTerm'. Use
     'twoTerms' to get shingles and 'oneTermWithContext' to use the current context while producing
     auto-completed terms. Known values are: "oneTerm", "twoTerms", and "oneTermWithContext".
    :vartype autocomplete_mode: str or ~azure.search.documents.models.AutocompleteMode
    :ivar filter: An OData expression that filters the documents used to produce completed terms
     for the Autocomplete result.
    :vartype filter: str
    :ivar use_fuzzy_matching: A value indicating whether to use fuzzy matching for the autocomplete
     query. Default is false. When set to true, the query will find terms even if there's a
     substituted or missing character in the search text. While this provides a better experience in
     some scenarios, it comes at a performance cost as fuzzy autocomplete queries are slower and
     consume more resources.
    :vartype use_fuzzy_matching: bool
    :ivar highlight_post_tag: A string tag that is appended to hit highlights. Must be set with
     highlightPreTag. If omitted, hit highlighting is disabled.
    :vartype highlight_post_tag: str
    :ivar highlight_pre_tag: A string tag that is prepended to hit highlights. Must be set with
     highlightPostTag. If omitted, hit highlighting is disabled.
    :vartype highlight_pre_tag: str
    :ivar minimum_coverage: A number between 0 and 100 indicating the percentage of the index that
     must be covered by an autocomplete query in order for the query to be reported as a success.
     This parameter can be useful for ensuring search availability even for services with only one
     replica. The default is 80.
    :vartype minimum_coverage: float
    :ivar search_fields: The list of field names to consider when querying for auto-completed
     terms. Target fields must be included in the specified suggester.
    :vartype search_fields: list[str]
    :ivar top: The number of auto-completed terms to retrieve. This must be a value between 1 and
     100. The default is 5.
    :vartype top: int
    """

    _attribute_map = {
        "autocomplete_mode": {"key": "autocompleteMode", "type": "str"},
        "filter": {"key": "$filter", "type": "str"},
        "use_fuzzy_matching": {"key": "UseFuzzyMatching", "type": "bool"},
        "highlight_post_tag": {"key": "highlightPostTag", "type": "str"},
        "highlight_pre_tag": {"key": "highlightPreTag", "type": "str"},
        "minimum_coverage": {"key": "minimumCoverage", "type": "float"},
        "search_fields": {"key": "searchFields", "type": "[str]"},
        "top": {"key": "$top", "type": "int"},
    }

    def __init__(
        self,
        *,
        autocomplete_mode: Optional[Union[str, "_models.AutocompleteMode"]] = None,
        filter: Optional[str] = None,  # pylint: disable=redefined-builtin
        use_fuzzy_matching: Optional[bool] = None,
        highlight_post_tag: Optional[str] = None,
        highlight_pre_tag: Optional[str] = None,
        minimum_coverage: Optional[float] = None,
        search_fields: Optional[List[str]] = None,
        top: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword autocomplete_mode: Specifies the mode for Autocomplete. The default is 'oneTerm'. Use
         'twoTerms' to get shingles and 'oneTermWithContext' to use the current context while producing
         auto-completed terms. Known values are: "oneTerm", "twoTerms", and "oneTermWithContext".
        :paramtype autocomplete_mode: str or ~azure.search.documents.models.AutocompleteMode
        :keyword filter: An OData expression that filters the documents used to produce completed terms
         for the Autocomplete result.
        :paramtype filter: str
        :keyword use_fuzzy_matching: A value indicating whether to use fuzzy matching for the
         autocomplete query. Default is false. When set to true, the query will find terms even if
         there's a substituted or missing character in the search text. While this provides a better
         experience in some scenarios, it comes at a performance cost as fuzzy autocomplete queries are
         slower and consume more resources.
        :paramtype use_fuzzy_matching: bool
        :keyword highlight_post_tag: A string tag that is appended to hit highlights. Must be set with
         highlightPreTag. If omitted, hit highlighting is disabled.
        :paramtype highlight_post_tag: str
        :keyword highlight_pre_tag: A string tag that is prepended to hit highlights. Must be set with
         highlightPostTag. If omitted, hit highlighting is disabled.
        :paramtype highlight_pre_tag: str
        :keyword minimum_coverage: A number between 0 and 100 indicating the percentage of the index
         that must be covered by an autocomplete query in order for the query to be reported as a
         success. This parameter can be useful for ensuring search availability even for services with
         only one replica. The default is 80.
        :paramtype minimum_coverage: float
        :keyword search_fields: The list of field names to consider when querying for auto-completed
         terms. Target fields must be included in the specified suggester.
        :paramtype search_fields: list[str]
        :keyword top: The number of auto-completed terms to retrieve. This must be a value between 1
         and 100. The default is 5.
        :paramtype top: int
        """
        super().__init__(**kwargs)
        self.autocomplete_mode = autocomplete_mode
        self.filter = filter
        self.use_fuzzy_matching = use_fuzzy_matching
        self.highlight_post_tag = highlight_post_tag
        self.highlight_pre_tag = highlight_pre_tag
        self.minimum_coverage = minimum_coverage
        self.search_fields = search_fields
        self.top = top


class AutocompleteRequest(_serialization.Model):
    """Parameters for fuzzy matching, and other autocomplete query behaviors.

    All required parameters must be populated in order to send to server.

    :ivar search_text: The search text on which to base autocomplete results. Required.
    :vartype search_text: str
    :ivar autocomplete_mode: Specifies the mode for Autocomplete. The default is 'oneTerm'. Use
     'twoTerms' to get shingles and 'oneTermWithContext' to use the current context while producing
     auto-completed terms. Known values are: "oneTerm", "twoTerms", and "oneTermWithContext".
    :vartype autocomplete_mode: str or ~azure.search.documents.models.AutocompleteMode
    :ivar filter: An OData expression that filters the documents used to produce completed terms
     for the Autocomplete result.
    :vartype filter: str
    :ivar use_fuzzy_matching: A value indicating whether to use fuzzy matching for the autocomplete
     query. Default is false. When set to true, the query will autocomplete terms even if there's a
     substituted or missing character in the search text. While this provides a better experience in
     some scenarios, it comes at a performance cost as fuzzy autocomplete queries are slower and
     consume more resources.
    :vartype use_fuzzy_matching: bool
    :ivar highlight_post_tag: A string tag that is appended to hit highlights. Must be set with
     highlightPreTag. If omitted, hit highlighting is disabled.
    :vartype highlight_post_tag: str
    :ivar highlight_pre_tag: A string tag that is prepended to hit highlights. Must be set with
     highlightPostTag. If omitted, hit highlighting is disabled.
    :vartype highlight_pre_tag: str
    :ivar minimum_coverage: A number between 0 and 100 indicating the percentage of the index that
     must be covered by an autocomplete query in order for the query to be reported as a success.
     This parameter can be useful for ensuring search availability even for services with only one
     replica. The default is 80.
    :vartype minimum_coverage: float
    :ivar search_fields: The comma-separated list of field names to consider when querying for
     auto-completed terms. Target fields must be included in the specified suggester.
    :vartype search_fields: str
    :ivar suggester_name: The name of the suggester as specified in the suggesters collection
     that's part of the index definition. Required.
    :vartype suggester_name: str
    :ivar top: The number of auto-completed terms to retrieve. This must be a value between 1 and
     100. The default is 5.
    :vartype top: int
    """

    _validation = {
        "search_text": {"required": True},
        "suggester_name": {"required": True},
    }

    _attribute_map = {
        "search_text": {"key": "search", "type": "str"},
        "autocomplete_mode": {"key": "autocompleteMode", "type": "str"},
        "filter": {"key": "filter", "type": "str"},
        "use_fuzzy_matching": {"key": "fuzzy", "type": "bool"},
        "highlight_post_tag": {"key": "highlightPostTag", "type": "str"},
        "highlight_pre_tag": {"key": "highlightPreTag", "type": "str"},
        "minimum_coverage": {"key": "minimumCoverage", "type": "float"},
        "search_fields": {"key": "searchFields", "type": "str"},
        "suggester_name": {"key": "suggesterName", "type": "str"},
        "top": {"key": "top", "type": "int"},
    }

    def __init__(
        self,
        *,
        search_text: str,
        suggester_name: str,
        autocomplete_mode: Optional[Union[str, "_models.AutocompleteMode"]] = None,
        filter: Optional[str] = None,  # pylint: disable=redefined-builtin
        use_fuzzy_matching: Optional[bool] = None,
        highlight_post_tag: Optional[str] = None,
        highlight_pre_tag: Optional[str] = None,
        minimum_coverage: Optional[float] = None,
        search_fields: Optional[str] = None,
        top: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword search_text: The search text on which to base autocomplete results. Required.
        :paramtype search_text: str
        :keyword autocomplete_mode: Specifies the mode for Autocomplete. The default is 'oneTerm'. Use
         'twoTerms' to get shingles and 'oneTermWithContext' to use the current context while producing
         auto-completed terms. Known values are: "oneTerm", "twoTerms", and "oneTermWithContext".
        :paramtype autocomplete_mode: str or ~azure.search.documents.models.AutocompleteMode
        :keyword filter: An OData expression that filters the documents used to produce completed terms
         for the Autocomplete result.
        :paramtype filter: str
        :keyword use_fuzzy_matching: A value indicating whether to use fuzzy matching for the
         autocomplete query. Default is false. When set to true, the query will autocomplete terms even
         if there's a substituted or missing character in the search text. While this provides a better
         experience in some scenarios, it comes at a performance cost as fuzzy autocomplete queries are
         slower and consume more resources.
        :paramtype use_fuzzy_matching: bool
        :keyword highlight_post_tag: A string tag that is appended to hit highlights. Must be set with
         highlightPreTag. If omitted, hit highlighting is disabled.
        :paramtype highlight_post_tag: str
        :keyword highlight_pre_tag: A string tag that is prepended to hit highlights. Must be set with
         highlightPostTag. If omitted, hit highlighting is disabled.
        :paramtype highlight_pre_tag: str
        :keyword minimum_coverage: A number between 0 and 100 indicating the percentage of the index
         that must be covered by an autocomplete query in order for the query to be reported as a
         success. This parameter can be useful for ensuring search availability even for services with
         only one replica. The default is 80.
        :paramtype minimum_coverage: float
        :keyword search_fields: The comma-separated list of field names to consider when querying for
         auto-completed terms. Target fields must be included in the specified suggester.
        :paramtype search_fields: str
        :keyword suggester_name: The name of the suggester as specified in the suggesters collection
         that's part of the index definition. Required.
        :paramtype suggester_name: str
        :keyword top: The number of auto-completed terms to retrieve. This must be a value between 1
         and 100. The default is 5.
        :paramtype top: int
        """
        super().__init__(**kwargs)
        self.search_text = search_text
        self.autocomplete_mode = autocomplete_mode
        self.filter = filter
        self.use_fuzzy_matching = use_fuzzy_matching
        self.highlight_post_tag = highlight_post_tag
        self.highlight_pre_tag = highlight_pre_tag
        self.minimum_coverage = minimum_coverage
        self.search_fields = search_fields
        self.suggester_name = suggester_name
        self.top = top


class AutocompleteResult(_serialization.Model):
    """The result of Autocomplete query.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar coverage: A value indicating the percentage of the index that was considered by the
     autocomplete request, or null if minimumCoverage was not specified in the request.
    :vartype coverage: float
    :ivar results: The list of returned Autocompleted items. Required.
    :vartype results: list[~azure.search.documents.models.AutocompleteItem]
    """

    _validation = {
        "coverage": {"readonly": True},
        "results": {"required": True, "readonly": True},
    }

    _attribute_map = {
        "coverage": {"key": "@search\\.coverage", "type": "float"},
        "results": {"key": "value", "type": "[AutocompleteItem]"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.coverage = None
        self.results = None


class ErrorAdditionalInfo(_serialization.Model):
    """The resource management error additional info.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar type: The additional info type.
    :vartype type: str
    :ivar info: The additional info.
    :vartype info: JSON
    """

    _validation = {
        "type": {"readonly": True},
        "info": {"readonly": True},
    }

    _attribute_map = {
        "type": {"key": "type", "type": "str"},
        "info": {"key": "info", "type": "object"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.type = None
        self.info = None


class ErrorDetail(_serialization.Model):
    """The error detail.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar code: The error code.
    :vartype code: str
    :ivar message: The error message.
    :vartype message: str
    :ivar target: The error target.
    :vartype target: str
    :ivar details: The error details.
    :vartype details: list[~azure.search.documents.models.ErrorDetail]
    :ivar additional_info: The error additional info.
    :vartype additional_info: list[~azure.search.documents.models.ErrorAdditionalInfo]
    """

    _validation = {
        "code": {"readonly": True},
        "message": {"readonly": True},
        "target": {"readonly": True},
        "details": {"readonly": True},
        "additional_info": {"readonly": True},
    }

    _attribute_map = {
        "code": {"key": "code", "type": "str"},
        "message": {"key": "message", "type": "str"},
        "target": {"key": "target", "type": "str"},
        "details": {"key": "details", "type": "[ErrorDetail]"},
        "additional_info": {"key": "additionalInfo", "type": "[ErrorAdditionalInfo]"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.code = None
        self.message = None
        self.target = None
        self.details = None
        self.additional_info = None


class ErrorResponse(_serialization.Model):
    """Common error response for all Azure Resource Manager APIs to return error details for failed
    operations. (This also follows the OData error response format.).

    :ivar error: The error object.
    :vartype error: ~azure.search.documents.models.ErrorDetail
    """

    _attribute_map = {
        "error": {"key": "error", "type": "ErrorDetail"},
    }

    def __init__(self, *, error: Optional["_models.ErrorDetail"] = None, **kwargs: Any) -> None:
        """
        :keyword error: The error object.
        :paramtype error: ~azure.search.documents.models.ErrorDetail
        """
        super().__init__(**kwargs)
        self.error = error


class FacetResult(_serialization.Model):
    """A single bucket of a facet query result. Reports the number of documents with a field value
    falling within a particular range or having a particular value or interval.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar additional_properties: Unmatched properties from the message are deserialized to this
     collection.
    :vartype additional_properties: dict[str, any]
    :ivar count: The approximate count of documents falling within the bucket described by this
     facet.
    :vartype count: int
    """

    _validation = {
        "count": {"readonly": True},
    }

    _attribute_map = {
        "additional_properties": {"key": "", "type": "{object}"},
        "count": {"key": "count", "type": "int"},
    }

    def __init__(self, *, additional_properties: Optional[Dict[str, Any]] = None, **kwargs: Any) -> None:
        """
        :keyword additional_properties: Unmatched properties from the message are deserialized to this
         collection.
        :paramtype additional_properties: dict[str, any]
        """
        super().__init__(**kwargs)
        self.additional_properties = additional_properties
        self.count = None


class IndexAction(_serialization.Model):
    """Represents an index action that operates on a document.

    :ivar additional_properties: Unmatched properties from the message are deserialized to this
     collection.
    :vartype additional_properties: dict[str, any]
    :ivar action_type: The operation to perform on a document in an indexing batch. Known values
     are: "upload", "merge", "mergeOrUpload", and "delete".
    :vartype action_type: str or ~azure.search.documents.models.IndexActionType
    """

    _attribute_map = {
        "additional_properties": {"key": "", "type": "{object}"},
        "action_type": {"key": "@search\\.action", "type": "str"},
    }

    def __init__(
        self,
        *,
        additional_properties: Optional[Dict[str, Any]] = None,
        action_type: Optional[Union[str, "_models.IndexActionType"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword additional_properties: Unmatched properties from the message are deserialized to this
         collection.
        :paramtype additional_properties: dict[str, any]
        :keyword action_type: The operation to perform on a document in an indexing batch. Known values
         are: "upload", "merge", "mergeOrUpload", and "delete".
        :paramtype action_type: str or ~azure.search.documents.models.IndexActionType
        """
        super().__init__(**kwargs)
        self.additional_properties = additional_properties
        self.action_type = action_type


class IndexBatch(_serialization.Model):
    """Contains a batch of document write actions to send to the index.

    All required parameters must be populated in order to send to server.

    :ivar actions: The actions in the batch. Required.
    :vartype actions: list[~azure.search.documents.models.IndexAction]
    """

    _validation = {
        "actions": {"required": True},
    }

    _attribute_map = {
        "actions": {"key": "value", "type": "[IndexAction]"},
    }

    def __init__(self, *, actions: List["_models.IndexAction"], **kwargs: Any) -> None:
        """
        :keyword actions: The actions in the batch. Required.
        :paramtype actions: list[~azure.search.documents.models.IndexAction]
        """
        super().__init__(**kwargs)
        self.actions = actions


class IndexDocumentsResult(_serialization.Model):
    """Response containing the status of operations for all documents in the indexing request.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar results: The list of status information for each document in the indexing request.
     Required.
    :vartype results: list[~azure.search.documents.models.IndexingResult]
    """

    _validation = {
        "results": {"required": True, "readonly": True},
    }

    _attribute_map = {
        "results": {"key": "value", "type": "[IndexingResult]"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.results = None


class IndexingResult(_serialization.Model):
    """Status of an indexing operation for a single document.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar key: The key of a document that was in the indexing request. Required.
    :vartype key: str
    :ivar error_message: The error message explaining why the indexing operation failed for the
     document identified by the key; null if indexing succeeded.
    :vartype error_message: str
    :ivar succeeded: A value indicating whether the indexing operation succeeded for the document
     identified by the key. Required.
    :vartype succeeded: bool
    :ivar status_code: The status code of the indexing operation. Possible values include: 200 for
     a successful update or delete, 201 for successful document creation, 400 for a malformed input
     document, 404 for document not found, 409 for a version conflict, 422 when the index is
     temporarily unavailable, or 503 for when the service is too busy. Required.
    :vartype status_code: int
    """

    _validation = {
        "key": {"required": True, "readonly": True},
        "error_message": {"readonly": True},
        "succeeded": {"required": True, "readonly": True},
        "status_code": {"required": True, "readonly": True},
    }

    _attribute_map = {
        "key": {"key": "key", "type": "str"},
        "error_message": {"key": "errorMessage", "type": "str"},
        "succeeded": {"key": "status", "type": "bool"},
        "status_code": {"key": "statusCode", "type": "int"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.key = None
        self.error_message = None
        self.succeeded = None
        self.status_code = None


class QueryAnswerResult(_serialization.Model):
    """An answer is a text passage extracted from the contents of the most relevant documents that
    matched the query. Answers are extracted from the top search results. Answer candidates are
    scored and the top answers are selected.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar additional_properties: Unmatched properties from the message are deserialized to this
     collection.
    :vartype additional_properties: dict[str, any]
    :ivar score: The score value represents how relevant the answer is to the query relative to
     other answers returned for the query.
    :vartype score: float
    :ivar key: The key of the document the answer was extracted from.
    :vartype key: str
    :ivar text: The text passage extracted from the document contents as the answer.
    :vartype text: str
    :ivar highlights: Same text passage as in the Text property with highlighted text phrases most
     relevant to the query.
    :vartype highlights: str
    """

    _validation = {
        "score": {"readonly": True},
        "key": {"readonly": True},
        "text": {"readonly": True},
        "highlights": {"readonly": True},
    }

    _attribute_map = {
        "additional_properties": {"key": "", "type": "{object}"},
        "score": {"key": "score", "type": "float"},
        "key": {"key": "key", "type": "str"},
        "text": {"key": "text", "type": "str"},
        "highlights": {"key": "highlights", "type": "str"},
    }

    def __init__(self, *, additional_properties: Optional[Dict[str, Any]] = None, **kwargs: Any) -> None:
        """
        :keyword additional_properties: Unmatched properties from the message are deserialized to this
         collection.
        :paramtype additional_properties: dict[str, any]
        """
        super().__init__(**kwargs)
        self.additional_properties = additional_properties
        self.score = None
        self.key = None
        self.text = None
        self.highlights = None


class QueryCaptionResult(_serialization.Model):
    """Captions are the most representative passages from the document relatively to the search query.
    They are often used as document summary. Captions are only returned for queries of type
    ``semantic``.

    Variables are only populated by the server, and will be ignored when sending a request.

    :ivar additional_properties: Unmatched properties from the message are deserialized to this
     collection.
    :vartype additional_properties: dict[str, any]
    :ivar text: A representative text passage extracted from the document most relevant to the
     search query.
    :vartype text: str
    :ivar highlights: Same text passage as in the Text property with highlighted phrases most
     relevant to the query.
    :vartype highlights: str
    """

    _validation = {
        "text": {"readonly": True},
        "highlights": {"readonly": True},
    }

    _attribute_map = {
        "additional_properties": {"key": "", "type": "{object}"},
        "text": {"key": "text", "type": "str"},
        "highlights": {"key": "highlights", "type": "str"},
    }

    def __init__(self, *, additional_properties: Optional[Dict[str, Any]] = None, **kwargs: Any) -> None:
        """
        :keyword additional_properties: Unmatched properties from the message are deserialized to this
         collection.
        :paramtype additional_properties: dict[str, any]
        """
        super().__init__(**kwargs)
        self.additional_properties = additional_properties
        self.text = None
        self.highlights = None


class RequestOptions(_serialization.Model):
    """Parameter group.

    :ivar x_ms_client_request_id: The tracking ID sent with the request to help with debugging.
    :vartype x_ms_client_request_id: str
    """

    _attribute_map = {
        "x_ms_client_request_id": {"key": "x-ms-client-request-id", "type": "str"},
    }

    def __init__(self, *, x_ms_client_request_id: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword x_ms_client_request_id: The tracking ID sent with the request to help with debugging.
        :paramtype x_ms_client_request_id: str
        """
        super().__init__(**kwargs)
        self.x_ms_client_request_id = x_ms_client_request_id


class SearchDocumentsResult(_serialization.Model):
    """Response containing search results from an index.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar count: The total count of results found by the search operation, or null if the count was
     not requested. If present, the count may be greater than the number of results in this
     response. This can happen if you use the $top or $skip parameters, or if the query can't return
     all the requested documents in a single response.
    :vartype count: int
    :ivar coverage: A value indicating the percentage of the index that was included in the query,
     or null if minimumCoverage was not specified in the request.
    :vartype coverage: float
    :ivar facets: The facet query results for the search operation, organized as a collection of
     buckets for each faceted field; null if the query did not include any facet expressions.
    :vartype facets: dict[str, list[~azure.search.documents.models.FacetResult]]
    :ivar answers: The answers query results for the search operation; null if the answers query
     parameter was not specified or set to 'none'.
    :vartype answers: list[~azure.search.documents.models.QueryAnswerResult]
    :ivar next_page_parameters: Continuation JSON payload returned when the query can't return all
     the requested results in a single response. You can use this JSON along with @odata.nextLink to
     formulate another POST Search request to get the next part of the search response.
    :vartype next_page_parameters: ~azure.search.documents.models.SearchRequest
    :ivar results: The sequence of results returned by the query. Required.
    :vartype results: list[~azure.search.documents.models.SearchResult]
    :ivar next_link: Continuation URL returned when the query can't return all the requested
     results in a single response. You can use this URL to formulate another GET or POST Search
     request to get the next part of the search response. Make sure to use the same verb (GET or
     POST) as the request that produced this response.
    :vartype next_link: str
    :ivar semantic_partial_response_reason: Reason that a partial response was returned for a
     semantic ranking request. Known values are: "maxWaitExceeded", "capacityOverloaded", and
     "transient".
    :vartype semantic_partial_response_reason: str or
     ~azure.search.documents.models.SemanticErrorReason
    :ivar semantic_partial_response_type: Type of partial response that was returned for a semantic
     ranking request. Known values are: "baseResults" and "rerankedResults".
    :vartype semantic_partial_response_type: str or
     ~azure.search.documents.models.SemanticSearchResultsType
    """

    _validation = {
        "count": {"readonly": True},
        "coverage": {"readonly": True},
        "facets": {"readonly": True},
        "answers": {"readonly": True},
        "next_page_parameters": {"readonly": True},
        "results": {"required": True, "readonly": True},
        "next_link": {"readonly": True},
        "semantic_partial_response_reason": {"readonly": True},
        "semantic_partial_response_type": {"readonly": True},
    }

    _attribute_map = {
        "count": {"key": "@odata\\.count", "type": "int"},
        "coverage": {"key": "@search\\.coverage", "type": "float"},
        "facets": {"key": "@search\\.facets", "type": "{[FacetResult]}"},
        "answers": {"key": "@search\\.answers", "type": "[QueryAnswerResult]"},
        "next_page_parameters": {"key": "@search\\.nextPageParameters", "type": "SearchRequest"},
        "results": {"key": "value", "type": "[SearchResult]"},
        "next_link": {"key": "@odata\\.nextLink", "type": "str"},
        "semantic_partial_response_reason": {"key": "@search\\.semanticPartialResponseReason", "type": "str"},
        "semantic_partial_response_type": {"key": "@search\\.semanticPartialResponseType", "type": "str"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.count = None
        self.coverage = None
        self.facets = None
        self.answers = None
        self.next_page_parameters = None
        self.results = None
        self.next_link = None
        self.semantic_partial_response_reason = None
        self.semantic_partial_response_type = None


class SearchOptions(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Parameter group.

    :ivar include_total_result_count: A value that specifies whether to fetch the total count of
     results. Default is false. Setting this value to true may have a performance impact. Note that
     the count returned is an approximation.
    :vartype include_total_result_count: bool
    :ivar facets: The list of facet expressions to apply to the search query. Each facet expression
     contains a field name, optionally followed by a comma-separated list of name:value pairs.
    :vartype facets: list[str]
    :ivar filter: The OData $filter expression to apply to the search query.
    :vartype filter: str
    :ivar highlight_fields: The list of field names to use for hit highlights. Only searchable
     fields can be used for hit highlighting.
    :vartype highlight_fields: list[str]
    :ivar highlight_post_tag: A string tag that is appended to hit highlights. Must be set with
     highlightPreTag. Default is &lt;/em&gt;.
    :vartype highlight_post_tag: str
    :ivar highlight_pre_tag: A string tag that is prepended to hit highlights. Must be set with
     highlightPostTag. Default is &lt;em&gt;.
    :vartype highlight_pre_tag: str
    :ivar minimum_coverage: A number between 0 and 100 indicating the percentage of the index that
     must be covered by a search query in order for the query to be reported as a success. This
     parameter can be useful for ensuring search availability even for services with only one
     replica. The default is 100.
    :vartype minimum_coverage: float
    :ivar order_by: The list of OData $orderby expressions by which to sort the results. Each
     expression can be either a field name or a call to either the geo.distance() or the
     search.score() functions. Each expression can be followed by asc to indicate ascending, and
     desc to indicate descending. The default is ascending order. Ties will be broken by the match
     scores of documents. If no OrderBy is specified, the default sort order is descending by
     document match score. There can be at most 32 $orderby clauses.
    :vartype order_by: list[str]
    :ivar query_type: A value that specifies the syntax of the search query. The default is
     'simple'. Use 'full' if your query uses the Lucene query syntax. Known values are: "simple",
     "full", and "semantic".
    :vartype query_type: str or ~azure.search.documents.models.QueryType
    :ivar scoring_parameters: The list of parameter values to be used in scoring functions (for
     example, referencePointParameter) using the format name-values. For example, if the scoring
     profile defines a function with a parameter called 'mylocation' the parameter string would be
     "mylocation--122.2,44.8" (without the quotes).
    :vartype scoring_parameters: list[str]
    :ivar scoring_profile: The name of a scoring profile to evaluate match scores for matching
     documents in order to sort the results.
    :vartype scoring_profile: str
    :ivar search_fields: The list of field names to which to scope the full-text search. When using
     fielded search (fieldName:searchExpression) in a full Lucene query, the field names of each
     fielded search expression take precedence over any field names listed in this parameter.
    :vartype search_fields: list[str]
    :ivar search_mode: A value that specifies whether any or all of the search terms must be
     matched in order to count the document as a match. Known values are: "any" and "all".
    :vartype search_mode: str or ~azure.search.documents.models.SearchMode
    :ivar scoring_statistics: A value that specifies whether we want to calculate scoring
     statistics (such as document frequency) globally for more consistent scoring, or locally, for
     lower latency. Known values are: "local" and "global".
    :vartype scoring_statistics: str or ~azure.search.documents.models.ScoringStatistics
    :ivar session_id: A value to be used to create a sticky session, which can help to get more
     consistent results. As long as the same sessionId is used, a best-effort attempt will be made
     to target the same replica set. Be wary that reusing the same sessionID values repeatedly can
     interfere with the load balancing of the requests across replicas and adversely affect the
     performance of the search service. The value used as sessionId cannot start with a '_'
     character.
    :vartype session_id: str
    :ivar select: The list of fields to retrieve. If unspecified, all fields marked as retrievable
     in the schema are included.
    :vartype select: list[str]
    :ivar skip: The number of search results to skip. This value cannot be greater than 100,000. If
     you need to scan documents in sequence, but cannot use $skip due to this limitation, consider
     using $orderby on a totally-ordered key and $filter with a range query instead.
    :vartype skip: int
    :ivar top: The number of search results to retrieve. This can be used in conjunction with $skip
     to implement client-side paging of search results. If results are truncated due to server-side
     paging, the response will include a continuation token that can be used to issue another Search
     request for the next page of results.
    :vartype top: int
    :ivar semantic_configuration: The name of the semantic configuration that lists which fields
     should be used for semantic ranking, captions, highlights, and answers.
    :vartype semantic_configuration: str
    :ivar semantic_error_handling: Allows the user to choose whether a semantic call should fail
     completely, or to return partial results (default). Known values are: "partial" and "fail".
    :vartype semantic_error_handling: str or ~azure.search.documents.models.SemanticErrorMode
    :ivar semantic_max_wait_in_milliseconds: Allows the user to set an upper bound on the amount of
     time it takes for semantic enrichment to finish processing before the request fails.
    :vartype semantic_max_wait_in_milliseconds: int
    :ivar answers: This parameter is only valid if the query type is ``semantic``. If set, the
     query returns answers extracted from key passages in the highest ranked documents. The number
     of answers returned can be configured by appending the pipe character ``|`` followed by the
     ``count-<number of answers>`` option after the answers parameter value, such as
     ``extractive|count-3``. Default count is 1. The confidence threshold can be configured by
     appending the pipe character ``|`` followed by the ``threshold-<confidence threshold>`` option
     after the answers parameter value, such as ``extractive|threshold-0.9``. Default threshold is
     0.7. Known values are: "none" and "extractive".
    :vartype answers: str or ~azure.search.documents.models.QueryAnswerType
    :ivar captions: This parameter is only valid if the query type is ``semantic``. If set, the
     query returns captions extracted from key passages in the highest ranked documents. When
     Captions is set to ``extractive``\\ , highlighting is enabled by default, and can be configured
     by appending the pipe character ``|`` followed by the ``highlight-<true/false>`` option, such
     as ``extractive|highlight-true``. Defaults to ``None``. Known values are: "none" and
     "extractive".
    :vartype captions: str or ~azure.search.documents.models.QueryCaptionType
    :ivar semantic_query: Allows setting a separate search query that will be solely used for
     semantic reranking, semantic captions and semantic answers. Is useful for scenarios where there
     is a need to use different queries between the base retrieval and ranking phase, and the L2
     semantic phase.
    :vartype semantic_query: str
    """

    _validation = {
        "semantic_max_wait_in_milliseconds": {"minimum": 700},
    }

    _attribute_map = {
        "include_total_result_count": {"key": "IncludeTotalResultCount", "type": "bool"},
        "facets": {"key": "Facets", "type": "[str]"},
        "filter": {"key": "$filter", "type": "str"},
        "highlight_fields": {"key": "HighlightFields", "type": "[str]"},
        "highlight_post_tag": {"key": "highlightPostTag", "type": "str"},
        "highlight_pre_tag": {"key": "highlightPreTag", "type": "str"},
        "minimum_coverage": {"key": "minimumCoverage", "type": "float"},
        "order_by": {"key": "OrderBy", "type": "[str]"},
        "query_type": {"key": "queryType", "type": "str"},
        "scoring_parameters": {"key": "ScoringParameters", "type": "[str]"},
        "scoring_profile": {"key": "scoringProfile", "type": "str"},
        "search_fields": {"key": "searchFields", "type": "[str]"},
        "search_mode": {"key": "searchMode", "type": "str"},
        "scoring_statistics": {"key": "scoringStatistics", "type": "str"},
        "session_id": {"key": "sessionId", "type": "str"},
        "select": {"key": "$select", "type": "[str]"},
        "skip": {"key": "$skip", "type": "int"},
        "top": {"key": "$top", "type": "int"},
        "semantic_configuration": {"key": "semanticConfiguration", "type": "str"},
        "semantic_error_handling": {"key": "semanticErrorHandling", "type": "str"},
        "semantic_max_wait_in_milliseconds": {"key": "semanticMaxWaitInMilliseconds", "type": "int"},
        "answers": {"key": "answers", "type": "str"},
        "captions": {"key": "captions", "type": "str"},
        "semantic_query": {"key": "semanticQuery", "type": "str"},
    }

    def __init__(  # pylint: disable=too-many-locals
        self,
        *,
        include_total_result_count: Optional[bool] = None,
        facets: Optional[List[str]] = None,
        filter: Optional[str] = None,  # pylint: disable=redefined-builtin
        highlight_fields: Optional[List[str]] = None,
        highlight_post_tag: Optional[str] = None,
        highlight_pre_tag: Optional[str] = None,
        minimum_coverage: Optional[float] = None,
        order_by: Optional[List[str]] = None,
        query_type: Optional[Union[str, "_models.QueryType"]] = None,
        scoring_parameters: Optional[List[str]] = None,
        scoring_profile: Optional[str] = None,
        search_fields: Optional[List[str]] = None,
        search_mode: Optional[Union[str, "_models.SearchMode"]] = None,
        scoring_statistics: Optional[Union[str, "_models.ScoringStatistics"]] = None,
        session_id: Optional[str] = None,
        select: Optional[List[str]] = None,
        skip: Optional[int] = None,
        top: Optional[int] = None,
        semantic_configuration: Optional[str] = None,
        semantic_error_handling: Optional[Union[str, "_models.SemanticErrorMode"]] = None,
        semantic_max_wait_in_milliseconds: Optional[int] = None,
        answers: Optional[Union[str, "_models.QueryAnswerType"]] = None,
        captions: Optional[Union[str, "_models.QueryCaptionType"]] = None,
        semantic_query: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword include_total_result_count: A value that specifies whether to fetch the total count of
         results. Default is false. Setting this value to true may have a performance impact. Note that
         the count returned is an approximation.
        :paramtype include_total_result_count: bool
        :keyword facets: The list of facet expressions to apply to the search query. Each facet
         expression contains a field name, optionally followed by a comma-separated list of name:value
         pairs.
        :paramtype facets: list[str]
        :keyword filter: The OData $filter expression to apply to the search query.
        :paramtype filter: str
        :keyword highlight_fields: The list of field names to use for hit highlights. Only searchable
         fields can be used for hit highlighting.
        :paramtype highlight_fields: list[str]
        :keyword highlight_post_tag: A string tag that is appended to hit highlights. Must be set with
         highlightPreTag. Default is &lt;/em&gt;.
        :paramtype highlight_post_tag: str
        :keyword highlight_pre_tag: A string tag that is prepended to hit highlights. Must be set with
         highlightPostTag. Default is &lt;em&gt;.
        :paramtype highlight_pre_tag: str
        :keyword minimum_coverage: A number between 0 and 100 indicating the percentage of the index
         that must be covered by a search query in order for the query to be reported as a success. This
         parameter can be useful for ensuring search availability even for services with only one
         replica. The default is 100.
        :paramtype minimum_coverage: float
        :keyword order_by: The list of OData $orderby expressions by which to sort the results. Each
         expression can be either a field name or a call to either the geo.distance() or the
         search.score() functions. Each expression can be followed by asc to indicate ascending, and
         desc to indicate descending. The default is ascending order. Ties will be broken by the match
         scores of documents. If no OrderBy is specified, the default sort order is descending by
         document match score. There can be at most 32 $orderby clauses.
        :paramtype order_by: list[str]
        :keyword query_type: A value that specifies the syntax of the search query. The default is
         'simple'. Use 'full' if your query uses the Lucene query syntax. Known values are: "simple",
         "full", and "semantic".
        :paramtype query_type: str or ~azure.search.documents.models.QueryType
        :keyword scoring_parameters: The list of parameter values to be used in scoring functions (for
         example, referencePointParameter) using the format name-values. For example, if the scoring
         profile defines a function with a parameter called 'mylocation' the parameter string would be
         "mylocation--122.2,44.8" (without the quotes).
        :paramtype scoring_parameters: list[str]
        :keyword scoring_profile: The name of a scoring profile to evaluate match scores for matching
         documents in order to sort the results.
        :paramtype scoring_profile: str
        :keyword search_fields: The list of field names to which to scope the full-text search. When
         using fielded search (fieldName:searchExpression) in a full Lucene query, the field names of
         each fielded search expression take precedence over any field names listed in this parameter.
        :paramtype search_fields: list[str]
        :keyword search_mode: A value that specifies whether any or all of the search terms must be
         matched in order to count the document as a match. Known values are: "any" and "all".
        :paramtype search_mode: str or ~azure.search.documents.models.SearchMode
        :keyword scoring_statistics: A value that specifies whether we want to calculate scoring
         statistics (such as document frequency) globally for more consistent scoring, or locally, for
         lower latency. Known values are: "local" and "global".
        :paramtype scoring_statistics: str or ~azure.search.documents.models.ScoringStatistics
        :keyword session_id: A value to be used to create a sticky session, which can help to get more
         consistent results. As long as the same sessionId is used, a best-effort attempt will be made
         to target the same replica set. Be wary that reusing the same sessionID values repeatedly can
         interfere with the load balancing of the requests across replicas and adversely affect the
         performance of the search service. The value used as sessionId cannot start with a '_'
         character.
        :paramtype session_id: str
        :keyword select: The list of fields to retrieve. If unspecified, all fields marked as
         retrievable in the schema are included.
        :paramtype select: list[str]
        :keyword skip: The number of search results to skip. This value cannot be greater than 100,000.
         If you need to scan documents in sequence, but cannot use $skip due to this limitation,
         consider using $orderby on a totally-ordered key and $filter with a range query instead.
        :paramtype skip: int
        :keyword top: The number of search results to retrieve. This can be used in conjunction with
         $skip to implement client-side paging of search results. If results are truncated due to
         server-side paging, the response will include a continuation token that can be used to issue
         another Search request for the next page of results.
        :paramtype top: int
        :keyword semantic_configuration: The name of the semantic configuration that lists which fields
         should be used for semantic ranking, captions, highlights, and answers.
        :paramtype semantic_configuration: str
        :keyword semantic_error_handling: Allows the user to choose whether a semantic call should fail
         completely, or to return partial results (default). Known values are: "partial" and "fail".
        :paramtype semantic_error_handling: str or ~azure.search.documents.models.SemanticErrorMode
        :keyword semantic_max_wait_in_milliseconds: Allows the user to set an upper bound on the amount
         of time it takes for semantic enrichment to finish processing before the request fails.
        :paramtype semantic_max_wait_in_milliseconds: int
        :keyword answers: This parameter is only valid if the query type is ``semantic``. If set, the
         query returns answers extracted from key passages in the highest ranked documents. The number
         of answers returned can be configured by appending the pipe character ``|`` followed by the
         ``count-<number of answers>`` option after the answers parameter value, such as
         ``extractive|count-3``. Default count is 1. The confidence threshold can be configured by
         appending the pipe character ``|`` followed by the ``threshold-<confidence threshold>`` option
         after the answers parameter value, such as ``extractive|threshold-0.9``. Default threshold is
         0.7. Known values are: "none" and "extractive".
        :paramtype answers: str or ~azure.search.documents.models.QueryAnswerType
        :keyword captions: This parameter is only valid if the query type is ``semantic``. If set, the
         query returns captions extracted from key passages in the highest ranked documents. When
         Captions is set to ``extractive``\\ , highlighting is enabled by default, and can be configured
         by appending the pipe character ``|`` followed by the ``highlight-<true/false>`` option, such
         as ``extractive|highlight-true``. Defaults to ``None``. Known values are: "none" and
         "extractive".
        :paramtype captions: str or ~azure.search.documents.models.QueryCaptionType
        :keyword semantic_query: Allows setting a separate search query that will be solely used for
         semantic reranking, semantic captions and semantic answers. Is useful for scenarios where there
         is a need to use different queries between the base retrieval and ranking phase, and the L2
         semantic phase.
        :paramtype semantic_query: str
        """
        super().__init__(**kwargs)
        self.include_total_result_count = include_total_result_count
        self.facets = facets
        self.filter = filter
        self.highlight_fields = highlight_fields
        self.highlight_post_tag = highlight_post_tag
        self.highlight_pre_tag = highlight_pre_tag
        self.minimum_coverage = minimum_coverage
        self.order_by = order_by
        self.query_type = query_type
        self.scoring_parameters = scoring_parameters
        self.scoring_profile = scoring_profile
        self.search_fields = search_fields
        self.search_mode = search_mode
        self.scoring_statistics = scoring_statistics
        self.session_id = session_id
        self.select = select
        self.skip = skip
        self.top = top
        self.semantic_configuration = semantic_configuration
        self.semantic_error_handling = semantic_error_handling
        self.semantic_max_wait_in_milliseconds = semantic_max_wait_in_milliseconds
        self.answers = answers
        self.captions = captions
        self.semantic_query = semantic_query


class SearchRequest(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Parameters for filtering, sorting, faceting, paging, and other search query behaviors.

    :ivar include_total_result_count: A value that specifies whether to fetch the total count of
     results. Default is false. Setting this value to true may have a performance impact. Note that
     the count returned is an approximation.
    :vartype include_total_result_count: bool
    :ivar facets: The list of facet expressions to apply to the search query. Each facet expression
     contains a field name, optionally followed by a comma-separated list of name:value pairs.
    :vartype facets: list[str]
    :ivar filter: The OData $filter expression to apply to the search query.
    :vartype filter: str
    :ivar highlight_fields: The comma-separated list of field names to use for hit highlights. Only
     searchable fields can be used for hit highlighting.
    :vartype highlight_fields: str
    :ivar highlight_post_tag: A string tag that is appended to hit highlights. Must be set with
     highlightPreTag. Default is &lt;/em&gt;.
    :vartype highlight_post_tag: str
    :ivar highlight_pre_tag: A string tag that is prepended to hit highlights. Must be set with
     highlightPostTag. Default is &lt;em&gt;.
    :vartype highlight_pre_tag: str
    :ivar minimum_coverage: A number between 0 and 100 indicating the percentage of the index that
     must be covered by a search query in order for the query to be reported as a success. This
     parameter can be useful for ensuring search availability even for services with only one
     replica. The default is 100.
    :vartype minimum_coverage: float
    :ivar order_by: The comma-separated list of OData $orderby expressions by which to sort the
     results. Each expression can be either a field name or a call to either the geo.distance() or
     the search.score() functions. Each expression can be followed by asc to indicate ascending, or
     desc to indicate descending. The default is ascending order. Ties will be broken by the match
     scores of documents. If no $orderby is specified, the default sort order is descending by
     document match score. There can be at most 32 $orderby clauses.
    :vartype order_by: str
    :ivar query_type: A value that specifies the syntax of the search query. The default is
     'simple'. Use 'full' if your query uses the Lucene query syntax. Known values are: "simple",
     "full", and "semantic".
    :vartype query_type: str or ~azure.search.documents.models.QueryType
    :ivar scoring_statistics: A value that specifies whether we want to calculate scoring
     statistics (such as document frequency) globally for more consistent scoring, or locally, for
     lower latency. The default is 'local'. Use 'global' to aggregate scoring statistics globally
     before scoring. Using global scoring statistics can increase latency of search queries. Known
     values are: "local" and "global".
    :vartype scoring_statistics: str or ~azure.search.documents.models.ScoringStatistics
    :ivar session_id: A value to be used to create a sticky session, which can help getting more
     consistent results. As long as the same sessionId is used, a best-effort attempt will be made
     to target the same replica set. Be wary that reusing the same sessionID values repeatedly can
     interfere with the load balancing of the requests across replicas and adversely affect the
     performance of the search service. The value used as sessionId cannot start with a '_'
     character.
    :vartype session_id: str
    :ivar scoring_parameters: The list of parameter values to be used in scoring functions (for
     example, referencePointParameter) using the format name-values. For example, if the scoring
     profile defines a function with a parameter called 'mylocation' the parameter string would be
     "mylocation--122.2,44.8" (without the quotes).
    :vartype scoring_parameters: list[str]
    :ivar scoring_profile: The name of a scoring profile to evaluate match scores for matching
     documents in order to sort the results.
    :vartype scoring_profile: str
    :ivar search_text: A full-text search query expression; Use "*" or omit this parameter to match
     all documents.
    :vartype search_text: str
    :ivar search_fields: The comma-separated list of field names to which to scope the full-text
     search. When using fielded search (fieldName:searchExpression) in a full Lucene query, the
     field names of each fielded search expression take precedence over any field names listed in
     this parameter.
    :vartype search_fields: str
    :ivar search_mode: A value that specifies whether any or all of the search terms must be
     matched in order to count the document as a match. Known values are: "any" and "all".
    :vartype search_mode: str or ~azure.search.documents.models.SearchMode
    :ivar select: The comma-separated list of fields to retrieve. If unspecified, all fields marked
     as retrievable in the schema are included.
    :vartype select: str
    :ivar skip: The number of search results to skip. This value cannot be greater than 100,000. If
     you need to scan documents in sequence, but cannot use skip due to this limitation, consider
     using orderby on a totally-ordered key and filter with a range query instead.
    :vartype skip: int
    :ivar top: The number of search results to retrieve. This can be used in conjunction with $skip
     to implement client-side paging of search results. If results are truncated due to server-side
     paging, the response will include a continuation token that can be used to issue another Search
     request for the next page of results.
    :vartype top: int
    :ivar semantic_configuration: The name of a semantic configuration that will be used when
     processing documents for queries of type semantic.
    :vartype semantic_configuration: str
    :ivar semantic_error_handling: Allows the user to choose whether a semantic call should fail
     completely (default / current behavior), or to return partial results. Known values are:
     "partial" and "fail".
    :vartype semantic_error_handling: str or ~azure.search.documents.models.SemanticErrorMode
    :ivar semantic_max_wait_in_milliseconds: Allows the user to set an upper bound on the amount of
     time it takes for semantic enrichment to finish processing before the request fails.
    :vartype semantic_max_wait_in_milliseconds: int
    :ivar semantic_query: Allows setting a separate search query that will be solely used for
     semantic reranking, semantic captions and semantic answers. Is useful for scenarios where there
     is a need to use different queries between the base retrieval and ranking phase, and the L2
     semantic phase.
    :vartype semantic_query: str
    :ivar answers: A value that specifies whether answers should be returned as part of the search
     response. Known values are: "none" and "extractive".
    :vartype answers: str or ~azure.search.documents.models.QueryAnswerType
    :ivar captions: A value that specifies whether captions should be returned as part of the
     search response. Known values are: "none" and "extractive".
    :vartype captions: str or ~azure.search.documents.models.QueryCaptionType
    :ivar vector_queries: The query parameters for vector and hybrid search queries.
    :vartype vector_queries: list[~azure.search.documents.models.VectorQuery]
    :ivar vector_filter_mode: Determines whether or not filters are applied before or after the
     vector search is performed. Default is 'preFilter' for new indexes. Known values are:
     "postFilter" and "preFilter".
    :vartype vector_filter_mode: str or ~azure.search.documents.models.VectorFilterMode
    """

    _validation = {
        "semantic_max_wait_in_milliseconds": {"minimum": 700},
    }

    _attribute_map = {
        "include_total_result_count": {"key": "count", "type": "bool"},
        "facets": {"key": "facets", "type": "[str]"},
        "filter": {"key": "filter", "type": "str"},
        "highlight_fields": {"key": "highlight", "type": "str"},
        "highlight_post_tag": {"key": "highlightPostTag", "type": "str"},
        "highlight_pre_tag": {"key": "highlightPreTag", "type": "str"},
        "minimum_coverage": {"key": "minimumCoverage", "type": "float"},
        "order_by": {"key": "orderby", "type": "str"},
        "query_type": {"key": "queryType", "type": "str"},
        "scoring_statistics": {"key": "scoringStatistics", "type": "str"},
        "session_id": {"key": "sessionId", "type": "str"},
        "scoring_parameters": {"key": "scoringParameters", "type": "[str]"},
        "scoring_profile": {"key": "scoringProfile", "type": "str"},
        "search_text": {"key": "search", "type": "str"},
        "search_fields": {"key": "searchFields", "type": "str"},
        "search_mode": {"key": "searchMode", "type": "str"},
        "select": {"key": "select", "type": "str"},
        "skip": {"key": "skip", "type": "int"},
        "top": {"key": "top", "type": "int"},
        "semantic_configuration": {"key": "semanticConfiguration", "type": "str"},
        "semantic_error_handling": {"key": "semanticErrorHandling", "type": "str"},
        "semantic_max_wait_in_milliseconds": {"key": "semanticMaxWaitInMilliseconds", "type": "int"},
        "semantic_query": {"key": "semanticQuery", "type": "str"},
        "answers": {"key": "answers", "type": "str"},
        "captions": {"key": "captions", "type": "str"},
        "vector_queries": {"key": "vectorQueries", "type": "[VectorQuery]"},
        "vector_filter_mode": {"key": "vectorFilterMode", "type": "str"},
    }

    def __init__(  # pylint: disable=too-many-locals
        self,
        *,
        include_total_result_count: Optional[bool] = None,
        facets: Optional[List[str]] = None,
        filter: Optional[str] = None,  # pylint: disable=redefined-builtin
        highlight_fields: Optional[str] = None,
        highlight_post_tag: Optional[str] = None,
        highlight_pre_tag: Optional[str] = None,
        minimum_coverage: Optional[float] = None,
        order_by: Optional[str] = None,
        query_type: Optional[Union[str, "_models.QueryType"]] = None,
        scoring_statistics: Optional[Union[str, "_models.ScoringStatistics"]] = None,
        session_id: Optional[str] = None,
        scoring_parameters: Optional[List[str]] = None,
        scoring_profile: Optional[str] = None,
        search_text: Optional[str] = None,
        search_fields: Optional[str] = None,
        search_mode: Optional[Union[str, "_models.SearchMode"]] = None,
        select: Optional[str] = None,
        skip: Optional[int] = None,
        top: Optional[int] = None,
        semantic_configuration: Optional[str] = None,
        semantic_error_handling: Optional[Union[str, "_models.SemanticErrorMode"]] = None,
        semantic_max_wait_in_milliseconds: Optional[int] = None,
        semantic_query: Optional[str] = None,
        answers: Optional[Union[str, "_models.QueryAnswerType"]] = None,
        captions: Optional[Union[str, "_models.QueryCaptionType"]] = None,
        vector_queries: Optional[List["_models.VectorQuery"]] = None,
        vector_filter_mode: Optional[Union[str, "_models.VectorFilterMode"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword include_total_result_count: A value that specifies whether to fetch the total count of
         results. Default is false. Setting this value to true may have a performance impact. Note that
         the count returned is an approximation.
        :paramtype include_total_result_count: bool
        :keyword facets: The list of facet expressions to apply to the search query. Each facet
         expression contains a field name, optionally followed by a comma-separated list of name:value
         pairs.
        :paramtype facets: list[str]
        :keyword filter: The OData $filter expression to apply to the search query.
        :paramtype filter: str
        :keyword highlight_fields: The comma-separated list of field names to use for hit highlights.
         Only searchable fields can be used for hit highlighting.
        :paramtype highlight_fields: str
        :keyword highlight_post_tag: A string tag that is appended to hit highlights. Must be set with
         highlightPreTag. Default is &lt;/em&gt;.
        :paramtype highlight_post_tag: str
        :keyword highlight_pre_tag: A string tag that is prepended to hit highlights. Must be set with
         highlightPostTag. Default is &lt;em&gt;.
        :paramtype highlight_pre_tag: str
        :keyword minimum_coverage: A number between 0 and 100 indicating the percentage of the index
         that must be covered by a search query in order for the query to be reported as a success. This
         parameter can be useful for ensuring search availability even for services with only one
         replica. The default is 100.
        :paramtype minimum_coverage: float
        :keyword order_by: The comma-separated list of OData $orderby expressions by which to sort the
         results. Each expression can be either a field name or a call to either the geo.distance() or
         the search.score() functions. Each expression can be followed by asc to indicate ascending, or
         desc to indicate descending. The default is ascending order. Ties will be broken by the match
         scores of documents. If no $orderby is specified, the default sort order is descending by
         document match score. There can be at most 32 $orderby clauses.
        :paramtype order_by: str
        :keyword query_type: A value that specifies the syntax of the search query. The default is
         'simple'. Use 'full' if your query uses the Lucene query syntax. Known values are: "simple",
         "full", and "semantic".
        :paramtype query_type: str or ~azure.search.documents.models.QueryType
        :keyword scoring_statistics: A value that specifies whether we want to calculate scoring
         statistics (such as document frequency) globally for more consistent scoring, or locally, for
         lower latency. The default is 'local'. Use 'global' to aggregate scoring statistics globally
         before scoring. Using global scoring statistics can increase latency of search queries. Known
         values are: "local", "global", and "global".
        :paramtype scoring_statistics: str or ~azure.search.documents.models.ScoringStatistics
        :keyword session_id: A value to be used to create a sticky session, which can help getting more
         consistent results. As long as the same sessionId is used, a best-effort attempt will be made
         to target the same replica set. Be wary that reusing the same sessionID values repeatedly can
         interfere with the load balancing of the requests across replicas and adversely affect the
         performance of the search service. The value used as sessionId cannot start with a '_'
         character.
        :paramtype session_id: str
        :keyword scoring_parameters: The list of parameter values to be used in scoring functions (for
         example, referencePointParameter) using the format name-values. For example, if the scoring
         profile defines a function with a parameter called 'mylocation' the parameter string would be
         "mylocation--122.2,44.8" (without the quotes).
        :paramtype scoring_parameters: list[str]
        :keyword scoring_profile: The name of a scoring profile to evaluate match scores for matching
         documents in order to sort the results.
        :paramtype scoring_profile: str
        :keyword search_text: A full-text search query expression; Use "*" or omit this parameter to
         match all documents.
        :paramtype search_text: str
        :keyword search_fields: The comma-separated list of field names to which to scope the full-text
         search. When using fielded search (fieldName:searchExpression) in a full Lucene query, the
         field names of each fielded search expression take precedence over any field names listed in
         this parameter.
        :paramtype search_fields: str
        :keyword search_mode: A value that specifies whether any or all of the search terms must be
         matched in order to count the document as a match. Known values are: "any" and "all".
        :paramtype search_mode: str or ~azure.search.documents.models.SearchMode
        :keyword select: The comma-separated list of fields to retrieve. If unspecified, all fields
         marked as retrievable in the schema are included.
        :paramtype select: str
        :keyword skip: The number of search results to skip. This value cannot be greater than 100,000.
         If you need to scan documents in sequence, but cannot use skip due to this limitation, consider
         using orderby on a totally-ordered key and filter with a range query instead.
        :paramtype skip: int
        :keyword top: The number of search results to retrieve. This can be used in conjunction with
         $skip to implement client-side paging of search results. If results are truncated due to
         server-side paging, the response will include a continuation token that can be used to issue
         another Search request for the next page of results.
        :paramtype top: int
        :keyword semantic_configuration: The name of a semantic configuration that will be used when
         processing documents for queries of type semantic.
        :paramtype semantic_configuration: str
        :keyword semantic_error_handling: Allows the user to choose whether a semantic call should fail
         completely (default / current behavior), or to return partial results. Known values are:
         "partial" and "fail".
        :paramtype semantic_error_handling: str or ~azure.search.documents.models.SemanticErrorMode
        :keyword semantic_max_wait_in_milliseconds: Allows the user to set an upper bound on the amount
         of time it takes for semantic enrichment to finish processing before the request fails.
        :paramtype semantic_max_wait_in_milliseconds: int
        :keyword semantic_query: Allows setting a separate search query that will be solely used for
         semantic reranking, semantic captions and semantic answers. Is useful for scenarios where there
         is a need to use different queries between the base retrieval and ranking phase, and the L2
         semantic phase.
        :paramtype semantic_query: str
        :keyword answers: A value that specifies whether answers should be returned as part of the
         search response. Known values are: "none" and "extractive".
        :paramtype answers: str or ~azure.search.documents.models.QueryAnswerType
        :keyword captions: A value that specifies whether captions should be returned as part of the
         search response. Known values are: "none" and "extractive".
        :paramtype captions: str or ~azure.search.documents.models.QueryCaptionType
        :keyword vector_queries: The query parameters for vector and hybrid search queries.
        :paramtype vector_queries: list[~azure.search.documents.models.VectorQuery]
        :keyword vector_filter_mode: Determines whether or not filters are applied before or after the
         vector search is performed. Default is 'preFilter' for new indexes. Known values are:
         "postFilter" and "preFilter".
        :paramtype vector_filter_mode: str or ~azure.search.documents.models.VectorFilterMode
        """
        super().__init__(**kwargs)
        self.include_total_result_count = include_total_result_count
        self.facets = facets
        self.filter = filter
        self.highlight_fields = highlight_fields
        self.highlight_post_tag = highlight_post_tag
        self.highlight_pre_tag = highlight_pre_tag
        self.minimum_coverage = minimum_coverage
        self.order_by = order_by
        self.query_type = query_type
        self.scoring_statistics = scoring_statistics
        self.session_id = session_id
        self.scoring_parameters = scoring_parameters
        self.scoring_profile = scoring_profile
        self.search_text = search_text
        self.search_fields = search_fields
        self.search_mode = search_mode
        self.select = select
        self.skip = skip
        self.top = top
        self.semantic_configuration = semantic_configuration
        self.semantic_error_handling = semantic_error_handling
        self.semantic_max_wait_in_milliseconds = semantic_max_wait_in_milliseconds
        self.semantic_query = semantic_query
        self.answers = answers
        self.captions = captions
        self.vector_queries = vector_queries
        self.vector_filter_mode = vector_filter_mode


class SearchResult(_serialization.Model):
    """Contains a document found by a search query, plus associated metadata.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar additional_properties: Unmatched properties from the message are deserialized to this
     collection.
    :vartype additional_properties: dict[str, any]
    :ivar score: The relevance score of the document compared to other documents returned by the
     query. Required.
    :vartype score: float
    :ivar reranker_score: The relevance score computed by the semantic ranker for the top search
     results. Search results are sorted by the RerankerScore first and then by the Score.
     RerankerScore is only returned for queries of type 'semantic'.
    :vartype reranker_score: float
    :ivar highlights: Text fragments from the document that indicate the matching search terms,
     organized by each applicable field; null if hit highlighting was not enabled for the query.
    :vartype highlights: dict[str, list[str]]
    :ivar captions: Captions are the most representative passages from the document relatively to
     the search query. They are often used as document summary. Captions are only returned for
     queries of type 'semantic'.
    :vartype captions: list[~azure.search.documents.models.QueryCaptionResult]
    """

    _validation = {
        "score": {"required": True, "readonly": True},
        "reranker_score": {"readonly": True},
        "highlights": {"readonly": True},
        "captions": {"readonly": True},
    }

    _attribute_map = {
        "additional_properties": {"key": "", "type": "{object}"},
        "score": {"key": "@search\\.score", "type": "float"},
        "reranker_score": {"key": "@search\\.rerankerScore", "type": "float"},
        "highlights": {"key": "@search\\.highlights", "type": "{[str]}"},
        "captions": {"key": "@search\\.captions", "type": "[QueryCaptionResult]"},
    }

    def __init__(self, *, additional_properties: Optional[Dict[str, Any]] = None, **kwargs: Any) -> None:
        """
        :keyword additional_properties: Unmatched properties from the message are deserialized to this
         collection.
        :paramtype additional_properties: dict[str, any]
        """
        super().__init__(**kwargs)
        self.additional_properties = additional_properties
        self.score = None
        self.reranker_score = None
        self.highlights = None
        self.captions = None


class SuggestDocumentsResult(_serialization.Model):
    """Response containing suggestion query results from an index.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar results: The sequence of results returned by the query. Required.
    :vartype results: list[~azure.search.documents.models.SuggestResult]
    :ivar coverage: A value indicating the percentage of the index that was included in the query,
     or null if minimumCoverage was not set in the request.
    :vartype coverage: float
    """

    _validation = {
        "results": {"required": True, "readonly": True},
        "coverage": {"readonly": True},
    }

    _attribute_map = {
        "results": {"key": "value", "type": "[SuggestResult]"},
        "coverage": {"key": "@search\\.coverage", "type": "float"},
    }

    def __init__(self, **kwargs: Any) -> None:
        """ """
        super().__init__(**kwargs)
        self.results = None
        self.coverage = None


class SuggestOptions(_serialization.Model):
    """Parameter group.

    :ivar filter: An OData expression that filters the documents considered for suggestions.
    :vartype filter: str
    :ivar use_fuzzy_matching: A value indicating whether to use fuzzy matching for the suggestions
     query. Default is false. When set to true, the query will find terms even if there's a
     substituted or missing character in the search text. While this provides a better experience in
     some scenarios, it comes at a performance cost as fuzzy suggestions queries are slower and
     consume more resources.
    :vartype use_fuzzy_matching: bool
    :ivar highlight_post_tag: A string tag that is appended to hit highlights. Must be set with
     highlightPreTag. If omitted, hit highlighting of suggestions is disabled.
    :vartype highlight_post_tag: str
    :ivar highlight_pre_tag: A string tag that is prepended to hit highlights. Must be set with
     highlightPostTag. If omitted, hit highlighting of suggestions is disabled.
    :vartype highlight_pre_tag: str
    :ivar minimum_coverage: A number between 0 and 100 indicating the percentage of the index that
     must be covered by a suggestions query in order for the query to be reported as a success. This
     parameter can be useful for ensuring search availability even for services with only one
     replica. The default is 80.
    :vartype minimum_coverage: float
    :ivar order_by: The list of OData $orderby expressions by which to sort the results. Each
     expression can be either a field name or a call to either the geo.distance() or the
     search.score() functions. Each expression can be followed by asc to indicate ascending, or desc
     to indicate descending. The default is ascending order. Ties will be broken by the match scores
     of documents. If no $orderby is specified, the default sort order is descending by document
     match score. There can be at most 32 $orderby clauses.
    :vartype order_by: list[str]
    :ivar search_fields: The list of field names to search for the specified search text. Target
     fields must be included in the specified suggester.
    :vartype search_fields: list[str]
    :ivar select: The list of fields to retrieve. If unspecified, only the key field will be
     included in the results.
    :vartype select: list[str]
    :ivar top: The number of suggestions to retrieve. The value must be a number between 1 and 100.
     The default is 5.
    :vartype top: int
    """

    _attribute_map = {
        "filter": {"key": "$filter", "type": "str"},
        "use_fuzzy_matching": {"key": "UseFuzzyMatching", "type": "bool"},
        "highlight_post_tag": {"key": "highlightPostTag", "type": "str"},
        "highlight_pre_tag": {"key": "highlightPreTag", "type": "str"},
        "minimum_coverage": {"key": "minimumCoverage", "type": "float"},
        "order_by": {"key": "OrderBy", "type": "[str]"},
        "search_fields": {"key": "searchFields", "type": "[str]"},
        "select": {"key": "$select", "type": "[str]"},
        "top": {"key": "$top", "type": "int"},
    }

    def __init__(
        self,
        *,
        filter: Optional[str] = None,  # pylint: disable=redefined-builtin
        use_fuzzy_matching: Optional[bool] = None,
        highlight_post_tag: Optional[str] = None,
        highlight_pre_tag: Optional[str] = None,
        minimum_coverage: Optional[float] = None,
        order_by: Optional[List[str]] = None,
        search_fields: Optional[List[str]] = None,
        select: Optional[List[str]] = None,
        top: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword filter: An OData expression that filters the documents considered for suggestions.
        :paramtype filter: str
        :keyword use_fuzzy_matching: A value indicating whether to use fuzzy matching for the
         suggestions query. Default is false. When set to true, the query will find terms even if
         there's a substituted or missing character in the search text. While this provides a better
         experience in some scenarios, it comes at a performance cost as fuzzy suggestions queries are
         slower and consume more resources.
        :paramtype use_fuzzy_matching: bool
        :keyword highlight_post_tag: A string tag that is appended to hit highlights. Must be set with
         highlightPreTag. If omitted, hit highlighting of suggestions is disabled.
        :paramtype highlight_post_tag: str
        :keyword highlight_pre_tag: A string tag that is prepended to hit highlights. Must be set with
         highlightPostTag. If omitted, hit highlighting of suggestions is disabled.
        :paramtype highlight_pre_tag: str
        :keyword minimum_coverage: A number between 0 and 100 indicating the percentage of the index
         that must be covered by a suggestions query in order for the query to be reported as a success.
         This parameter can be useful for ensuring search availability even for services with only one
         replica. The default is 80.
        :paramtype minimum_coverage: float
        :keyword order_by: The list of OData $orderby expressions by which to sort the results. Each
         expression can be either a field name or a call to either the geo.distance() or the
         search.score() functions. Each expression can be followed by asc to indicate ascending, or desc
         to indicate descending. The default is ascending order. Ties will be broken by the match scores
         of documents. If no $orderby is specified, the default sort order is descending by document
         match score. There can be at most 32 $orderby clauses.
        :paramtype order_by: list[str]
        :keyword search_fields: The list of field names to search for the specified search text. Target
         fields must be included in the specified suggester.
        :paramtype search_fields: list[str]
        :keyword select: The list of fields to retrieve. If unspecified, only the key field will be
         included in the results.
        :paramtype select: list[str]
        :keyword top: The number of suggestions to retrieve. The value must be a number between 1 and
         100. The default is 5.
        :paramtype top: int
        """
        super().__init__(**kwargs)
        self.filter = filter
        self.use_fuzzy_matching = use_fuzzy_matching
        self.highlight_post_tag = highlight_post_tag
        self.highlight_pre_tag = highlight_pre_tag
        self.minimum_coverage = minimum_coverage
        self.order_by = order_by
        self.search_fields = search_fields
        self.select = select
        self.top = top


class SuggestRequest(_serialization.Model):  # pylint: disable=too-many-instance-attributes
    """Parameters for filtering, sorting, fuzzy matching, and other suggestions query behaviors.

    All required parameters must be populated in order to send to server.

    :ivar filter: An OData expression that filters the documents considered for suggestions.
    :vartype filter: str
    :ivar use_fuzzy_matching: A value indicating whether to use fuzzy matching for the suggestion
     query. Default is false. When set to true, the query will find suggestions even if there's a
     substituted or missing character in the search text. While this provides a better experience in
     some scenarios, it comes at a performance cost as fuzzy suggestion searches are slower and
     consume more resources.
    :vartype use_fuzzy_matching: bool
    :ivar highlight_post_tag: A string tag that is appended to hit highlights. Must be set with
     highlightPreTag. If omitted, hit highlighting of suggestions is disabled.
    :vartype highlight_post_tag: str
    :ivar highlight_pre_tag: A string tag that is prepended to hit highlights. Must be set with
     highlightPostTag. If omitted, hit highlighting of suggestions is disabled.
    :vartype highlight_pre_tag: str
    :ivar minimum_coverage: A number between 0 and 100 indicating the percentage of the index that
     must be covered by a suggestion query in order for the query to be reported as a success. This
     parameter can be useful for ensuring search availability even for services with only one
     replica. The default is 80.
    :vartype minimum_coverage: float
    :ivar order_by: The comma-separated list of OData $orderby expressions by which to sort the
     results. Each expression can be either a field name or a call to either the geo.distance() or
     the search.score() functions. Each expression can be followed by asc to indicate ascending, or
     desc to indicate descending. The default is ascending order. Ties will be broken by the match
     scores of documents. If no $orderby is specified, the default sort order is descending by
     document match score. There can be at most 32 $orderby clauses.
    :vartype order_by: str
    :ivar search_text: The search text to use to suggest documents. Must be at least 1 character,
     and no more than 100 characters. Required.
    :vartype search_text: str
    :ivar search_fields: The comma-separated list of field names to search for the specified search
     text. Target fields must be included in the specified suggester.
    :vartype search_fields: str
    :ivar select: The comma-separated list of fields to retrieve. If unspecified, only the key
     field will be included in the results.
    :vartype select: str
    :ivar suggester_name: The name of the suggester as specified in the suggesters collection
     that's part of the index definition. Required.
    :vartype suggester_name: str
    :ivar top: The number of suggestions to retrieve. This must be a value between 1 and 100. The
     default is 5.
    :vartype top: int
    """

    _validation = {
        "search_text": {"required": True},
        "suggester_name": {"required": True},
    }

    _attribute_map = {
        "filter": {"key": "filter", "type": "str"},
        "use_fuzzy_matching": {"key": "fuzzy", "type": "bool"},
        "highlight_post_tag": {"key": "highlightPostTag", "type": "str"},
        "highlight_pre_tag": {"key": "highlightPreTag", "type": "str"},
        "minimum_coverage": {"key": "minimumCoverage", "type": "float"},
        "order_by": {"key": "orderby", "type": "str"},
        "search_text": {"key": "search", "type": "str"},
        "search_fields": {"key": "searchFields", "type": "str"},
        "select": {"key": "select", "type": "str"},
        "suggester_name": {"key": "suggesterName", "type": "str"},
        "top": {"key": "top", "type": "int"},
    }

    def __init__(
        self,
        *,
        search_text: str,
        suggester_name: str,
        filter: Optional[str] = None,  # pylint: disable=redefined-builtin
        use_fuzzy_matching: Optional[bool] = None,
        highlight_post_tag: Optional[str] = None,
        highlight_pre_tag: Optional[str] = None,
        minimum_coverage: Optional[float] = None,
        order_by: Optional[str] = None,
        search_fields: Optional[str] = None,
        select: Optional[str] = None,
        top: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword filter: An OData expression that filters the documents considered for suggestions.
        :paramtype filter: str
        :keyword use_fuzzy_matching: A value indicating whether to use fuzzy matching for the
         suggestion query. Default is false. When set to true, the query will find suggestions even if
         there's a substituted or missing character in the search text. While this provides a better
         experience in some scenarios, it comes at a performance cost as fuzzy suggestion searches are
         slower and consume more resources.
        :paramtype use_fuzzy_matching: bool
        :keyword highlight_post_tag: A string tag that is appended to hit highlights. Must be set with
         highlightPreTag. If omitted, hit highlighting of suggestions is disabled.
        :paramtype highlight_post_tag: str
        :keyword highlight_pre_tag: A string tag that is prepended to hit highlights. Must be set with
         highlightPostTag. If omitted, hit highlighting of suggestions is disabled.
        :paramtype highlight_pre_tag: str
        :keyword minimum_coverage: A number between 0 and 100 indicating the percentage of the index
         that must be covered by a suggestion query in order for the query to be reported as a success.
         This parameter can be useful for ensuring search availability even for services with only one
         replica. The default is 80.
        :paramtype minimum_coverage: float
        :keyword order_by: The comma-separated list of OData $orderby expressions by which to sort the
         results. Each expression can be either a field name or a call to either the geo.distance() or
         the search.score() functions. Each expression can be followed by asc to indicate ascending, or
         desc to indicate descending. The default is ascending order. Ties will be broken by the match
         scores of documents. If no $orderby is specified, the default sort order is descending by
         document match score. There can be at most 32 $orderby clauses.
        :paramtype order_by: str
        :keyword search_text: The search text to use to suggest documents. Must be at least 1
         character, and no more than 100 characters. Required.
        :paramtype search_text: str
        :keyword search_fields: The comma-separated list of field names to search for the specified
         search text. Target fields must be included in the specified suggester.
        :paramtype search_fields: str
        :keyword select: The comma-separated list of fields to retrieve. If unspecified, only the key
         field will be included in the results.
        :paramtype select: str
        :keyword suggester_name: The name of the suggester as specified in the suggesters collection
         that's part of the index definition. Required.
        :paramtype suggester_name: str
        :keyword top: The number of suggestions to retrieve. This must be a value between 1 and 100.
         The default is 5.
        :paramtype top: int
        """
        super().__init__(**kwargs)
        self.filter = filter
        self.use_fuzzy_matching = use_fuzzy_matching
        self.highlight_post_tag = highlight_post_tag
        self.highlight_pre_tag = highlight_pre_tag
        self.minimum_coverage = minimum_coverage
        self.order_by = order_by
        self.search_text = search_text
        self.search_fields = search_fields
        self.select = select
        self.suggester_name = suggester_name
        self.top = top


class SuggestResult(_serialization.Model):
    """A result containing a document found by a suggestion query, plus associated metadata.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar additional_properties: Unmatched properties from the message are deserialized to this
     collection.
    :vartype additional_properties: dict[str, any]
    :ivar text: The text of the suggestion result. Required.
    :vartype text: str
    """

    _validation = {
        "text": {"required": True, "readonly": True},
    }

    _attribute_map = {
        "additional_properties": {"key": "", "type": "{object}"},
        "text": {"key": "@search\\.text", "type": "str"},
    }

    def __init__(self, *, additional_properties: Optional[Dict[str, Any]] = None, **kwargs: Any) -> None:
        """
        :keyword additional_properties: Unmatched properties from the message are deserialized to this
         collection.
        :paramtype additional_properties: dict[str, any]
        """
        super().__init__(**kwargs)
        self.additional_properties = additional_properties
        self.text = None


class VectorQuery(_serialization.Model):
    """The query parameters for vector and hybrid search queries.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    VectorizableTextQuery, VectorizedQuery

    All required parameters must be populated in order to send to server.

    :ivar kind: The kind of vector query being performed. Required. Known values are: "vector" and
     "text".
    :vartype kind: str or ~azure.search.documents.models.VectorQueryKind
    :ivar k_nearest_neighbors: Number of nearest neighbors to return as top hits.
    :vartype k_nearest_neighbors: int
    :ivar fields: Vector Fields of type Collection(Edm.Single) to be included in the vector
     searched.
    :vartype fields: str
    :ivar exhaustive: When true, triggers an exhaustive k-nearest neighbor search across all
     vectors within the vector index. Useful for scenarios where exact matches are critical, such as
     determining ground truth values.
    :vartype exhaustive: bool
    :ivar oversampling: Oversampling factor. Minimum value is 1. It overrides the
     'defaultOversampling' parameter configured in the index definition. It can be set only when
     'rerankWithOriginalVectors' is true. This parameter is only permitted when a compression method
     is used on the underlying vector field.
    :vartype oversampling: float
    :ivar weight: Relative weight of the vector query when compared to other vector query and/or
     the text query within the same search request. This value is used when combining the results of
     multiple ranking lists produced by the different vector queries and/or the results retrieved
     through the text query. The higher the weight, the higher the documents that matched that query
     will be in the final ranking. Default is 1.0 and the value needs to be a positive number larger
     than zero.
    :vartype weight: float
    """

    _validation = {
        "kind": {"required": True},
    }

    _attribute_map = {
        "kind": {"key": "kind", "type": "str"},
        "k_nearest_neighbors": {"key": "k", "type": "int"},
        "fields": {"key": "fields", "type": "str"},
        "exhaustive": {"key": "exhaustive", "type": "bool"},
        "oversampling": {"key": "oversampling", "type": "float"},
        "weight": {"key": "weight", "type": "float"},
    }

    _subtype_map = {"kind": {"text": "VectorizableTextQuery", "vector": "VectorizedQuery"}}

    def __init__(
        self,
        *,
        k_nearest_neighbors: Optional[int] = None,
        fields: Optional[str] = None,
        exhaustive: Optional[bool] = None,
        oversampling: Optional[float] = None,
        weight: Optional[float] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword k_nearest_neighbors: Number of nearest neighbors to return as top hits.
        :paramtype k_nearest_neighbors: int
        :keyword fields: Vector Fields of type Collection(Edm.Single) to be included in the vector
         searched.
        :paramtype fields: str
        :keyword exhaustive: When true, triggers an exhaustive k-nearest neighbor search across all
         vectors within the vector index. Useful for scenarios where exact matches are critical, such as
         determining ground truth values.
        :paramtype exhaustive: bool
        :keyword oversampling: Oversampling factor. Minimum value is 1. It overrides the
         'defaultOversampling' parameter configured in the index definition. It can be set only when
         'rerankWithOriginalVectors' is true. This parameter is only permitted when a compression method
         is used on the underlying vector field.
        :paramtype oversampling: float
        :keyword weight: Relative weight of the vector query when compared to other vector query and/or
         the text query within the same search request. This value is used when combining the results of
         multiple ranking lists produced by the different vector queries and/or the results retrieved
         through the text query. The higher the weight, the higher the documents that matched that query
         will be in the final ranking. Default is 1.0 and the value needs to be a positive number larger
         than zero.
        :paramtype weight: float
        """
        super().__init__(**kwargs)
        self.kind: Optional[str] = None
        self.k_nearest_neighbors = k_nearest_neighbors
        self.fields = fields
        self.exhaustive = exhaustive
        self.oversampling = oversampling
        self.weight = weight


class VectorizableTextQuery(VectorQuery):
    """The query parameters to use for vector search when a text value that needs to be vectorized is
    provided.

    All required parameters must be populated in order to send to server.

    :ivar kind: The kind of vector query being performed. Required. Known values are: "vector" and
     "text".
    :vartype kind: str or ~azure.search.documents.models.VectorQueryKind
    :ivar k_nearest_neighbors: Number of nearest neighbors to return as top hits.
    :vartype k_nearest_neighbors: int
    :ivar fields: Vector Fields of type Collection(Edm.Single) to be included in the vector
     searched.
    :vartype fields: str
    :ivar exhaustive: When true, triggers an exhaustive k-nearest neighbor search across all
     vectors within the vector index. Useful for scenarios where exact matches are critical, such as
     determining ground truth values.
    :vartype exhaustive: bool
    :ivar oversampling: Oversampling factor. Minimum value is 1. It overrides the
     'defaultOversampling' parameter configured in the index definition. It can be set only when
     'rerankWithOriginalVectors' is true. This parameter is only permitted when a compression method
     is used on the underlying vector field.
    :vartype oversampling: float
    :ivar weight: Relative weight of the vector query when compared to other vector query and/or
     the text query within the same search request. This value is used when combining the results of
     multiple ranking lists produced by the different vector queries and/or the results retrieved
     through the text query. The higher the weight, the higher the documents that matched that query
     will be in the final ranking. Default is 1.0 and the value needs to be a positive number larger
     than zero.
    :vartype weight: float
    :ivar text: The text to be vectorized to perform a vector search query. Required.
    :vartype text: str
    """

    _validation = {
        "kind": {"required": True},
        "text": {"required": True},
    }

    _attribute_map = {
        "kind": {"key": "kind", "type": "str"},
        "k_nearest_neighbors": {"key": "k", "type": "int"},
        "fields": {"key": "fields", "type": "str"},
        "exhaustive": {"key": "exhaustive", "type": "bool"},
        "oversampling": {"key": "oversampling", "type": "float"},
        "weight": {"key": "weight", "type": "float"},
        "text": {"key": "text", "type": "str"},
    }

    def __init__(
        self,
        *,
        text: str,
        k_nearest_neighbors: Optional[int] = None,
        fields: Optional[str] = None,
        exhaustive: Optional[bool] = None,
        oversampling: Optional[float] = None,
        weight: Optional[float] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword k_nearest_neighbors: Number of nearest neighbors to return as top hits.
        :paramtype k_nearest_neighbors: int
        :keyword fields: Vector Fields of type Collection(Edm.Single) to be included in the vector
         searched.
        :paramtype fields: str
        :keyword exhaustive: When true, triggers an exhaustive k-nearest neighbor search across all
         vectors within the vector index. Useful for scenarios where exact matches are critical, such as
         determining ground truth values.
        :paramtype exhaustive: bool
        :keyword oversampling: Oversampling factor. Minimum value is 1. It overrides the
         'defaultOversampling' parameter configured in the index definition. It can be set only when
         'rerankWithOriginalVectors' is true. This parameter is only permitted when a compression method
         is used on the underlying vector field.
        :paramtype oversampling: float
        :keyword weight: Relative weight of the vector query when compared to other vector query and/or
         the text query within the same search request. This value is used when combining the results of
         multiple ranking lists produced by the different vector queries and/or the results retrieved
         through the text query. The higher the weight, the higher the documents that matched that query
         will be in the final ranking. Default is 1.0 and the value needs to be a positive number larger
         than zero.
        :paramtype weight: float
        :keyword text: The text to be vectorized to perform a vector search query. Required.
        :paramtype text: str
        """
        super().__init__(
            k_nearest_neighbors=k_nearest_neighbors,
            fields=fields,
            exhaustive=exhaustive,
            oversampling=oversampling,
            weight=weight,
            **kwargs
        )
        self.kind: str = "text"
        self.text = text


class VectorizedQuery(VectorQuery):
    """The query parameters to use for vector search when a raw vector value is provided.

    All required parameters must be populated in order to send to server.

    :ivar kind: The kind of vector query being performed. Required. Known values are: "vector" and
     "text".
    :vartype kind: str or ~azure.search.documents.models.VectorQueryKind
    :ivar k_nearest_neighbors: Number of nearest neighbors to return as top hits.
    :vartype k_nearest_neighbors: int
    :ivar fields: Vector Fields of type Collection(Edm.Single) to be included in the vector
     searched.
    :vartype fields: str
    :ivar exhaustive: When true, triggers an exhaustive k-nearest neighbor search across all
     vectors within the vector index. Useful for scenarios where exact matches are critical, such as
     determining ground truth values.
    :vartype exhaustive: bool
    :ivar oversampling: Oversampling factor. Minimum value is 1. It overrides the
     'defaultOversampling' parameter configured in the index definition. It can be set only when
     'rerankWithOriginalVectors' is true. This parameter is only permitted when a compression method
     is used on the underlying vector field.
    :vartype oversampling: float
    :ivar weight: Relative weight of the vector query when compared to other vector query and/or
     the text query within the same search request. This value is used when combining the results of
     multiple ranking lists produced by the different vector queries and/or the results retrieved
     through the text query. The higher the weight, the higher the documents that matched that query
     will be in the final ranking. Default is 1.0 and the value needs to be a positive number larger
     than zero.
    :vartype weight: float
    :ivar vector: The vector representation of a search query. Required.
    :vartype vector: list[float]
    """

    _validation = {
        "kind": {"required": True},
        "vector": {"required": True},
    }

    _attribute_map = {
        "kind": {"key": "kind", "type": "str"},
        "k_nearest_neighbors": {"key": "k", "type": "int"},
        "fields": {"key": "fields", "type": "str"},
        "exhaustive": {"key": "exhaustive", "type": "bool"},
        "oversampling": {"key": "oversampling", "type": "float"},
        "weight": {"key": "weight", "type": "float"},
        "vector": {"key": "vector", "type": "[float]"},
    }

    def __init__(
        self,
        *,
        vector: List[float],
        k_nearest_neighbors: Optional[int] = None,
        fields: Optional[str] = None,
        exhaustive: Optional[bool] = None,
        oversampling: Optional[float] = None,
        weight: Optional[float] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword k_nearest_neighbors: Number of nearest neighbors to return as top hits.
        :paramtype k_nearest_neighbors: int
        :keyword fields: Vector Fields of type Collection(Edm.Single) to be included in the vector
         searched.
        :paramtype fields: str
        :keyword exhaustive: When true, triggers an exhaustive k-nearest neighbor search across all
         vectors within the vector index. Useful for scenarios where exact matches are critical, such as
         determining ground truth values.
        :paramtype exhaustive: bool
        :keyword oversampling: Oversampling factor. Minimum value is 1. It overrides the
         'defaultOversampling' parameter configured in the index definition. It can be set only when
         'rerankWithOriginalVectors' is true. This parameter is only permitted when a compression method
         is used on the underlying vector field.
        :paramtype oversampling: float
        :keyword weight: Relative weight of the vector query when compared to other vector query and/or
         the text query within the same search request. This value is used when combining the results of
         multiple ranking lists produced by the different vector queries and/or the results retrieved
         through the text query. The higher the weight, the higher the documents that matched that query
         will be in the final ranking. Default is 1.0 and the value needs to be a positive number larger
         than zero.
        :paramtype weight: float
        :keyword vector: The vector representation of a search query. Required.
        :paramtype vector: list[float]
        """
        super().__init__(
            k_nearest_neighbors=k_nearest_neighbors,
            fields=fields,
            exhaustive=exhaustive,
            oversampling=oversampling,
            weight=weight,
            **kwargs
        )
        self.kind: str = "vector"
        self.vector = vector
