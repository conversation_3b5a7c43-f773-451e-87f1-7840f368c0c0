# coding=utf-8
# --------------------------------------------------------------------------
# Code generated by Microsoft (R) AutoRest Code Generator (autorest: 3.10.2, generator: @autorest/python@6.15.0)
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

from copy import deepcopy
from typing import Any, Awaitable
from typing_extensions import Self

from azure.core import AsyncPipelineClient
from azure.core.pipeline import policies
from azure.core.rest import AsyncHttpResponse, HttpRequest

from .. import models as _models
from .._serialization import Deserializer, Serializer
from ._configuration import SearchServiceClientConfiguration
from .operations import (
    DataSourcesOperations,
    IndexersOperations,
    IndexesOperations,
    SearchServiceClientOperationsMixin,
    SkillsetsOperations,
    SynonymMapsOperations,
)


class SearchServiceClient(SearchServiceClientOperationsMixin):  # pylint: disable=client-accepts-api-version-keyword
    """Client that can be used to manage and query indexes and documents, as well as manage other
    resources, on a search service.

    :ivar data_sources: DataSourcesOperations operations
    :vartype data_sources: azure.search.documents.indexes.aio.operations.DataSourcesOperations
    :ivar indexers: IndexersOperations operations
    :vartype indexers: azure.search.documents.indexes.aio.operations.IndexersOperations
    :ivar skillsets: SkillsetsOperations operations
    :vartype skillsets: azure.search.documents.indexes.aio.operations.SkillsetsOperations
    :ivar synonym_maps: SynonymMapsOperations operations
    :vartype synonym_maps: azure.search.documents.indexes.aio.operations.SynonymMapsOperations
    :ivar indexes: IndexesOperations operations
    :vartype indexes: azure.search.documents.indexes.aio.operations.IndexesOperations
    :param endpoint: The endpoint URL of the search service. Required.
    :type endpoint: str
    :keyword api_version: Api Version. Default value is "2024-07-01". Note that overriding this
     default value may result in unsupported behavior.
    :paramtype api_version: str
    """

    def __init__(  # pylint: disable=missing-client-constructor-parameter-credential
        self, endpoint: str, **kwargs: Any
    ) -> None:
        _endpoint = "{endpoint}"
        self._config = SearchServiceClientConfiguration(endpoint=endpoint, **kwargs)
        _policies = kwargs.pop("policies", None)
        if _policies is None:
            _policies = [
                policies.RequestIdPolicy(**kwargs),
                self._config.headers_policy,
                self._config.user_agent_policy,
                self._config.proxy_policy,
                policies.ContentDecodePolicy(**kwargs),
                self._config.redirect_policy,
                self._config.retry_policy,
                self._config.authentication_policy,
                self._config.custom_hook_policy,
                self._config.logging_policy,
                policies.DistributedTracingPolicy(**kwargs),
                policies.SensitiveHeaderCleanupPolicy(**kwargs) if self._config.redirect_policy else None,
                self._config.http_logging_policy,
            ]
        self._client: AsyncPipelineClient = AsyncPipelineClient(base_url=_endpoint, policies=_policies, **kwargs)

        client_models = {k: v for k, v in _models.__dict__.items() if isinstance(v, type)}
        self._serialize = Serializer(client_models)
        self._deserialize = Deserializer(client_models)
        self._serialize.client_side_validation = False
        self.data_sources = DataSourcesOperations(self._client, self._config, self._serialize, self._deserialize)
        self.indexers = IndexersOperations(self._client, self._config, self._serialize, self._deserialize)
        self.skillsets = SkillsetsOperations(self._client, self._config, self._serialize, self._deserialize)
        self.synonym_maps = SynonymMapsOperations(self._client, self._config, self._serialize, self._deserialize)
        self.indexes = IndexesOperations(self._client, self._config, self._serialize, self._deserialize)

    def _send_request(
        self, request: HttpRequest, *, stream: bool = False, **kwargs: Any
    ) -> Awaitable[AsyncHttpResponse]:
        """Runs the network request through the client's chained policies.

        >>> from azure.core.rest import HttpRequest
        >>> request = HttpRequest("GET", "https://www.example.org/")
        <HttpRequest [GET], url: 'https://www.example.org/'>
        >>> response = await client._send_request(request)
        <AsyncHttpResponse: 200 OK>

        For more information on this code flow, see https://aka.ms/azsdk/dpcodegen/python/send_request

        :param request: The network request you want to make. Required.
        :type request: ~azure.core.rest.HttpRequest
        :keyword bool stream: Whether the response payload will be streamed. Defaults to False.
        :return: The response of your network call. Does not do error handling on your response.
        :rtype: ~azure.core.rest.AsyncHttpResponse
        """

        request_copy = deepcopy(request)
        path_format_arguments = {
            "endpoint": self._serialize.url("self._config.endpoint", self._config.endpoint, "str", skip_quote=True),
        }

        request_copy.url = self._client.format_url(request_copy.url, **path_format_arguments)
        return self._client.send_request(request_copy, stream=stream, **kwargs)  # type: ignore

    async def close(self) -> None:
        await self._client.close()

    async def __aenter__(self) -> Self:
        await self._client.__aenter__()
        return self

    async def __aexit__(self, *exc_details: Any) -> None:
        await self._client.__aexit__(*exc_details)
