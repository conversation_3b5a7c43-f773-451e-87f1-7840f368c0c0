# pylint: disable=line-too-long,useless-suppression,too-many-lines
# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) Python Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------
# pylint: disable=useless-super-delegation

from typing import Any, Dict, List, Literal, Mapping, Optional, TYPE_CHECKING, Union, overload

from .._utils.model_base import Model as _Model, rest_discriminator, rest_field
from ._enums import CredentialType, DatasetType, DeploymentType, IndexType, PendingUploadType

if TYPE_CHECKING:
    from .. import models as _models


class AgentEvaluation(_Model):
    """Evaluation response for agent evaluation run.

    :ivar id: Identifier of the agent evaluation run. Required.
    :vartype id: str
    :ivar status: Status of the agent evaluation. Options: Running, Completed, Failed. Required.
    :vartype status: str
    :ivar error: The reason of the request failure for the long running process, if applicable.
    :vartype error: str
    :ivar result: The agent evaluation result.
    :vartype result: list[~azure.ai.projects.models.AgentEvaluationResult]
    """

    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Identifier of the agent evaluation run. Required."""
    status: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Status of the agent evaluation. Options: Running, Completed, Failed. Required."""
    error: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The reason of the request failure for the long running process, if applicable."""
    result: Optional[List["_models.AgentEvaluationResult"]] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The agent evaluation result."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        status: str,
        error: Optional[str] = None,
        result: Optional[List["_models.AgentEvaluationResult"]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AgentEvaluationRedactionConfiguration(_Model):
    """The redaction configuration will allow the user to control what is redacted.

    :ivar redact_score_properties: Redact score properties. If not specified, the default is to
     redact in production.
    :vartype redact_score_properties: bool
    """

    redact_score_properties: Optional[bool] = rest_field(
        name="redactScoreProperties", visibility=["read", "create", "update", "delete", "query"]
    )
    """Redact score properties. If not specified, the default is to redact in production."""

    @overload
    def __init__(
        self,
        *,
        redact_score_properties: Optional[bool] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AgentEvaluationRequest(_Model):
    """Evaluation request for agent run.

    :ivar run_id: Identifier of the agent run. Required.
    :vartype run_id: str
    :ivar thread_id: Identifier of the agent thread. This field is mandatory currently, but it will
     be optional in the future.
    :vartype thread_id: str
    :ivar evaluators: Evaluators to be used for the evaluation. Required.
    :vartype evaluators: dict[str, ~azure.ai.projects.models.EvaluatorConfiguration]
    :ivar sampling_configuration: Sampling configuration for the evaluation.
    :vartype sampling_configuration: ~azure.ai.projects.models.AgentEvaluationSamplingConfiguration
    :ivar redaction_configuration: Redaction configuration for the evaluation.
    :vartype redaction_configuration:
     ~azure.ai.projects.models.AgentEvaluationRedactionConfiguration
    :ivar app_insights_connection_string: Pass the AppInsights connection string to the agent
     evaluation for the evaluation results and the errors logs. Required.
    :vartype app_insights_connection_string: str
    """

    run_id: str = rest_field(name="runId", visibility=["read", "create", "update", "delete", "query"])
    """Identifier of the agent run. Required."""
    thread_id: Optional[str] = rest_field(name="threadId", visibility=["read", "create", "update", "delete", "query"])
    """Identifier of the agent thread. This field is mandatory currently, but it will be optional in
     the future."""
    evaluators: Dict[str, "_models.EvaluatorConfiguration"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Evaluators to be used for the evaluation. Required."""
    sampling_configuration: Optional["_models.AgentEvaluationSamplingConfiguration"] = rest_field(
        name="samplingConfiguration", visibility=["read", "create", "update", "delete", "query"]
    )
    """Sampling configuration for the evaluation."""
    redaction_configuration: Optional["_models.AgentEvaluationRedactionConfiguration"] = rest_field(
        name="redactionConfiguration", visibility=["read", "create", "update", "delete", "query"]
    )
    """Redaction configuration for the evaluation."""
    app_insights_connection_string: str = rest_field(
        name="appInsightsConnectionString", visibility=["read", "create", "update", "delete", "query"]
    )
    """Pass the AppInsights connection string to the agent evaluation for the evaluation results and
     the errors logs. Required."""

    @overload
    def __init__(
        self,
        *,
        run_id: str,
        evaluators: Dict[str, "_models.EvaluatorConfiguration"],
        app_insights_connection_string: str,
        thread_id: Optional[str] = None,
        sampling_configuration: Optional["_models.AgentEvaluationSamplingConfiguration"] = None,
        redaction_configuration: Optional["_models.AgentEvaluationRedactionConfiguration"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AgentEvaluationResult(_Model):
    """Result for the agent evaluation evaluator run.

    :ivar evaluator: Evaluator's name. This is the name of the evaluator that was used to evaluate
     the agent's completion. Required.
    :vartype evaluator: str
    :ivar evaluator_id: Identifier of the evaluator. Required.
    :vartype evaluator_id: str
    :ivar score: Score of the given evaluator. No restriction on range. Required.
    :vartype score: float
    :ivar status: Status of the evaluator result. Options: Running, Completed, Failed,
     NotApplicable. Required.
    :vartype status: str
    :ivar reason: Reasoning for the evaluation result.
    :vartype reason: str
    :ivar version: Version of the evaluator that was used to evaluate the agent's completion.
    :vartype version: str
    :ivar thread_id: The unique identifier of the thread.
    :vartype thread_id: str
    :ivar run_id: The unique identifier of the run. Required.
    :vartype run_id: str
    :ivar error: A string explaining why there was an error, if applicable.
    :vartype error: str
    :ivar additional_details: Additional properties relevant to the evaluator. These will differ
     between evaluators.
    :vartype additional_details: dict[str, str]
    """

    evaluator: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Evaluator's name. This is the name of the evaluator that was used to evaluate the agent's
     completion. Required."""
    evaluator_id: str = rest_field(name="evaluatorId", visibility=["read", "create", "update", "delete", "query"])
    """Identifier of the evaluator. Required."""
    score: float = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Score of the given evaluator. No restriction on range. Required."""
    status: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Status of the evaluator result. Options: Running, Completed, Failed, NotApplicable. Required."""
    reason: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Reasoning for the evaluation result."""
    version: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Version of the evaluator that was used to evaluate the agent's completion."""
    thread_id: Optional[str] = rest_field(name="threadId", visibility=["read", "create", "update", "delete", "query"])
    """The unique identifier of the thread."""
    run_id: str = rest_field(name="runId", visibility=["read", "create", "update", "delete", "query"])
    """The unique identifier of the run. Required."""
    error: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A string explaining why there was an error, if applicable."""
    additional_details: Optional[Dict[str, str]] = rest_field(
        name="additionalDetails", visibility=["read", "create", "update", "delete", "query"]
    )
    """Additional properties relevant to the evaluator. These will differ between evaluators."""

    @overload
    def __init__(
        self,
        *,
        evaluator: str,
        evaluator_id: str,
        score: float,
        status: str,
        run_id: str,
        reason: Optional[str] = None,
        version: Optional[str] = None,
        thread_id: Optional[str] = None,
        error: Optional[str] = None,
        additional_details: Optional[Dict[str, str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AgentEvaluationSamplingConfiguration(_Model):
    """Definition for sampling strategy.

    :ivar name: Name of the sampling strategy. Required.
    :vartype name: str
    :ivar sampling_percent: Percentage of sampling per hour (0-100). Required.
    :vartype sampling_percent: float
    :ivar max_request_rate: Maximum request rate per hour (0 to 1000). Required.
    :vartype max_request_rate: float
    """

    name: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Name of the sampling strategy. Required."""
    sampling_percent: float = rest_field(
        name="samplingPercent", visibility=["read", "create", "update", "delete", "query"]
    )
    """Percentage of sampling per hour (0-100). Required."""
    max_request_rate: float = rest_field(
        name="maxRequestRate", visibility=["read", "create", "update", "delete", "query"]
    )
    """Maximum request rate per hour (0 to 1000). Required."""

    @overload
    def __init__(
        self,
        *,
        name: str,
        sampling_percent: float,
        max_request_rate: float,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class BaseCredentials(_Model):
    """A base class for connection credentials.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    EntraIDCredentials, ApiKeyCredentials, CustomCredential, NoAuthenticationCredentials,
    SASCredentials

    :ivar type: The type of credential used by the connection. Required. Known values are:
     "ApiKey", "AAD", "SAS", "CustomKeys", and "None".
    :vartype type: str or ~azure.ai.projects.models.CredentialType
    """

    __mapping__: Dict[str, _Model] = {}
    type: str = rest_discriminator(name="type", visibility=["read"])
    """The type of credential used by the connection. Required. Known values are: \"ApiKey\", \"AAD\",
     \"SAS\", \"CustomKeys\", and \"None\"."""

    @overload
    def __init__(
        self,
        *,
        type: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class ApiKeyCredentials(BaseCredentials, discriminator="ApiKey"):
    """API Key Credential definition.

    :ivar type: The credential type. Required. API Key credential
    :vartype type: str or ~azure.ai.projects.models.API_KEY
    :ivar api_key: API Key.
    :vartype api_key: str
    """

    type: Literal[CredentialType.API_KEY] = rest_discriminator(name="type", visibility=["read"])  # type: ignore
    """The credential type. Required. API Key credential"""
    api_key: Optional[str] = rest_field(name="key", visibility=["read"])
    """API Key."""

    @overload
    def __init__(
        self,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=CredentialType.API_KEY, **kwargs)


class AssetCredentialResponse(_Model):
    """Represents a reference to a blob for consumption.

    :ivar blob_reference: Credential info to access the storage account. Required.
    :vartype blob_reference: ~azure.ai.projects.models.BlobReference
    """

    blob_reference: "_models.BlobReference" = rest_field(
        name="blobReference", visibility=["read", "create", "update", "delete", "query"]
    )
    """Credential info to access the storage account. Required."""

    @overload
    def __init__(
        self,
        *,
        blob_reference: "_models.BlobReference",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class Index(_Model):
    """Index resource Definition.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    AzureAISearchIndex, CosmosDBIndex, ManagedAzureAISearchIndex

    :ivar type: Type of index. Required. Known values are: "AzureSearch",
     "CosmosDBNoSqlVectorStore", and "ManagedAzureSearch".
    :vartype type: str or ~azure.ai.projects.models.IndexType
    :ivar id: Asset ID, a unique identifier for the asset.
    :vartype id: str
    :ivar name: The name of the resource. Required.
    :vartype name: str
    :ivar version: The version of the resource. Required.
    :vartype version: str
    :ivar description: The asset description text.
    :vartype description: str
    :ivar tags: Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    """

    __mapping__: Dict[str, _Model] = {}
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """Type of index. Required. Known values are: \"AzureSearch\", \"CosmosDBNoSqlVectorStore\", and
     \"ManagedAzureSearch\"."""
    id: Optional[str] = rest_field(visibility=["read"])
    """Asset ID, a unique identifier for the asset."""
    name: str = rest_field(visibility=["read"])
    """The name of the resource. Required."""
    version: str = rest_field(visibility=["read"])
    """The version of the resource. Required."""
    description: Optional[str] = rest_field(visibility=["create", "update"])
    """The asset description text."""
    tags: Optional[Dict[str, str]] = rest_field(visibility=["create", "update"])
    """Tag dictionary. Tags can be added, removed, and updated."""

    @overload
    def __init__(
        self,
        *,
        type: str,
        description: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AzureAISearchIndex(Index, discriminator="AzureSearch"):
    """Azure AI Search Index Definition.

    :ivar id: Asset ID, a unique identifier for the asset.
    :vartype id: str
    :ivar name: The name of the resource. Required.
    :vartype name: str
    :ivar version: The version of the resource. Required.
    :vartype version: str
    :ivar description: The asset description text.
    :vartype description: str
    :ivar tags: Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar type: Type of index. Required. Azure search
    :vartype type: str or ~azure.ai.projects.models.AZURE_SEARCH
    :ivar connection_name: Name of connection to Azure AI Search. Required.
    :vartype connection_name: str
    :ivar index_name: Name of index in Azure AI Search resource to attach. Required.
    :vartype index_name: str
    :ivar field_mapping: Field mapping configuration.
    :vartype field_mapping: ~azure.ai.projects.models.FieldMapping
    """

    type: Literal[IndexType.AZURE_SEARCH] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """Type of index. Required. Azure search"""
    connection_name: str = rest_field(name="connectionName", visibility=["create"])
    """Name of connection to Azure AI Search. Required."""
    index_name: str = rest_field(name="indexName", visibility=["create"])
    """Name of index in Azure AI Search resource to attach. Required."""
    field_mapping: Optional["_models.FieldMapping"] = rest_field(name="fieldMapping", visibility=["create"])
    """Field mapping configuration."""

    @overload
    def __init__(
        self,
        *,
        connection_name: str,
        index_name: str,
        description: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
        field_mapping: Optional["_models.FieldMapping"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=IndexType.AZURE_SEARCH, **kwargs)


class TargetConfig(_Model):
    """Abstract class for target configuration.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    AzureOpenAIModelConfiguration

    :ivar type: Type of the model configuration. Required. Default value is None.
    :vartype type: str
    """

    __mapping__: Dict[str, _Model] = {}
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """Type of the model configuration. Required. Default value is None."""

    @overload
    def __init__(
        self,
        *,
        type: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AzureOpenAIModelConfiguration(TargetConfig, discriminator="AzureOpenAIModel"):
    """Azure OpenAI model configuration. The API version would be selected by the service for querying
    the model.

    :ivar type: Required. Default value is "AzureOpenAIModel".
    :vartype type: str
    :ivar model_deployment_name: Deployment name for AOAI model. Example: gpt-4o if in AIServices
     or connection based ``connection_name/deployment_name`` (i.e. ``my-aoai-connection/gpt-4o``.
     Required.
    :vartype model_deployment_name: str
    """

    type: Literal["AzureOpenAIModel"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """Required. Default value is \"AzureOpenAIModel\"."""
    model_deployment_name: str = rest_field(
        name="modelDeploymentName", visibility=["read", "create", "update", "delete", "query"]
    )
    """Deployment name for AOAI model. Example: gpt-4o if in AIServices or connection based
     ``connection_name/deployment_name`` (i.e. ``my-aoai-connection/gpt-4o``. Required."""

    @overload
    def __init__(
        self,
        *,
        model_deployment_name: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="AzureOpenAIModel", **kwargs)


class BlobReference(_Model):
    """Blob reference details.

    :ivar blob_uri: Blob URI path for client to upload data. Example:
     ``https://blob.windows.core.net/Container/Path``. Required.
    :vartype blob_uri: str
    :ivar storage_account_arm_id: ARM ID of the storage account to use. Required.
    :vartype storage_account_arm_id: str
    :ivar credential: Credential info to access the storage account. Required.
    :vartype credential: ~azure.ai.projects.models.SasCredential
    """

    blob_uri: str = rest_field(name="blobUri", visibility=["read", "create", "update", "delete", "query"])
    """Blob URI path for client to upload data. Example: ``https://blob.windows.core.net/Container/Path``. Required."""
    storage_account_arm_id: str = rest_field(
        name="storageAccountArmId", visibility=["read", "create", "update", "delete", "query"]
    )
    """ARM ID of the storage account to use. Required."""
    credential: "_models.SasCredential" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Credential info to access the storage account. Required."""

    @overload
    def __init__(
        self,
        *,
        blob_uri: str,
        storage_account_arm_id: str,
        credential: "_models.SasCredential",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class Connection(_Model):
    """Response from the list and get connections operations.

    :ivar name: The friendly name of the connection, provided by the user. Required.
    :vartype name: str
    :ivar id: A unique identifier for the connection, generated by the service. Required.
    :vartype id: str
    :ivar type: Category of the connection. Required. Known values are: "AzureOpenAI", "AzureBlob",
     "AzureStorageAccount", "CognitiveSearch", "CosmosDB", "ApiKey", "AppConfig", "AppInsights", and
     "CustomKeys".
    :vartype type: str or ~azure.ai.projects.models.ConnectionType
    :ivar target: The connection URL to be used for this service. Required.
    :vartype target: str
    :ivar is_default: Whether the connection is tagged as the default connection of its type.
     Required.
    :vartype is_default: bool
    :ivar credentials: The credentials used by the connection. Required.
    :vartype credentials: ~azure.ai.projects.models.BaseCredentials
    :ivar metadata: Metadata of the connection. Required.
    :vartype metadata: dict[str, str]
    """

    name: str = rest_field(visibility=["read"])
    """The friendly name of the connection, provided by the user. Required."""
    id: str = rest_field(visibility=["read"])
    """A unique identifier for the connection, generated by the service. Required."""
    type: Union[str, "_models.ConnectionType"] = rest_field(visibility=["read"])
    """Category of the connection. Required. Known values are: \"AzureOpenAI\", \"AzureBlob\",
     \"AzureStorageAccount\", \"CognitiveSearch\", \"CosmosDB\", \"ApiKey\", \"AppConfig\",
     \"AppInsights\", and \"CustomKeys\"."""
    target: str = rest_field(visibility=["read"])
    """The connection URL to be used for this service. Required."""
    is_default: bool = rest_field(name="isDefault", visibility=["read"])
    """Whether the connection is tagged as the default connection of its type. Required."""
    credentials: "_models.BaseCredentials" = rest_field(visibility=["read"])
    """The credentials used by the connection. Required."""
    metadata: Dict[str, str] = rest_field(visibility=["read"])
    """Metadata of the connection. Required."""


class CosmosDBIndex(Index, discriminator="CosmosDBNoSqlVectorStore"):
    """CosmosDB Vector Store Index Definition.

    :ivar id: Asset ID, a unique identifier for the asset.
    :vartype id: str
    :ivar name: The name of the resource. Required.
    :vartype name: str
    :ivar version: The version of the resource. Required.
    :vartype version: str
    :ivar description: The asset description text.
    :vartype description: str
    :ivar tags: Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar type: Type of index. Required. CosmosDB
    :vartype type: str or ~azure.ai.projects.models.COSMOS_DB
    :ivar connection_name: Name of connection to CosmosDB. Required.
    :vartype connection_name: str
    :ivar database_name: Name of the CosmosDB Database. Required.
    :vartype database_name: str
    :ivar container_name: Name of CosmosDB Container. Required.
    :vartype container_name: str
    :ivar embedding_configuration: Embedding model configuration. Required.
    :vartype embedding_configuration: ~azure.ai.projects.models.EmbeddingConfiguration
    :ivar field_mapping: Field mapping configuration. Required.
    :vartype field_mapping: ~azure.ai.projects.models.FieldMapping
    """

    type: Literal[IndexType.COSMOS_DB] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """Type of index. Required. CosmosDB"""
    connection_name: str = rest_field(name="connectionName", visibility=["create"])
    """Name of connection to CosmosDB. Required."""
    database_name: str = rest_field(name="databaseName", visibility=["create"])
    """Name of the CosmosDB Database. Required."""
    container_name: str = rest_field(name="containerName", visibility=["create"])
    """Name of CosmosDB Container. Required."""
    embedding_configuration: "_models.EmbeddingConfiguration" = rest_field(
        name="embeddingConfiguration", visibility=["create"]
    )
    """Embedding model configuration. Required."""
    field_mapping: "_models.FieldMapping" = rest_field(name="fieldMapping", visibility=["create"])
    """Field mapping configuration. Required."""

    @overload
    def __init__(
        self,
        *,
        connection_name: str,
        database_name: str,
        container_name: str,
        embedding_configuration: "_models.EmbeddingConfiguration",
        field_mapping: "_models.FieldMapping",
        description: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=IndexType.COSMOS_DB, **kwargs)


class CustomCredential(BaseCredentials, discriminator="CustomKeys"):
    """Custom credential definition.

    :ivar type: The credential type. Required. Custom credential
    :vartype type: str or ~azure.ai.projects.models.CUSTOM
    :ivar credential_keys: The credential type. Required.
    :vartype credential_keys: dict[str, str]
    """

    type: Literal[CredentialType.CUSTOM] = rest_discriminator(name="type", visibility=["read"])  # type: ignore
    """The credential type. Required. Custom credential"""
    credential_keys: Dict[str, str] = rest_field(name="keys", visibility=["read"])
    """The credential type. Required."""

    @overload
    def __init__(
        self,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=CredentialType.CUSTOM, **kwargs)


class DatasetVersion(_Model):
    """DatasetVersion Definition.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    FileDatasetVersion, FolderDatasetVersion

    :ivar data_uri: URI of the data. Example: ``https://go.microsoft.com/fwlink/?linkid=2202330``. Required.
    :vartype data_uri: str
    :ivar type: Dataset type. Required. Known values are: "uri_file" and "uri_folder".
    :vartype type: str or ~azure.ai.projects.models.DatasetType
    :ivar is_reference: Indicates if the dataset holds a reference to the storage, or the dataset
     manages storage itself. If true, the underlying data will not be deleted when the dataset
     version is deleted.
    :vartype is_reference: bool
    :ivar connection_name: The Azure Storage Account connection name. Required if
     startPendingUploadVersion was not called before creating the Dataset.
    :vartype connection_name: str
    :ivar id: Asset ID, a unique identifier for the asset.
    :vartype id: str
    :ivar name: The name of the resource. Required.
    :vartype name: str
    :ivar version: The version of the resource. Required.
    :vartype version: str
    :ivar description: The asset description text.
    :vartype description: str
    :ivar tags: Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    """

    __mapping__: Dict[str, _Model] = {}
    data_uri: str = rest_field(name="dataUri", visibility=["read", "create"])
    """URI of the data. Example: ``https://go.microsoft.com/fwlink/?linkid=2202330``. Required."""
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """Dataset type. Required. Known values are: \"uri_file\" and \"uri_folder\"."""
    is_reference: Optional[bool] = rest_field(name="isReference", visibility=["read"])
    """Indicates if the dataset holds a reference to the storage, or the dataset manages storage
     itself. If true, the underlying data will not be deleted when the dataset version is deleted."""
    connection_name: Optional[str] = rest_field(name="connectionName", visibility=["read", "create"])
    """The Azure Storage Account connection name. Required if startPendingUploadVersion was not called
     before creating the Dataset."""
    id: Optional[str] = rest_field(visibility=["read"])
    """Asset ID, a unique identifier for the asset."""
    name: str = rest_field(visibility=["read"])
    """The name of the resource. Required."""
    version: str = rest_field(visibility=["read"])
    """The version of the resource. Required."""
    description: Optional[str] = rest_field(visibility=["create", "update"])
    """The asset description text."""
    tags: Optional[Dict[str, str]] = rest_field(visibility=["create", "update"])
    """Tag dictionary. Tags can be added, removed, and updated."""

    @overload
    def __init__(
        self,
        *,
        data_uri: str,
        type: str,
        connection_name: Optional[str] = None,
        description: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class Deployment(_Model):
    """Model Deployment Definition.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    ModelDeployment

    :ivar type: The type of the deployment. Required. "ModelDeployment"
    :vartype type: str or ~azure.ai.projects.models.DeploymentType
    :ivar name: Name of the deployment. Required.
    :vartype name: str
    """

    __mapping__: Dict[str, _Model] = {}
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """The type of the deployment. Required. \"ModelDeployment\""""
    name: str = rest_field(visibility=["read"])
    """Name of the deployment. Required."""

    @overload
    def __init__(
        self,
        *,
        type: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class EmbeddingConfiguration(_Model):
    """Embedding configuration class.

    :ivar model_deployment_name: Deployment name of embedding model. It can point to a model
     deployment either in the parent AIServices or a connection. Required.
    :vartype model_deployment_name: str
    :ivar embedding_field: Embedding field. Required.
    :vartype embedding_field: str
    """

    model_deployment_name: str = rest_field(name="modelDeploymentName", visibility=["create"])
    """Deployment name of embedding model. It can point to a model deployment either in the parent
     AIServices or a connection. Required."""
    embedding_field: str = rest_field(name="embeddingField", visibility=["create"])
    """Embedding field. Required."""

    @overload
    def __init__(
        self,
        *,
        model_deployment_name: str,
        embedding_field: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class EntraIDCredentials(BaseCredentials, discriminator="AAD"):
    """Entra ID credential definition.

    :ivar type: The credential type. Required. Entra ID credential (formerly known as AAD)
    :vartype type: str or ~azure.ai.projects.models.ENTRA_ID
    """

    type: Literal[CredentialType.ENTRA_ID] = rest_discriminator(name="type", visibility=["read"])  # type: ignore
    """The credential type. Required. Entra ID credential (formerly known as AAD)"""

    @overload
    def __init__(
        self,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=CredentialType.ENTRA_ID, **kwargs)


class Evaluation(_Model):
    """Evaluation Definition.

    :ivar name: Identifier of the evaluation. Required.
    :vartype name: str
    :ivar data: Data for evaluation. Required.
    :vartype data: ~azure.ai.projects.models.InputData
    :ivar display_name: Display Name for evaluation. It helps to find the evaluation easily in AI
     Foundry. It does not need to be unique.
    :vartype display_name: str
    :ivar description: Description of the evaluation. It can be used to store additional
     information about the evaluation and is mutable.
    :vartype description: str
    :ivar status: Status of the evaluation. It is set by service and is read-only.
    :vartype status: str
    :ivar tags: Evaluation's tags. Unlike properties, tags are fully mutable.
    :vartype tags: dict[str, str]
    :ivar properties: Evaluation's properties. Unlike tags, properties are add-only. Once added, a
     property cannot be removed.
    :vartype properties: dict[str, str]
    :ivar evaluators: Evaluators to be used for the evaluation. Required.
    :vartype evaluators: dict[str, ~azure.ai.projects.models.EvaluatorConfiguration]
    """

    name: str = rest_field(name="id", visibility=["read"])
    """Identifier of the evaluation. Required."""
    data: "_models.InputData" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Data for evaluation. Required."""
    display_name: Optional[str] = rest_field(
        name="displayName", visibility=["read", "create", "update", "delete", "query"]
    )
    """Display Name for evaluation. It helps to find the evaluation easily in AI Foundry. It does not
     need to be unique."""
    description: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Description of the evaluation. It can be used to store additional information about the
     evaluation and is mutable."""
    status: Optional[str] = rest_field(visibility=["read"])
    """Status of the evaluation. It is set by service and is read-only."""
    tags: Optional[Dict[str, str]] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Evaluation's tags. Unlike properties, tags are fully mutable."""
    properties: Optional[Dict[str, str]] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Evaluation's properties. Unlike tags, properties are add-only. Once added, a property cannot be
     removed."""
    evaluators: Dict[str, "_models.EvaluatorConfiguration"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Evaluators to be used for the evaluation. Required."""

    @overload
    def __init__(
        self,
        *,
        data: "_models.InputData",
        evaluators: Dict[str, "_models.EvaluatorConfiguration"],
        display_name: Optional[str] = None,
        description: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
        properties: Optional[Dict[str, str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class EvaluatorConfiguration(_Model):
    """Evaluator Configuration.

    :ivar id: Identifier of the evaluator. Required.
    :vartype id: str
    :ivar init_params: Initialization parameters of the evaluator.
    :vartype init_params: dict[str, any]
    :ivar data_mapping: Data parameters of the evaluator.
    :vartype data_mapping: dict[str, str]
    """

    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Identifier of the evaluator. Required."""
    init_params: Optional[Dict[str, Any]] = rest_field(
        name="initParams", visibility=["read", "create", "update", "delete", "query"]
    )
    """Initialization parameters of the evaluator."""
    data_mapping: Optional[Dict[str, str]] = rest_field(
        name="dataMapping", visibility=["read", "create", "update", "delete", "query"]
    )
    """Data parameters of the evaluator."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        init_params: Optional[Dict[str, Any]] = None,
        data_mapping: Optional[Dict[str, str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class FieldMapping(_Model):
    """Field mapping configuration class.

    :ivar content_fields: List of fields with text content. Required.
    :vartype content_fields: list[str]
    :ivar filepath_field: Path of file to be used as a source of text content.
    :vartype filepath_field: str
    :ivar title_field: Field containing the title of the document.
    :vartype title_field: str
    :ivar url_field: Field containing the url of the document.
    :vartype url_field: str
    :ivar vector_fields: List of fields with vector content.
    :vartype vector_fields: list[str]
    :ivar metadata_fields: List of fields with metadata content.
    :vartype metadata_fields: list[str]
    """

    content_fields: List[str] = rest_field(name="contentFields", visibility=["create"])
    """List of fields with text content. Required."""
    filepath_field: Optional[str] = rest_field(name="filepathField", visibility=["create"])
    """Path of file to be used as a source of text content."""
    title_field: Optional[str] = rest_field(name="titleField", visibility=["create"])
    """Field containing the title of the document."""
    url_field: Optional[str] = rest_field(name="urlField", visibility=["create"])
    """Field containing the url of the document."""
    vector_fields: Optional[List[str]] = rest_field(name="vectorFields", visibility=["create"])
    """List of fields with vector content."""
    metadata_fields: Optional[List[str]] = rest_field(name="metadataFields", visibility=["create"])
    """List of fields with metadata content."""

    @overload
    def __init__(
        self,
        *,
        content_fields: List[str],
        filepath_field: Optional[str] = None,
        title_field: Optional[str] = None,
        url_field: Optional[str] = None,
        vector_fields: Optional[List[str]] = None,
        metadata_fields: Optional[List[str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class FileDatasetVersion(DatasetVersion, discriminator="uri_file"):
    """FileDatasetVersion Definition.

    :ivar data_uri: URI of the data. Example: ``https://go.microsoft.com/fwlink/?linkid=2202330``. Required.
    :vartype data_uri: str
    :ivar is_reference: Indicates if the dataset holds a reference to the storage, or the dataset
     manages storage itself. If true, the underlying data will not be deleted when the dataset
     version is deleted.
    :vartype is_reference: bool
    :ivar connection_name: The Azure Storage Account connection name. Required if
     startPendingUploadVersion was not called before creating the Dataset.
    :vartype connection_name: str
    :ivar id: Asset ID, a unique identifier for the asset.
    :vartype id: str
    :ivar name: The name of the resource. Required.
    :vartype name: str
    :ivar version: The version of the resource. Required.
    :vartype version: str
    :ivar description: The asset description text.
    :vartype description: str
    :ivar tags: Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar type: Dataset type. Required. URI file.
    :vartype type: str or ~azure.ai.projects.models.URI_FILE
    """

    type: Literal[DatasetType.URI_FILE] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """Dataset type. Required. URI file."""

    @overload
    def __init__(
        self,
        *,
        data_uri: str,
        connection_name: Optional[str] = None,
        description: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=DatasetType.URI_FILE, **kwargs)


class FolderDatasetVersion(DatasetVersion, discriminator="uri_folder"):
    """FileDatasetVersion Definition.

    :ivar data_uri: URI of the data. Example: ``https://go.microsoft.com/fwlink/?linkid=2202330``. Required.
    :vartype data_uri: str
    :ivar is_reference: Indicates if the dataset holds a reference to the storage, or the dataset
     manages storage itself. If true, the underlying data will not be deleted when the dataset
     version is deleted.
    :vartype is_reference: bool
    :ivar connection_name: The Azure Storage Account connection name. Required if
     startPendingUploadVersion was not called before creating the Dataset.
    :vartype connection_name: str
    :ivar id: Asset ID, a unique identifier for the asset.
    :vartype id: str
    :ivar name: The name of the resource. Required.
    :vartype name: str
    :ivar version: The version of the resource. Required.
    :vartype version: str
    :ivar description: The asset description text.
    :vartype description: str
    :ivar tags: Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar type: Dataset type. Required. URI folder.
    :vartype type: str or ~azure.ai.projects.models.URI_FOLDER
    """

    type: Literal[DatasetType.URI_FOLDER] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """Dataset type. Required. URI folder."""

    @overload
    def __init__(
        self,
        *,
        data_uri: str,
        connection_name: Optional[str] = None,
        description: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=DatasetType.URI_FOLDER, **kwargs)


class InputData(_Model):
    """Abstract data class.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    InputDataset

    :ivar type: Type of the data. Required. Default value is None.
    :vartype type: str
    """

    __mapping__: Dict[str, _Model] = {}
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """Type of the data. Required. Default value is None."""

    @overload
    def __init__(
        self,
        *,
        type: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class InputDataset(InputData, discriminator="dataset"):
    """Dataset as source for evaluation.

    :ivar type: Required. Default value is "dataset".
    :vartype type: str
    :ivar id: Evaluation input data. Required.
    :vartype id: str
    """

    type: Literal["dataset"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """Required. Default value is \"dataset\"."""
    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Evaluation input data. Required."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="dataset", **kwargs)


class ManagedAzureAISearchIndex(Index, discriminator="ManagedAzureSearch"):
    """Managed Azure AI Search Index Definition.

    :ivar id: Asset ID, a unique identifier for the asset.
    :vartype id: str
    :ivar name: The name of the resource. Required.
    :vartype name: str
    :ivar version: The version of the resource. Required.
    :vartype version: str
    :ivar description: The asset description text.
    :vartype description: str
    :ivar tags: Tag dictionary. Tags can be added, removed, and updated.
    :vartype tags: dict[str, str]
    :ivar type: Type of index. Required. Managed Azure Search
    :vartype type: str or ~azure.ai.projects.models.MANAGED_AZURE_SEARCH
    :ivar vector_store_id: Vector store id of managed index. Required.
    :vartype vector_store_id: str
    """

    type: Literal[IndexType.MANAGED_AZURE_SEARCH] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """Type of index. Required. Managed Azure Search"""
    vector_store_id: str = rest_field(name="vectorStoreId", visibility=["create"])
    """Vector store id of managed index. Required."""

    @overload
    def __init__(
        self,
        *,
        vector_store_id: str,
        description: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=IndexType.MANAGED_AZURE_SEARCH, **kwargs)


class ModelDeployment(Deployment, discriminator="ModelDeployment"):
    """Model Deployment Definition.

    :ivar name: Name of the deployment. Required.
    :vartype name: str
    :ivar type: The type of the deployment. Required. Model deployment
    :vartype type: str or ~azure.ai.projects.models.MODEL_DEPLOYMENT
    :ivar model_name: Publisher-specific name of the deployed model. Required.
    :vartype model_name: str
    :ivar model_version: Publisher-specific version of the deployed model. Required.
    :vartype model_version: str
    :ivar model_publisher: Name of the deployed model's publisher. Required.
    :vartype model_publisher: str
    :ivar capabilities: Capabilities of deployed model. Required.
    :vartype capabilities: dict[str, str]
    :ivar sku: Sku of the model deployment. Required.
    :vartype sku: ~azure.ai.projects.models.Sku
    :ivar connection_name: Name of the connection the deployment comes from.
    :vartype connection_name: str
    """

    type: Literal[DeploymentType.MODEL_DEPLOYMENT] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The type of the deployment. Required. Model deployment"""
    model_name: str = rest_field(name="modelName", visibility=["read"])
    """Publisher-specific name of the deployed model. Required."""
    model_version: str = rest_field(name="modelVersion", visibility=["read"])
    """Publisher-specific version of the deployed model. Required."""
    model_publisher: str = rest_field(name="modelPublisher", visibility=["read"])
    """Name of the deployed model's publisher. Required."""
    capabilities: Dict[str, str] = rest_field(visibility=["read"])
    """Capabilities of deployed model. Required."""
    sku: "_models.Sku" = rest_field(visibility=["read"])
    """Sku of the model deployment. Required."""
    connection_name: Optional[str] = rest_field(name="connectionName", visibility=["read"])
    """Name of the connection the deployment comes from."""

    @overload
    def __init__(
        self,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=DeploymentType.MODEL_DEPLOYMENT, **kwargs)


class NoAuthenticationCredentials(BaseCredentials, discriminator="None"):
    """Credentials that do not require authentication.

    :ivar type: The credential type. Required. No credential
    :vartype type: str or ~azure.ai.projects.models.NONE
    """

    type: Literal[CredentialType.NONE] = rest_discriminator(name="type", visibility=["read"])  # type: ignore
    """The credential type. Required. No credential"""

    @overload
    def __init__(
        self,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=CredentialType.NONE, **kwargs)


class PendingUploadRequest(_Model):
    """Represents a request for a pending upload.

    :ivar pending_upload_id: If PendingUploadId is not provided, a random GUID will be used.
    :vartype pending_upload_id: str
    :ivar connection_name: Azure Storage Account connection name to use for generating temporary
     SAS token.
    :vartype connection_name: str
    :ivar pending_upload_type: BlobReference is the only supported type. Required. Blob Reference
     is the only supported type.
    :vartype pending_upload_type: str or ~azure.ai.projects.models.BLOB_REFERENCE
    """

    pending_upload_id: Optional[str] = rest_field(
        name="pendingUploadId", visibility=["read", "create", "update", "delete", "query"]
    )
    """If PendingUploadId is not provided, a random GUID will be used."""
    connection_name: Optional[str] = rest_field(
        name="connectionName", visibility=["read", "create", "update", "delete", "query"]
    )
    """Azure Storage Account connection name to use for generating temporary SAS token."""
    pending_upload_type: Literal[PendingUploadType.BLOB_REFERENCE] = rest_field(
        name="pendingUploadType", visibility=["read", "create", "update", "delete", "query"]
    )
    """BlobReference is the only supported type. Required. Blob Reference is the only supported type."""

    @overload
    def __init__(
        self,
        *,
        pending_upload_type: Literal[PendingUploadType.BLOB_REFERENCE],
        pending_upload_id: Optional[str] = None,
        connection_name: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class PendingUploadResponse(_Model):
    """Represents the response for a pending upload request.

    :ivar blob_reference: Container-level read, write, list SAS. Required.
    :vartype blob_reference: ~azure.ai.projects.models.BlobReference
    :ivar pending_upload_id: ID for this upload request. Required.
    :vartype pending_upload_id: str
    :ivar version: Version of asset to be created if user did not specify version when initially
     creating upload.
    :vartype version: str
    :ivar pending_upload_type: BlobReference is the only supported type. Required. Blob Reference
     is the only supported type.
    :vartype pending_upload_type: str or ~azure.ai.projects.models.BLOB_REFERENCE
    """

    blob_reference: "_models.BlobReference" = rest_field(
        name="blobReference", visibility=["read", "create", "update", "delete", "query"]
    )
    """Container-level read, write, list SAS. Required."""
    pending_upload_id: str = rest_field(
        name="pendingUploadId", visibility=["read", "create", "update", "delete", "query"]
    )
    """ID for this upload request. Required."""
    version: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Version of asset to be created if user did not specify version when initially creating upload."""
    pending_upload_type: Literal[PendingUploadType.BLOB_REFERENCE] = rest_field(
        name="pendingUploadType", visibility=["read", "create", "update", "delete", "query"]
    )
    """BlobReference is the only supported type. Required. Blob Reference is the only supported type."""

    @overload
    def __init__(
        self,
        *,
        blob_reference: "_models.BlobReference",
        pending_upload_id: str,
        pending_upload_type: Literal[PendingUploadType.BLOB_REFERENCE],
        version: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RedTeam(_Model):
    """Red team details.

    :ivar name: Identifier of the red team run. Required.
    :vartype name: str
    :ivar display_name: Name of the red-team run.
    :vartype display_name: str
    :ivar num_turns: Number of simulation rounds.
    :vartype num_turns: int
    :ivar attack_strategies: List of attack strategies or nested lists of attack strategies.
    :vartype attack_strategies: list[str or ~azure.ai.projects.models.AttackStrategy]
    :ivar simulation_only: Simulation-only or Simulation + Evaluation. Default false, if true the
     scan outputs conversation not evaluation result.
    :vartype simulation_only: bool
    :ivar risk_categories: List of risk categories to generate attack objectives for.
    :vartype risk_categories: list[str or ~azure.ai.projects.models.RiskCategory]
    :ivar application_scenario: Application scenario for the red team operation, to generate
     scenario specific attacks.
    :vartype application_scenario: str
    :ivar tags: Red team's tags. Unlike properties, tags are fully mutable.
    :vartype tags: dict[str, str]
    :ivar properties: Red team's properties. Unlike tags, properties are add-only. Once added, a
     property cannot be removed.
    :vartype properties: dict[str, str]
    :ivar status: Status of the red-team. It is set by service and is read-only.
    :vartype status: str
    :ivar target: Target configuration for the red-team run. Required.
    :vartype target: ~azure.ai.projects.models.TargetConfig
    """

    name: str = rest_field(name="id", visibility=["read"])
    """Identifier of the red team run. Required."""
    display_name: Optional[str] = rest_field(
        name="displayName", visibility=["read", "create", "update", "delete", "query"]
    )
    """Name of the red-team run."""
    num_turns: Optional[int] = rest_field(name="numTurns", visibility=["read", "create", "update", "delete", "query"])
    """Number of simulation rounds."""
    attack_strategies: Optional[List[Union[str, "_models.AttackStrategy"]]] = rest_field(
        name="attackStrategies", visibility=["read", "create", "update", "delete", "query"]
    )
    """List of attack strategies or nested lists of attack strategies."""
    simulation_only: Optional[bool] = rest_field(
        name="simulationOnly", visibility=["read", "create", "update", "delete", "query"]
    )
    """Simulation-only or Simulation + Evaluation. Default false, if true the scan outputs
     conversation not evaluation result."""
    risk_categories: Optional[List[Union[str, "_models.RiskCategory"]]] = rest_field(
        name="riskCategories", visibility=["read", "create", "update", "delete", "query"]
    )
    """List of risk categories to generate attack objectives for."""
    application_scenario: Optional[str] = rest_field(
        name="applicationScenario", visibility=["read", "create", "update", "delete", "query"]
    )
    """Application scenario for the red team operation, to generate scenario specific attacks."""
    tags: Optional[Dict[str, str]] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Red team's tags. Unlike properties, tags are fully mutable."""
    properties: Optional[Dict[str, str]] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Red team's properties. Unlike tags, properties are add-only. Once added, a property cannot be
     removed."""
    status: Optional[str] = rest_field(visibility=["read"])
    """Status of the red-team. It is set by service and is read-only."""
    target: "_models.TargetConfig" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Target configuration for the red-team run. Required."""

    @overload
    def __init__(
        self,
        *,
        target: "_models.TargetConfig",
        display_name: Optional[str] = None,
        num_turns: Optional[int] = None,
        attack_strategies: Optional[List[Union[str, "_models.AttackStrategy"]]] = None,
        simulation_only: Optional[bool] = None,
        risk_categories: Optional[List[Union[str, "_models.RiskCategory"]]] = None,
        application_scenario: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
        properties: Optional[Dict[str, str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class SasCredential(_Model):
    """SAS Credential definition.

    :ivar sas_uri: SAS uri. Required.
    :vartype sas_uri: str
    :ivar type: Type of credential. Required. Default value is "SAS".
    :vartype type: str
    """

    sas_uri: str = rest_field(name="sasUri", visibility=["read"])
    """SAS uri. Required."""
    type: Literal["SAS"] = rest_field(visibility=["read"])
    """Type of credential. Required. Default value is \"SAS\"."""

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.type: Literal["SAS"] = "SAS"


class SASCredentials(BaseCredentials, discriminator="SAS"):
    """Shared Access Signature (SAS) credential definition.

    :ivar type: The credential type. Required. Shared Access Signature (SAS) credential
    :vartype type: str or ~azure.ai.projects.models.SAS
    :ivar sas_token: SAS token.
    :vartype sas_token: str
    """

    type: Literal[CredentialType.SAS] = rest_discriminator(name="type", visibility=["read"])  # type: ignore
    """The credential type. Required. Shared Access Signature (SAS) credential"""
    sas_token: Optional[str] = rest_field(name="SAS", visibility=["read"])
    """SAS token."""

    @overload
    def __init__(
        self,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=CredentialType.SAS, **kwargs)


class Sku(_Model):
    """Sku information.

    :ivar capacity: Sku capacity. Required.
    :vartype capacity: int
    :ivar family: Sku family. Required.
    :vartype family: str
    :ivar name: Sku name. Required.
    :vartype name: str
    :ivar size: Sku size. Required.
    :vartype size: str
    :ivar tier: Sku tier. Required.
    :vartype tier: str
    """

    capacity: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Sku capacity. Required."""
    family: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Sku family. Required."""
    name: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Sku name. Required."""
    size: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Sku size. Required."""
    tier: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Sku tier. Required."""

    @overload
    def __init__(
        self,
        *,
        capacity: int,
        family: str,
        name: str,
        size: str,
        tier: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
