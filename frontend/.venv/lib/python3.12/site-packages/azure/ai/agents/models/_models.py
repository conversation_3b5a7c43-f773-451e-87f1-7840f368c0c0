# pylint: disable=line-too-long,useless-suppression,too-many-lines
# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) Python Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------
# pylint: disable=useless-super-delegation

import datetime
from typing import Any, Dict, List, Literal, Mapping, Optional, TYPE_CHECKING, Union, overload

from .._utils.model_base import Model as _Model, rest_discriminator, rest_field
from .._utils.utils import FileType
from ._enums import (
    MessageBlockType,
    OpenApiAuthType,
    RunStepType,
    VectorStoreChunkingStrategyRequestType,
    VectorStoreChunkingStrategyResponseType,
)

if TYPE_CHECKING:
    from .. import _types, models as _models


class Agent(_Model):
    """Represents an agent that can call the model and use tools.

    :ivar id: The identifier, which can be referenced in API endpoints. Required.
    :vartype id: str
    :ivar object: The object type, which is always assistant. Required. Default value is
     "assistant".
    :vartype object: str
    :ivar created_at: The Unix timestamp, in seconds, representing when this object was created.
     Required.
    :vartype created_at: ~datetime.datetime
    :ivar name: The name of the agent. Required.
    :vartype name: str
    :ivar description: The description of the agent. Required.
    :vartype description: str
    :ivar model: The ID of the model to use. Required.
    :vartype model: str
    :ivar instructions: The system instructions for the agent to use. Required.
    :vartype instructions: str
    :ivar tools: The collection of tools enabled for the agent. Required.
    :vartype tools: list[~azure.ai.agents.models.ToolDefinition]
    :ivar tool_resources: A set of resources that are used by the agent's tools. The resources are
     specific to the type of tool. For example, the ``code_interpreter``
     tool requires a list of file IDs, while the ``file_search`` tool requires a list of vector
     store IDs. Required.
    :vartype tool_resources: ~azure.ai.agents.models.ToolResources
    :ivar temperature: What sampling temperature to use, between 0 and 2. Higher values like 0.8
     will make the output more random,
     while lower values like 0.2 will make it more focused and deterministic. Required.
    :vartype temperature: float
    :ivar top_p: An alternative to sampling with temperature, called nucleus sampling, where the
     model considers the results of the tokens with top_p probability mass.
     So 0.1 means only the tokens comprising the top 10% probability mass are considered.

     We generally recommend altering this or temperature but not both. Required.
    :vartype top_p: float
    :ivar response_format: The response format of the tool calls used by this agent. Is one of the
     following types: str, Union[str, "_models.AgentsResponseFormatMode"], AgentsResponseFormat,
     ResponseFormatJsonSchemaType
    :vartype response_format: str or str or ~azure.ai.agents.models.AgentsResponseFormatMode or
     ~azure.ai.agents.models.AgentsResponseFormat or
     ~azure.ai.agents.models.ResponseFormatJsonSchemaType
    :ivar metadata: A set of up to 16 key/value pairs that can be attached to an object, used for
     storing additional information about that object in a structured format. Keys may be up to 64
     characters in length and values may be up to 512 characters in length. Required.
    :vartype metadata: dict[str, str]
    """

    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The identifier, which can be referenced in API endpoints. Required."""
    object: Literal["assistant"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The object type, which is always assistant. Required. Default value is \"assistant\"."""
    created_at: datetime.datetime = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp, in seconds, representing when this object was created. Required."""
    name: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The name of the agent. Required."""
    description: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The description of the agent. Required."""
    model: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the model to use. Required."""
    instructions: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The system instructions for the agent to use. Required."""
    tools: List["_models.ToolDefinition"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The collection of tools enabled for the agent. Required."""
    tool_resources: "_models.ToolResources" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A set of resources that are used by the agent's tools. The resources are specific to the type
     of tool. For example, the ``code_interpreter``
     tool requires a list of file IDs, while the ``file_search`` tool requires a list of vector
     store IDs. Required."""
    temperature: float = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output
     more random,
     while lower values like 0.2 will make it more focused and deterministic. Required."""
    top_p: float = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """An alternative to sampling with temperature, called nucleus sampling, where the model considers
     the results of the tokens with top_p probability mass.
     So 0.1 means only the tokens comprising the top 10% probability mass are considered.
     
     We generally recommend altering this or temperature but not both. Required."""
    response_format: Optional["_types.AgentsResponseFormatOption"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The response format of the tool calls used by this agent. Is one of the following types: str,
     Union[str, \"_models.AgentsResponseFormatMode\"], AgentsResponseFormat,
     ResponseFormatJsonSchemaType"""
    metadata: Dict[str, str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A set of up to 16 key/value pairs that can be attached to an object, used for storing
     additional information about that object in a structured format. Keys may be up to 64
     characters in length and values may be up to 512 characters in length. Required."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        created_at: datetime.datetime,
        name: str,
        description: str,
        model: str,
        instructions: str,
        tools: List["_models.ToolDefinition"],
        tool_resources: "_models.ToolResources",
        temperature: float,
        top_p: float,
        metadata: Dict[str, str],
        response_format: Optional["_types.AgentsResponseFormatOption"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.object: Literal["assistant"] = "assistant"


class AgentDeletionStatus(_Model):
    """The status of an agent deletion operation.

    :ivar id: The ID of the resource specified for deletion. Required.
    :vartype id: str
    :ivar deleted: A value indicating whether deletion was successful. Required.
    :vartype deleted: bool
    :ivar object: The object type, which is always 'assistant.deleted'. Required. Default value is
     "assistant.deleted".
    :vartype object: str
    """

    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the resource specified for deletion. Required."""
    deleted: bool = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A value indicating whether deletion was successful. Required."""
    object: Literal["assistant.deleted"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The object type, which is always 'assistant.deleted'. Required. Default value is
     \"assistant.deleted\"."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        deleted: bool,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.object: Literal["assistant.deleted"] = "assistant.deleted"


class AgentErrorDetail(_Model):
    """Describes the error information returned by the agents API.

    :ivar message: Human-readable description of the error.
    :vartype message: str
    :ivar type: Error type identifier (e.g. ``invalid_request_error``).
    :vartype type: str
    :ivar param: Name of the parameter that caused the error, if applicable.
    :vartype param: str
    :ivar code: Machine-readable error code.
    :vartype code: str
    """

    message: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Human-readable description of the error."""
    type: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Error type identifier (e.g. ``invalid_request_error``)."""
    param: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Name of the parameter that caused the error, if applicable."""
    code: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Machine-readable error code."""

    @overload
    def __init__(
        self,
        *,
        message: Optional[str] = None,
        type: Optional[str] = None,
        param: Optional[str] = None,
        code: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AgentsNamedToolChoice(_Model):
    """Specifies a tool the model should use. Use to force the model to call a specific tool.

    :ivar type: the type of tool. If type is ``function``, the function name must be set. Required.
     Known values are: "function", "code_interpreter", "file_search", "bing_grounding",
     "azure_ai_search", and "connected_agent".
    :vartype type: str or ~azure.ai.agents.models.AgentsNamedToolChoiceType
    :ivar function: The name of the function to call.
    :vartype function: ~azure.ai.agents.models.FunctionName
    """

    type: Union[str, "_models.AgentsNamedToolChoiceType"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """the type of tool. If type is ``function``, the function name must be set. Required. Known
     values are: \"function\", \"code_interpreter\", \"file_search\", \"bing_grounding\",
     \"azure_ai_search\", and \"connected_agent\"."""
    function: Optional["_models.FunctionName"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The name of the function to call."""

    @overload
    def __init__(
        self,
        *,
        type: Union[str, "_models.AgentsNamedToolChoiceType"],
        function: Optional["_models.FunctionName"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AgentsResponseFormat(_Model):
    """An object describing the expected output of the model. If ``json_object`` only ``function``
    type ``tools`` are allowed to be passed to the Run.
    If ``text`` the model can return text or any value needed.

    :ivar type: Must be one of ``text`` or ``json_object``. Known values are: "text" and
     "json_object".
    :vartype type: str or ~azure.ai.agents.models.ResponseFormat
    """

    type: Optional[Union[str, "_models.ResponseFormat"]] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Must be one of ``text`` or ``json_object``. Known values are: \"text\" and \"json_object\"."""

    @overload
    def __init__(
        self,
        *,
        type: Optional[Union[str, "_models.ResponseFormat"]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AgentThread(_Model):
    """Information about a single thread associated with an agent.

    :ivar id: The identifier, which can be referenced in API endpoints. Required.
    :vartype id: str
    :ivar object: The object type, which is always 'thread'. Required. Default value is "thread".
    :vartype object: str
    :ivar created_at: The Unix timestamp, in seconds, representing when this object was created.
     Required.
    :vartype created_at: ~datetime.datetime
    :ivar tool_resources: A set of resources that are made available to the agent's tools in this
     thread. The resources are specific to the type
     of tool. For example, the ``code_interpreter`` tool requires a list of file IDs, while the
     ``file_search`` tool requires a list
     of vector store IDs. Required.
    :vartype tool_resources: ~azure.ai.agents.models.ToolResources
    :ivar metadata: A set of up to 16 key/value pairs that can be attached to an object, used for
     storing additional information about that object in a structured format. Keys may be up to 64
     characters in length and values may be up to 512 characters in length. Required.
    :vartype metadata: dict[str, str]
    """

    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The identifier, which can be referenced in API endpoints. Required."""
    object: Literal["thread"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The object type, which is always 'thread'. Required. Default value is \"thread\"."""
    created_at: datetime.datetime = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp, in seconds, representing when this object was created. Required."""
    tool_resources: "_models.ToolResources" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A set of resources that are made available to the agent's tools in this thread. The resources
     are specific to the type
     of tool. For example, the ``code_interpreter`` tool requires a list of file IDs, while the
     ``file_search`` tool requires a list
     of vector store IDs. Required."""
    metadata: Dict[str, str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A set of up to 16 key/value pairs that can be attached to an object, used for storing
     additional information about that object in a structured format. Keys may be up to 64
     characters in length and values may be up to 512 characters in length. Required."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        created_at: datetime.datetime,
        tool_resources: "_models.ToolResources",
        metadata: Dict[str, str],
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.object: Literal["thread"] = "thread"


class AgentThreadCreationOptions(_Model):
    """The details used to create a new agent thread.

    :ivar messages: The initial messages to associate with the new thread.
    :vartype messages: list[~azure.ai.agents.models.ThreadMessageOptions]
    :ivar tool_resources: A set of resources that are made available to the agent's tools in this
     thread. The resources are specific to the
     type of tool. For example, the ``code_interpreter`` tool requires a list of file IDs, while the
     ``file_search`` tool requires
     a list of vector store IDs.
    :vartype tool_resources: ~azure.ai.agents.models.ToolResources
    :ivar metadata: A set of up to 16 key/value pairs that can be attached to an object, used for
     storing additional information about that object in a structured format. Keys may be up to 64
     characters in length and values may be up to 512 characters in length.
    :vartype metadata: dict[str, str]
    """

    messages: Optional[List["_models.ThreadMessageOptions"]] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The initial messages to associate with the new thread."""
    tool_resources: Optional["_models.ToolResources"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """A set of resources that are made available to the agent's tools in this thread. The resources
     are specific to the
     type of tool. For example, the ``code_interpreter`` tool requires a list of file IDs, while the
     ``file_search`` tool requires
     a list of vector store IDs."""
    metadata: Optional[Dict[str, str]] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A set of up to 16 key/value pairs that can be attached to an object, used for storing
     additional information about that object in a structured format. Keys may be up to 64
     characters in length and values may be up to 512 characters in length."""

    @overload
    def __init__(
        self,
        *,
        messages: Optional[List["_models.ThreadMessageOptions"]] = None,
        tool_resources: Optional["_models.ToolResources"] = None,
        metadata: Optional[Dict[str, str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AgentV1Error(_Model):
    """Error payload returned by the agents API.

    :ivar error: Represents the error. Required.
    :vartype error: ~azure.ai.agents.models.AgentErrorDetail
    """

    error: "_models.AgentErrorDetail" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Represents the error. Required."""

    @overload
    def __init__(
        self,
        *,
        error: "_models.AgentErrorDetail",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AISearchIndexResource(_Model):
    """A AI Search Index resource.

    :ivar index_connection_id: An index connection id in an IndexResource attached to this agent.
    :vartype index_connection_id: str
    :ivar index_name: The name of an index in an IndexResource attached to this agent.
    :vartype index_name: str
    :ivar query_type: Type of query in an AIIndexResource attached to this agent. Known values are:
     "simple", "semantic", "vector", "vector_simple_hybrid", and "vector_semantic_hybrid".
    :vartype query_type: str or ~azure.ai.agents.models.AzureAISearchQueryType
    :ivar top_k: Number of documents to retrieve from search and present to the model.
    :vartype top_k: int
    :ivar filter: filter string for search resource.
    :vartype filter: str
    :ivar index_asset_id: Index asset id for search resource.
    :vartype index_asset_id: str
    """

    index_connection_id: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """An index connection id in an IndexResource attached to this agent."""
    index_name: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The name of an index in an IndexResource attached to this agent."""
    query_type: Optional[Union[str, "_models.AzureAISearchQueryType"]] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Type of query in an AIIndexResource attached to this agent. Known values are: \"simple\",
     \"semantic\", \"vector\", \"vector_simple_hybrid\", and \"vector_semantic_hybrid\"."""
    top_k: Optional[int] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Number of documents to retrieve from search and present to the model."""
    filter: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """filter string for search resource."""
    index_asset_id: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Index asset id for search resource."""

    @overload
    def __init__(
        self,
        *,
        index_connection_id: Optional[str] = None,
        index_name: Optional[str] = None,
        query_type: Optional[Union[str, "_models.AzureAISearchQueryType"]] = None,
        top_k: Optional[int] = None,
        filter: Optional[str] = None,  # pylint: disable=redefined-builtin
        index_asset_id: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class ToolDefinition(_Model):
    """An abstract representation of an input tool definition that an agent can use.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    AzureAISearchToolDefinition, AzureFunctionToolDefinition, BingGroundingToolDefinition,
    CodeInterpreterToolDefinition, ConnectedAgentToolDefinition, FileSearchToolDefinition,
    FunctionToolDefinition, OpenApiToolDefinition

    :ivar type: The object type. Required. Default value is None.
    :vartype type: str
    """

    __mapping__: Dict[str, _Model] = {}
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """The object type. Required. Default value is None."""

    @overload
    def __init__(
        self,
        *,
        type: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AzureAISearchToolDefinition(ToolDefinition, discriminator="azure_ai_search"):
    """The input definition information for an Azure AI search tool as used to configure an agent.

    :ivar type: The object type, which is always 'azure_ai_search'. Required. Default value is
     "azure_ai_search".
    :vartype type: str
    """

    type: Literal["azure_ai_search"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'azure_ai_search'. Required. Default value is
     \"azure_ai_search\"."""

    @overload
    def __init__(
        self,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="azure_ai_search", **kwargs)


class AzureAISearchToolResource(_Model):
    """A set of index resources used by the ``azure_ai_search`` tool.

    :ivar index_list: The indices attached to this agent. There can be a maximum of 1 index
     resource attached to the agent.
    :vartype index_list: list[~azure.ai.agents.models.AISearchIndexResource]
    """

    index_list: Optional[List["_models.AISearchIndexResource"]] = rest_field(
        name="indexes", visibility=["read", "create", "update", "delete", "query"]
    )
    """The indices attached to this agent. There can be a maximum of 1 index
     resource attached to the agent."""

    @overload
    def __init__(
        self,
        *,
        index_list: Optional[List["_models.AISearchIndexResource"]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AzureFunctionBinding(_Model):
    """The structure for keeping storage queue name and URI.

    :ivar type: The type of binding, which is always 'storage_queue'. Required. Default value is
     "storage_queue".
    :vartype type: str
    :ivar storage_queue: Storage queue. Required.
    :vartype storage_queue: ~azure.ai.agents.models.AzureFunctionStorageQueue
    """

    type: Literal["storage_queue"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The type of binding, which is always 'storage_queue'. Required. Default value is
     \"storage_queue\"."""
    storage_queue: "_models.AzureFunctionStorageQueue" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Storage queue. Required."""

    @overload
    def __init__(
        self,
        *,
        storage_queue: "_models.AzureFunctionStorageQueue",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.type: Literal["storage_queue"] = "storage_queue"


class AzureFunctionDefinition(_Model):
    """The definition of Azure function.

    :ivar function: The definition of azure function and its parameters. Required.
    :vartype function: ~azure.ai.agents.models.FunctionDefinition
    :ivar input_binding: Input storage queue. The queue storage trigger runs a function as messages
     are added to it. Required.
    :vartype input_binding: ~azure.ai.agents.models.AzureFunctionBinding
    :ivar output_binding: Output storage queue. The function writes output to this queue when the
     input items are processed. Required.
    :vartype output_binding: ~azure.ai.agents.models.AzureFunctionBinding
    """

    function: "_models.FunctionDefinition" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The definition of azure function and its parameters. Required."""
    input_binding: "_models.AzureFunctionBinding" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Input storage queue. The queue storage trigger runs a function as messages are added to it.
     Required."""
    output_binding: "_models.AzureFunctionBinding" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Output storage queue. The function writes output to this queue when the input items are
     processed. Required."""

    @overload
    def __init__(
        self,
        *,
        function: "_models.FunctionDefinition",
        input_binding: "_models.AzureFunctionBinding",
        output_binding: "_models.AzureFunctionBinding",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AzureFunctionStorageQueue(_Model):
    """The structure for keeping storage queue name and URI.

    :ivar storage_service_endpoint: URI to the Azure Storage Queue service allowing you to
     manipulate a queue. Required.
    :vartype storage_service_endpoint: str
    :ivar queue_name: The name of an Azure function storage queue. Required.
    :vartype queue_name: str
    """

    storage_service_endpoint: str = rest_field(
        name="queue_service_endpoint", visibility=["read", "create", "update", "delete", "query"]
    )
    """URI to the Azure Storage Queue service allowing you to manipulate a queue. Required."""
    queue_name: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The name of an Azure function storage queue. Required."""

    @overload
    def __init__(
        self,
        *,
        storage_service_endpoint: str,
        queue_name: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AzureFunctionToolDefinition(ToolDefinition, discriminator="azure_function"):
    """The input definition information for a azure function tool as used to configure an agent.

    :ivar type: The object type, which is always 'azure_function'. Required. Default value is
     "azure_function".
    :vartype type: str
    :ivar azure_function: The definition of the concrete function that the function tool should
     call. Required.
    :vartype azure_function: ~azure.ai.agents.models.AzureFunctionDefinition
    """

    type: Literal["azure_function"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'azure_function'. Required. Default value is
     \"azure_function\"."""
    azure_function: "_models.AzureFunctionDefinition" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The definition of the concrete function that the function tool should call. Required."""

    @overload
    def __init__(
        self,
        *,
        azure_function: "_models.AzureFunctionDefinition",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="azure_function", **kwargs)


class BingGroundingSearchConfiguration(_Model):
    """Search configuration for Bing Grounding.

    :ivar connection_id: Connection id for grounding with bing search. Required.
    :vartype connection_id: str
    :ivar market: The market where the results come from.
    :vartype market: str
    :ivar set_lang: The language to use for user interface strings when calling Bing API.
    :vartype set_lang: str
    :ivar count: The number of search results to return in the bing api response.
    :vartype count: int
    :ivar freshness: Filter search results by a specific time range. Accepted values:
     `https://learn.microsoft.com/bing/search-apis/bing-web-search/reference/query-parameters
     <https://learn.microsoft.com/bing/search-apis/bing-web-search/reference/query-parameters>`_.
    :vartype freshness: str
    """

    connection_id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Connection id for grounding with bing search. Required."""
    market: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The market where the results come from."""
    set_lang: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The language to use for user interface strings when calling Bing API."""
    count: Optional[int] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The number of search results to return in the bing api response."""
    freshness: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Filter search results by a specific time range. Accepted values:
     `https://learn.microsoft.com/bing/search-apis/bing-web-search/reference/query-parameters
     <https://learn.microsoft.com/bing/search-apis/bing-web-search/reference/query-parameters>`_."""

    @overload
    def __init__(
        self,
        *,
        connection_id: str,
        market: Optional[str] = None,
        set_lang: Optional[str] = None,
        count: Optional[int] = None,
        freshness: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class BingGroundingSearchToolParameters(_Model):
    """The bing grounding search tool parameters.

    :ivar search_configurations: The search configurations attached to this tool. There can be a
     maximum of 1
     search configuration resource attached to the tool. Required.
    :vartype search_configurations: list[~azure.ai.agents.models.BingGroundingSearchConfiguration]
    """

    search_configurations: List["_models.BingGroundingSearchConfiguration"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The search configurations attached to this tool. There can be a maximum of 1
     search configuration resource attached to the tool. Required."""

    @overload
    def __init__(
        self,
        *,
        search_configurations: List["_models.BingGroundingSearchConfiguration"],
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class BingGroundingToolDefinition(ToolDefinition, discriminator="bing_grounding"):
    """The input definition information for a bing grounding search tool as used to configure an
    agent.

    :ivar type: The object type, which is always 'bing_grounding'. Required. Default value is
     "bing_grounding".
    :vartype type: str
    :ivar bing_grounding: The bing grounding search tool parameters. Required.
    :vartype bing_grounding: ~azure.ai.agents.models.BingGroundingSearchToolParameters
    """

    type: Literal["bing_grounding"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'bing_grounding'. Required. Default value is
     \"bing_grounding\"."""
    bing_grounding: "_models.BingGroundingSearchToolParameters" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The bing grounding search tool parameters. Required."""

    @overload
    def __init__(
        self,
        *,
        bing_grounding: "_models.BingGroundingSearchToolParameters",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="bing_grounding", **kwargs)


class CodeInterpreterToolDefinition(ToolDefinition, discriminator="code_interpreter"):
    """The input definition information for a code interpreter tool as used to configure an agent.

    :ivar type: The object type, which is always 'code_interpreter'. Required. Default value is
     "code_interpreter".
    :vartype type: str
    """

    type: Literal["code_interpreter"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'code_interpreter'. Required. Default value is
     \"code_interpreter\"."""

    @overload
    def __init__(
        self,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="code_interpreter", **kwargs)


class CodeInterpreterToolResource(_Model):
    """A set of resources that are used by the ``code_interpreter`` tool.

    :ivar file_ids: A list of file IDs made available to the ``code_interpreter`` tool. There can
     be a maximum of 20 files
     associated with the tool.
    :vartype file_ids: list[str]
    :ivar data_sources: The data sources to be used. This option is mutually exclusive with the
     ``fileIds`` property.
    :vartype data_sources: list[~azure.ai.agents.models.VectorStoreDataSource]
    """

    file_ids: Optional[List[str]] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A list of file IDs made available to the ``code_interpreter`` tool. There can be a maximum of
     20 files
     associated with the tool."""
    data_sources: Optional[List["_models.VectorStoreDataSource"]] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The data sources to be used. This option is mutually exclusive with the ``fileIds`` property."""

    @overload
    def __init__(
        self,
        *,
        file_ids: Optional[List[str]] = None,
        data_sources: Optional[List["_models.VectorStoreDataSource"]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class ConnectedAgentDetails(_Model):
    """Information for connecting one agent to another as a tool.

    :ivar id: The identifier of the child agent. Required.
    :vartype id: str
    :ivar name: The name of the agent to be called. Required.
    :vartype name: str
    :ivar description: A description of what the agent does, used by the model to choose when and
     how to call the agent. Required.
    :vartype description: str
    """

    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The identifier of the child agent. Required."""
    name: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The name of the agent to be called. Required."""
    description: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A description of what the agent does, used by the model to choose when and how to call the
     agent. Required."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        name: str,
        description: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class ConnectedAgentToolDefinition(ToolDefinition, discriminator="connected_agent"):
    """The input definition information for a connected agent tool which defines a domain specific
    sub-agent.

    :ivar type: The object type, which is always 'connected_agent'. Required. Default value is
     "connected_agent".
    :vartype type: str
    :ivar connected_agent: The sub-agent to connect. Required.
    :vartype connected_agent: ~azure.ai.agents.models.ConnectedAgentDetails
    """

    type: Literal["connected_agent"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'connected_agent'. Required. Default value is
     \"connected_agent\"."""
    connected_agent: "_models.ConnectedAgentDetails" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The sub-agent to connect. Required."""

    @overload
    def __init__(
        self,
        *,
        connected_agent: "_models.ConnectedAgentDetails",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="connected_agent", **kwargs)


class FileDeletionStatus(_Model):
    """A status response from a file deletion operation.

    :ivar id: The ID of the resource specified for deletion. Required.
    :vartype id: str
    :ivar deleted: A value indicating whether deletion was successful. Required.
    :vartype deleted: bool
    :ivar object: The object type, which is always 'file'. Required. Default value is "file".
    :vartype object: str
    """

    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the resource specified for deletion. Required."""
    deleted: bool = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A value indicating whether deletion was successful. Required."""
    object: Literal["file"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The object type, which is always 'file'. Required. Default value is \"file\"."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        deleted: bool,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.object: Literal["file"] = "file"


class FileInfo(_Model):
    """Represents an agent that can call the model and use tools.

    :ivar object: The object type, which is always 'file'. Required. Default value is "file".
    :vartype object: str
    :ivar id: The identifier, which can be referenced in API endpoints. Required.
    :vartype id: str
    :ivar bytes: The size of the file, in bytes. Required.
    :vartype bytes: int
    :ivar filename: The name of the file. Required.
    :vartype filename: str
    :ivar created_at: The Unix timestamp, in seconds, representing when this object was created.
     Required.
    :vartype created_at: ~datetime.datetime
    :ivar purpose: The intended purpose of a file. Required. Known values are: "assistants",
     "assistants_output", and "vision".
    :vartype purpose: str or ~azure.ai.agents.models.FilePurpose
    :ivar status: The state of the file. This field is available in Azure OpenAI only. Known values
     are: "uploaded", "pending", "running", "processed", "error", "deleting", and "deleted".
    :vartype status: str or ~azure.ai.agents.models.FileState
    :ivar status_details: The error message with details in case processing of this file failed.
     This field is available in Azure OpenAI only.
    :vartype status_details: str
    """

    object: Literal["file"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The object type, which is always 'file'. Required. Default value is \"file\"."""
    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The identifier, which can be referenced in API endpoints. Required."""
    bytes: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The size of the file, in bytes. Required."""
    filename: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The name of the file. Required."""
    created_at: datetime.datetime = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp, in seconds, representing when this object was created. Required."""
    purpose: Union[str, "_models.FilePurpose"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The intended purpose of a file. Required. Known values are: \"assistants\",
     \"assistants_output\", and \"vision\"."""
    status: Optional[Union[str, "_models.FileState"]] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The state of the file. This field is available in Azure OpenAI only. Known values are:
     \"uploaded\", \"pending\", \"running\", \"processed\", \"error\", \"deleting\", and
     \"deleted\"."""
    status_details: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The error message with details in case processing of this file failed. This field is available
     in Azure OpenAI only."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        bytes: int,
        filename: str,
        created_at: datetime.datetime,
        purpose: Union[str, "_models.FilePurpose"],
        status: Optional[Union[str, "_models.FileState"]] = None,
        status_details: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.object: Literal["file"] = "file"


class FileListResponse(_Model):
    """The response data from a file list operation.

    :ivar object: The object type, which is always 'list'. Required. Default value is "list".
    :vartype object: str
    :ivar data: The files returned for the request. Required.
    :vartype data: list[~azure.ai.agents.models.FileInfo]
    """

    object: Literal["list"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The object type, which is always 'list'. Required. Default value is \"list\"."""
    data: List["_models.FileInfo"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The files returned for the request. Required."""

    @overload
    def __init__(
        self,
        *,
        data: List["_models.FileInfo"],
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.object: Literal["list"] = "list"


class FileSearchRankingOptions(_Model):
    """Ranking options for file search.

    :ivar ranker: File search ranker. Required.
    :vartype ranker: str
    :ivar score_threshold: Ranker search threshold. Required.
    :vartype score_threshold: float
    """

    ranker: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """File search ranker. Required."""
    score_threshold: float = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Ranker search threshold. Required."""

    @overload
    def __init__(
        self,
        *,
        ranker: str,
        score_threshold: float,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class FileSearchToolCallContent(_Model):
    """The file search result content object.

    :ivar type: The type of the content. Required. Default value is "text".
    :vartype type: str
    :ivar text: The text content of the file. Required.
    :vartype text: str
    """

    type: Literal["text"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The type of the content. Required. Default value is \"text\"."""
    text: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The text content of the file. Required."""

    @overload
    def __init__(
        self,
        *,
        text: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.type: Literal["text"] = "text"


class FileSearchToolDefinition(ToolDefinition, discriminator="file_search"):
    """The input definition information for a file search tool as used to configure an agent.

    :ivar type: The object type, which is always 'file_search'. Required. Default value is
     "file_search".
    :vartype type: str
    :ivar file_search: Options overrides for the file search tool.
    :vartype file_search: ~azure.ai.agents.models.FileSearchToolDefinitionDetails
    """

    type: Literal["file_search"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'file_search'. Required. Default value is \"file_search\"."""
    file_search: Optional["_models.FileSearchToolDefinitionDetails"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Options overrides for the file search tool."""

    @overload
    def __init__(
        self,
        *,
        file_search: Optional["_models.FileSearchToolDefinitionDetails"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="file_search", **kwargs)


class FileSearchToolDefinitionDetails(_Model):
    """Options overrides for the file search tool.

    :ivar max_num_results: The maximum number of results the file search tool should output. The
     default is 20 for gpt-4* models and 5 for gpt-3.5-turbo. This number should be between 1 and 50
     inclusive.

     Note that the file search tool may output fewer than ``max_num_results`` results. See the file
     search tool documentation for more information.
    :vartype max_num_results: int
    :ivar ranking_options: Ranking options for file search.
    :vartype ranking_options: ~azure.ai.agents.models.FileSearchRankingOptions
    """

    max_num_results: Optional[int] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The maximum number of results the file search tool should output. The default is 20 for gpt-4*
     models and 5 for gpt-3.5-turbo. This number should be between 1 and 50 inclusive.
     
     Note that the file search tool may output fewer than ``max_num_results`` results. See the file
     search tool documentation for more information."""
    ranking_options: Optional["_models.FileSearchRankingOptions"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Ranking options for file search."""

    @overload
    def __init__(
        self,
        *,
        max_num_results: Optional[int] = None,
        ranking_options: Optional["_models.FileSearchRankingOptions"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class FileSearchToolResource(_Model):
    """A set of resources that are used by the ``file_search`` tool.

    :ivar vector_store_ids: The ID of the vector store attached to this agent. There can be a
     maximum of 1 vector
     store attached to the agent.
    :vartype vector_store_ids: list[str]
    :ivar vector_stores: The list of vector store configuration objects from Azure.
     This list is limited to one element.
     The only element of this list contains the list of azure asset IDs used by the search tool.
    :vartype vector_stores: list[~azure.ai.agents.models.VectorStoreConfigurations]
    """

    vector_store_ids: Optional[List[str]] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the vector store attached to this agent. There can be a maximum of 1 vector
     store attached to the agent."""
    vector_stores: Optional[List["_models.VectorStoreConfigurations"]] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The list of vector store configuration objects from Azure.
     This list is limited to one element.
     The only element of this list contains the list of azure asset IDs used by the search tool."""

    @overload
    def __init__(
        self,
        *,
        vector_store_ids: Optional[List[str]] = None,
        vector_stores: Optional[List["_models.VectorStoreConfigurations"]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class FunctionDefinition(_Model):
    """The input definition information for a function.

    :ivar name: The name of the function to be called. Required.
    :vartype name: str
    :ivar description: A description of what the function does, used by the model to choose when
     and how to call the function.
    :vartype description: str
    :ivar parameters: The parameters the functions accepts, described as a JSON Schema object.
     Required.
    :vartype parameters: any
    """

    name: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The name of the function to be called. Required."""
    description: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A description of what the function does, used by the model to choose when and how to call the
     function."""
    parameters: Any = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The parameters the functions accepts, described as a JSON Schema object. Required."""

    @overload
    def __init__(
        self,
        *,
        name: str,
        parameters: Any,
        description: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class FunctionName(_Model):
    """The function name that will be used, if using the ``function`` tool.

    :ivar name: The name of the function to call. Required.
    :vartype name: str
    """

    name: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The name of the function to call. Required."""

    @overload
    def __init__(
        self,
        *,
        name: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class FunctionToolDefinition(ToolDefinition, discriminator="function"):
    """The input definition information for a function tool as used to configure an agent.

    :ivar type: The object type, which is always 'function'. Required. Default value is "function".
    :vartype type: str
    :ivar function: The definition of the concrete function that the function tool should call.
     Required.
    :vartype function: ~azure.ai.agents.models.FunctionDefinition
    """

    type: Literal["function"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'function'. Required. Default value is \"function\"."""
    function: "_models.FunctionDefinition" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The definition of the concrete function that the function tool should call. Required."""

    @overload
    def __init__(
        self,
        *,
        function: "_models.FunctionDefinition",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="function", **kwargs)


class IncompleteRunDetails(_Model):
    """Details on why the run is incomplete. Will be ``null`` if the run is not incomplete.

    :ivar reason: The reason why the run is incomplete. This indicates which specific token limit
     was reached during the run. Required. Known values are: "max_completion_tokens" and
     "max_prompt_tokens".
    :vartype reason: str or ~azure.ai.agents.models.IncompleteDetailsReason
    """

    reason: Union[str, "_models.IncompleteDetailsReason"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The reason why the run is incomplete. This indicates which specific token limit was reached
     during the run. Required. Known values are: \"max_completion_tokens\" and
     \"max_prompt_tokens\"."""

    @overload
    def __init__(
        self,
        *,
        reason: Union[str, "_models.IncompleteDetailsReason"],
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class MessageAttachment(_Model):
    """This describes to which tools a file has been attached.

    :ivar file_id: The ID of the file to attach to the message.
    :vartype file_id: str
    :ivar data_source: Azure asset ID.
    :vartype data_source: ~azure.ai.agents.models.VectorStoreDataSource
    :ivar tools: The tools to add to this file. Required.
    :vartype tools: list[~azure.ai.agents.models.CodeInterpreterToolDefinition or
     ~azure.ai.agents.models.FileSearchToolDefinition]
    """

    file_id: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the file to attach to the message."""
    data_source: Optional["_models.VectorStoreDataSource"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Azure asset ID."""
    tools: List["_types.MessageAttachmentToolDefinition"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The tools to add to this file. Required."""

    @overload
    def __init__(
        self,
        *,
        tools: List["_types.MessageAttachmentToolDefinition"],
        file_id: Optional[str] = None,
        data_source: Optional["_models.VectorStoreDataSource"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class MessageContent(_Model):
    """An abstract representation of a single item of thread message content.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    MessageImageFileContent, MessageTextContent

    :ivar type: The object type. Required. Default value is None.
    :vartype type: str
    """

    __mapping__: Dict[str, _Model] = {}
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """The object type. Required. Default value is None."""

    @overload
    def __init__(
        self,
        *,
        type: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class MessageDelta(_Model):
    """Represents the typed 'delta' payload within a streaming message delta chunk.

    :ivar role: The entity that produced the message. Required. Known values are: "user" and
     "assistant".
    :vartype role: str or ~azure.ai.agents.models.MessageRole
    :ivar content: The content of the message as an array of text and/or images. Required.
    :vartype content: list[~azure.ai.agents.models.MessageDeltaContent]
    """

    role: Union[str, "_models.MessageRole"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The entity that produced the message. Required. Known values are: \"user\" and \"assistant\"."""
    content: List["_models.MessageDeltaContent"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The content of the message as an array of text and/or images. Required."""

    @overload
    def __init__(
        self,
        *,
        role: Union[str, "_models.MessageRole"],
        content: List["_models.MessageDeltaContent"],
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class MessageDeltaChunk(_Model):
    """Represents a message delta i.e. any changed fields on a message during streaming.

    :ivar id: The identifier of the message, which can be referenced in API endpoints. Required.
    :vartype id: str
    :ivar object: The object type, which is always ``thread.message.delta``. Required. Default
     value is "thread.message.delta".
    :vartype object: str
    :ivar delta: The delta containing the fields that have changed on the Message. Required.
    :vartype delta: ~azure.ai.agents.models.MessageDelta
    """

    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The identifier of the message, which can be referenced in API endpoints. Required."""
    object: Literal["thread.message.delta"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The object type, which is always ``thread.message.delta``. Required. Default value is
     \"thread.message.delta\"."""
    delta: "_models.MessageDelta" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The delta containing the fields that have changed on the Message. Required."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        delta: "_models.MessageDelta",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.object: Literal["thread.message.delta"] = "thread.message.delta"


class MessageDeltaContent(_Model):
    """The abstract base representation of a partial streamed message content payload.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    MessageDeltaImageFileContent, MessageDeltaTextContent

    :ivar index: The index of the content part of the message. Required.
    :vartype index: int
    :ivar type: The type of content for this content part. Required. Default value is None.
    :vartype type: str
    """

    __mapping__: Dict[str, _Model] = {}
    index: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The index of the content part of the message. Required."""
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """The type of content for this content part. Required. Default value is None."""

    @overload
    def __init__(
        self,
        *,
        index: int,
        type: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class MessageDeltaImageFileContent(MessageDeltaContent, discriminator="image_file"):
    """Represents a streamed image file content part within a streaming message delta chunk.

    :ivar index: The index of the content part of the message. Required.
    :vartype index: int
    :ivar type: The type of content for this content part, which is always "image_file.". Required.
     Default value is "image_file".
    :vartype type: str
    :ivar image_file: The image_file data.
    :vartype image_file: ~azure.ai.agents.models.MessageDeltaImageFileContentObject
    """

    type: Literal["image_file"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The type of content for this content part, which is always \"image_file.\". Required. Default
     value is \"image_file\"."""
    image_file: Optional["_models.MessageDeltaImageFileContentObject"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The image_file data."""

    @overload
    def __init__(
        self,
        *,
        index: int,
        image_file: Optional["_models.MessageDeltaImageFileContentObject"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="image_file", **kwargs)


class MessageDeltaImageFileContentObject(_Model):
    """Represents the 'image_file' payload within streaming image file content.

    :ivar file_id: The file ID of the image in the message content.
    :vartype file_id: str
    """

    file_id: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The file ID of the image in the message content."""

    @overload
    def __init__(
        self,
        *,
        file_id: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class MessageDeltaTextAnnotation(_Model):
    """The abstract base representation of a streamed text content part's text annotation.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    MessageDeltaTextFileCitationAnnotation, MessageDeltaTextFilePathAnnotation,
    MessageDeltaTextUrlCitationAnnotation

    :ivar index: The index of the annotation within a text content part. Required.
    :vartype index: int
    :ivar type: The type of the text content annotation. Required. Default value is None.
    :vartype type: str
    """

    __mapping__: Dict[str, _Model] = {}
    index: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The index of the annotation within a text content part. Required."""
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """The type of the text content annotation. Required. Default value is None."""

    @overload
    def __init__(
        self,
        *,
        index: int,
        type: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class MessageDeltaTextContent(MessageDeltaContent, discriminator="text"):
    """Represents a streamed text content part within a streaming message delta chunk.

    :ivar index: The index of the content part of the message. Required.
    :vartype index: int
    :ivar type: The type of content for this content part, which is always "text.". Required.
     Default value is "text".
    :vartype type: str
    :ivar text: The text content details.
    :vartype text: ~azure.ai.agents.models.MessageDeltaTextContentObject
    """

    type: Literal["text"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The type of content for this content part, which is always \"text.\". Required. Default value
     is \"text\"."""
    text: Optional["_models.MessageDeltaTextContentObject"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The text content details."""

    @overload
    def __init__(
        self,
        *,
        index: int,
        text: Optional["_models.MessageDeltaTextContentObject"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="text", **kwargs)


class MessageDeltaTextContentObject(_Model):
    """Represents the data of a streamed text content part within a streaming message delta chunk.

    :ivar value: The data that makes up the text.
    :vartype value: str
    :ivar annotations: Annotations for the text.
    :vartype annotations: list[~azure.ai.agents.models.MessageDeltaTextAnnotation]
    """

    value: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The data that makes up the text."""
    annotations: Optional[List["_models.MessageDeltaTextAnnotation"]] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Annotations for the text."""

    @overload
    def __init__(
        self,
        *,
        value: Optional[str] = None,
        annotations: Optional[List["_models.MessageDeltaTextAnnotation"]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class MessageDeltaTextFileCitationAnnotation(MessageDeltaTextAnnotation, discriminator="file_citation"):
    """Represents a streamed file citation applied to a streaming text content part.

    :ivar index: The index of the annotation within a text content part. Required.
    :vartype index: int
    :ivar type: The type of the text content annotation, which is always "file_citation.".
     Required. Default value is "file_citation".
    :vartype type: str
    :ivar file_citation: The file citation information.
    :vartype file_citation: ~azure.ai.agents.models.MessageDeltaTextFileCitationAnnotationObject
    :ivar text: The text in the message content that needs to be replaced.
    :vartype text: str
    :ivar start_index: The start index of this annotation in the content text.
    :vartype start_index: int
    :ivar end_index: The end index of this annotation in the content text.
    :vartype end_index: int
    """

    type: Literal["file_citation"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The type of the text content annotation, which is always \"file_citation.\". Required. Default
     value is \"file_citation\"."""
    file_citation: Optional["_models.MessageDeltaTextFileCitationAnnotationObject"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The file citation information."""
    text: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The text in the message content that needs to be replaced."""
    start_index: Optional[int] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The start index of this annotation in the content text."""
    end_index: Optional[int] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The end index of this annotation in the content text."""

    @overload
    def __init__(
        self,
        *,
        index: int,
        file_citation: Optional["_models.MessageDeltaTextFileCitationAnnotationObject"] = None,
        text: Optional[str] = None,
        start_index: Optional[int] = None,
        end_index: Optional[int] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="file_citation", **kwargs)


class MessageDeltaTextFileCitationAnnotationObject(_Model):  # pylint: disable=name-too-long
    """Represents the data of a streamed file citation as applied to a streaming text content part.

    :ivar file_id: The ID of the specific file the citation is from.
    :vartype file_id: str
    :ivar quote: The specific quote in the cited file.
    :vartype quote: str
    """

    file_id: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the specific file the citation is from."""
    quote: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The specific quote in the cited file."""

    @overload
    def __init__(
        self,
        *,
        file_id: Optional[str] = None,
        quote: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class MessageDeltaTextFilePathAnnotation(MessageDeltaTextAnnotation, discriminator="file_path"):
    """Represents a streamed file path annotation applied to a streaming text content part.

    :ivar index: The index of the annotation within a text content part. Required.
    :vartype index: int
    :ivar type: The type of the text content annotation, which is always "file_path.". Required.
     Default value is "file_path".
    :vartype type: str
    :ivar file_path: The file path information.
    :vartype file_path: ~azure.ai.agents.models.MessageDeltaTextFilePathAnnotationObject
    :ivar start_index: The start index of this annotation in the content text.
    :vartype start_index: int
    :ivar end_index: The end index of this annotation in the content text.
    :vartype end_index: int
    :ivar text: The text in the message content that needs to be replaced.
    :vartype text: str
    """

    type: Literal["file_path"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The type of the text content annotation, which is always \"file_path.\". Required. Default
     value is \"file_path\"."""
    file_path: Optional["_models.MessageDeltaTextFilePathAnnotationObject"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The file path information."""
    start_index: Optional[int] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The start index of this annotation in the content text."""
    end_index: Optional[int] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The end index of this annotation in the content text."""
    text: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The text in the message content that needs to be replaced."""

    @overload
    def __init__(
        self,
        *,
        index: int,
        file_path: Optional["_models.MessageDeltaTextFilePathAnnotationObject"] = None,
        start_index: Optional[int] = None,
        end_index: Optional[int] = None,
        text: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="file_path", **kwargs)


class MessageDeltaTextFilePathAnnotationObject(_Model):
    """Represents the data of a streamed file path annotation as applied to a streaming text content
    part.

    :ivar file_id: The file ID for the annotation.
    :vartype file_id: str
    """

    file_id: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The file ID for the annotation."""

    @overload
    def __init__(
        self,
        *,
        file_id: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class MessageDeltaTextUrlCitationAnnotation(MessageDeltaTextAnnotation, discriminator="url_citation"):
    """A citation within the message that points to a specific URL associated with the message.
    Generated when the agent uses tools such as 'bing_grounding' to search the Internet.

    :ivar index: The index of the annotation within a text content part. Required.
    :vartype index: int
    :ivar type: The object type, which is always 'url_citation'. Required. Default value is
     "url_citation".
    :vartype type: str
    :ivar url_citation: The details of the URL citation. Required.
    :vartype url_citation: ~azure.ai.agents.models.MessageDeltaTextUrlCitationDetails
    :ivar start_index: The first text index associated with this text annotation.
    :vartype start_index: int
    :ivar end_index: The last text index associated with this text annotation.
    :vartype end_index: int
    """

    type: Literal["url_citation"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'url_citation'. Required. Default value is \"url_citation\"."""
    url_citation: "_models.MessageDeltaTextUrlCitationDetails" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The details of the URL citation. Required."""
    start_index: Optional[int] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The first text index associated with this text annotation."""
    end_index: Optional[int] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The last text index associated with this text annotation."""

    @overload
    def __init__(
        self,
        *,
        index: int,
        url_citation: "_models.MessageDeltaTextUrlCitationDetails",
        start_index: Optional[int] = None,
        end_index: Optional[int] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="url_citation", **kwargs)


class MessageDeltaTextUrlCitationDetails(_Model):
    """A representation of a URL citation, as used in text thread message content.

    :ivar url: The URL associated with this citation. Required.
    :vartype url: str
    :ivar title: The title of the URL.
    :vartype title: str
    """

    url: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The URL associated with this citation. Required."""
    title: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The title of the URL."""

    @overload
    def __init__(
        self,
        *,
        url: str,
        title: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class MessageImageFileContent(MessageContent, discriminator="image_file"):
    """A representation of image file content in a thread message.

    :ivar type: The object type, which is always 'image_file'. Required. Default value is
     "image_file".
    :vartype type: str
    :ivar image_file: The image file for this thread message content item. Required.
    :vartype image_file: ~azure.ai.agents.models.MessageImageFileDetails
    """

    type: Literal["image_file"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'image_file'. Required. Default value is \"image_file\"."""
    image_file: "_models.MessageImageFileDetails" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The image file for this thread message content item. Required."""

    @overload
    def __init__(
        self,
        *,
        image_file: "_models.MessageImageFileDetails",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="image_file", **kwargs)


class MessageImageFileDetails(_Model):
    """An image reference, as represented in thread message content.

    :ivar file_id: The ID for the file associated with this image. Required.
    :vartype file_id: str
    """

    file_id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID for the file associated with this image. Required."""

    @overload
    def __init__(
        self,
        *,
        file_id: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class MessageImageFileParam(_Model):
    """Defines how an internally uploaded image file is referenced when creating an image-file block.

    :ivar file_id: The ID of the previously uploaded image file. Required.
    :vartype file_id: str
    :ivar detail: Optional detail level for the image (auto, low, or high). Known values are:
     "auto", "low", and "high".
    :vartype detail: str or ~azure.ai.agents.models.ImageDetailLevel
    """

    file_id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the previously uploaded image file. Required."""
    detail: Optional[Union[str, "_models.ImageDetailLevel"]] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Optional detail level for the image (auto, low, or high). Known values are: \"auto\", \"low\",
     and \"high\"."""

    @overload
    def __init__(
        self,
        *,
        file_id: str,
        detail: Optional[Union[str, "_models.ImageDetailLevel"]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class MessageImageUrlParam(_Model):
    """Defines how an external image URL is referenced when creating an image-URL block.

    :ivar url: The publicly accessible URL of the external image. Required.
    :vartype url: str
    :ivar detail: Optional detail level for the image (auto, low, or high). Defaults to 'auto' if
     not specified. Known values are: "auto", "low", and "high".
    :vartype detail: str or ~azure.ai.agents.models.ImageDetailLevel
    """

    url: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The publicly accessible URL of the external image. Required."""
    detail: Optional[Union[str, "_models.ImageDetailLevel"]] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Optional detail level for the image (auto, low, or high). Defaults to 'auto' if not specified.
     Known values are: \"auto\", \"low\", and \"high\"."""

    @overload
    def __init__(
        self,
        *,
        url: str,
        detail: Optional[Union[str, "_models.ImageDetailLevel"]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class MessageIncompleteDetails(_Model):
    """Information providing additional detail about a message entering an incomplete status.

    :ivar reason: The provided reason describing why the message was marked as incomplete.
     Required. Known values are: "content_filter", "max_tokens", "run_cancelled", "run_failed", and
     "run_expired".
    :vartype reason: str or ~azure.ai.agents.models.MessageIncompleteDetailsReason
    """

    reason: Union[str, "_models.MessageIncompleteDetailsReason"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The provided reason describing why the message was marked as incomplete. Required. Known values
     are: \"content_filter\", \"max_tokens\", \"run_cancelled\", \"run_failed\", and
     \"run_expired\"."""

    @overload
    def __init__(
        self,
        *,
        reason: Union[str, "_models.MessageIncompleteDetailsReason"],
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class MessageInputContentBlock(_Model):
    """Defines a single content block when creating a message. The 'type' field determines whether it
    is text, an image file, or an external image URL, etc.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    MessageInputImageFileBlock, MessageInputImageUrlBlock, MessageInputTextBlock

    :ivar type: Specifies which kind of content block this is (text, image_file, image_url, etc.).
     Required. Known values are: "text", "image_file", and "image_url".
    :vartype type: str or ~azure.ai.agents.models.MessageBlockType
    """

    __mapping__: Dict[str, _Model] = {}
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """Specifies which kind of content block this is (text, image_file, image_url, etc.). Required.
     Known values are: \"text\", \"image_file\", and \"image_url\"."""

    @overload
    def __init__(
        self,
        *,
        type: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class MessageInputImageFileBlock(MessageInputContentBlock, discriminator="image_file"):
    """An image-file block in a new message, referencing an internally uploaded image by file ID.

    :ivar type: Must be 'image_file' for an internally uploaded image block. Required. Indicates a
     block referencing an internally uploaded image file.
    :vartype type: str or ~azure.ai.agents.models.IMAGE_FILE
    :ivar image_file: Information about the referenced image file, including file ID and optional
     detail level. Required.
    :vartype image_file: ~azure.ai.agents.models.MessageImageFileParam
    """

    type: Literal[MessageBlockType.IMAGE_FILE] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """Must be 'image_file' for an internally uploaded image block. Required. Indicates a block
     referencing an internally uploaded image file."""
    image_file: "_models.MessageImageFileParam" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Information about the referenced image file, including file ID and optional detail level.
     Required."""

    @overload
    def __init__(
        self,
        *,
        image_file: "_models.MessageImageFileParam",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=MessageBlockType.IMAGE_FILE, **kwargs)


class MessageInputImageUrlBlock(MessageInputContentBlock, discriminator="image_url"):
    """An image-URL block in a new message, referencing an external image by URL.

    :ivar type: Must be 'image_url' for an externally hosted image block. Required. Indicates a
     block referencing an external image URL.
    :vartype type: str or ~azure.ai.agents.models.IMAGE_URL
    :ivar image_url: Information about the external image URL, including the URL and optional
     detail level. Required.
    :vartype image_url: ~azure.ai.agents.models.MessageImageUrlParam
    """

    type: Literal[MessageBlockType.IMAGE_URL] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """Must be 'image_url' for an externally hosted image block. Required. Indicates a block
     referencing an external image URL."""
    image_url: "_models.MessageImageUrlParam" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Information about the external image URL, including the URL and optional detail level.
     Required."""

    @overload
    def __init__(
        self,
        *,
        image_url: "_models.MessageImageUrlParam",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=MessageBlockType.IMAGE_URL, **kwargs)


class MessageInputTextBlock(MessageInputContentBlock, discriminator="text"):
    """A text block in a new message, containing plain text content.

    :ivar type: Must be 'text' for a text block. Required. Indicates a block containing text
     content.
    :vartype type: str or ~azure.ai.agents.models.TEXT
    :ivar text: The plain text content for this block. Required.
    :vartype text: str
    """

    type: Literal[MessageBlockType.TEXT] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """Must be 'text' for a text block. Required. Indicates a block containing text content."""
    text: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The plain text content for this block. Required."""

    @overload
    def __init__(
        self,
        *,
        text: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=MessageBlockType.TEXT, **kwargs)


class MessageTextAnnotation(_Model):
    """An abstract representation of an annotation to text thread message content.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    MessageTextFileCitationAnnotation, MessageTextFilePathAnnotation,
    MessageTextUrlCitationAnnotation

    :ivar type: The object type. Required. Default value is None.
    :vartype type: str
    :ivar text: The textual content associated with this text annotation item. Required.
    :vartype text: str
    """

    __mapping__: Dict[str, _Model] = {}
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """The object type. Required. Default value is None."""
    text: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The textual content associated with this text annotation item. Required."""

    @overload
    def __init__(
        self,
        *,
        type: str,
        text: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class MessageTextContent(MessageContent, discriminator="text"):
    """A representation of a textual item of thread message content.

    :ivar type: The object type, which is always 'text'. Required. Default value is "text".
    :vartype type: str
    :ivar text: The text and associated annotations for this thread message content item. Required.
    :vartype text: ~azure.ai.agents.models.MessageTextDetails
    """

    type: Literal["text"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'text'. Required. Default value is \"text\"."""
    text: "_models.MessageTextDetails" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The text and associated annotations for this thread message content item. Required."""

    @overload
    def __init__(
        self,
        *,
        text: "_models.MessageTextDetails",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="text", **kwargs)


class MessageTextDetails(_Model):
    """The text and associated annotations for a single item of agent thread message content.

    :ivar value: The text data. Required.
    :vartype value: str
    :ivar annotations: A list of annotations associated with this text. Required.
    :vartype annotations: list[~azure.ai.agents.models.MessageTextAnnotation]
    """

    value: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The text data. Required."""
    annotations: List["_models.MessageTextAnnotation"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """A list of annotations associated with this text. Required."""

    @overload
    def __init__(
        self,
        *,
        value: str,
        annotations: List["_models.MessageTextAnnotation"],
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class MessageTextFileCitationAnnotation(MessageTextAnnotation, discriminator="file_citation"):
    """A citation within the message that points to a specific quote from a specific File associated
    with the agent or the message. Generated when the agent uses the 'file_search' tool to search
    files.

    :ivar text: The textual content associated with this text annotation item. Required.
    :vartype text: str
    :ivar type: The object type, which is always 'file_citation'. Required. Default value is
     "file_citation".
    :vartype type: str
    :ivar file_citation: A citation within the message that points to a specific quote from a
     specific file.
     Generated when the agent uses the "file_search" tool to search files. Required.
    :vartype file_citation: ~azure.ai.agents.models.MessageTextFileCitationDetails
    :ivar start_index: The first text index associated with this text annotation.
    :vartype start_index: int
    :ivar end_index: The last text index associated with this text annotation.
    :vartype end_index: int
    """

    type: Literal["file_citation"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'file_citation'. Required. Default value is \"file_citation\"."""
    file_citation: "_models.MessageTextFileCitationDetails" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """A citation within the message that points to a specific quote from a specific file.
     Generated when the agent uses the \"file_search\" tool to search files. Required."""
    start_index: Optional[int] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The first text index associated with this text annotation."""
    end_index: Optional[int] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The last text index associated with this text annotation."""

    @overload
    def __init__(
        self,
        *,
        text: str,
        file_citation: "_models.MessageTextFileCitationDetails",
        start_index: Optional[int] = None,
        end_index: Optional[int] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="file_citation", **kwargs)


class MessageTextFileCitationDetails(_Model):
    """A representation of a file-based text citation, as used in a file-based annotation of text
    thread message content.

    :ivar file_id: The ID of the file associated with this citation. Required.
    :vartype file_id: str
    :ivar quote: The specific quote cited in the associated file. Required.
    :vartype quote: str
    """

    file_id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the file associated with this citation. Required."""
    quote: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The specific quote cited in the associated file. Required."""

    @overload
    def __init__(
        self,
        *,
        file_id: str,
        quote: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class MessageTextFilePathAnnotation(MessageTextAnnotation, discriminator="file_path"):
    """A citation within the message that points to a file located at a specific path.

    :ivar text: The textual content associated with this text annotation item. Required.
    :vartype text: str
    :ivar type: The object type, which is always 'file_path'. Required. Default value is
     "file_path".
    :vartype type: str
    :ivar file_path: A URL for the file that's generated when the agent used the code_interpreter
     tool to generate a file. Required.
    :vartype file_path: ~azure.ai.agents.models.MessageTextFilePathDetails
    :ivar start_index: The first text index associated with this text annotation.
    :vartype start_index: int
    :ivar end_index: The last text index associated with this text annotation.
    :vartype end_index: int
    """

    type: Literal["file_path"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'file_path'. Required. Default value is \"file_path\"."""
    file_path: "_models.MessageTextFilePathDetails" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """A URL for the file that's generated when the agent used the code_interpreter tool to generate a
     file. Required."""
    start_index: Optional[int] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The first text index associated with this text annotation."""
    end_index: Optional[int] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The last text index associated with this text annotation."""

    @overload
    def __init__(
        self,
        *,
        text: str,
        file_path: "_models.MessageTextFilePathDetails",
        start_index: Optional[int] = None,
        end_index: Optional[int] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="file_path", **kwargs)


class MessageTextFilePathDetails(_Model):
    """An encapsulation of an image file ID, as used by message image content.

    :ivar file_id: The ID of the specific file that the citation is from. Required.
    :vartype file_id: str
    """

    file_id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the specific file that the citation is from. Required."""

    @overload
    def __init__(
        self,
        *,
        file_id: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class MessageTextUrlCitationAnnotation(MessageTextAnnotation, discriminator="url_citation"):
    """A citation within the message that points to a specific URL associated with the message.
    Generated when the agent uses tools such as 'bing_grounding' to search the Internet.

    :ivar text: The textual content associated with this text annotation item. Required.
    :vartype text: str
    :ivar type: The object type, which is always 'url_citation'. Required. Default value is
     "url_citation".
    :vartype type: str
    :ivar url_citation: The details of the URL citation. Required.
    :vartype url_citation: ~azure.ai.agents.models.MessageTextUrlCitationDetails
    :ivar start_index: The first text index associated with this text annotation.
    :vartype start_index: int
    :ivar end_index: The last text index associated with this text annotation.
    :vartype end_index: int
    """

    type: Literal["url_citation"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'url_citation'. Required. Default value is \"url_citation\"."""
    url_citation: "_models.MessageTextUrlCitationDetails" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The details of the URL citation. Required."""
    start_index: Optional[int] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The first text index associated with this text annotation."""
    end_index: Optional[int] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The last text index associated with this text annotation."""

    @overload
    def __init__(
        self,
        *,
        text: str,
        url_citation: "_models.MessageTextUrlCitationDetails",
        start_index: Optional[int] = None,
        end_index: Optional[int] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="url_citation", **kwargs)


class MessageTextUrlCitationDetails(_Model):
    """A representation of a URL citation, as used in text thread message content.

    :ivar url: The URL associated with this citation. Required.
    :vartype url: str
    :ivar title: The title of the URL.
    :vartype title: str
    """

    url: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The URL associated with this citation. Required."""
    title: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The title of the URL."""

    @overload
    def __init__(
        self,
        *,
        url: str,
        title: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class OpenApiAuthDetails(_Model):
    """authentication details for OpenApiFunctionDefinition.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    OpenApiAnonymousAuthDetails, OpenApiConnectionAuthDetails, OpenApiManagedAuthDetails

    :ivar type: The type of authentication, must be anonymous/connection/managed_identity.
     Required. Known values are: "anonymous", "connection", and "managed_identity".
    :vartype type: str or ~azure.ai.agents.models.OpenApiAuthType
    """

    __mapping__: Dict[str, _Model] = {}
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """The type of authentication, must be anonymous/connection/managed_identity. Required. Known
     values are: \"anonymous\", \"connection\", and \"managed_identity\"."""

    @overload
    def __init__(
        self,
        *,
        type: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class OpenApiAnonymousAuthDetails(OpenApiAuthDetails, discriminator="anonymous"):
    """Security details for OpenApi anonymous authentication.

    :ivar type: The object type, which is always 'anonymous'. Required.
    :vartype type: str or ~azure.ai.agents.models.ANONYMOUS
    """

    type: Literal[OpenApiAuthType.ANONYMOUS] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'anonymous'. Required."""

    @overload
    def __init__(
        self,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=OpenApiAuthType.ANONYMOUS, **kwargs)


class OpenApiConnectionAuthDetails(OpenApiAuthDetails, discriminator="connection"):
    """Security details for OpenApi connection authentication.

    :ivar type: The object type, which is always 'connection'. Required.
    :vartype type: str or ~azure.ai.agents.models.CONNECTION
    :ivar security_scheme: Connection auth security details. Required.
    :vartype security_scheme: ~azure.ai.agents.models.OpenApiConnectionSecurityScheme
    """

    type: Literal[OpenApiAuthType.CONNECTION] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'connection'. Required."""
    security_scheme: "_models.OpenApiConnectionSecurityScheme" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Connection auth security details. Required."""

    @overload
    def __init__(
        self,
        *,
        security_scheme: "_models.OpenApiConnectionSecurityScheme",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=OpenApiAuthType.CONNECTION, **kwargs)


class OpenApiConnectionSecurityScheme(_Model):
    """Security scheme for OpenApi managed_identity authentication.

    :ivar connection_id: Connection id for Connection auth type. Required.
    :vartype connection_id: str
    """

    connection_id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Connection id for Connection auth type. Required."""

    @overload
    def __init__(
        self,
        *,
        connection_id: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class OpenApiFunctionDefinition(_Model):
    """The input definition information for an openapi function.

    :ivar name: The name of the function to be called. Required.
    :vartype name: str
    :ivar description: A description of what the function does, used by the model to choose when
     and how to call the function.
    :vartype description: str
    :ivar spec: The openapi function shape, described as a JSON Schema object. Required.
    :vartype spec: any
    :ivar auth: Open API authentication details. Required.
    :vartype auth: ~azure.ai.agents.models.OpenApiAuthDetails
    :ivar default_params: List of OpenAPI spec parameters that will use user-provided defaults.
    :vartype default_params: list[str]
    :ivar functions: List of function definitions used by OpenApi tool.
    :vartype functions: list[~azure.ai.agents.models.FunctionDefinition]
    """

    name: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The name of the function to be called. Required."""
    description: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A description of what the function does, used by the model to choose when and how to call the
     function."""
    spec: Any = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The openapi function shape, described as a JSON Schema object. Required."""
    auth: "_models.OpenApiAuthDetails" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Open API authentication details. Required."""
    default_params: Optional[List[str]] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """List of OpenAPI spec parameters that will use user-provided defaults."""
    functions: Optional[List["_models.FunctionDefinition"]] = rest_field(visibility=["read"])
    """List of function definitions used by OpenApi tool."""

    @overload
    def __init__(
        self,
        *,
        name: str,
        spec: Any,
        auth: "_models.OpenApiAuthDetails",
        description: Optional[str] = None,
        default_params: Optional[List[str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class OpenApiManagedAuthDetails(OpenApiAuthDetails, discriminator="managed_identity"):
    """Security details for OpenApi managed_identity authentication.

    :ivar type: The object type, which is always 'managed_identity'. Required.
    :vartype type: str or ~azure.ai.agents.models.MANAGED_IDENTITY
    :ivar security_scheme: Connection auth security details. Required.
    :vartype security_scheme: ~azure.ai.agents.models.OpenApiManagedSecurityScheme
    """

    type: Literal[OpenApiAuthType.MANAGED_IDENTITY] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'managed_identity'. Required."""
    security_scheme: "_models.OpenApiManagedSecurityScheme" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Connection auth security details. Required."""

    @overload
    def __init__(
        self,
        *,
        security_scheme: "_models.OpenApiManagedSecurityScheme",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=OpenApiAuthType.MANAGED_IDENTITY, **kwargs)


class OpenApiManagedSecurityScheme(_Model):
    """Security scheme for OpenApi managed_identity authentication.

    :ivar audience: Authentication scope for managed_identity auth type. Required.
    :vartype audience: str
    """

    audience: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Authentication scope for managed_identity auth type. Required."""

    @overload
    def __init__(
        self,
        *,
        audience: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class OpenApiToolDefinition(ToolDefinition, discriminator="openapi"):
    """The input definition information for an OpenAPI tool as used to configure an agent.

    :ivar type: The object type, which is always 'openapi'. Required. Default value is "openapi".
    :vartype type: str
    :ivar openapi: The openapi function definition. Required.
    :vartype openapi: ~azure.ai.agents.models.OpenApiFunctionDefinition
    """

    type: Literal["openapi"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'openapi'. Required. Default value is \"openapi\"."""
    openapi: "_models.OpenApiFunctionDefinition" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The openapi function definition. Required."""

    @overload
    def __init__(
        self,
        *,
        openapi: "_models.OpenApiFunctionDefinition",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="openapi", **kwargs)


class RequiredAction(_Model):
    """An abstract representation of a required action for an agent thread run to continue.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    SubmitToolOutputsAction

    :ivar type: The object type. Required. Default value is None.
    :vartype type: str
    """

    __mapping__: Dict[str, _Model] = {}
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """The object type. Required. Default value is None."""

    @overload
    def __init__(
        self,
        *,
        type: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RequiredToolCall(_Model):
    """An abstract representation of a tool invocation needed by the model to continue a run.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    RequiredFunctionToolCall

    :ivar type: The object type for the required tool call. Required. Default value is None.
    :vartype type: str
    :ivar id: The ID of the tool call. This ID must be referenced when submitting tool outputs.
     Required.
    :vartype id: str
    """

    __mapping__: Dict[str, _Model] = {}
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """The object type for the required tool call. Required. Default value is None."""
    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the tool call. This ID must be referenced when submitting tool outputs. Required."""

    @overload
    def __init__(
        self,
        *,
        type: str,
        id: str,  # pylint: disable=redefined-builtin
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RequiredFunctionToolCall(RequiredToolCall, discriminator="function"):
    """A representation of a requested call to a function tool, needed by the model to continue
    evaluation of a run.

    :ivar id: The ID of the tool call. This ID must be referenced when submitting tool outputs.
     Required.
    :vartype id: str
    :ivar type: The object type of the required tool call. Always 'function' for function tools.
     Required. Default value is "function".
    :vartype type: str
    :ivar function: Detailed information about the function to be executed by the tool that
     includes name and arguments. Required.
    :vartype function: ~azure.ai.agents.models.RequiredFunctionToolCallDetails
    """

    type: Literal["function"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type of the required tool call. Always 'function' for function tools. Required.
     Default value is \"function\"."""
    function: "_models.RequiredFunctionToolCallDetails" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Detailed information about the function to be executed by the tool that includes name and
     arguments. Required."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        function: "_models.RequiredFunctionToolCallDetails",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="function", **kwargs)


class RequiredFunctionToolCallDetails(_Model):
    """The detailed information for a function invocation, as provided by a required action invoking a
    function tool, that includes the name of and arguments to the function.

    :ivar name: The name of the function. Required.
    :vartype name: str
    :ivar arguments: The arguments to use when invoking the named function, as provided by the
     model. Arguments are presented as a JSON document that should be validated and parsed for
     evaluation. Required.
    :vartype arguments: str
    """

    name: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The name of the function. Required."""
    arguments: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The arguments to use when invoking the named function, as provided by the model. Arguments are
     presented as a JSON document that should be validated and parsed for evaluation. Required."""

    @overload
    def __init__(
        self,
        *,
        name: str,
        arguments: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class ResponseFormatJsonSchema(_Model):
    """A description of what the response format is for, used by the model to determine how to respond
    in the format.

    :ivar description: A description of what the response format is for, used by the model to
     determine how to respond in the format.
    :vartype description: str
    :ivar name: The name of a schema. Required.
    :vartype name: str
    :ivar schema: The JSON schema object, describing the response format. Required.
    :vartype schema: any
    """

    description: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A description of what the response format is for, used by the model to determine how to respond
     in the format."""
    name: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The name of a schema. Required."""
    schema: Any = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The JSON schema object, describing the response format. Required."""

    @overload
    def __init__(
        self,
        *,
        name: str,
        schema: Any,
        description: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class ResponseFormatJsonSchemaType(_Model):
    """The type of response format being defined: ``json_schema``.

    :ivar type: Type. Required. Default value is "json_schema".
    :vartype type: str
    :ivar json_schema: The JSON schema, describing response format. Required.
    :vartype json_schema: ~azure.ai.agents.models.ResponseFormatJsonSchema
    """

    type: Literal["json_schema"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Type. Required. Default value is \"json_schema\"."""
    json_schema: "_models.ResponseFormatJsonSchema" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The JSON schema, describing response format. Required."""

    @overload
    def __init__(
        self,
        *,
        json_schema: "_models.ResponseFormatJsonSchema",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.type: Literal["json_schema"] = "json_schema"


class RunCompletionUsage(_Model):
    """Usage statistics related to the run. This value will be ``null`` if the run is not in a
    terminal state (i.e. ``in_progress``, ``queued``, etc.).

    :ivar completion_tokens: Number of completion tokens used over the course of the run. Required.
    :vartype completion_tokens: int
    :ivar prompt_tokens: Number of prompt tokens used over the course of the run. Required.
    :vartype prompt_tokens: int
    :ivar total_tokens: Total number of tokens used (prompt + completion). Required.
    :vartype total_tokens: int
    """

    completion_tokens: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Number of completion tokens used over the course of the run. Required."""
    prompt_tokens: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Number of prompt tokens used over the course of the run. Required."""
    total_tokens: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Total number of tokens used (prompt + completion). Required."""

    @overload
    def __init__(
        self,
        *,
        completion_tokens: int,
        prompt_tokens: int,
        total_tokens: int,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RunError(_Model):
    """The details of an error as encountered by an agent thread run.

    :ivar code: The status for the error. Required.
    :vartype code: str
    :ivar message: The human-readable text associated with the error. Required.
    :vartype message: str
    """

    code: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The status for the error. Required."""
    message: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The human-readable text associated with the error. Required."""

    @overload
    def __init__(
        self,
        *,
        code: str,
        message: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RunStep(_Model):
    """Detailed information about a single step of an agent thread run.

    :ivar id: The identifier, which can be referenced in API endpoints. Required.
    :vartype id: str
    :ivar object: The object type, which is always 'thread.run.step'. Required. Default value is
     "thread.run.step".
    :vartype object: str
    :ivar type: The type of run step, which can be either message_creation or tool_calls. Required.
     Known values are: "message_creation" and "tool_calls".
    :vartype type: str or ~azure.ai.agents.models.RunStepType
    :ivar agent_id: The ID of the agent associated with the run step. Required.
    :vartype agent_id: str
    :ivar thread_id: The ID of the thread that was run. Required.
    :vartype thread_id: str
    :ivar run_id: The ID of the run that this run step is a part of. Required.
    :vartype run_id: str
    :ivar status: The status of this run step. Required. Known values are: "in_progress",
     "cancelled", "failed", "completed", and "expired".
    :vartype status: str or ~azure.ai.agents.models.RunStepStatus
    :ivar step_details: The details for this run step. Required.
    :vartype step_details: ~azure.ai.agents.models.RunStepDetails
    :ivar last_error: If applicable, information about the last error encountered by this run step.
     Required.
    :vartype last_error: ~azure.ai.agents.models.RunStepError
    :ivar created_at: The Unix timestamp, in seconds, representing when this object was created.
     Required.
    :vartype created_at: ~datetime.datetime
    :ivar expired_at: The Unix timestamp, in seconds, representing when this item expired.
     Required.
    :vartype expired_at: ~datetime.datetime
    :ivar completed_at: The Unix timestamp, in seconds, representing when this completed. Required.
    :vartype completed_at: ~datetime.datetime
    :ivar cancelled_at: The Unix timestamp, in seconds, representing when this was cancelled.
     Required.
    :vartype cancelled_at: ~datetime.datetime
    :ivar failed_at: The Unix timestamp, in seconds, representing when this failed. Required.
    :vartype failed_at: ~datetime.datetime
    :ivar usage: Usage statistics related to the run step. This value will be ``null`` while the
     run step's status is ``in_progress``.
    :vartype usage: ~azure.ai.agents.models.RunStepCompletionUsage
    :ivar metadata: A set of up to 16 key/value pairs that can be attached to an object, used for
     storing additional information about that object in a structured format. Keys may be up to 64
     characters in length and values may be up to 512 characters in length. Required.
    :vartype metadata: dict[str, str]
    """

    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The identifier, which can be referenced in API endpoints. Required."""
    object: Literal["thread.run.step"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The object type, which is always 'thread.run.step'. Required. Default value is
     \"thread.run.step\"."""
    type: Union[str, "_models.RunStepType"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The type of run step, which can be either message_creation or tool_calls. Required. Known
     values are: \"message_creation\" and \"tool_calls\"."""
    agent_id: str = rest_field(name="assistant_id", visibility=["read", "create", "update", "delete", "query"])
    """The ID of the agent associated with the run step. Required."""
    thread_id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the thread that was run. Required."""
    run_id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the run that this run step is a part of. Required."""
    status: Union[str, "_models.RunStepStatus"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The status of this run step. Required. Known values are: \"in_progress\", \"cancelled\",
     \"failed\", \"completed\", and \"expired\"."""
    step_details: "_models.RunStepDetails" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The details for this run step. Required."""
    last_error: "_models.RunStepError" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """If applicable, information about the last error encountered by this run step. Required."""
    created_at: datetime.datetime = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp, in seconds, representing when this object was created. Required."""
    expired_at: datetime.datetime = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp, in seconds, representing when this item expired. Required."""
    completed_at: datetime.datetime = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp, in seconds, representing when this completed. Required."""
    cancelled_at: datetime.datetime = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp, in seconds, representing when this was cancelled. Required."""
    failed_at: datetime.datetime = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp, in seconds, representing when this failed. Required."""
    usage: Optional["_models.RunStepCompletionUsage"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Usage statistics related to the run step. This value will be ``null`` while the run step's
     status is ``in_progress``."""
    metadata: Dict[str, str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A set of up to 16 key/value pairs that can be attached to an object, used for storing
     additional information about that object in a structured format. Keys may be up to 64
     characters in length and values may be up to 512 characters in length. Required."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        type: Union[str, "_models.RunStepType"],
        agent_id: str,
        thread_id: str,
        run_id: str,
        status: Union[str, "_models.RunStepStatus"],
        step_details: "_models.RunStepDetails",
        last_error: "_models.RunStepError",
        created_at: datetime.datetime,
        expired_at: datetime.datetime,
        completed_at: datetime.datetime,
        cancelled_at: datetime.datetime,
        failed_at: datetime.datetime,
        metadata: Dict[str, str],
        usage: Optional["_models.RunStepCompletionUsage"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.object: Literal["thread.run.step"] = "thread.run.step"


class RunStepToolCall(_Model):
    """An abstract representation of a detailed tool call as recorded within a run step for an
    existing run.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    RunStepAzureAISearchToolCall, RunStepBingGroundingToolCall, RunStepCodeInterpreterToolCall,
    RunStepFileSearchToolCall, RunStepFunctionToolCall, RunStepOpenAPIToolCall

    :ivar type: The object type. Required. Default value is None.
    :vartype type: str
    :ivar id: The ID of the tool call. This ID must be referenced when you submit tool outputs.
     Required.
    :vartype id: str
    """

    __mapping__: Dict[str, _Model] = {}
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """The object type. Required. Default value is None."""
    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the tool call. This ID must be referenced when you submit tool outputs. Required."""

    @overload
    def __init__(
        self,
        *,
        type: str,
        id: str,  # pylint: disable=redefined-builtin
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RunStepAzureAISearchToolCall(RunStepToolCall, discriminator="azure_ai_search"):
    """A record of a call to an Azure AI Search tool, issued by the model in evaluation of a defined
    tool, that represents
    executed Azure AI search.

    :ivar id: The ID of the tool call. This ID must be referenced when you submit tool outputs.
     Required.
    :vartype id: str
    :ivar type: The object type, which is always 'azure_ai_search'. Required. Default value is
     "azure_ai_search".
    :vartype type: str
    :ivar azure_ai_search: Reserved for future use. Required.
    :vartype azure_ai_search: dict[str, str]
    """

    type: Literal["azure_ai_search"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'azure_ai_search'. Required. Default value is
     \"azure_ai_search\"."""
    azure_ai_search: Dict[str, str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Reserved for future use. Required."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        azure_ai_search: Dict[str, str],
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="azure_ai_search", **kwargs)


class RunStepBingGroundingToolCall(RunStepToolCall, discriminator="bing_grounding"):
    """A record of a call to a bing grounding tool, issued by the model in evaluation of a defined
    tool, that represents
    executed search with bing grounding.

    :ivar id: The ID of the tool call. This ID must be referenced when you submit tool outputs.
     Required.
    :vartype id: str
    :ivar type: The object type, which is always 'bing_grounding'. Required. Default value is
     "bing_grounding".
    :vartype type: str
    :ivar bing_grounding: Reserved for future use. Required.
    :vartype bing_grounding: dict[str, str]
    """

    type: Literal["bing_grounding"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'bing_grounding'. Required. Default value is
     \"bing_grounding\"."""
    bing_grounding: Dict[str, str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Reserved for future use. Required."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        bing_grounding: Dict[str, str],
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="bing_grounding", **kwargs)


class RunStepCodeInterpreterToolCallOutput(_Model):
    """An abstract representation of an emitted output from a code interpreter tool.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    RunStepCodeInterpreterImageOutput, RunStepCodeInterpreterLogOutput

    :ivar type: The object type. Required. Default value is None.
    :vartype type: str
    """

    __mapping__: Dict[str, _Model] = {}
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """The object type. Required. Default value is None."""

    @overload
    def __init__(
        self,
        *,
        type: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RunStepCodeInterpreterImageOutput(RunStepCodeInterpreterToolCallOutput, discriminator="image"):
    """A representation of an image output emitted by a code interpreter tool in response to a tool
    call by the model.

    :ivar type: The object type, which is always 'image'. Required. Default value is "image".
    :vartype type: str
    :ivar image: Referential information for the image associated with this output. Required.
    :vartype image: ~azure.ai.agents.models.RunStepCodeInterpreterImageReference
    """

    type: Literal["image"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'image'. Required. Default value is \"image\"."""
    image: "_models.RunStepCodeInterpreterImageReference" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Referential information for the image associated with this output. Required."""

    @overload
    def __init__(
        self,
        *,
        image: "_models.RunStepCodeInterpreterImageReference",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="image", **kwargs)


class RunStepCodeInterpreterImageReference(_Model):
    """An image reference emitted by a code interpreter tool in response to a tool call by the model.

    :ivar file_id: The ID of the file associated with this image. Required.
    :vartype file_id: str
    """

    file_id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the file associated with this image. Required."""

    @overload
    def __init__(
        self,
        *,
        file_id: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RunStepCodeInterpreterLogOutput(RunStepCodeInterpreterToolCallOutput, discriminator="logs"):
    """A representation of a log output emitted by a code interpreter tool in response to a tool call
    by the model.

    :ivar type: The object type, which is always 'logs'. Required. Default value is "logs".
    :vartype type: str
    :ivar logs: The serialized log output emitted by the code interpreter. Required.
    :vartype logs: str
    """

    type: Literal["logs"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'logs'. Required. Default value is \"logs\"."""
    logs: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The serialized log output emitted by the code interpreter. Required."""

    @overload
    def __init__(
        self,
        *,
        logs: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="logs", **kwargs)


class RunStepCodeInterpreterToolCall(RunStepToolCall, discriminator="code_interpreter"):
    """A record of a call to a code interpreter tool, issued by the model in evaluation of a defined
    tool, that
    represents inputs and outputs consumed and emitted by the code interpreter.

    :ivar id: The ID of the tool call. This ID must be referenced when you submit tool outputs.
     Required.
    :vartype id: str
    :ivar type: The object type, which is always 'code_interpreter'. Required. Default value is
     "code_interpreter".
    :vartype type: str
    :ivar code_interpreter: The details of the tool call to the code interpreter tool. Required.
    :vartype code_interpreter: ~azure.ai.agents.models.RunStepCodeInterpreterToolCallDetails
    """

    type: Literal["code_interpreter"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'code_interpreter'. Required. Default value is
     \"code_interpreter\"."""
    code_interpreter: "_models.RunStepCodeInterpreterToolCallDetails" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The details of the tool call to the code interpreter tool. Required."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        code_interpreter: "_models.RunStepCodeInterpreterToolCallDetails",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="code_interpreter", **kwargs)


class RunStepCodeInterpreterToolCallDetails(_Model):
    """The detailed information about a code interpreter invocation by the model.

    :ivar input: The input provided by the model to the code interpreter tool. Required.
    :vartype input: str
    :ivar outputs: The outputs produced by the code interpreter tool back to the model in response
     to the tool call. Required.
    :vartype outputs: list[~azure.ai.agents.models.RunStepCodeInterpreterToolCallOutput]
    """

    input: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The input provided by the model to the code interpreter tool. Required."""
    outputs: List["_models.RunStepCodeInterpreterToolCallOutput"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The outputs produced by the code interpreter tool back to the model in response to the tool
     call. Required."""

    @overload
    def __init__(
        self,
        *,
        input: str,
        outputs: List["_models.RunStepCodeInterpreterToolCallOutput"],
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RunStepCompletionUsage(_Model):
    """Usage statistics related to the run step.

    :ivar completion_tokens: Number of completion tokens used over the course of the run step.
     Required.
    :vartype completion_tokens: int
    :ivar prompt_tokens: Number of prompt tokens used over the course of the run step. Required.
    :vartype prompt_tokens: int
    :ivar total_tokens: Total number of tokens used (prompt + completion). Required.
    :vartype total_tokens: int
    """

    completion_tokens: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Number of completion tokens used over the course of the run step. Required."""
    prompt_tokens: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Number of prompt tokens used over the course of the run step. Required."""
    total_tokens: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Total number of tokens used (prompt + completion). Required."""

    @overload
    def __init__(
        self,
        *,
        completion_tokens: int,
        prompt_tokens: int,
        total_tokens: int,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RunStepDelta(_Model):
    """Represents the delta payload in a streaming run step delta chunk.

    :ivar step_details: The details of the run step.
    :vartype step_details: ~azure.ai.agents.models.RunStepDeltaDetail
    """

    step_details: Optional["_models.RunStepDeltaDetail"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The details of the run step."""

    @overload
    def __init__(
        self,
        *,
        step_details: Optional["_models.RunStepDeltaDetail"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RunStepDeltaChunk(_Model):
    """Represents a run step delta i.e. any changed fields on a run step during streaming.

    :ivar id: The identifier of the run step, which can be referenced in API endpoints. Required.
    :vartype id: str
    :ivar object: The object type, which is always ``thread.run.step.delta``. Required. Default
     value is "thread.run.step.delta".
    :vartype object: str
    :ivar delta: The delta containing the fields that have changed on the run step. Required.
    :vartype delta: ~azure.ai.agents.models.RunStepDelta
    """

    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The identifier of the run step, which can be referenced in API endpoints. Required."""
    object: Literal["thread.run.step.delta"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The object type, which is always ``thread.run.step.delta``. Required. Default value is
     \"thread.run.step.delta\"."""
    delta: "_models.RunStepDelta" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The delta containing the fields that have changed on the run step. Required."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        delta: "_models.RunStepDelta",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.object: Literal["thread.run.step.delta"] = "thread.run.step.delta"


class RunStepDeltaCodeInterpreterDetailItemObject(_Model):  # pylint: disable=name-too-long
    """Represents the Code Interpreter tool call data in a streaming run step's tool calls.

    :ivar input: The input into the Code Interpreter tool call.
    :vartype input: str
    :ivar outputs: The outputs from the Code Interpreter tool call. Code Interpreter can output one
     or more
     items, including text (``logs``) or images (``image``). Each of these are represented by a
     different object type.
    :vartype outputs: list[~azure.ai.agents.models.RunStepDeltaCodeInterpreterOutput]
    """

    input: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The input into the Code Interpreter tool call."""
    outputs: Optional[List["_models.RunStepDeltaCodeInterpreterOutput"]] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The outputs from the Code Interpreter tool call. Code Interpreter can output one or more
     items, including text (``logs``) or images (``image``). Each of these are represented by a
     different object type."""

    @overload
    def __init__(
        self,
        *,
        input: Optional[str] = None,
        outputs: Optional[List["_models.RunStepDeltaCodeInterpreterOutput"]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RunStepDeltaCodeInterpreterOutput(_Model):
    """The abstract base representation of a streaming run step tool call's Code Interpreter tool
    output.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    RunStepDeltaCodeInterpreterImageOutput, RunStepDeltaCodeInterpreterLogOutput

    :ivar index: The index of the output in the streaming run step tool call's Code Interpreter
     outputs array. Required.
    :vartype index: int
    :ivar type: The type of the streaming run step tool call's Code Interpreter output. Required.
     Default value is None.
    :vartype type: str
    """

    __mapping__: Dict[str, _Model] = {}
    index: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The index of the output in the streaming run step tool call's Code Interpreter outputs array.
     Required."""
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """The type of the streaming run step tool call's Code Interpreter output. Required. Default value
     is None."""

    @overload
    def __init__(
        self,
        *,
        index: int,
        type: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RunStepDeltaCodeInterpreterImageOutput(RunStepDeltaCodeInterpreterOutput, discriminator="image"):
    """Represents an image output as produced the Code interpreter tool and as represented in a
    streaming run step's delta tool calls collection.

    :ivar index: The index of the output in the streaming run step tool call's Code Interpreter
     outputs array. Required.
    :vartype index: int
    :ivar type: The object type, which is always "image.". Required. Default value is "image".
    :vartype type: str
    :ivar image: The image data for the Code Interpreter tool call output.
    :vartype image: ~azure.ai.agents.models.RunStepDeltaCodeInterpreterImageOutputObject
    """

    type: Literal["image"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always \"image.\". Required. Default value is \"image\"."""
    image: Optional["_models.RunStepDeltaCodeInterpreterImageOutputObject"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The image data for the Code Interpreter tool call output."""

    @overload
    def __init__(
        self,
        *,
        index: int,
        image: Optional["_models.RunStepDeltaCodeInterpreterImageOutputObject"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="image", **kwargs)


class RunStepDeltaCodeInterpreterImageOutputObject(_Model):  # pylint: disable=name-too-long
    """Represents the data for a streaming run step's Code Interpreter tool call image output.

    :ivar file_id: The file ID for the image.
    :vartype file_id: str
    """

    file_id: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The file ID for the image."""

    @overload
    def __init__(
        self,
        *,
        file_id: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RunStepDeltaCodeInterpreterLogOutput(RunStepDeltaCodeInterpreterOutput, discriminator="logs"):
    """Represents a log output as produced by the Code Interpreter tool and as represented in a
    streaming run step's delta tool calls collection.

    :ivar index: The index of the output in the streaming run step tool call's Code Interpreter
     outputs array. Required.
    :vartype index: int
    :ivar type: The type of the object, which is always "logs.". Required. Default value is "logs".
    :vartype type: str
    :ivar logs: The text output from the Code Interpreter tool call.
    :vartype logs: str
    """

    type: Literal["logs"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The type of the object, which is always \"logs.\". Required. Default value is \"logs\"."""
    logs: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The text output from the Code Interpreter tool call."""

    @overload
    def __init__(
        self,
        *,
        index: int,
        logs: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="logs", **kwargs)


class RunStepDeltaToolCall(_Model):
    """The abstract base representation of a single tool call within a streaming run step's delta tool
    call details.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    RunStepDeltaCodeInterpreterToolCall, RunStepDeltaFileSearchToolCall,
    RunStepDeltaFunctionToolCall

    :ivar index: The index of the tool call detail in the run step's tool_calls array. Required.
    :vartype index: int
    :ivar id: The ID of the tool call, used when submitting outputs to the run. Required.
    :vartype id: str
    :ivar type: The type of the tool call detail item in a streaming run step's details. Required.
     Default value is None.
    :vartype type: str
    """

    __mapping__: Dict[str, _Model] = {}
    index: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The index of the tool call detail in the run step's tool_calls array. Required."""
    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the tool call, used when submitting outputs to the run. Required."""
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """The type of the tool call detail item in a streaming run step's details. Required. Default
     value is None."""

    @overload
    def __init__(
        self,
        *,
        index: int,
        id: str,  # pylint: disable=redefined-builtin
        type: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RunStepDeltaCodeInterpreterToolCall(RunStepDeltaToolCall, discriminator="code_interpreter"):
    """Represents a Code Interpreter tool call within a streaming run step's tool call details.

    :ivar index: The index of the tool call detail in the run step's tool_calls array. Required.
    :vartype index: int
    :ivar id: The ID of the tool call, used when submitting outputs to the run. Required.
    :vartype id: str
    :ivar type: The object type, which is always "code_interpreter.". Required. Default value is
     "code_interpreter".
    :vartype type: str
    :ivar code_interpreter: The Code Interpreter data for the tool call.
    :vartype code_interpreter: ~azure.ai.agents.models.RunStepDeltaCodeInterpreterDetailItemObject
    """

    type: Literal["code_interpreter"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always \"code_interpreter.\". Required. Default value is
     \"code_interpreter\"."""
    code_interpreter: Optional["_models.RunStepDeltaCodeInterpreterDetailItemObject"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The Code Interpreter data for the tool call."""

    @overload
    def __init__(
        self,
        *,
        index: int,
        id: str,  # pylint: disable=redefined-builtin
        code_interpreter: Optional["_models.RunStepDeltaCodeInterpreterDetailItemObject"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="code_interpreter", **kwargs)


class RunStepDeltaDetail(_Model):
    """Represents a single run step detail item in a streaming run step's delta payload.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    RunStepDeltaMessageCreation, RunStepDeltaToolCallObject

    :ivar type: The object type for the run step detail object. Required. Default value is None.
    :vartype type: str
    """

    __mapping__: Dict[str, _Model] = {}
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """The object type for the run step detail object. Required. Default value is None."""

    @overload
    def __init__(
        self,
        *,
        type: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RunStepDeltaFileSearchToolCall(RunStepDeltaToolCall, discriminator="file_search"):
    """Represents a file search tool call within a streaming run step's tool call details.

    :ivar index: The index of the tool call detail in the run step's tool_calls array. Required.
    :vartype index: int
    :ivar id: The ID of the tool call, used when submitting outputs to the run. Required.
    :vartype id: str
    :ivar type: The object type, which is always "file_search.". Required. Default value is
     "file_search".
    :vartype type: str
    :ivar file_search: Reserved for future use.
    :vartype file_search: ~azure.ai.agents.models.RunStepFileSearchToolCallResults
    """

    type: Literal["file_search"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always \"file_search.\". Required. Default value is \"file_search\"."""
    file_search: Optional["_models.RunStepFileSearchToolCallResults"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Reserved for future use."""

    @overload
    def __init__(
        self,
        *,
        index: int,
        id: str,  # pylint: disable=redefined-builtin
        file_search: Optional["_models.RunStepFileSearchToolCallResults"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="file_search", **kwargs)


class RunStepDeltaFunction(_Model):
    """Represents the function data in a streaming run step delta's function tool call.

    :ivar name: The name of the function.
    :vartype name: str
    :ivar arguments: The arguments passed to the function as input.
    :vartype arguments: str
    :ivar output: The output of the function, null if outputs have not yet been submitted.
    :vartype output: str
    """

    name: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The name of the function."""
    arguments: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The arguments passed to the function as input."""
    output: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The output of the function, null if outputs have not yet been submitted."""

    @overload
    def __init__(
        self,
        *,
        name: Optional[str] = None,
        arguments: Optional[str] = None,
        output: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RunStepDeltaFunctionToolCall(RunStepDeltaToolCall, discriminator="function"):
    """Represents a function tool call within a streaming run step's tool call details.

    :ivar index: The index of the tool call detail in the run step's tool_calls array. Required.
    :vartype index: int
    :ivar id: The ID of the tool call, used when submitting outputs to the run. Required.
    :vartype id: str
    :ivar type: The object type, which is always "function.". Required. Default value is
     "function".
    :vartype type: str
    :ivar function: The function data for the tool call.
    :vartype function: ~azure.ai.agents.models.RunStepDeltaFunction
    """

    type: Literal["function"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always \"function.\". Required. Default value is \"function\"."""
    function: Optional["_models.RunStepDeltaFunction"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The function data for the tool call."""

    @overload
    def __init__(
        self,
        *,
        index: int,
        id: str,  # pylint: disable=redefined-builtin
        function: Optional["_models.RunStepDeltaFunction"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="function", **kwargs)


class RunStepDeltaMessageCreation(RunStepDeltaDetail, discriminator="message_creation"):
    """Represents a message creation within a streaming run step delta.

    :ivar type: The object type, which is always "message_creation.". Required. Default value is
     "message_creation".
    :vartype type: str
    :ivar message_creation: The message creation data.
    :vartype message_creation: ~azure.ai.agents.models.RunStepDeltaMessageCreationObject
    """

    type: Literal["message_creation"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always \"message_creation.\". Required. Default value is
     \"message_creation\"."""
    message_creation: Optional["_models.RunStepDeltaMessageCreationObject"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The message creation data."""

    @overload
    def __init__(
        self,
        *,
        message_creation: Optional["_models.RunStepDeltaMessageCreationObject"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="message_creation", **kwargs)


class RunStepDeltaMessageCreationObject(_Model):
    """Represents the data within a streaming run step message creation response object.

    :ivar message_id: The ID of the newly-created message.
    :vartype message_id: str
    """

    message_id: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the newly-created message."""

    @overload
    def __init__(
        self,
        *,
        message_id: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RunStepDeltaToolCallObject(RunStepDeltaDetail, discriminator="tool_calls"):
    """Represents an invocation of tool calls as part of a streaming run step.

    :ivar type: The object type, which is always "tool_calls.". Required. Default value is
     "tool_calls".
    :vartype type: str
    :ivar tool_calls: The collection of tool calls for the tool call detail item.
    :vartype tool_calls: list[~azure.ai.agents.models.RunStepDeltaToolCall]
    """

    type: Literal["tool_calls"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always \"tool_calls.\". Required. Default value is \"tool_calls\"."""
    tool_calls: Optional[List["_models.RunStepDeltaToolCall"]] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The collection of tool calls for the tool call detail item."""

    @overload
    def __init__(
        self,
        *,
        tool_calls: Optional[List["_models.RunStepDeltaToolCall"]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="tool_calls", **kwargs)


class RunStepDetails(_Model):
    """An abstract representation of the details for a run step.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    RunStepMessageCreationDetails, RunStepToolCallDetails

    :ivar type: The object type. Required. Known values are: "message_creation" and "tool_calls".
    :vartype type: str or ~azure.ai.agents.models.RunStepType
    """

    __mapping__: Dict[str, _Model] = {}
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """The object type. Required. Known values are: \"message_creation\" and \"tool_calls\"."""

    @overload
    def __init__(
        self,
        *,
        type: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RunStepError(_Model):
    """The error information associated with a failed run step.

    :ivar code: The error code for this error. Required. Known values are: "server_error" and
     "rate_limit_exceeded".
    :vartype code: str or ~azure.ai.agents.models.RunStepErrorCode
    :ivar message: The human-readable text associated with this error. Required.
    :vartype message: str
    """

    code: Union[str, "_models.RunStepErrorCode"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The error code for this error. Required. Known values are: \"server_error\" and
     \"rate_limit_exceeded\"."""
    message: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The human-readable text associated with this error. Required."""

    @overload
    def __init__(
        self,
        *,
        code: Union[str, "_models.RunStepErrorCode"],
        message: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RunStepFileSearchToolCall(RunStepToolCall, discriminator="file_search"):
    """A record of a call to a file search tool, issued by the model in evaluation of a defined tool,
    that represents
    executed file search.

    :ivar type: The object type, which is always 'file_search'. Required. Default value is
     "file_search".
    :vartype type: str
    :ivar id: The ID of the tool call. This ID must be referenced when you submit tool outputs.
     Required.
    :vartype id: str
    :ivar file_search: For now, this is always going to be an empty object. Required.
    :vartype file_search: ~azure.ai.agents.models.RunStepFileSearchToolCallResults
    """

    type: Literal["file_search"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'file_search'. Required. Default value is \"file_search\"."""
    file_search: "_models.RunStepFileSearchToolCallResults" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """For now, this is always going to be an empty object. Required."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        file_search: "_models.RunStepFileSearchToolCallResults",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="file_search", **kwargs)


class RunStepFileSearchToolCallResult(_Model):
    """File search tool call result.

    :ivar file_id: The ID of the file that result was found in. Required.
    :vartype file_id: str
    :ivar file_name: The name of the file that result was found in. Required.
    :vartype file_name: str
    :ivar score: The score of the result. All values must be a floating point number between 0 and
     1. Required.
    :vartype score: float
    :ivar content: The content of the result that was found. The content is only included if
     requested via the include query parameter.
    :vartype content: list[~azure.ai.agents.models.FileSearchToolCallContent]
    """

    file_id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the file that result was found in. Required."""
    file_name: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The name of the file that result was found in. Required."""
    score: float = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The score of the result. All values must be a floating point number between 0 and 1. Required."""
    content: Optional[List["_models.FileSearchToolCallContent"]] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The content of the result that was found. The content is only included if requested via the
     include query parameter."""

    @overload
    def __init__(
        self,
        *,
        file_id: str,
        file_name: str,
        score: float,
        content: Optional[List["_models.FileSearchToolCallContent"]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RunStepFileSearchToolCallResults(_Model):
    """The results of the file search.

    :ivar ranking_options: Ranking options for file search.
    :vartype ranking_options: ~azure.ai.agents.models.FileSearchRankingOptions
    :ivar results: The array of a file search results. Required.
    :vartype results: list[~azure.ai.agents.models.RunStepFileSearchToolCallResult]
    """

    ranking_options: Optional["_models.FileSearchRankingOptions"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Ranking options for file search."""
    results: List["_models.RunStepFileSearchToolCallResult"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The array of a file search results. Required."""

    @overload
    def __init__(
        self,
        *,
        results: List["_models.RunStepFileSearchToolCallResult"],
        ranking_options: Optional["_models.FileSearchRankingOptions"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RunStepFunctionToolCall(RunStepToolCall, discriminator="function"):
    """A record of a call to a function tool, issued by the model in evaluation of a defined tool,
    that represents the inputs
    and output consumed and emitted by the specified function.

    :ivar id: The ID of the tool call. This ID must be referenced when you submit tool outputs.
     Required.
    :vartype id: str
    :ivar type: The object type, which is always 'function'. Required. Default value is "function".
    :vartype type: str
    :ivar function: The detailed information about the function called by the model. Required.
    :vartype function: ~azure.ai.agents.models.RunStepFunctionToolCallDetails
    """

    type: Literal["function"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'function'. Required. Default value is \"function\"."""
    function: "_models.RunStepFunctionToolCallDetails" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The detailed information about the function called by the model. Required."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        function: "_models.RunStepFunctionToolCallDetails",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="function", **kwargs)


class RunStepFunctionToolCallDetails(_Model):
    """The detailed information about the function called by the model.

    :ivar name: The name of the function. Required.
    :vartype name: str
    :ivar arguments: The arguments that the model requires are provided to the named function.
     Required.
    :vartype arguments: str
    """

    name: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The name of the function. Required."""
    arguments: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The arguments that the model requires are provided to the named function. Required."""

    @overload
    def __init__(
        self,
        *,
        name: str,
        arguments: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RunStepMessageCreationDetails(RunStepDetails, discriminator="message_creation"):
    """The detailed information associated with a message creation run step.

    :ivar type: The object type, which is always 'message_creation'. Required. Represents a run
     step to create a message.
    :vartype type: str or ~azure.ai.agents.models.MESSAGE_CREATION
    :ivar message_creation: Information about the message creation associated with this run step.
     Required.
    :vartype message_creation: ~azure.ai.agents.models.RunStepMessageCreationReference
    """

    type: Literal[RunStepType.MESSAGE_CREATION] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'message_creation'. Required. Represents a run step to create
     a message."""
    message_creation: "_models.RunStepMessageCreationReference" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Information about the message creation associated with this run step. Required."""

    @overload
    def __init__(
        self,
        *,
        message_creation: "_models.RunStepMessageCreationReference",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=RunStepType.MESSAGE_CREATION, **kwargs)


class RunStepMessageCreationReference(_Model):
    """The details of a message created as a part of a run step.

    :ivar message_id: The ID of the message created by this run step. Required.
    :vartype message_id: str
    """

    message_id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the message created by this run step. Required."""

    @overload
    def __init__(
        self,
        *,
        message_id: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class RunStepOpenAPIToolCall(RunStepToolCall, discriminator="openapi"):
    """A record of a call to an OpenAPI tool, issued by the model in evaluation of a defined tool,
    that represents
    executed OpenAPI operations.

    :ivar id: The ID of the tool call. This ID must be referenced when you submit tool outputs.
     Required.
    :vartype id: str
    :ivar type: The object type, which is always 'openapi'. Required. Default value is "openapi".
    :vartype type: str
    :ivar open_api: Reserved for future use. Required.
    :vartype open_api: dict[str, str]
    """

    type: Literal["openapi"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'openapi'. Required. Default value is \"openapi\"."""
    open_api: Dict[str, str] = rest_field(name="openapi", visibility=["read", "create", "update", "delete", "query"])
    """Reserved for future use. Required."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        open_api: Dict[str, str],
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="openapi", **kwargs)


class RunStepToolCallDetails(RunStepDetails, discriminator="tool_calls"):
    """The detailed information associated with a run step calling tools.

    :ivar type: The object type, which is always 'tool_calls'. Required. Represents a run step that
     calls tools.
    :vartype type: str or ~azure.ai.agents.models.TOOL_CALLS
    :ivar tool_calls: A list of tool call details for this run step. Required.
    :vartype tool_calls: list[~azure.ai.agents.models.RunStepToolCall]
    """

    type: Literal[RunStepType.TOOL_CALLS] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'tool_calls'. Required. Represents a run step that calls
     tools."""
    tool_calls: List["_models.RunStepToolCall"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A list of tool call details for this run step. Required."""

    @overload
    def __init__(
        self,
        *,
        tool_calls: List["_models.RunStepToolCall"],
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=RunStepType.TOOL_CALLS, **kwargs)


class SubmitToolOutputsAction(RequiredAction, discriminator="submit_tool_outputs"):
    """The details for required tool calls that must be submitted for an agent thread run to continue.

    :ivar type: The object type, which is always 'submit_tool_outputs'. Required. Default value is
     "submit_tool_outputs".
    :vartype type: str
    :ivar submit_tool_outputs: The details describing tools that should be called to submit tool
     outputs. Required.
    :vartype submit_tool_outputs: ~azure.ai.agents.models.SubmitToolOutputsDetails
    """

    type: Literal["submit_tool_outputs"] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'submit_tool_outputs'. Required. Default value is
     \"submit_tool_outputs\"."""
    submit_tool_outputs: "_models.SubmitToolOutputsDetails" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The details describing tools that should be called to submit tool outputs. Required."""

    @overload
    def __init__(
        self,
        *,
        submit_tool_outputs: "_models.SubmitToolOutputsDetails",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type="submit_tool_outputs", **kwargs)


class SubmitToolOutputsDetails(_Model):
    """The details describing tools that should be called to submit tool outputs.

    :ivar tool_calls: The list of tool calls that must be resolved for the agent thread run to
     continue. Required.
    :vartype tool_calls: list[~azure.ai.agents.models.RequiredToolCall]
    """

    tool_calls: List["_models.RequiredToolCall"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The list of tool calls that must be resolved for the agent thread run to continue. Required."""

    @overload
    def __init__(
        self,
        *,
        tool_calls: List["_models.RequiredToolCall"],
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class ThreadDeletionStatus(_Model):
    """The status of a thread deletion operation.

    :ivar id: The ID of the resource specified for deletion. Required.
    :vartype id: str
    :ivar deleted: A value indicating whether deletion was successful. Required.
    :vartype deleted: bool
    :ivar object: The object type, which is always 'thread.deleted'. Required. Default value is
     "thread.deleted".
    :vartype object: str
    """

    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the resource specified for deletion. Required."""
    deleted: bool = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A value indicating whether deletion was successful. Required."""
    object: Literal["thread.deleted"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The object type, which is always 'thread.deleted'. Required. Default value is
     \"thread.deleted\"."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        deleted: bool,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.object: Literal["thread.deleted"] = "thread.deleted"


class ThreadMessage(_Model):
    """A single, existing message within an agent thread.

    :ivar id: The identifier, which can be referenced in API endpoints. Required.
    :vartype id: str
    :ivar object: The object type, which is always 'thread.message'. Required. Default value is
     "thread.message".
    :vartype object: str
    :ivar created_at: The Unix timestamp, in seconds, representing when this object was created.
     Required.
    :vartype created_at: ~datetime.datetime
    :ivar thread_id: The ID of the thread that this message belongs to. Required.
    :vartype thread_id: str
    :ivar status: The status of the message. Required. Known values are: "in_progress",
     "incomplete", and "completed".
    :vartype status: str or ~azure.ai.agents.models.MessageStatus
    :ivar incomplete_details: On an incomplete message, details about why the message is
     incomplete. Required.
    :vartype incomplete_details: ~azure.ai.agents.models.MessageIncompleteDetails
    :ivar completed_at: The Unix timestamp (in seconds) for when the message was completed.
     Required.
    :vartype completed_at: ~datetime.datetime
    :ivar incomplete_at: The Unix timestamp (in seconds) for when the message was marked as
     incomplete. Required.
    :vartype incomplete_at: ~datetime.datetime
    :ivar role: The role associated with the agent thread message. Required. Known values are:
     "user" and "assistant".
    :vartype role: str or ~azure.ai.agents.models.MessageRole
    :ivar content: The list of content items associated with the agent thread message. Required.
    :vartype content: list[~azure.ai.agents.models.MessageContent]
    :ivar agent_id: If applicable, the ID of the agent that authored this message. Required.
    :vartype agent_id: str
    :ivar run_id: If applicable, the ID of the run associated with the authoring of this message.
     Required.
    :vartype run_id: str
    :ivar attachments: A list of files attached to the message, and the tools they were added to.
     Required.
    :vartype attachments: list[~azure.ai.agents.models.MessageAttachment]
    :ivar metadata: A set of up to 16 key/value pairs that can be attached to an object, used for
     storing additional information about that object in a structured format. Keys may be up to 64
     characters in length and values may be up to 512 characters in length. Required.
    :vartype metadata: dict[str, str]
    """

    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The identifier, which can be referenced in API endpoints. Required."""
    object: Literal["thread.message"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The object type, which is always 'thread.message'. Required. Default value is
     \"thread.message\"."""
    created_at: datetime.datetime = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp, in seconds, representing when this object was created. Required."""
    thread_id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the thread that this message belongs to. Required."""
    status: Union[str, "_models.MessageStatus"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The status of the message. Required. Known values are: \"in_progress\", \"incomplete\", and
     \"completed\"."""
    incomplete_details: "_models.MessageIncompleteDetails" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """On an incomplete message, details about why the message is incomplete. Required."""
    completed_at: datetime.datetime = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp (in seconds) for when the message was completed. Required."""
    incomplete_at: datetime.datetime = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp (in seconds) for when the message was marked as incomplete. Required."""
    role: Union[str, "_models.MessageRole"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The role associated with the agent thread message. Required. Known values are: \"user\" and
     \"assistant\"."""
    content: List["_models.MessageContent"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The list of content items associated with the agent thread message. Required."""
    agent_id: str = rest_field(name="assistant_id", visibility=["read", "create", "update", "delete", "query"])
    """If applicable, the ID of the agent that authored this message. Required."""
    run_id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """If applicable, the ID of the run associated with the authoring of this message. Required."""
    attachments: List["_models.MessageAttachment"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """A list of files attached to the message, and the tools they were added to. Required."""
    metadata: Dict[str, str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A set of up to 16 key/value pairs that can be attached to an object, used for storing
     additional information about that object in a structured format. Keys may be up to 64
     characters in length and values may be up to 512 characters in length. Required."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        created_at: datetime.datetime,
        thread_id: str,
        status: Union[str, "_models.MessageStatus"],
        incomplete_details: "_models.MessageIncompleteDetails",
        completed_at: datetime.datetime,
        incomplete_at: datetime.datetime,
        role: Union[str, "_models.MessageRole"],
        content: List["_models.MessageContent"],
        agent_id: str,
        run_id: str,
        attachments: List["_models.MessageAttachment"],
        metadata: Dict[str, str],
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.object: Literal["thread.message"] = "thread.message"


class ThreadMessageOptions(_Model):
    """A single message within an agent thread,
    as provided during that thread's creation for its initial state.

    :ivar role: The role of the entity that is creating the message. Allowed values include:
     ``user``, which indicates the message is sent by an actual user (and should be
     used in most cases to represent user-generated messages), and ``assistant``,
     which indicates the message is generated by the agent (use this value to insert
     messages from the agent into the conversation). Required. Known values are: "user" and
     "assistant".
    :vartype role: str or ~azure.ai.agents.models.MessageRole
    :ivar content: The content of the initial message. This may be a basic string (if you only
     need text) or an array of typed content blocks (for example, text, image_file,
     image_url, and so on). Required. Is either a str type or a [MessageInputContentBlock] type.
    :vartype content: str or list[~azure.ai.agents.models.MessageInputContentBlock]
    :ivar attachments: A list of files attached to the message, and the tools they should be added
     to.
    :vartype attachments: list[~azure.ai.agents.models.MessageAttachment]
    :ivar metadata: A set of up to 16 key/value pairs that can be attached to an object, used for
     storing additional information about that object in a structured format. Keys may be up to 64
     characters in length and values may be up to 512 characters in length.
    :vartype metadata: dict[str, str]
    """

    role: Union[str, "_models.MessageRole"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The role of the entity that is creating the message. Allowed values include:
     ``user``, which indicates the message is sent by an actual user (and should be
     used in most cases to represent user-generated messages), and ``assistant``,
     which indicates the message is generated by the agent (use this value to insert
     messages from the agent into the conversation). Required. Known values are: \"user\" and
     \"assistant\"."""
    content: "_types.MessageInputContent" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The content of the initial message. This may be a basic string (if you only
     need text) or an array of typed content blocks (for example, text, image_file,
     image_url, and so on). Required. Is either a str type or a [MessageInputContentBlock] type."""
    attachments: Optional[List["_models.MessageAttachment"]] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """A list of files attached to the message, and the tools they should be added to."""
    metadata: Optional[Dict[str, str]] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A set of up to 16 key/value pairs that can be attached to an object, used for storing
     additional information about that object in a structured format. Keys may be up to 64
     characters in length and values may be up to 512 characters in length."""

    @overload
    def __init__(
        self,
        *,
        role: Union[str, "_models.MessageRole"],
        content: "_types.MessageInputContent",
        attachments: Optional[List["_models.MessageAttachment"]] = None,
        metadata: Optional[Dict[str, str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class ThreadRun(_Model):
    """Data representing a single evaluation run of an agent thread.

    :ivar id: The identifier, which can be referenced in API endpoints. Required.
    :vartype id: str
    :ivar object: The object type, which is always 'thread.run'. Required. Default value is
     "thread.run".
    :vartype object: str
    :ivar thread_id: The ID of the thread associated with this run. Required.
    :vartype thread_id: str
    :ivar agent_id: The ID of the agent associated with the thread this run was performed against.
     Required.
    :vartype agent_id: str
    :ivar status: The status of the agent thread run. Required. Known values are: "queued",
     "in_progress", "requires_action", "cancelling", "cancelled", "failed", "completed", and
     "expired".
    :vartype status: str or ~azure.ai.agents.models.RunStatus
    :ivar required_action: The details of the action required for the agent thread run to continue.
    :vartype required_action: ~azure.ai.agents.models.RequiredAction
    :ivar last_error: The last error, if any, encountered by this agent thread run. Required.
    :vartype last_error: ~azure.ai.agents.models.RunError
    :ivar model: The ID of the model to use. Required.
    :vartype model: str
    :ivar instructions: The overridden system instructions used for this agent thread run.
     Required.
    :vartype instructions: str
    :ivar tools: The overridden enabled tools used for this agent thread run. Required.
    :vartype tools: list[~azure.ai.agents.models.ToolDefinition]
    :ivar created_at: The Unix timestamp, in seconds, representing when this object was created.
     Required.
    :vartype created_at: ~datetime.datetime
    :ivar expires_at: The Unix timestamp, in seconds, representing when this item expires.
     Required.
    :vartype expires_at: ~datetime.datetime
    :ivar started_at: The Unix timestamp, in seconds, representing when this item was started.
     Required.
    :vartype started_at: ~datetime.datetime
    :ivar completed_at: The Unix timestamp, in seconds, representing when this completed. Required.
    :vartype completed_at: ~datetime.datetime
    :ivar cancelled_at: The Unix timestamp, in seconds, representing when this was cancelled.
     Required.
    :vartype cancelled_at: ~datetime.datetime
    :ivar failed_at: The Unix timestamp, in seconds, representing when this failed. Required.
    :vartype failed_at: ~datetime.datetime
    :ivar incomplete_details: Details on why the run is incomplete. Will be ``null`` if the run is
     not incomplete. Required.
    :vartype incomplete_details: ~azure.ai.agents.models.IncompleteRunDetails
    :ivar usage: Usage statistics related to the run. This value will be ``null`` if the run is not
     in a terminal state (i.e. ``in_progress``, ``queued``, etc.). Required.
    :vartype usage: ~azure.ai.agents.models.RunCompletionUsage
    :ivar temperature: The sampling temperature used for this run. If not set, defaults to 1.
    :vartype temperature: float
    :ivar top_p: The nucleus sampling value used for this run. If not set, defaults to 1.
    :vartype top_p: float
    :ivar max_prompt_tokens: The maximum number of prompt tokens specified to have been used over
     the course of the run. Required.
    :vartype max_prompt_tokens: int
    :ivar max_completion_tokens: The maximum number of completion tokens specified to have been
     used over the course of the run. Required.
    :vartype max_completion_tokens: int
    :ivar truncation_strategy: The strategy to use for dropping messages as the context windows
     moves forward. Required.
    :vartype truncation_strategy: ~azure.ai.agents.models.TruncationObject
    :ivar tool_choice: Controls whether or not and which tool is called by the model. Required. Is
     one of the following types: str, Union[str, "_models.AgentsToolChoiceOptionMode"],
     AgentsNamedToolChoice
    :vartype tool_choice: str or str or ~azure.ai.agents.models.AgentsToolChoiceOptionMode or
     ~azure.ai.agents.models.AgentsNamedToolChoice
    :ivar response_format: The response format of the tool calls used in this run. Required. Is one
     of the following types: str, Union[str, "_models.AgentsResponseFormatMode"],
     AgentsResponseFormat, ResponseFormatJsonSchemaType
    :vartype response_format: str or str or ~azure.ai.agents.models.AgentsResponseFormatMode or
     ~azure.ai.agents.models.AgentsResponseFormat or
     ~azure.ai.agents.models.ResponseFormatJsonSchemaType
    :ivar metadata: A set of up to 16 key/value pairs that can be attached to an object, used for
     storing additional information about that object in a structured format. Keys may be up to 64
     characters in length and values may be up to 512 characters in length. Required.
    :vartype metadata: dict[str, str]
    :ivar tool_resources: Override the tools the agent can use for this run. This is useful for
     modifying the behavior on a per-run basis.
    :vartype tool_resources: ~azure.ai.agents.models.ToolResources
    :ivar parallel_tool_calls: Determines if tools can be executed in parallel within the run.
     Required.
    :vartype parallel_tool_calls: bool
    """

    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The identifier, which can be referenced in API endpoints. Required."""
    object: Literal["thread.run"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The object type, which is always 'thread.run'. Required. Default value is \"thread.run\"."""
    thread_id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the thread associated with this run. Required."""
    agent_id: str = rest_field(name="assistant_id", visibility=["read", "create", "update", "delete", "query"])
    """The ID of the agent associated with the thread this run was performed against. Required."""
    status: Union[str, "_models.RunStatus"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The status of the agent thread run. Required. Known values are: \"queued\", \"in_progress\",
     \"requires_action\", \"cancelling\", \"cancelled\", \"failed\", \"completed\", and \"expired\"."""
    required_action: Optional["_models.RequiredAction"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The details of the action required for the agent thread run to continue."""
    last_error: "_models.RunError" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The last error, if any, encountered by this agent thread run. Required."""
    model: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the model to use. Required."""
    instructions: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The overridden system instructions used for this agent thread run. Required."""
    tools: List["_models.ToolDefinition"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The overridden enabled tools used for this agent thread run. Required."""
    created_at: datetime.datetime = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp, in seconds, representing when this object was created. Required."""
    expires_at: datetime.datetime = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp, in seconds, representing when this item expires. Required."""
    started_at: datetime.datetime = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp, in seconds, representing when this item was started. Required."""
    completed_at: datetime.datetime = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp, in seconds, representing when this completed. Required."""
    cancelled_at: datetime.datetime = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp, in seconds, representing when this was cancelled. Required."""
    failed_at: datetime.datetime = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp, in seconds, representing when this failed. Required."""
    incomplete_details: "_models.IncompleteRunDetails" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Details on why the run is incomplete. Will be ``null`` if the run is not incomplete. Required."""
    usage: "_models.RunCompletionUsage" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Usage statistics related to the run. This value will be ``null`` if the run is not in a
     terminal state (i.e. ``in_progress``, ``queued``, etc.). Required."""
    temperature: Optional[float] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The sampling temperature used for this run. If not set, defaults to 1."""
    top_p: Optional[float] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The nucleus sampling value used for this run. If not set, defaults to 1."""
    max_prompt_tokens: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The maximum number of prompt tokens specified to have been used over the course of the run.
     Required."""
    max_completion_tokens: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The maximum number of completion tokens specified to have been used over the course of the run.
     Required."""
    truncation_strategy: "_models.TruncationObject" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The strategy to use for dropping messages as the context windows moves forward. Required."""
    tool_choice: "_types.AgentsToolChoiceOption" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Controls whether or not and which tool is called by the model. Required. Is one of the
     following types: str, Union[str, \"_models.AgentsToolChoiceOptionMode\"], AgentsNamedToolChoice"""
    response_format: "_types.AgentsResponseFormatOption" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The response format of the tool calls used in this run. Required. Is one of the following
     types: str, Union[str, \"_models.AgentsResponseFormatMode\"], AgentsResponseFormat,
     ResponseFormatJsonSchemaType"""
    metadata: Dict[str, str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A set of up to 16 key/value pairs that can be attached to an object, used for storing
     additional information about that object in a structured format. Keys may be up to 64
     characters in length and values may be up to 512 characters in length. Required."""
    tool_resources: Optional["_models.ToolResources"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Override the tools the agent can use for this run. This is useful for modifying the behavior on
     a per-run basis."""
    parallel_tool_calls: bool = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Determines if tools can be executed in parallel within the run. Required."""

    @overload
    def __init__(  # pylint: disable=too-many-locals
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        thread_id: str,
        agent_id: str,
        status: Union[str, "_models.RunStatus"],
        last_error: "_models.RunError",
        model: str,
        instructions: str,
        tools: List["_models.ToolDefinition"],
        created_at: datetime.datetime,
        expires_at: datetime.datetime,
        started_at: datetime.datetime,
        completed_at: datetime.datetime,
        cancelled_at: datetime.datetime,
        failed_at: datetime.datetime,
        incomplete_details: "_models.IncompleteRunDetails",
        usage: "_models.RunCompletionUsage",
        max_prompt_tokens: int,
        max_completion_tokens: int,
        truncation_strategy: "_models.TruncationObject",
        tool_choice: "_types.AgentsToolChoiceOption",
        response_format: "_types.AgentsResponseFormatOption",
        metadata: Dict[str, str],
        parallel_tool_calls: bool,
        required_action: Optional["_models.RequiredAction"] = None,
        temperature: Optional[float] = None,
        top_p: Optional[float] = None,
        tool_resources: Optional["_models.ToolResources"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.object: Literal["thread.run"] = "thread.run"


class ToolOutput(_Model):
    """The data provided during a tool outputs submission to resolve pending tool calls and allow the
    model to continue.

    :ivar tool_call_id: The ID of the tool call being resolved, as provided in the tool calls of a
     required action from a run.
    :vartype tool_call_id: str
    :ivar output: The output from the tool to be submitted.
    :vartype output: str
    """

    tool_call_id: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the tool call being resolved, as provided in the tool calls of a required action from
     a run."""
    output: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The output from the tool to be submitted."""

    @overload
    def __init__(
        self,
        *,
        tool_call_id: Optional[str] = None,
        output: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class ToolResources(_Model):
    """A set of resources that are used by the agent's tools. The resources are specific to the type
    of
    tool. For example, the ``code_interpreter`` tool requires a list of file IDs, while the
    ``file_search``
    tool requires a list of vector store IDs.

    :ivar code_interpreter: Resources to be used by the ``code_interpreter`` tool consisting of
     file IDs.
    :vartype code_interpreter: ~azure.ai.agents.models.CodeInterpreterToolResource
    :ivar file_search: Resources to be used by the ``file_search`` tool consisting of vector store
     IDs.
    :vartype file_search: ~azure.ai.agents.models.FileSearchToolResource
    :ivar azure_ai_search: Resources to be used by the ``azure_ai_search`` tool consisting of index
     IDs and names.
    :vartype azure_ai_search: ~azure.ai.agents.models.AzureAISearchToolResource
    """

    code_interpreter: Optional["_models.CodeInterpreterToolResource"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Resources to be used by the ``code_interpreter`` tool consisting of file IDs."""
    file_search: Optional["_models.FileSearchToolResource"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Resources to be used by the ``file_search`` tool consisting of vector store IDs."""
    azure_ai_search: Optional["_models.AzureAISearchToolResource"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Resources to be used by the ``azure_ai_search`` tool consisting of index IDs and names."""

    @overload
    def __init__(
        self,
        *,
        code_interpreter: Optional["_models.CodeInterpreterToolResource"] = None,
        file_search: Optional["_models.FileSearchToolResource"] = None,
        azure_ai_search: Optional["_models.AzureAISearchToolResource"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class TruncationObject(_Model):
    """Controls for how a thread will be truncated prior to the run. Use this to control the initial
    context window of the run.

    :ivar type: The truncation strategy to use for the thread. The default is ``auto``. If set to
     ``last_messages``, the thread will
     be truncated to the ``lastMessages`` count most recent messages in the thread. When set to
     ``auto``, messages in the middle of the thread
     will be dropped to fit the context length of the model, ``max_prompt_tokens``. Required. Known
     values are: "auto" and "last_messages".
    :vartype type: str or ~azure.ai.agents.models.TruncationStrategy
    :ivar last_messages: The number of most recent messages from the thread when constructing the
     context for the run.
    :vartype last_messages: int
    """

    type: Union[str, "_models.TruncationStrategy"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The truncation strategy to use for the thread. The default is ``auto``. If set to
     ``last_messages``, the thread will
     be truncated to the ``lastMessages`` count most recent messages in the thread. When set to
     ``auto``, messages in the middle of the thread
     will be dropped to fit the context length of the model, ``max_prompt_tokens``. Required. Known
     values are: \"auto\" and \"last_messages\"."""
    last_messages: Optional[int] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The number of most recent messages from the thread when constructing the context for the run."""

    @overload
    def __init__(
        self,
        *,
        type: Union[str, "_models.TruncationStrategy"],
        last_messages: Optional[int] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class UploadFileRequest(_Model):
    """UploadFileRequest.

    :ivar file: The file data, in bytes. Required.
    :vartype file: ~azure.ai.agents._utils.utils.FileType
    :ivar purpose: The intended purpose of the uploaded file. Use ``assistants`` for Agents and
     Message files, ``vision`` for Agents image file inputs, ``batch`` for Batch API, and
     ``fine-tune`` for Fine-tuning. Required. Known values are: "assistants", "assistants_output",
     and "vision".
    :vartype purpose: str or ~azure.ai.agents.models.FilePurpose
    :ivar filename: The name of the file.
    :vartype filename: str
    """

    file: FileType = rest_field(
        visibility=["read", "create", "update", "delete", "query"], is_multipart_file_input=True
    )
    """The file data, in bytes. Required."""
    purpose: Union[str, "_models.FilePurpose"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The intended purpose of the uploaded file. Use ``assistants`` for Agents and Message files,
     ``vision`` for Agents image file inputs, ``batch`` for Batch API, and ``fine-tune`` for
     Fine-tuning. Required. Known values are: \"assistants\", \"assistants_output\", and \"vision\"."""
    filename: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The name of the file."""

    @overload
    def __init__(
        self,
        *,
        file: FileType,
        purpose: Union[str, "_models.FilePurpose"],
        filename: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class VectorStore(_Model):
    """A vector store is a collection of processed files can be used by the ``file_search`` tool.

    :ivar id: The identifier, which can be referenced in API endpoints. Required.
    :vartype id: str
    :ivar object: The object type, which is always ``vector_store``. Required. Default value is
     "vector_store".
    :vartype object: str
    :ivar created_at: The Unix timestamp (in seconds) for when the vector store was created.
     Required.
    :vartype created_at: ~datetime.datetime
    :ivar name: The name of the vector store. Required.
    :vartype name: str
    :ivar usage_bytes: The total number of bytes used by the files in the vector store. Required.
    :vartype usage_bytes: int
    :ivar file_counts: Files count grouped by status processed or being processed by this vector
     store. Required.
    :vartype file_counts: ~azure.ai.agents.models.VectorStoreFileCount
    :ivar status: The status of the vector store, which can be either ``expired``, ``in_progress``,
     or ``completed``. A status of ``completed`` indicates that the vector store is ready for use.
     Required. Known values are: "expired", "in_progress", and "completed".
    :vartype status: str or ~azure.ai.agents.models.VectorStoreStatus
    :ivar expires_after: Details on when this vector store expires.
    :vartype expires_after: ~azure.ai.agents.models.VectorStoreExpirationPolicy
    :ivar expires_at: The Unix timestamp (in seconds) for when the vector store will expire.
    :vartype expires_at: ~datetime.datetime
    :ivar last_active_at: The Unix timestamp (in seconds) for when the vector store was last
     active. Required.
    :vartype last_active_at: ~datetime.datetime
    :ivar metadata: A set of up to 16 key/value pairs that can be attached to an object, used for
     storing additional information about that object in a structured format. Keys may be up to 64
     characters in length and values may be up to 512 characters in length. Required.
    :vartype metadata: dict[str, str]
    """

    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The identifier, which can be referenced in API endpoints. Required."""
    object: Literal["vector_store"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The object type, which is always ``vector_store``. Required. Default value is \"vector_store\"."""
    created_at: datetime.datetime = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp (in seconds) for when the vector store was created. Required."""
    name: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The name of the vector store. Required."""
    usage_bytes: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The total number of bytes used by the files in the vector store. Required."""
    file_counts: "_models.VectorStoreFileCount" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Files count grouped by status processed or being processed by this vector store. Required."""
    status: Union[str, "_models.VectorStoreStatus"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The status of the vector store, which can be either ``expired``, ``in_progress``, or
     ``completed``. A status of ``completed`` indicates that the vector store is ready for use.
     Required. Known values are: \"expired\", \"in_progress\", and \"completed\"."""
    expires_after: Optional["_models.VectorStoreExpirationPolicy"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Details on when this vector store expires."""
    expires_at: Optional[datetime.datetime] = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp (in seconds) for when the vector store will expire."""
    last_active_at: datetime.datetime = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp (in seconds) for when the vector store was last active. Required."""
    metadata: Dict[str, str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A set of up to 16 key/value pairs that can be attached to an object, used for storing
     additional information about that object in a structured format. Keys may be up to 64
     characters in length and values may be up to 512 characters in length. Required."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        created_at: datetime.datetime,
        name: str,
        usage_bytes: int,
        file_counts: "_models.VectorStoreFileCount",
        status: Union[str, "_models.VectorStoreStatus"],
        last_active_at: datetime.datetime,
        metadata: Dict[str, str],
        expires_after: Optional["_models.VectorStoreExpirationPolicy"] = None,
        expires_at: Optional[datetime.datetime] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.object: Literal["vector_store"] = "vector_store"


class VectorStoreChunkingStrategyRequest(_Model):
    """An abstract representation of a vector store chunking strategy configuration.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    VectorStoreAutoChunkingStrategyRequest, VectorStoreStaticChunkingStrategyRequest

    :ivar type: The object type. Required. Known values are: "auto" and "static".
    :vartype type: str or ~azure.ai.agents.models.VectorStoreChunkingStrategyRequestType
    """

    __mapping__: Dict[str, _Model] = {}
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """The object type. Required. Known values are: \"auto\" and \"static\"."""

    @overload
    def __init__(
        self,
        *,
        type: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class VectorStoreAutoChunkingStrategyRequest(VectorStoreChunkingStrategyRequest, discriminator="auto"):
    """The default strategy. This strategy currently uses a max_chunk_size_tokens of 800 and
    chunk_overlap_tokens of 400.

    :ivar type: The object type, which is always 'auto'. Required.
    :vartype type: str or ~azure.ai.agents.models.AUTO
    """

    type: Literal[VectorStoreChunkingStrategyRequestType.AUTO] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'auto'. Required."""

    @overload
    def __init__(
        self,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=VectorStoreChunkingStrategyRequestType.AUTO, **kwargs)


class VectorStoreChunkingStrategyResponse(_Model):
    """An abstract representation of a vector store chunking strategy configuration.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    VectorStoreAutoChunkingStrategyResponse, VectorStoreStaticChunkingStrategyResponse

    :ivar type: The object type. Required. Known values are: "other" and "static".
    :vartype type: str or ~azure.ai.agents.models.VectorStoreChunkingStrategyResponseType
    """

    __mapping__: Dict[str, _Model] = {}
    type: str = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])
    """The object type. Required. Known values are: \"other\" and \"static\"."""

    @overload
    def __init__(
        self,
        *,
        type: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class VectorStoreAutoChunkingStrategyResponse(VectorStoreChunkingStrategyResponse, discriminator="other"):
    """This is returned when the chunking strategy is unknown. Typically, this is because the file was
    indexed before the chunking_strategy concept was introduced in the API.

    :ivar type: The object type, which is always 'other'. Required.
    :vartype type: str or ~azure.ai.agents.models.OTHER
    """

    type: Literal[VectorStoreChunkingStrategyResponseType.OTHER] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'other'. Required."""

    @overload
    def __init__(
        self,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=VectorStoreChunkingStrategyResponseType.OTHER, **kwargs)


class VectorStoreConfiguration(_Model):
    """Vector storage configuration is the list of data sources, used when multiple
    files can be used for the enterprise file search.

    :ivar data_sources: Data sources. Required.
    :vartype data_sources: list[~azure.ai.agents.models.VectorStoreDataSource]
    """

    data_sources: List["_models.VectorStoreDataSource"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Data sources. Required."""

    @overload
    def __init__(
        self,
        *,
        data_sources: List["_models.VectorStoreDataSource"],
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class VectorStoreConfigurations(_Model):
    """The structure, containing the list of vector storage configurations i.e. the list of azure
    asset IDs.

    :ivar store_name: Name. Required.
    :vartype store_name: str
    :ivar store_configuration: Configurations. Required.
    :vartype store_configuration: ~azure.ai.agents.models.VectorStoreConfiguration
    """

    store_name: str = rest_field(name="name", visibility=["read", "create", "update", "delete", "query"])
    """Name. Required."""
    store_configuration: "_models.VectorStoreConfiguration" = rest_field(
        name="configuration", visibility=["read", "create", "update", "delete", "query"]
    )
    """Configurations. Required."""

    @overload
    def __init__(
        self,
        *,
        store_name: str,
        store_configuration: "_models.VectorStoreConfiguration",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class VectorStoreDataSource(_Model):
    """The structure, containing Azure asset URI path and the asset type of the file used as a data
    source
    for the enterprise file search.

    :ivar asset_identifier: Asset URI. Required.
    :vartype asset_identifier: str
    :ivar asset_type: The asset type. Required. Known values are: "uri_asset" and "id_asset".
    :vartype asset_type: str or ~azure.ai.agents.models.VectorStoreDataSourceAssetType
    """

    asset_identifier: str = rest_field(name="uri", visibility=["read", "create", "update", "delete", "query"])
    """Asset URI. Required."""
    asset_type: Union[str, "_models.VectorStoreDataSourceAssetType"] = rest_field(
        name="type", visibility=["read", "create", "update", "delete", "query"]
    )
    """The asset type. Required. Known values are: \"uri_asset\" and \"id_asset\"."""

    @overload
    def __init__(
        self,
        *,
        asset_identifier: str,
        asset_type: Union[str, "_models.VectorStoreDataSourceAssetType"],
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class VectorStoreDeletionStatus(_Model):
    """Response object for deleting a vector store.

    :ivar id: The ID of the resource specified for deletion. Required.
    :vartype id: str
    :ivar deleted: A value indicating whether deletion was successful. Required.
    :vartype deleted: bool
    :ivar object: The object type, which is always 'vector_store.deleted'. Required. Default value
     is "vector_store.deleted".
    :vartype object: str
    """

    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the resource specified for deletion. Required."""
    deleted: bool = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A value indicating whether deletion was successful. Required."""
    object: Literal["vector_store.deleted"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The object type, which is always 'vector_store.deleted'. Required. Default value is
     \"vector_store.deleted\"."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        deleted: bool,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.object: Literal["vector_store.deleted"] = "vector_store.deleted"


class VectorStoreExpirationPolicy(_Model):
    """The expiration policy for a vector store.

    :ivar anchor: Anchor timestamp after which the expiration policy applies. Supported anchors:
     ``last_active_at``. Required. "last_active_at"
    :vartype anchor: str or ~azure.ai.agents.models.VectorStoreExpirationPolicyAnchor
    :ivar days: The anchor timestamp after which the expiration policy applies. Required.
    :vartype days: int
    """

    anchor: Union[str, "_models.VectorStoreExpirationPolicyAnchor"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """Anchor timestamp after which the expiration policy applies. Supported anchors:
     ``last_active_at``. Required. \"last_active_at\""""
    days: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The anchor timestamp after which the expiration policy applies. Required."""

    @overload
    def __init__(
        self,
        *,
        anchor: Union[str, "_models.VectorStoreExpirationPolicyAnchor"],
        days: int,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class VectorStoreFile(_Model):
    """Description of a file attached to a vector store.

    :ivar id: The identifier, which can be referenced in API endpoints. Required.
    :vartype id: str
    :ivar object: The object type, which is always ``vector_store.file``. Required. Default value
     is "vector_store.file".
    :vartype object: str
    :ivar usage_bytes: The total vector store usage in bytes. Note that this may be different from
     the original file
     size. Required.
    :vartype usage_bytes: int
    :ivar created_at: The Unix timestamp (in seconds) for when the vector store file was created.
     Required.
    :vartype created_at: ~datetime.datetime
    :ivar vector_store_id: The ID of the vector store that the file is attached to. Required.
    :vartype vector_store_id: str
    :ivar status: The status of the vector store file, which can be either ``in_progress``,
     ``completed``, ``cancelled``, or ``failed``. The status ``completed`` indicates that the vector
     store file is ready for use. Required. Known values are: "in_progress", "completed", "failed",
     and "cancelled".
    :vartype status: str or ~azure.ai.agents.models.VectorStoreFileStatus
    :ivar last_error: The last error associated with this vector store file. Will be ``null`` if
     there are no errors. Required.
    :vartype last_error: ~azure.ai.agents.models.VectorStoreFileError
    :ivar chunking_strategy: The strategy used to chunk the file. Required.
    :vartype chunking_strategy: ~azure.ai.agents.models.VectorStoreChunkingStrategyResponse
    """

    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The identifier, which can be referenced in API endpoints. Required."""
    object: Literal["vector_store.file"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The object type, which is always ``vector_store.file``. Required. Default value is
     \"vector_store.file\"."""
    usage_bytes: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The total vector store usage in bytes. Note that this may be different from the original file
     size. Required."""
    created_at: datetime.datetime = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp (in seconds) for when the vector store file was created. Required."""
    vector_store_id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the vector store that the file is attached to. Required."""
    status: Union[str, "_models.VectorStoreFileStatus"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The status of the vector store file, which can be either ``in_progress``, ``completed``,
     ``cancelled``, or ``failed``. The status ``completed`` indicates that the vector store file is
     ready for use. Required. Known values are: \"in_progress\", \"completed\", \"failed\", and
     \"cancelled\"."""
    last_error: "_models.VectorStoreFileError" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The last error associated with this vector store file. Will be ``null`` if there are no errors.
     Required."""
    chunking_strategy: "_models.VectorStoreChunkingStrategyResponse" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The strategy used to chunk the file. Required."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        usage_bytes: int,
        created_at: datetime.datetime,
        vector_store_id: str,
        status: Union[str, "_models.VectorStoreFileStatus"],
        last_error: "_models.VectorStoreFileError",
        chunking_strategy: "_models.VectorStoreChunkingStrategyResponse",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.object: Literal["vector_store.file"] = "vector_store.file"


class VectorStoreFileBatch(_Model):
    """A batch of files attached to a vector store.

    :ivar id: The identifier, which can be referenced in API endpoints. Required.
    :vartype id: str
    :ivar object: The object type, which is always ``vector_store.file_batch``. Required. Default
     value is "vector_store.files_batch".
    :vartype object: str
    :ivar created_at: The Unix timestamp (in seconds) for when the vector store files batch was
     created. Required.
    :vartype created_at: ~datetime.datetime
    :ivar vector_store_id: The ID of the vector store that the file is attached to. Required.
    :vartype vector_store_id: str
    :ivar status: The status of the vector store files batch, which can be either ``in_progress``,
     ``completed``, ``cancelled`` or ``failed``. Required. Known values are: "in_progress",
     "completed", "cancelled", and "failed".
    :vartype status: str or ~azure.ai.agents.models.VectorStoreFileBatchStatus
    :ivar file_counts: Files count grouped by status processed or being processed by this vector
     store. Required.
    :vartype file_counts: ~azure.ai.agents.models.VectorStoreFileCount
    """

    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The identifier, which can be referenced in API endpoints. Required."""
    object: Literal["vector_store.files_batch"] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The object type, which is always ``vector_store.file_batch``. Required. Default value is
     \"vector_store.files_batch\"."""
    created_at: datetime.datetime = rest_field(
        visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """The Unix timestamp (in seconds) for when the vector store files batch was created. Required."""
    vector_store_id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the vector store that the file is attached to. Required."""
    status: Union[str, "_models.VectorStoreFileBatchStatus"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The status of the vector store files batch, which can be either ``in_progress``, ``completed``,
     ``cancelled`` or ``failed``. Required. Known values are: \"in_progress\", \"completed\",
     \"cancelled\", and \"failed\"."""
    file_counts: "_models.VectorStoreFileCount" = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Files count grouped by status processed or being processed by this vector store. Required."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        created_at: datetime.datetime,
        vector_store_id: str,
        status: Union[str, "_models.VectorStoreFileBatchStatus"],
        file_counts: "_models.VectorStoreFileCount",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.object: Literal["vector_store.files_batch"] = "vector_store.files_batch"


class VectorStoreFileCount(_Model):
    """Counts of files processed or being processed by this vector store grouped by status.

    :ivar in_progress: The number of files that are currently being processed. Required.
    :vartype in_progress: int
    :ivar completed: The number of files that have been successfully processed. Required.
    :vartype completed: int
    :ivar failed: The number of files that have failed to process. Required.
    :vartype failed: int
    :ivar cancelled: The number of files that were cancelled. Required.
    :vartype cancelled: int
    :ivar total: The total number of files. Required.
    :vartype total: int
    """

    in_progress: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The number of files that are currently being processed. Required."""
    completed: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The number of files that have been successfully processed. Required."""
    failed: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The number of files that have failed to process. Required."""
    cancelled: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The number of files that were cancelled. Required."""
    total: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The total number of files. Required."""

    @overload
    def __init__(
        self,
        *,
        in_progress: int,
        completed: int,
        failed: int,
        cancelled: int,
        total: int,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class VectorStoreFileDeletionStatus(_Model):
    """Response object for deleting a vector store file relationship.

    :ivar id: The ID of the resource specified for deletion. Required.
    :vartype id: str
    :ivar deleted: A value indicating whether deletion was successful. Required.
    :vartype deleted: bool
    :ivar object: The object type, which is always 'vector_store.deleted'. Required. Default value
     is "vector_store.file.deleted".
    :vartype object: str
    """

    id: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The ID of the resource specified for deletion. Required."""
    deleted: bool = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A value indicating whether deletion was successful. Required."""
    object: Literal["vector_store.file.deleted"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The object type, which is always 'vector_store.deleted'. Required. Default value is
     \"vector_store.file.deleted\"."""

    @overload
    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        deleted: bool,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.object: Literal["vector_store.file.deleted"] = "vector_store.file.deleted"


class VectorStoreFileError(_Model):
    """Details on the error that may have occurred while processing a file for this vector store.

    :ivar code: One of ``server_error`` or ``rate_limit_exceeded``. Required. Known values are:
     "server_error", "invalid_file", and "unsupported_file".
    :vartype code: str or ~azure.ai.agents.models.VectorStoreFileErrorCode
    :ivar message: A human-readable description of the error. Required.
    :vartype message: str
    """

    code: Union[str, "_models.VectorStoreFileErrorCode"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """One of ``server_error`` or ``rate_limit_exceeded``. Required. Known values are:
     \"server_error\", \"invalid_file\", and \"unsupported_file\"."""
    message: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """A human-readable description of the error. Required."""

    @overload
    def __init__(
        self,
        *,
        code: Union[str, "_models.VectorStoreFileErrorCode"],
        message: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class VectorStoreStaticChunkingStrategyOptions(_Model):
    """Options to configure a vector store static chunking strategy.

    :ivar max_chunk_size_tokens: The maximum number of tokens in each chunk. The default value is
     800. The minimum value is 100 and the maximum value is 4096. Required.
    :vartype max_chunk_size_tokens: int
    :ivar chunk_overlap_tokens: The number of tokens that overlap between chunks. The default value
     is 400.
     Note that the overlap must not exceed half of max_chunk_size_tokens. Required.
    :vartype chunk_overlap_tokens: int
    """

    max_chunk_size_tokens: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The maximum number of tokens in each chunk. The default value is 800. The minimum value is 100
     and the maximum value is 4096. Required."""
    chunk_overlap_tokens: int = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The number of tokens that overlap between chunks. The default value is 400.
     Note that the overlap must not exceed half of max_chunk_size_tokens. Required."""

    @overload
    def __init__(
        self,
        *,
        max_chunk_size_tokens: int,
        chunk_overlap_tokens: int,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class VectorStoreStaticChunkingStrategyRequest(VectorStoreChunkingStrategyRequest, discriminator="static"):
    """A statically configured chunking strategy.

    :ivar type: The object type, which is always 'static'. Required.
    :vartype type: str or ~azure.ai.agents.models.STATIC
    :ivar static: The options for the static chunking strategy. Required.
    :vartype static: ~azure.ai.agents.models.VectorStoreStaticChunkingStrategyOptions
    """

    type: Literal[VectorStoreChunkingStrategyRequestType.STATIC] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'static'. Required."""
    static: "_models.VectorStoreStaticChunkingStrategyOptions" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The options for the static chunking strategy. Required."""

    @overload
    def __init__(
        self,
        *,
        static: "_models.VectorStoreStaticChunkingStrategyOptions",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=VectorStoreChunkingStrategyRequestType.STATIC, **kwargs)


class VectorStoreStaticChunkingStrategyResponse(
    VectorStoreChunkingStrategyResponse, discriminator="static"
):  # pylint: disable=name-too-long
    """A statically configured chunking strategy.

    :ivar type: The object type, which is always 'static'. Required.
    :vartype type: str or ~azure.ai.agents.models.STATIC
    :ivar static: The options for the static chunking strategy. Required.
    :vartype static: ~azure.ai.agents.models.VectorStoreStaticChunkingStrategyOptions
    """

    type: Literal[VectorStoreChunkingStrategyResponseType.STATIC] = rest_discriminator(name="type", visibility=["read", "create", "update", "delete", "query"])  # type: ignore
    """The object type, which is always 'static'. Required."""
    static: "_models.VectorStoreStaticChunkingStrategyOptions" = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The options for the static chunking strategy. Required."""

    @overload
    def __init__(
        self,
        *,
        static: "_models.VectorStoreStaticChunkingStrategyOptions",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, type=VectorStoreChunkingStrategyResponseType.STATIC, **kwargs)
