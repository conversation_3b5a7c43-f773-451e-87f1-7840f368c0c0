#  Copyright (c) Microsoft Corporation. All rights reserved.
#  Licensed under the MIT License.

# binding types
COSMOS_DB = "cosmosDB"
COSMOS_DB_TRIGGER = "cosmosDBTrigger"
EVENT_HUB_TRIGGER = "eventHubTrigger"
EVENT_HUB = "eventHub"
HTTP_TRIGGER = "httpTrigger"
HTTP_OUTPUT = "http"
KAFKA = "kafka"
KAFKA_TRIGGER = "kafkaTrigger"
QUEUE = "queue"
QUEUE_TRIGGER = "queueTrigger"
SERVICE_BUS = "serviceBus"
SERVICE_BUS_TRIGGER = "serviceBusTrigger"
TIMER_TRIGGER = "timerTrigger"
WARMUP_TRIGGER = "warmupTrigger"
BLOB_TRIGGER = "blobTrigger"
BLOB = "blob"
EVENT_GRID_TRIGGER = "eventGridTrigger"
EVENT_GRID = "eventGrid"
TABLE = "table"
SQL = "sql"
SQL_TRIGGER = "sqlTrigger"
DAPR_SERVICE_INVOCATION_TRIGGER = "daprServiceInvocationTrigger"
DAPR_BINDING_TRIGGER = "daprBindingTrigger"
DAPR_TOPIC_TRIGGER = "daprTopicTrigger"
DAPR_STATE = "daprState"
DAPR_SECRET = "daprSecret"
DAPR_PUBLISH = "daprPublish"
DAPR_INVOKE = "daprInvoke"
DAPR_BINDING = "daprBinding"
ORCHESTRATION_TRIGGER = "orchestrationTrigger"
ACTIVITY_TRIGGER = "activityTrigger"
ENTITY_TRIGGER = "entityTrigger"
DURABLE_CLIENT = "durableClient"
ASSISTANT_SKILL_TRIGGER = "assistantSkillTrigger"
TEXT_COMPLETION = "textCompletion"
ASSISTANT_QUERY = "assistantQuery"
EMBEDDINGS = "embeddings"
EMBEDDINGS_STORE = "embeddingsStore"
ASSISTANT_CREATE = "assistantCreate"
ASSISTANT_POST = "assistantPost"
SEMANTIC_SEARCH = "semanticSearch"
MYSQL = "mysql"
MYSQL_TRIGGER = "mysqlTrigger"
