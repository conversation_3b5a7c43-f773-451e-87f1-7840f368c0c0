from enum import IntEnum


class HistoryEventType(IntEnum):
    """Defines the different types of history events being communicated."""

    EXECUTION_STARTED = 0
    EXECUTION_COMPLETED = 1
    EXECUTION_FAILED = 2
    EXECUTION_TERMINATED = 3
    TASK_SCHEDULED = 4
    TASK_COMPLETED = 5
    TASK_FAILED = 6
    SUB_ORCHESTRATION_INSTANCE_CREATED = 7
    SUB_ORCHESTRATION_INSTANCE_COMPLETED = 8
    SUB_ORCHESTRATION_INSTANCE_FAILED = 9
    TIMER_CREATED = 10
    TIMER_FIRED = 11
    ORCHESTRATOR_STARTED = 12
    ORCHESTRATOR_COMPLETED = 13
    EVENT_SENT = 14
    EVENT_RAISED = 15
    CONTINUE_AS_NEW = 16
    GENERIC_EVENT = 17
    HISTORY_STATE = 18
    EXECUTION_SUSPENDED = 19
    EXECUTION_RESUMED = 20
