# pylint: disable=too-many-lines
# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------
import datetime
import sys
from typing import Any, Callable, Dict, IO, Literal, Optional, TypeVar, Union

from azure.core import AsyncPipelineClient
from azure.core.exceptions import (
    ClientAuthenticationError,
    HttpResponseError,
    ResourceExistsError,
    ResourceNotFoundError,
    ResourceNotModifiedError,
    map_error,
)
from azure.core.pipeline import PipelineResponse
from azure.core.rest import AsyncHttpR<PERSON>ponse, HttpRequest
from azure.core.tracing.decorator_async import distributed_trace_async
from azure.core.utils import case_insensitive_dict

from ... import models as _models
from ..._serialization import Deserializer, Serializer
from ...operations._page_blob_operations import (
    build_clear_pages_request,
    build_copy_incremental_request,
    build_create_request,
    build_get_page_ranges_diff_request,
    build_get_page_ranges_request,
    build_resize_request,
    build_update_sequence_number_request,
    build_upload_pages_from_url_request,
    build_upload_pages_request,
)
from .._configuration import AzureBlobStorageConfiguration

if sys.version_info >= (3, 9):
    from collections.abc import MutableMapping
else:
    from typing import MutableMapping  # type: ignore
T = TypeVar("T")
ClsType = Optional[Callable[[PipelineResponse[HttpRequest, AsyncHttpResponse], T, Dict[str, Any]], Any]]


class PageBlobOperations:
    """
    .. warning::
        **DO NOT** instantiate this class directly.

        Instead, you should access the following operations through
        :class:`~azure.storage.blob.aio.AzureBlobStorage`'s
        :attr:`page_blob` attribute.
    """

    models = _models

    def __init__(self, *args, **kwargs) -> None:
        input_args = list(args)
        self._client: AsyncPipelineClient = input_args.pop(0) if input_args else kwargs.pop("client")
        self._config: AzureBlobStorageConfiguration = input_args.pop(0) if input_args else kwargs.pop("config")
        self._serialize: Serializer = input_args.pop(0) if input_args else kwargs.pop("serializer")
        self._deserialize: Deserializer = input_args.pop(0) if input_args else kwargs.pop("deserializer")

    @distributed_trace_async
    async def create(
        self,
        content_length: int,
        blob_content_length: int,
        timeout: Optional[int] = None,
        tier: Optional[Union[str, _models.PremiumPageBlobAccessTier]] = None,
        metadata: Optional[Dict[str, str]] = None,
        blob_sequence_number: int = 0,
        request_id_parameter: Optional[str] = None,
        blob_tags_string: Optional[str] = None,
        immutability_policy_expiry: Optional[datetime.datetime] = None,
        immutability_policy_mode: Optional[Union[str, _models.BlobImmutabilityPolicyMode]] = None,
        legal_hold: Optional[bool] = None,
        blob_http_headers: Optional[_models.BlobHTTPHeaders] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        cpk_info: Optional[_models.CpkInfo] = None,
        cpk_scope_info: Optional[_models.CpkScopeInfo] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        # pylint: disable=line-too-long
        """The Create operation creates a new page blob.

        :param content_length: The length of the request. Required.
        :type content_length: int
        :param blob_content_length: This header specifies the maximum size for the page blob, up to 1
         TB. The page blob size must be aligned to a 512-byte boundary. Required.
        :type blob_content_length: int
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param tier: Optional. Indicates the tier to be set on the page blob. Known values are: "P4",
         "P6", "P10", "P15", "P20", "P30", "P40", "P50", "P60", "P70", and "P80". Default value is None.
        :type tier: str or ~azure.storage.blob.models.PremiumPageBlobAccessTier
        :param metadata: Optional. Specifies a user-defined name-value pair associated with the blob.
         If no name-value pairs are specified, the operation will copy the metadata from the source blob
         or file to the destination blob. If one or more name-value pairs are specified, the destination
         blob is created with the specified metadata, and metadata is not copied from the source blob or
         file. Note that beginning with version 2009-09-19, metadata names must adhere to the naming
         rules for C# identifiers. See Naming and Referencing Containers, Blobs, and Metadata for more
         information. Default value is None.
        :type metadata: dict[str, str]
        :param blob_sequence_number: Set for page blobs only. The sequence number is a user-controlled
         value that you can use to track requests. The value of the sequence number must be between 0
         and 2^63 - 1. Default value is 0.
        :type blob_sequence_number: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param blob_tags_string: Optional.  Used to set blob tags in various blob operations. Default
         value is None.
        :type blob_tags_string: str
        :param immutability_policy_expiry: Specifies the date time when the blobs immutability policy
         is set to expire. Default value is None.
        :type immutability_policy_expiry: ~datetime.datetime
        :param immutability_policy_mode: Specifies the immutability policy mode to set on the blob.
         Known values are: "Mutable", "Unlocked", and "Locked". Default value is None.
        :type immutability_policy_mode: str or ~azure.storage.blob.models.BlobImmutabilityPolicyMode
        :param legal_hold: Specified if a legal hold should be set on the blob. Default value is None.
        :type legal_hold: bool
        :param blob_http_headers: Parameter group. Default value is None.
        :type blob_http_headers: ~azure.storage.blob.models.BlobHTTPHeaders
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param cpk_info: Parameter group. Default value is None.
        :type cpk_info: ~azure.storage.blob.models.CpkInfo
        :param cpk_scope_info: Parameter group. Default value is None.
        :type cpk_scope_info: ~azure.storage.blob.models.CpkScopeInfo
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = kwargs.pop("params", {}) or {}

        blob_type: Literal["PageBlob"] = kwargs.pop("blob_type", _headers.pop("x-ms-blob-type", "PageBlob"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _blob_content_type = None
        _blob_content_encoding = None
        _blob_content_language = None
        _blob_content_md5 = None
        _blob_cache_control = None
        _lease_id = None
        _blob_content_disposition = None
        _encryption_key = None
        _encryption_key_sha256 = None
        _encryption_algorithm = None
        _encryption_scope = None
        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        if blob_http_headers is not None:
            _blob_cache_control = blob_http_headers.blob_cache_control
            _blob_content_disposition = blob_http_headers.blob_content_disposition
            _blob_content_encoding = blob_http_headers.blob_content_encoding
            _blob_content_language = blob_http_headers.blob_content_language
            _blob_content_md5 = blob_http_headers.blob_content_md5
            _blob_content_type = blob_http_headers.blob_content_type
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if cpk_info is not None:
            _encryption_algorithm = cpk_info.encryption_algorithm
            _encryption_key = cpk_info.encryption_key
            _encryption_key_sha256 = cpk_info.encryption_key_sha256
        if cpk_scope_info is not None:
            _encryption_scope = cpk_scope_info.encryption_scope
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since

        _request = build_create_request(
            url=self._config.url,
            content_length=content_length,
            blob_content_length=blob_content_length,
            timeout=timeout,
            tier=tier,
            blob_content_type=_blob_content_type,
            blob_content_encoding=_blob_content_encoding,
            blob_content_language=_blob_content_language,
            blob_content_md5=_blob_content_md5,
            blob_cache_control=_blob_cache_control,
            metadata=metadata,
            lease_id=_lease_id,
            blob_content_disposition=_blob_content_disposition,
            encryption_key=_encryption_key,
            encryption_key_sha256=_encryption_key_sha256,
            encryption_algorithm=_encryption_algorithm,
            encryption_scope=_encryption_scope,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            blob_sequence_number=blob_sequence_number,
            request_id_parameter=request_id_parameter,
            blob_tags_string=blob_tags_string,
            immutability_policy_expiry=immutability_policy_expiry,
            immutability_policy_mode=immutability_policy_mode,
            legal_hold=legal_hold,
            blob_type=blob_type,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["Content-MD5"] = self._deserialize("bytearray", response.headers.get("Content-MD5"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["x-ms-version-id"] = self._deserialize("str", response.headers.get("x-ms-version-id"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-request-server-encrypted"] = self._deserialize(
            "bool", response.headers.get("x-ms-request-server-encrypted")
        )
        response_headers["x-ms-encryption-key-sha256"] = self._deserialize(
            "str", response.headers.get("x-ms-encryption-key-sha256")
        )
        response_headers["x-ms-encryption-scope"] = self._deserialize(
            "str", response.headers.get("x-ms-encryption-scope")
        )

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace_async
    async def upload_pages(
        self,
        content_length: int,
        body: IO[bytes],
        transactional_content_md5: Optional[bytes] = None,
        transactional_content_crc64: Optional[bytes] = None,
        timeout: Optional[int] = None,
        range: Optional[str] = None,
        request_id_parameter: Optional[str] = None,
        structured_body_type: Optional[str] = None,
        structured_content_length: Optional[int] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        cpk_info: Optional[_models.CpkInfo] = None,
        cpk_scope_info: Optional[_models.CpkScopeInfo] = None,
        sequence_number_access_conditions: Optional[_models.SequenceNumberAccessConditions] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        # pylint: disable=line-too-long
        """The Upload Pages operation writes a range of pages to a page blob.

        :param content_length: The length of the request. Required.
        :type content_length: int
        :param body: Initial data. Required.
        :type body: IO[bytes]
        :param transactional_content_md5: Specify the transactional md5 for the body, to be validated
         by the service. Default value is None.
        :type transactional_content_md5: bytes
        :param transactional_content_crc64: Specify the transactional crc64 for the body, to be
         validated by the service. Default value is None.
        :type transactional_content_crc64: bytes
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param range: Return only the bytes of the blob in the specified range. Default value is None.
        :type range: str
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param structured_body_type: Required if the request body is a structured message. Specifies
         the message schema version and properties. Default value is None.
        :type structured_body_type: str
        :param structured_content_length: Required if the request body is a structured message.
         Specifies the length of the blob/file content inside the message body. Will always be smaller
         than Content-Length. Default value is None.
        :type structured_content_length: int
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param cpk_info: Parameter group. Default value is None.
        :type cpk_info: ~azure.storage.blob.models.CpkInfo
        :param cpk_scope_info: Parameter group. Default value is None.
        :type cpk_scope_info: ~azure.storage.blob.models.CpkScopeInfo
        :param sequence_number_access_conditions: Parameter group. Default value is None.
        :type sequence_number_access_conditions:
         ~azure.storage.blob.models.SequenceNumberAccessConditions
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["page"] = kwargs.pop("comp", _params.pop("comp", "page"))
        page_write: Literal["update"] = kwargs.pop("page_write", _headers.pop("x-ms-page-write", "update"))
        content_type: str = kwargs.pop("content_type", _headers.pop("Content-Type", "application/octet-stream"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _lease_id = None
        _encryption_key = None
        _encryption_key_sha256 = None
        _encryption_algorithm = None
        _encryption_scope = None
        _if_sequence_number_less_than_or_equal_to = None
        _if_sequence_number_less_than = None
        _if_sequence_number_equal_to = None
        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if cpk_info is not None:
            _encryption_algorithm = cpk_info.encryption_algorithm
            _encryption_key = cpk_info.encryption_key
            _encryption_key_sha256 = cpk_info.encryption_key_sha256
        if cpk_scope_info is not None:
            _encryption_scope = cpk_scope_info.encryption_scope
        if sequence_number_access_conditions is not None:
            _if_sequence_number_equal_to = sequence_number_access_conditions.if_sequence_number_equal_to
            _if_sequence_number_less_than = sequence_number_access_conditions.if_sequence_number_less_than
            _if_sequence_number_less_than_or_equal_to = (
                sequence_number_access_conditions.if_sequence_number_less_than_or_equal_to
            )
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since
        _content = body

        _request = build_upload_pages_request(
            url=self._config.url,
            content_length=content_length,
            transactional_content_md5=transactional_content_md5,
            transactional_content_crc64=transactional_content_crc64,
            timeout=timeout,
            range=range,
            lease_id=_lease_id,
            encryption_key=_encryption_key,
            encryption_key_sha256=_encryption_key_sha256,
            encryption_algorithm=_encryption_algorithm,
            encryption_scope=_encryption_scope,
            if_sequence_number_less_than_or_equal_to=_if_sequence_number_less_than_or_equal_to,
            if_sequence_number_less_than=_if_sequence_number_less_than,
            if_sequence_number_equal_to=_if_sequence_number_equal_to,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            request_id_parameter=request_id_parameter,
            structured_body_type=structured_body_type,
            structured_content_length=structured_content_length,
            comp=comp,
            page_write=page_write,
            content_type=content_type,
            version=self._config.version,
            content=_content,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["Content-MD5"] = self._deserialize("bytearray", response.headers.get("Content-MD5"))
        response_headers["x-ms-content-crc64"] = self._deserialize(
            "bytearray", response.headers.get("x-ms-content-crc64")
        )
        response_headers["x-ms-blob-sequence-number"] = self._deserialize(
            "int", response.headers.get("x-ms-blob-sequence-number")
        )
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-request-server-encrypted"] = self._deserialize(
            "bool", response.headers.get("x-ms-request-server-encrypted")
        )
        response_headers["x-ms-encryption-key-sha256"] = self._deserialize(
            "str", response.headers.get("x-ms-encryption-key-sha256")
        )
        response_headers["x-ms-encryption-scope"] = self._deserialize(
            "str", response.headers.get("x-ms-encryption-scope")
        )
        response_headers["x-ms-structured-body"] = self._deserialize(
            "str", response.headers.get("x-ms-structured-body")
        )

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace_async
    async def clear_pages(
        self,
        content_length: int,
        timeout: Optional[int] = None,
        range: Optional[str] = None,
        request_id_parameter: Optional[str] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        cpk_info: Optional[_models.CpkInfo] = None,
        cpk_scope_info: Optional[_models.CpkScopeInfo] = None,
        sequence_number_access_conditions: Optional[_models.SequenceNumberAccessConditions] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        # pylint: disable=line-too-long
        """The Clear Pages operation clears a set of pages from a page blob.

        :param content_length: The length of the request. Required.
        :type content_length: int
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param range: Return only the bytes of the blob in the specified range. Default value is None.
        :type range: str
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param cpk_info: Parameter group. Default value is None.
        :type cpk_info: ~azure.storage.blob.models.CpkInfo
        :param cpk_scope_info: Parameter group. Default value is None.
        :type cpk_scope_info: ~azure.storage.blob.models.CpkScopeInfo
        :param sequence_number_access_conditions: Parameter group. Default value is None.
        :type sequence_number_access_conditions:
         ~azure.storage.blob.models.SequenceNumberAccessConditions
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["page"] = kwargs.pop("comp", _params.pop("comp", "page"))
        page_write: Literal["clear"] = kwargs.pop("page_write", _headers.pop("x-ms-page-write", "clear"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _lease_id = None
        _encryption_key = None
        _encryption_key_sha256 = None
        _encryption_algorithm = None
        _encryption_scope = None
        _if_sequence_number_less_than_or_equal_to = None
        _if_sequence_number_less_than = None
        _if_sequence_number_equal_to = None
        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if cpk_info is not None:
            _encryption_algorithm = cpk_info.encryption_algorithm
            _encryption_key = cpk_info.encryption_key
            _encryption_key_sha256 = cpk_info.encryption_key_sha256
        if cpk_scope_info is not None:
            _encryption_scope = cpk_scope_info.encryption_scope
        if sequence_number_access_conditions is not None:
            _if_sequence_number_equal_to = sequence_number_access_conditions.if_sequence_number_equal_to
            _if_sequence_number_less_than = sequence_number_access_conditions.if_sequence_number_less_than
            _if_sequence_number_less_than_or_equal_to = (
                sequence_number_access_conditions.if_sequence_number_less_than_or_equal_to
            )
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since

        _request = build_clear_pages_request(
            url=self._config.url,
            content_length=content_length,
            timeout=timeout,
            range=range,
            lease_id=_lease_id,
            encryption_key=_encryption_key,
            encryption_key_sha256=_encryption_key_sha256,
            encryption_algorithm=_encryption_algorithm,
            encryption_scope=_encryption_scope,
            if_sequence_number_less_than_or_equal_to=_if_sequence_number_less_than_or_equal_to,
            if_sequence_number_less_than=_if_sequence_number_less_than,
            if_sequence_number_equal_to=_if_sequence_number_equal_to,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            request_id_parameter=request_id_parameter,
            comp=comp,
            page_write=page_write,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["Content-MD5"] = self._deserialize("bytearray", response.headers.get("Content-MD5"))
        response_headers["x-ms-content-crc64"] = self._deserialize(
            "bytearray", response.headers.get("x-ms-content-crc64")
        )
        response_headers["x-ms-blob-sequence-number"] = self._deserialize(
            "int", response.headers.get("x-ms-blob-sequence-number")
        )
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace_async
    async def upload_pages_from_url(
        self,
        source_url: str,
        source_range: str,
        content_length: int,
        range: str,
        source_content_md5: Optional[bytes] = None,
        source_contentcrc64: Optional[bytes] = None,
        timeout: Optional[int] = None,
        request_id_parameter: Optional[str] = None,
        copy_source_authorization: Optional[str] = None,
        cpk_info: Optional[_models.CpkInfo] = None,
        cpk_scope_info: Optional[_models.CpkScopeInfo] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        sequence_number_access_conditions: Optional[_models.SequenceNumberAccessConditions] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        source_modified_access_conditions: Optional[_models.SourceModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        # pylint: disable=line-too-long
        """The Upload Pages operation writes a range of pages to a page blob where the contents are read
        from a URL.

        :param source_url: Specify a URL to the copy source. Required.
        :type source_url: str
        :param source_range: Bytes of source data in the specified range. The length of this range
         should match the ContentLength header and x-ms-range/Range destination range header. Required.
        :type source_range: str
        :param content_length: The length of the request. Required.
        :type content_length: int
        :param range: The range of bytes to which the source range would be written. The range should
         be 512 aligned and range-end is required. Required.
        :type range: str
        :param source_content_md5: Specify the md5 calculated for the range of bytes that must be read
         from the copy source. Default value is None.
        :type source_content_md5: bytes
        :param source_contentcrc64: Specify the crc64 calculated for the range of bytes that must be
         read from the copy source. Default value is None.
        :type source_contentcrc64: bytes
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param copy_source_authorization: Only Bearer type is supported. Credentials should be a valid
         OAuth access token to copy source. Default value is None.
        :type copy_source_authorization: str
        :param cpk_info: Parameter group. Default value is None.
        :type cpk_info: ~azure.storage.blob.models.CpkInfo
        :param cpk_scope_info: Parameter group. Default value is None.
        :type cpk_scope_info: ~azure.storage.blob.models.CpkScopeInfo
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param sequence_number_access_conditions: Parameter group. Default value is None.
        :type sequence_number_access_conditions:
         ~azure.storage.blob.models.SequenceNumberAccessConditions
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :param source_modified_access_conditions: Parameter group. Default value is None.
        :type source_modified_access_conditions:
         ~azure.storage.blob.models.SourceModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = case_insensitive_dict(kwargs.pop("headers", {}) or {})
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["page"] = kwargs.pop("comp", _params.pop("comp", "page"))
        page_write: Literal["update"] = kwargs.pop("page_write", _headers.pop("x-ms-page-write", "update"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _encryption_key = None
        _encryption_key_sha256 = None
        _encryption_algorithm = None
        _encryption_scope = None
        _lease_id = None
        _if_sequence_number_less_than_or_equal_to = None
        _if_sequence_number_less_than = None
        _if_sequence_number_equal_to = None
        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        _source_if_modified_since = None
        _source_if_unmodified_since = None
        _source_if_match = None
        _source_if_none_match = None
        if cpk_info is not None:
            _encryption_algorithm = cpk_info.encryption_algorithm
            _encryption_key = cpk_info.encryption_key
            _encryption_key_sha256 = cpk_info.encryption_key_sha256
        if cpk_scope_info is not None:
            _encryption_scope = cpk_scope_info.encryption_scope
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if sequence_number_access_conditions is not None:
            _if_sequence_number_equal_to = sequence_number_access_conditions.if_sequence_number_equal_to
            _if_sequence_number_less_than = sequence_number_access_conditions.if_sequence_number_less_than
            _if_sequence_number_less_than_or_equal_to = (
                sequence_number_access_conditions.if_sequence_number_less_than_or_equal_to
            )
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since
        if source_modified_access_conditions is not None:
            _source_if_match = source_modified_access_conditions.source_if_match
            _source_if_modified_since = source_modified_access_conditions.source_if_modified_since
            _source_if_none_match = source_modified_access_conditions.source_if_none_match
            _source_if_unmodified_since = source_modified_access_conditions.source_if_unmodified_since

        _request = build_upload_pages_from_url_request(
            url=self._config.url,
            source_url=source_url,
            source_range=source_range,
            content_length=content_length,
            range=range,
            source_content_md5=source_content_md5,
            source_contentcrc64=source_contentcrc64,
            timeout=timeout,
            encryption_key=_encryption_key,
            encryption_key_sha256=_encryption_key_sha256,
            encryption_algorithm=_encryption_algorithm,
            encryption_scope=_encryption_scope,
            lease_id=_lease_id,
            if_sequence_number_less_than_or_equal_to=_if_sequence_number_less_than_or_equal_to,
            if_sequence_number_less_than=_if_sequence_number_less_than,
            if_sequence_number_equal_to=_if_sequence_number_equal_to,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            source_if_modified_since=_source_if_modified_since,
            source_if_unmodified_since=_source_if_unmodified_since,
            source_if_match=_source_if_match,
            source_if_none_match=_source_if_none_match,
            request_id_parameter=request_id_parameter,
            copy_source_authorization=copy_source_authorization,
            comp=comp,
            page_write=page_write,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [201]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["Content-MD5"] = self._deserialize("bytearray", response.headers.get("Content-MD5"))
        response_headers["x-ms-content-crc64"] = self._deserialize(
            "bytearray", response.headers.get("x-ms-content-crc64")
        )
        response_headers["x-ms-blob-sequence-number"] = self._deserialize(
            "int", response.headers.get("x-ms-blob-sequence-number")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-request-server-encrypted"] = self._deserialize(
            "bool", response.headers.get("x-ms-request-server-encrypted")
        )
        response_headers["x-ms-encryption-key-sha256"] = self._deserialize(
            "str", response.headers.get("x-ms-encryption-key-sha256")
        )
        response_headers["x-ms-encryption-scope"] = self._deserialize(
            "str", response.headers.get("x-ms-encryption-scope")
        )

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace_async
    async def get_page_ranges(
        self,
        snapshot: Optional[str] = None,
        timeout: Optional[int] = None,
        range: Optional[str] = None,
        request_id_parameter: Optional[str] = None,
        marker: Optional[str] = None,
        maxresults: Optional[int] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> _models.PageList:
        # pylint: disable=line-too-long
        """The Get Page Ranges operation returns the list of valid page ranges for a page blob or snapshot
        of a page blob.

        :param snapshot: The snapshot parameter is an opaque DateTime value that, when present,
         specifies the blob snapshot to retrieve. For more information on working with blob snapshots,
         see :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob">Creating
         a Snapshot of a Blob.</a>`. Default value is None.
        :type snapshot: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param range: Return only the bytes of the blob in the specified range. Default value is None.
        :type range: str
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param marker: A string value that identifies the portion of the list of containers to be
         returned with the next listing operation. The operation returns the NextMarker value within the
         response body if the listing operation did not return all containers remaining to be listed
         with the current page. The NextMarker value can be used as the value for the marker parameter
         in a subsequent call to request the next page of list items. The marker value is opaque to the
         client. Default value is None.
        :type marker: str
        :param maxresults: Specifies the maximum number of containers to return. If the request does
         not specify maxresults, or specifies a value greater than 5000, the server will return up to
         5000 items. Note that if the listing operation crosses a partition boundary, then the service
         will return a continuation token for retrieving the remainder of the results. For this reason,
         it is possible that the service will return fewer results than specified by maxresults, or than
         the default of 5000. Default value is None.
        :type maxresults: int
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: PageList or the result of cls(response)
        :rtype: ~azure.storage.blob.models.PageList
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["pagelist"] = kwargs.pop("comp", _params.pop("comp", "pagelist"))
        cls: ClsType[_models.PageList] = kwargs.pop("cls", None)

        _lease_id = None
        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since

        _request = build_get_page_ranges_request(
            url=self._config.url,
            snapshot=snapshot,
            timeout=timeout,
            range=range,
            lease_id=_lease_id,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            request_id_parameter=request_id_parameter,
            marker=marker,
            maxresults=maxresults,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["x-ms-blob-content-length"] = self._deserialize(
            "int", response.headers.get("x-ms-blob-content-length")
        )
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        deserialized = self._deserialize("PageList", pipeline_response.http_response)

        if cls:
            return cls(pipeline_response, deserialized, response_headers)  # type: ignore

        return deserialized  # type: ignore

    @distributed_trace_async
    async def get_page_ranges_diff(
        self,
        snapshot: Optional[str] = None,
        timeout: Optional[int] = None,
        prevsnapshot: Optional[str] = None,
        prev_snapshot_url: Optional[str] = None,
        range: Optional[str] = None,
        request_id_parameter: Optional[str] = None,
        marker: Optional[str] = None,
        maxresults: Optional[int] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> _models.PageList:
        # pylint: disable=line-too-long
        """The Get Page Ranges Diff operation returns the list of valid page ranges for a page blob that
        were changed between target blob and previous snapshot.

        :param snapshot: The snapshot parameter is an opaque DateTime value that, when present,
         specifies the blob snapshot to retrieve. For more information on working with blob snapshots,
         see :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob">Creating
         a Snapshot of a Blob.</a>`. Default value is None.
        :type snapshot: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param prevsnapshot: Optional in version 2015-07-08 and newer. The prevsnapshot parameter is a
         DateTime value that specifies that the response will contain only pages that were changed
         between target blob and previous snapshot. Changed pages include both updated and cleared
         pages. The target blob may be a snapshot, as long as the snapshot specified by prevsnapshot is
         the older of the two. Note that incremental snapshots are currently supported only for blobs
         created on or after January 1, 2016. Default value is None.
        :type prevsnapshot: str
        :param prev_snapshot_url: Optional. This header is only supported in service versions
         2019-04-19 and after and specifies the URL of a previous snapshot of the target blob. The
         response will only contain pages that were changed between the target blob and its previous
         snapshot. Default value is None.
        :type prev_snapshot_url: str
        :param range: Return only the bytes of the blob in the specified range. Default value is None.
        :type range: str
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param marker: A string value that identifies the portion of the list of containers to be
         returned with the next listing operation. The operation returns the NextMarker value within the
         response body if the listing operation did not return all containers remaining to be listed
         with the current page. The NextMarker value can be used as the value for the marker parameter
         in a subsequent call to request the next page of list items. The marker value is opaque to the
         client. Default value is None.
        :type marker: str
        :param maxresults: Specifies the maximum number of containers to return. If the request does
         not specify maxresults, or specifies a value greater than 5000, the server will return up to
         5000 items. Note that if the listing operation crosses a partition boundary, then the service
         will return a continuation token for retrieving the remainder of the results. For this reason,
         it is possible that the service will return fewer results than specified by maxresults, or than
         the default of 5000. Default value is None.
        :type maxresults: int
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: PageList or the result of cls(response)
        :rtype: ~azure.storage.blob.models.PageList
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["pagelist"] = kwargs.pop("comp", _params.pop("comp", "pagelist"))
        cls: ClsType[_models.PageList] = kwargs.pop("cls", None)

        _lease_id = None
        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since

        _request = build_get_page_ranges_diff_request(
            url=self._config.url,
            snapshot=snapshot,
            timeout=timeout,
            prevsnapshot=prevsnapshot,
            prev_snapshot_url=prev_snapshot_url,
            range=range,
            lease_id=_lease_id,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            request_id_parameter=request_id_parameter,
            marker=marker,
            maxresults=maxresults,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["x-ms-blob-content-length"] = self._deserialize(
            "int", response.headers.get("x-ms-blob-content-length")
        )
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        deserialized = self._deserialize("PageList", pipeline_response.http_response)

        if cls:
            return cls(pipeline_response, deserialized, response_headers)  # type: ignore

        return deserialized  # type: ignore

    @distributed_trace_async
    async def resize(
        self,
        blob_content_length: int,
        timeout: Optional[int] = None,
        request_id_parameter: Optional[str] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        cpk_info: Optional[_models.CpkInfo] = None,
        cpk_scope_info: Optional[_models.CpkScopeInfo] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        # pylint: disable=line-too-long
        """Resize the Blob.

        :param blob_content_length: This header specifies the maximum size for the page blob, up to 1
         TB. The page blob size must be aligned to a 512-byte boundary. Required.
        :type blob_content_length: int
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param cpk_info: Parameter group. Default value is None.
        :type cpk_info: ~azure.storage.blob.models.CpkInfo
        :param cpk_scope_info: Parameter group. Default value is None.
        :type cpk_scope_info: ~azure.storage.blob.models.CpkScopeInfo
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["properties"] = kwargs.pop("comp", _params.pop("comp", "properties"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _lease_id = None
        _encryption_key = None
        _encryption_key_sha256 = None
        _encryption_algorithm = None
        _encryption_scope = None
        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if cpk_info is not None:
            _encryption_algorithm = cpk_info.encryption_algorithm
            _encryption_key = cpk_info.encryption_key
            _encryption_key_sha256 = cpk_info.encryption_key_sha256
        if cpk_scope_info is not None:
            _encryption_scope = cpk_scope_info.encryption_scope
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since

        _request = build_resize_request(
            url=self._config.url,
            blob_content_length=blob_content_length,
            timeout=timeout,
            lease_id=_lease_id,
            encryption_key=_encryption_key,
            encryption_key_sha256=_encryption_key_sha256,
            encryption_algorithm=_encryption_algorithm,
            encryption_scope=_encryption_scope,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            request_id_parameter=request_id_parameter,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-blob-sequence-number"] = self._deserialize(
            "int", response.headers.get("x-ms-blob-sequence-number")
        )
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace_async
    async def update_sequence_number(
        self,
        sequence_number_action: Union[str, _models.SequenceNumberActionType],
        timeout: Optional[int] = None,
        blob_sequence_number: int = 0,
        request_id_parameter: Optional[str] = None,
        lease_access_conditions: Optional[_models.LeaseAccessConditions] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        # pylint: disable=line-too-long
        """Update the sequence number of the blob.

        :param sequence_number_action: Required if the x-ms-blob-sequence-number header is set for the
         request. This property applies to page blobs only. This property indicates how the service
         should modify the blob's sequence number. Known values are: "max", "update", and "increment".
         Required.
        :type sequence_number_action: str or ~azure.storage.blob.models.SequenceNumberActionType
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param blob_sequence_number: Set for page blobs only. The sequence number is a user-controlled
         value that you can use to track requests. The value of the sequence number must be between 0
         and 2^63 - 1. Default value is 0.
        :type blob_sequence_number: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param lease_access_conditions: Parameter group. Default value is None.
        :type lease_access_conditions: ~azure.storage.blob.models.LeaseAccessConditions
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["properties"] = kwargs.pop("comp", _params.pop("comp", "properties"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _lease_id = None
        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        if lease_access_conditions is not None:
            _lease_id = lease_access_conditions.lease_id
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since

        _request = build_update_sequence_number_request(
            url=self._config.url,
            sequence_number_action=sequence_number_action,
            timeout=timeout,
            lease_id=_lease_id,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            blob_sequence_number=blob_sequence_number,
            request_id_parameter=request_id_parameter,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [200]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-blob-sequence-number"] = self._deserialize(
            "int", response.headers.get("x-ms-blob-sequence-number")
        )
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore

    @distributed_trace_async
    async def copy_incremental(
        self,
        copy_source: str,
        timeout: Optional[int] = None,
        request_id_parameter: Optional[str] = None,
        modified_access_conditions: Optional[_models.ModifiedAccessConditions] = None,
        **kwargs: Any
    ) -> None:
        # pylint: disable=line-too-long
        """The Copy Incremental operation copies a snapshot of the source page blob to a destination page
        blob. The snapshot is copied such that only the differential changes between the previously
        copied snapshot are transferred to the destination. The copied snapshots are complete copies of
        the original snapshot and can be read or copied from as usual. This API is supported since REST
        version 2016-05-31.

        :param copy_source: Specifies the name of the source page blob snapshot. This value is a URL of
         up to 2 KB in length that specifies a page blob snapshot. The value should be URL-encoded as it
         would appear in a request URI. The source blob must either be public or must be authenticated
         via a shared access signature. Required.
        :type copy_source: str
        :param timeout: The timeout parameter is expressed in seconds. For more information, see
         :code:`<a
         href="https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations">Setting
         Timeouts for Blob Service Operations.</a>`. Default value is None.
        :type timeout: int
        :param request_id_parameter: Provides a client-generated, opaque value with a 1 KB character
         limit that is recorded in the analytics logs when storage analytics logging is enabled. Default
         value is None.
        :type request_id_parameter: str
        :param modified_access_conditions: Parameter group. Default value is None.
        :type modified_access_conditions: ~azure.storage.blob.models.ModifiedAccessConditions
        :return: None or the result of cls(response)
        :rtype: None
        :raises ~azure.core.exceptions.HttpResponseError:
        """
        error_map: MutableMapping = {
            401: ClientAuthenticationError,
            404: ResourceNotFoundError,
            409: ResourceExistsError,
            304: ResourceNotModifiedError,
        }
        error_map.update(kwargs.pop("error_map", {}) or {})

        _headers = kwargs.pop("headers", {}) or {}
        _params = case_insensitive_dict(kwargs.pop("params", {}) or {})

        comp: Literal["incrementalcopy"] = kwargs.pop("comp", _params.pop("comp", "incrementalcopy"))
        cls: ClsType[None] = kwargs.pop("cls", None)

        _if_modified_since = None
        _if_unmodified_since = None
        _if_match = None
        _if_none_match = None
        _if_tags = None
        if modified_access_conditions is not None:
            _if_match = modified_access_conditions.if_match
            _if_modified_since = modified_access_conditions.if_modified_since
            _if_none_match = modified_access_conditions.if_none_match
            _if_tags = modified_access_conditions.if_tags
            _if_unmodified_since = modified_access_conditions.if_unmodified_since

        _request = build_copy_incremental_request(
            url=self._config.url,
            copy_source=copy_source,
            timeout=timeout,
            if_modified_since=_if_modified_since,
            if_unmodified_since=_if_unmodified_since,
            if_match=_if_match,
            if_none_match=_if_none_match,
            if_tags=_if_tags,
            request_id_parameter=request_id_parameter,
            comp=comp,
            version=self._config.version,
            headers=_headers,
            params=_params,
        )
        _request.url = self._client.format_url(_request.url)

        _stream = False
        pipeline_response: PipelineResponse = await self._client._pipeline.run(  # pylint: disable=protected-access
            _request, stream=_stream, **kwargs
        )

        response = pipeline_response.http_response

        if response.status_code not in [202]:
            map_error(status_code=response.status_code, response=response, error_map=error_map)
            error = self._deserialize.failsafe_deserialize(_models.StorageError, pipeline_response)
            raise HttpResponseError(response=response, model=error)

        response_headers = {}
        response_headers["ETag"] = self._deserialize("str", response.headers.get("ETag"))
        response_headers["Last-Modified"] = self._deserialize("rfc-1123", response.headers.get("Last-Modified"))
        response_headers["x-ms-client-request-id"] = self._deserialize(
            "str", response.headers.get("x-ms-client-request-id")
        )
        response_headers["x-ms-request-id"] = self._deserialize("str", response.headers.get("x-ms-request-id"))
        response_headers["x-ms-version"] = self._deserialize("str", response.headers.get("x-ms-version"))
        response_headers["Date"] = self._deserialize("rfc-1123", response.headers.get("Date"))
        response_headers["x-ms-copy-id"] = self._deserialize("str", response.headers.get("x-ms-copy-id"))
        response_headers["x-ms-copy-status"] = self._deserialize("str", response.headers.get("x-ms-copy-status"))

        if cls:
            return cls(pipeline_response, None, response_headers)  # type: ignore
