# pylint: disable=too-many-lines
# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------

import datetime
import sys
from typing import Any, Dict, List, Optional, TYPE_CHECKING, Union

from .. import _serialization

if sys.version_info >= (3, 9):
    from collections.abc import MutableMapping
else:
    from typing import MutableMapping  # type: ignore

if TYPE_CHECKING:
    from .. import models as _models
JSON = MutableMapping[str, Any]  # pylint: disable=unsubscriptable-object


class AccessPolicy(_serialization.Model):
    """An Access policy.

    :ivar start: the date-time the policy is active.
    :vartype start: str
    :ivar expiry: the date-time the policy expires.
    :vartype expiry: str
    :ivar permission: the permissions for the acl policy.
    :vartype permission: str
    """

    _attribute_map = {
        "start": {"key": "Start", "type": "str"},
        "expiry": {"key": "Expiry", "type": "str"},
        "permission": {"key": "Permission", "type": "str"},
    }

    def __init__(
        self,
        *,
        start: Optional[str] = None,
        expiry: Optional[str] = None,
        permission: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword start: the date-time the policy is active.
        :paramtype start: str
        :keyword expiry: the date-time the policy expires.
        :paramtype expiry: str
        :keyword permission: the permissions for the acl policy.
        :paramtype permission: str
        """
        super().__init__(**kwargs)
        self.start = start
        self.expiry = expiry
        self.permission = permission


class AppendPositionAccessConditions(_serialization.Model):
    """Parameter group.

    :ivar max_size: Optional conditional header. The max length in bytes permitted for the append
     blob. If the Append Block operation would cause the blob to exceed that limit or if the blob
     size is already greater than the value specified in this header, the request will fail with
     MaxBlobSizeConditionNotMet error (HTTP status code 412 - Precondition Failed).
    :vartype max_size: int
    :ivar append_position: Optional conditional header, used only for the Append Block operation. A
     number indicating the byte offset to compare. Append Block will succeed only if the append
     position is equal to this number. If it is not, the request will fail with the
     AppendPositionConditionNotMet error (HTTP status code 412 - Precondition Failed).
    :vartype append_position: int
    """

    _attribute_map = {
        "max_size": {"key": "maxSize", "type": "int"},
        "append_position": {"key": "appendPosition", "type": "int"},
    }

    def __init__(self, *, max_size: Optional[int] = None, append_position: Optional[int] = None, **kwargs: Any) -> None:
        """
        :keyword max_size: Optional conditional header. The max length in bytes permitted for the
         append blob. If the Append Block operation would cause the blob to exceed that limit or if the
         blob size is already greater than the value specified in this header, the request will fail
         with MaxBlobSizeConditionNotMet error (HTTP status code 412 - Precondition Failed).
        :paramtype max_size: int
        :keyword append_position: Optional conditional header, used only for the Append Block
         operation. A number indicating the byte offset to compare. Append Block will succeed only if
         the append position is equal to this number. If it is not, the request will fail with the
         AppendPositionConditionNotMet error (HTTP status code 412 - Precondition Failed).
        :paramtype append_position: int
        """
        super().__init__(**kwargs)
        self.max_size = max_size
        self.append_position = append_position


class ArrowConfiguration(_serialization.Model):
    """Groups the settings used for formatting the response if the response should be Arrow formatted.

    All required parameters must be populated in order to send to server.

    :ivar schema: Required.
    :vartype schema: list[~azure.storage.blob.models.ArrowField]
    """

    _validation = {
        "schema": {"required": True},
    }

    _attribute_map = {
        "schema": {
            "key": "Schema",
            "type": "[ArrowField]",
            "xml": {"name": "Schema", "wrapped": True, "itemsName": "Field"},
        },
    }
    _xml_map = {"name": "ArrowConfiguration"}

    def __init__(self, *, schema: List["_models.ArrowField"], **kwargs: Any) -> None:
        """
        :keyword schema: Required.
        :paramtype schema: list[~azure.storage.blob.models.ArrowField]
        """
        super().__init__(**kwargs)
        self.schema = schema


class ArrowField(_serialization.Model):
    """Groups settings regarding specific field of an arrow schema.

    All required parameters must be populated in order to send to server.

    :ivar type: Required.
    :vartype type: str
    :ivar name:
    :vartype name: str
    :ivar precision:
    :vartype precision: int
    :ivar scale:
    :vartype scale: int
    """

    _validation = {
        "type": {"required": True},
    }

    _attribute_map = {
        "type": {"key": "Type", "type": "str"},
        "name": {"key": "Name", "type": "str"},
        "precision": {"key": "Precision", "type": "int"},
        "scale": {"key": "Scale", "type": "int"},
    }
    _xml_map = {"name": "Field"}

    def __init__(
        self,
        *,
        type: str,
        name: Optional[str] = None,
        precision: Optional[int] = None,
        scale: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword type: Required.
        :paramtype type: str
        :keyword name:
        :paramtype name: str
        :keyword precision:
        :paramtype precision: int
        :keyword scale:
        :paramtype scale: int
        """
        super().__init__(**kwargs)
        self.type = type
        self.name = name
        self.precision = precision
        self.scale = scale


class BlobFlatListSegment(_serialization.Model):
    """BlobFlatListSegment.

    All required parameters must be populated in order to send to server.

    :ivar blob_items: Required.
    :vartype blob_items: list[~azure.storage.blob.models.BlobItemInternal]
    """

    _validation = {
        "blob_items": {"required": True},
    }

    _attribute_map = {
        "blob_items": {"key": "BlobItems", "type": "[BlobItemInternal]", "xml": {"itemsName": "Blob"}},
    }
    _xml_map = {"name": "Blobs"}

    def __init__(self, *, blob_items: List["_models.BlobItemInternal"], **kwargs: Any) -> None:
        """
        :keyword blob_items: Required.
        :paramtype blob_items: list[~azure.storage.blob.models.BlobItemInternal]
        """
        super().__init__(**kwargs)
        self.blob_items = blob_items


class BlobHierarchyListSegment(_serialization.Model):
    """BlobHierarchyListSegment.

    All required parameters must be populated in order to send to server.

    :ivar blob_prefixes:
    :vartype blob_prefixes: list[~azure.storage.blob.models.BlobPrefix]
    :ivar blob_items: Required.
    :vartype blob_items: list[~azure.storage.blob.models.BlobItemInternal]
    """

    _validation = {
        "blob_items": {"required": True},
    }

    _attribute_map = {
        "blob_prefixes": {"key": "BlobPrefixes", "type": "[BlobPrefix]", "xml": {"name": "BlobPrefix"}},
        "blob_items": {"key": "BlobItems", "type": "[BlobItemInternal]", "xml": {"name": "Blob", "itemsName": "Blob"}},
    }
    _xml_map = {"name": "Blobs"}

    def __init__(
        self,
        *,
        blob_items: List["_models.BlobItemInternal"],
        blob_prefixes: Optional[List["_models.BlobPrefix"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword blob_prefixes:
        :paramtype blob_prefixes: list[~azure.storage.blob.models.BlobPrefix]
        :keyword blob_items: Required.
        :paramtype blob_items: list[~azure.storage.blob.models.BlobItemInternal]
        """
        super().__init__(**kwargs)
        self.blob_prefixes = blob_prefixes
        self.blob_items = blob_items


class BlobHTTPHeaders(_serialization.Model):
    """Parameter group.

    :ivar blob_cache_control: Optional. Sets the blob's cache control. If specified, this property
     is stored with the blob and returned with a read request.
    :vartype blob_cache_control: str
    :ivar blob_content_type: Optional. Sets the blob's content type. If specified, this property is
     stored with the blob and returned with a read request.
    :vartype blob_content_type: str
    :ivar blob_content_md5: Optional. An MD5 hash of the blob content. Note that this hash is not
     validated, as the hashes for the individual blocks were validated when each was uploaded.
    :vartype blob_content_md5: bytes
    :ivar blob_content_encoding: Optional. Sets the blob's content encoding. If specified, this
     property is stored with the blob and returned with a read request.
    :vartype blob_content_encoding: str
    :ivar blob_content_language: Optional. Set the blob's content language. If specified, this
     property is stored with the blob and returned with a read request.
    :vartype blob_content_language: str
    :ivar blob_content_disposition: Optional. Sets the blob's Content-Disposition header.
    :vartype blob_content_disposition: str
    """

    _attribute_map = {
        "blob_cache_control": {"key": "blobCacheControl", "type": "str"},
        "blob_content_type": {"key": "blobContentType", "type": "str"},
        "blob_content_md5": {"key": "blobContentMD5", "type": "bytearray"},
        "blob_content_encoding": {"key": "blobContentEncoding", "type": "str"},
        "blob_content_language": {"key": "blobContentLanguage", "type": "str"},
        "blob_content_disposition": {"key": "blobContentDisposition", "type": "str"},
    }

    def __init__(
        self,
        *,
        blob_cache_control: Optional[str] = None,
        blob_content_type: Optional[str] = None,
        blob_content_md5: Optional[bytes] = None,
        blob_content_encoding: Optional[str] = None,
        blob_content_language: Optional[str] = None,
        blob_content_disposition: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword blob_cache_control: Optional. Sets the blob's cache control. If specified, this
         property is stored with the blob and returned with a read request.
        :paramtype blob_cache_control: str
        :keyword blob_content_type: Optional. Sets the blob's content type. If specified, this property
         is stored with the blob and returned with a read request.
        :paramtype blob_content_type: str
        :keyword blob_content_md5: Optional. An MD5 hash of the blob content. Note that this hash is
         not validated, as the hashes for the individual blocks were validated when each was uploaded.
        :paramtype blob_content_md5: bytes
        :keyword blob_content_encoding: Optional. Sets the blob's content encoding. If specified, this
         property is stored with the blob and returned with a read request.
        :paramtype blob_content_encoding: str
        :keyword blob_content_language: Optional. Set the blob's content language. If specified, this
         property is stored with the blob and returned with a read request.
        :paramtype blob_content_language: str
        :keyword blob_content_disposition: Optional. Sets the blob's Content-Disposition header.
        :paramtype blob_content_disposition: str
        """
        super().__init__(**kwargs)
        self.blob_cache_control = blob_cache_control
        self.blob_content_type = blob_content_type
        self.blob_content_md5 = blob_content_md5
        self.blob_content_encoding = blob_content_encoding
        self.blob_content_language = blob_content_language
        self.blob_content_disposition = blob_content_disposition


class BlobItemInternal(_serialization.Model):
    """An Azure Storage blob.

    All required parameters must be populated in order to send to server.

    :ivar name: Required.
    :vartype name: ~azure.storage.blob.models.BlobName
    :ivar deleted: Required.
    :vartype deleted: bool
    :ivar snapshot: Required.
    :vartype snapshot: str
    :ivar version_id:
    :vartype version_id: str
    :ivar is_current_version:
    :vartype is_current_version: bool
    :ivar properties: Properties of a blob. Required.
    :vartype properties: ~azure.storage.blob.models.BlobPropertiesInternal
    :ivar metadata:
    :vartype metadata: ~azure.storage.blob.models.BlobMetadata
    :ivar blob_tags: Blob tags.
    :vartype blob_tags: ~azure.storage.blob.models.BlobTags
    :ivar has_versions_only:
    :vartype has_versions_only: bool
    :ivar object_replication_metadata: Dictionary of :code:`<string>`.
    :vartype object_replication_metadata: dict[str, str]
    """

    _validation = {
        "name": {"required": True},
        "deleted": {"required": True},
        "snapshot": {"required": True},
        "properties": {"required": True},
    }

    _attribute_map = {
        "name": {"key": "Name", "type": "BlobName"},
        "deleted": {"key": "Deleted", "type": "bool"},
        "snapshot": {"key": "Snapshot", "type": "str"},
        "version_id": {"key": "VersionId", "type": "str"},
        "is_current_version": {"key": "IsCurrentVersion", "type": "bool"},
        "properties": {"key": "Properties", "type": "BlobPropertiesInternal"},
        "metadata": {"key": "Metadata", "type": "BlobMetadata"},
        "blob_tags": {"key": "BlobTags", "type": "BlobTags"},
        "has_versions_only": {"key": "HasVersionsOnly", "type": "bool"},
        "object_replication_metadata": {"key": "OrMetadata", "type": "{str}"},
    }
    _xml_map = {"name": "Blob"}

    def __init__(
        self,
        *,
        name: "_models.BlobName",
        deleted: bool,
        snapshot: str,
        properties: "_models.BlobPropertiesInternal",
        version_id: Optional[str] = None,
        is_current_version: Optional[bool] = None,
        metadata: Optional["_models.BlobMetadata"] = None,
        blob_tags: Optional["_models.BlobTags"] = None,
        has_versions_only: Optional[bool] = None,
        object_replication_metadata: Optional[Dict[str, str]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword name: Required.
        :paramtype name: ~azure.storage.blob.models.BlobName
        :keyword deleted: Required.
        :paramtype deleted: bool
        :keyword snapshot: Required.
        :paramtype snapshot: str
        :keyword version_id:
        :paramtype version_id: str
        :keyword is_current_version:
        :paramtype is_current_version: bool
        :keyword properties: Properties of a blob. Required.
        :paramtype properties: ~azure.storage.blob.models.BlobPropertiesInternal
        :keyword metadata:
        :paramtype metadata: ~azure.storage.blob.models.BlobMetadata
        :keyword blob_tags: Blob tags.
        :paramtype blob_tags: ~azure.storage.blob.models.BlobTags
        :keyword has_versions_only:
        :paramtype has_versions_only: bool
        :keyword object_replication_metadata: Dictionary of :code:`<string>`.
        :paramtype object_replication_metadata: dict[str, str]
        """
        super().__init__(**kwargs)
        self.name = name
        self.deleted = deleted
        self.snapshot = snapshot
        self.version_id = version_id
        self.is_current_version = is_current_version
        self.properties = properties
        self.metadata = metadata
        self.blob_tags = blob_tags
        self.has_versions_only = has_versions_only
        self.object_replication_metadata = object_replication_metadata


class BlobMetadata(_serialization.Model):
    """BlobMetadata.

    :ivar additional_properties: Unmatched properties from the message are deserialized to this
     collection.
    :vartype additional_properties: dict[str, str]
    :ivar encrypted:
    :vartype encrypted: str
    """

    _attribute_map = {
        "additional_properties": {"key": "", "type": "{str}"},
        "encrypted": {"key": "Encrypted", "type": "str", "xml": {"attr": True}},
    }
    _xml_map = {"name": "Metadata"}

    def __init__(
        self, *, additional_properties: Optional[Dict[str, str]] = None, encrypted: Optional[str] = None, **kwargs: Any
    ) -> None:
        """
        :keyword additional_properties: Unmatched properties from the message are deserialized to this
         collection.
        :paramtype additional_properties: dict[str, str]
        :keyword encrypted:
        :paramtype encrypted: str
        """
        super().__init__(**kwargs)
        self.additional_properties = additional_properties
        self.encrypted = encrypted


class BlobName(_serialization.Model):
    """BlobName.

    :ivar encoded: Indicates if the blob name is encoded.
    :vartype encoded: bool
    :ivar content: The name of the blob.
    :vartype content: str
    """

    _attribute_map = {
        "encoded": {"key": "Encoded", "type": "bool", "xml": {"name": "Encoded", "attr": True}},
        "content": {"key": "content", "type": "str", "xml": {"text": True}},
    }

    def __init__(self, *, encoded: Optional[bool] = None, content: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword encoded: Indicates if the blob name is encoded.
        :paramtype encoded: bool
        :keyword content: The name of the blob.
        :paramtype content: str
        """
        super().__init__(**kwargs)
        self.encoded = encoded
        self.content = content


class BlobPrefix(_serialization.Model):
    """BlobPrefix.

    All required parameters must be populated in order to send to server.

    :ivar name: Required.
    :vartype name: ~azure.storage.blob.models.BlobName
    """

    _validation = {
        "name": {"required": True},
    }

    _attribute_map = {
        "name": {"key": "Name", "type": "BlobName"},
    }

    def __init__(self, *, name: "_models.BlobName", **kwargs: Any) -> None:
        """
        :keyword name: Required.
        :paramtype name: ~azure.storage.blob.models.BlobName
        """
        super().__init__(**kwargs)
        self.name = name


class BlobPropertiesInternal(_serialization.Model):
    """Properties of a blob.

    All required parameters must be populated in order to send to server.

    :ivar creation_time:
    :vartype creation_time: ~datetime.datetime
    :ivar last_modified: Required.
    :vartype last_modified: ~datetime.datetime
    :ivar etag: Required.
    :vartype etag: str
    :ivar content_length: Size in bytes.
    :vartype content_length: int
    :ivar content_type:
    :vartype content_type: str
    :ivar content_encoding:
    :vartype content_encoding: str
    :ivar content_language:
    :vartype content_language: str
    :ivar content_md5:
    :vartype content_md5: bytes
    :ivar content_disposition:
    :vartype content_disposition: str
    :ivar cache_control:
    :vartype cache_control: str
    :ivar blob_sequence_number:
    :vartype blob_sequence_number: int
    :ivar blob_type: Known values are: "BlockBlob", "PageBlob", and "AppendBlob".
    :vartype blob_type: str or ~azure.storage.blob.models.BlobType
    :ivar lease_status: Known values are: "locked" and "unlocked".
    :vartype lease_status: str or ~azure.storage.blob.models.LeaseStatusType
    :ivar lease_state: Known values are: "available", "leased", "expired", "breaking", and
     "broken".
    :vartype lease_state: str or ~azure.storage.blob.models.LeaseStateType
    :ivar lease_duration: Known values are: "infinite" and "fixed".
    :vartype lease_duration: str or ~azure.storage.blob.models.LeaseDurationType
    :ivar copy_id:
    :vartype copy_id: str
    :ivar copy_status: Known values are: "pending", "success", "aborted", and "failed".
    :vartype copy_status: str or ~azure.storage.blob.models.CopyStatusType
    :ivar copy_source:
    :vartype copy_source: str
    :ivar copy_progress:
    :vartype copy_progress: str
    :ivar copy_completion_time:
    :vartype copy_completion_time: ~datetime.datetime
    :ivar copy_status_description:
    :vartype copy_status_description: str
    :ivar server_encrypted:
    :vartype server_encrypted: bool
    :ivar incremental_copy:
    :vartype incremental_copy: bool
    :ivar destination_snapshot:
    :vartype destination_snapshot: str
    :ivar deleted_time:
    :vartype deleted_time: ~datetime.datetime
    :ivar remaining_retention_days:
    :vartype remaining_retention_days: int
    :ivar access_tier: Known values are: "P4", "P6", "P10", "P15", "P20", "P30", "P40", "P50",
     "P60", "P70", "P80", "Hot", "Cool", "Archive", "Premium", and "Cold".
    :vartype access_tier: str or ~azure.storage.blob.models.AccessTier
    :ivar access_tier_inferred:
    :vartype access_tier_inferred: bool
    :ivar archive_status: Known values are: "rehydrate-pending-to-hot",
     "rehydrate-pending-to-cool", and "rehydrate-pending-to-cold".
    :vartype archive_status: str or ~azure.storage.blob.models.ArchiveStatus
    :ivar customer_provided_key_sha256:
    :vartype customer_provided_key_sha256: str
    :ivar encryption_scope: The name of the encryption scope under which the blob is encrypted.
    :vartype encryption_scope: str
    :ivar access_tier_change_time:
    :vartype access_tier_change_time: ~datetime.datetime
    :ivar tag_count:
    :vartype tag_count: int
    :ivar expires_on:
    :vartype expires_on: ~datetime.datetime
    :ivar is_sealed:
    :vartype is_sealed: bool
    :ivar rehydrate_priority: If an object is in rehydrate pending state then this header is
     returned with priority of rehydrate. Valid values are High and Standard. Known values are:
     "High" and "Standard".
    :vartype rehydrate_priority: str or ~azure.storage.blob.models.RehydratePriority
    :ivar last_accessed_on:
    :vartype last_accessed_on: ~datetime.datetime
    :ivar immutability_policy_expires_on:
    :vartype immutability_policy_expires_on: ~datetime.datetime
    :ivar immutability_policy_mode: Known values are: "Mutable", "Unlocked", and "Locked".
    :vartype immutability_policy_mode: str or ~azure.storage.blob.models.BlobImmutabilityPolicyMode
    :ivar legal_hold:
    :vartype legal_hold: bool
    """

    _validation = {
        "last_modified": {"required": True},
        "etag": {"required": True},
    }

    _attribute_map = {
        "creation_time": {"key": "Creation-Time", "type": "rfc-1123"},
        "last_modified": {"key": "Last-Modified", "type": "rfc-1123"},
        "etag": {"key": "Etag", "type": "str"},
        "content_length": {"key": "Content-Length", "type": "int"},
        "content_type": {"key": "Content-Type", "type": "str"},
        "content_encoding": {"key": "Content-Encoding", "type": "str"},
        "content_language": {"key": "Content-Language", "type": "str"},
        "content_md5": {"key": "Content-MD5", "type": "bytearray"},
        "content_disposition": {"key": "Content-Disposition", "type": "str"},
        "cache_control": {"key": "Cache-Control", "type": "str"},
        "blob_sequence_number": {"key": "x-ms-blob-sequence-number", "type": "int"},
        "blob_type": {"key": "BlobType", "type": "str"},
        "lease_status": {"key": "LeaseStatus", "type": "str"},
        "lease_state": {"key": "LeaseState", "type": "str"},
        "lease_duration": {"key": "LeaseDuration", "type": "str"},
        "copy_id": {"key": "CopyId", "type": "str"},
        "copy_status": {"key": "CopyStatus", "type": "str"},
        "copy_source": {"key": "CopySource", "type": "str"},
        "copy_progress": {"key": "CopyProgress", "type": "str"},
        "copy_completion_time": {"key": "CopyCompletionTime", "type": "rfc-1123"},
        "copy_status_description": {"key": "CopyStatusDescription", "type": "str"},
        "server_encrypted": {"key": "ServerEncrypted", "type": "bool"},
        "incremental_copy": {"key": "IncrementalCopy", "type": "bool"},
        "destination_snapshot": {"key": "DestinationSnapshot", "type": "str"},
        "deleted_time": {"key": "DeletedTime", "type": "rfc-1123"},
        "remaining_retention_days": {"key": "RemainingRetentionDays", "type": "int"},
        "access_tier": {"key": "AccessTier", "type": "str"},
        "access_tier_inferred": {"key": "AccessTierInferred", "type": "bool"},
        "archive_status": {"key": "ArchiveStatus", "type": "str"},
        "customer_provided_key_sha256": {"key": "CustomerProvidedKeySha256", "type": "str"},
        "encryption_scope": {"key": "EncryptionScope", "type": "str"},
        "access_tier_change_time": {"key": "AccessTierChangeTime", "type": "rfc-1123"},
        "tag_count": {"key": "TagCount", "type": "int"},
        "expires_on": {"key": "Expiry-Time", "type": "rfc-1123"},
        "is_sealed": {"key": "Sealed", "type": "bool"},
        "rehydrate_priority": {"key": "RehydratePriority", "type": "str"},
        "last_accessed_on": {"key": "LastAccessTime", "type": "rfc-1123"},
        "immutability_policy_expires_on": {"key": "ImmutabilityPolicyUntilDate", "type": "rfc-1123"},
        "immutability_policy_mode": {"key": "ImmutabilityPolicyMode", "type": "str"},
        "legal_hold": {"key": "LegalHold", "type": "bool"},
    }
    _xml_map = {"name": "Properties"}

    def __init__(  # pylint: disable=too-many-locals
        self,
        *,
        last_modified: datetime.datetime,
        etag: str,
        creation_time: Optional[datetime.datetime] = None,
        content_length: Optional[int] = None,
        content_type: Optional[str] = None,
        content_encoding: Optional[str] = None,
        content_language: Optional[str] = None,
        content_md5: Optional[bytes] = None,
        content_disposition: Optional[str] = None,
        cache_control: Optional[str] = None,
        blob_sequence_number: Optional[int] = None,
        blob_type: Optional[Union[str, "_models.BlobType"]] = None,
        lease_status: Optional[Union[str, "_models.LeaseStatusType"]] = None,
        lease_state: Optional[Union[str, "_models.LeaseStateType"]] = None,
        lease_duration: Optional[Union[str, "_models.LeaseDurationType"]] = None,
        copy_id: Optional[str] = None,
        copy_status: Optional[Union[str, "_models.CopyStatusType"]] = None,
        copy_source: Optional[str] = None,
        copy_progress: Optional[str] = None,
        copy_completion_time: Optional[datetime.datetime] = None,
        copy_status_description: Optional[str] = None,
        server_encrypted: Optional[bool] = None,
        incremental_copy: Optional[bool] = None,
        destination_snapshot: Optional[str] = None,
        deleted_time: Optional[datetime.datetime] = None,
        remaining_retention_days: Optional[int] = None,
        access_tier: Optional[Union[str, "_models.AccessTier"]] = None,
        access_tier_inferred: Optional[bool] = None,
        archive_status: Optional[Union[str, "_models.ArchiveStatus"]] = None,
        customer_provided_key_sha256: Optional[str] = None,
        encryption_scope: Optional[str] = None,
        access_tier_change_time: Optional[datetime.datetime] = None,
        tag_count: Optional[int] = None,
        expires_on: Optional[datetime.datetime] = None,
        is_sealed: Optional[bool] = None,
        rehydrate_priority: Optional[Union[str, "_models.RehydratePriority"]] = None,
        last_accessed_on: Optional[datetime.datetime] = None,
        immutability_policy_expires_on: Optional[datetime.datetime] = None,
        immutability_policy_mode: Optional[Union[str, "_models.BlobImmutabilityPolicyMode"]] = None,
        legal_hold: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword creation_time:
        :paramtype creation_time: ~datetime.datetime
        :keyword last_modified: Required.
        :paramtype last_modified: ~datetime.datetime
        :keyword etag: Required.
        :paramtype etag: str
        :keyword content_length: Size in bytes.
        :paramtype content_length: int
        :keyword content_type:
        :paramtype content_type: str
        :keyword content_encoding:
        :paramtype content_encoding: str
        :keyword content_language:
        :paramtype content_language: str
        :keyword content_md5:
        :paramtype content_md5: bytes
        :keyword content_disposition:
        :paramtype content_disposition: str
        :keyword cache_control:
        :paramtype cache_control: str
        :keyword blob_sequence_number:
        :paramtype blob_sequence_number: int
        :keyword blob_type: Known values are: "BlockBlob", "PageBlob", and "AppendBlob".
        :paramtype blob_type: str or ~azure.storage.blob.models.BlobType
        :keyword lease_status: Known values are: "locked" and "unlocked".
        :paramtype lease_status: str or ~azure.storage.blob.models.LeaseStatusType
        :keyword lease_state: Known values are: "available", "leased", "expired", "breaking", and
         "broken".
        :paramtype lease_state: str or ~azure.storage.blob.models.LeaseStateType
        :keyword lease_duration: Known values are: "infinite" and "fixed".
        :paramtype lease_duration: str or ~azure.storage.blob.models.LeaseDurationType
        :keyword copy_id:
        :paramtype copy_id: str
        :keyword copy_status: Known values are: "pending", "success", "aborted", and "failed".
        :paramtype copy_status: str or ~azure.storage.blob.models.CopyStatusType
        :keyword copy_source:
        :paramtype copy_source: str
        :keyword copy_progress:
        :paramtype copy_progress: str
        :keyword copy_completion_time:
        :paramtype copy_completion_time: ~datetime.datetime
        :keyword copy_status_description:
        :paramtype copy_status_description: str
        :keyword server_encrypted:
        :paramtype server_encrypted: bool
        :keyword incremental_copy:
        :paramtype incremental_copy: bool
        :keyword destination_snapshot:
        :paramtype destination_snapshot: str
        :keyword deleted_time:
        :paramtype deleted_time: ~datetime.datetime
        :keyword remaining_retention_days:
        :paramtype remaining_retention_days: int
        :keyword access_tier: Known values are: "P4", "P6", "P10", "P15", "P20", "P30", "P40", "P50",
         "P60", "P70", "P80", "Hot", "Cool", "Archive", "Premium", and "Cold".
        :paramtype access_tier: str or ~azure.storage.blob.models.AccessTier
        :keyword access_tier_inferred:
        :paramtype access_tier_inferred: bool
        :keyword archive_status: Known values are: "rehydrate-pending-to-hot",
         "rehydrate-pending-to-cool", and "rehydrate-pending-to-cold".
        :paramtype archive_status: str or ~azure.storage.blob.models.ArchiveStatus
        :keyword customer_provided_key_sha256:
        :paramtype customer_provided_key_sha256: str
        :keyword encryption_scope: The name of the encryption scope under which the blob is encrypted.
        :paramtype encryption_scope: str
        :keyword access_tier_change_time:
        :paramtype access_tier_change_time: ~datetime.datetime
        :keyword tag_count:
        :paramtype tag_count: int
        :keyword expires_on:
        :paramtype expires_on: ~datetime.datetime
        :keyword is_sealed:
        :paramtype is_sealed: bool
        :keyword rehydrate_priority: If an object is in rehydrate pending state then this header is
         returned with priority of rehydrate. Valid values are High and Standard. Known values are:
         "High" and "Standard".
        :paramtype rehydrate_priority: str or ~azure.storage.blob.models.RehydratePriority
        :keyword last_accessed_on:
        :paramtype last_accessed_on: ~datetime.datetime
        :keyword immutability_policy_expires_on:
        :paramtype immutability_policy_expires_on: ~datetime.datetime
        :keyword immutability_policy_mode: Known values are: "Mutable", "Unlocked", and "Locked".
        :paramtype immutability_policy_mode: str or
         ~azure.storage.blob.models.BlobImmutabilityPolicyMode
        :keyword legal_hold:
        :paramtype legal_hold: bool
        """
        super().__init__(**kwargs)
        self.creation_time = creation_time
        self.last_modified = last_modified
        self.etag = etag
        self.content_length = content_length
        self.content_type = content_type
        self.content_encoding = content_encoding
        self.content_language = content_language
        self.content_md5 = content_md5
        self.content_disposition = content_disposition
        self.cache_control = cache_control
        self.blob_sequence_number = blob_sequence_number
        self.blob_type = blob_type
        self.lease_status = lease_status
        self.lease_state = lease_state
        self.lease_duration = lease_duration
        self.copy_id = copy_id
        self.copy_status = copy_status
        self.copy_source = copy_source
        self.copy_progress = copy_progress
        self.copy_completion_time = copy_completion_time
        self.copy_status_description = copy_status_description
        self.server_encrypted = server_encrypted
        self.incremental_copy = incremental_copy
        self.destination_snapshot = destination_snapshot
        self.deleted_time = deleted_time
        self.remaining_retention_days = remaining_retention_days
        self.access_tier = access_tier
        self.access_tier_inferred = access_tier_inferred
        self.archive_status = archive_status
        self.customer_provided_key_sha256 = customer_provided_key_sha256
        self.encryption_scope = encryption_scope
        self.access_tier_change_time = access_tier_change_time
        self.tag_count = tag_count
        self.expires_on = expires_on
        self.is_sealed = is_sealed
        self.rehydrate_priority = rehydrate_priority
        self.last_accessed_on = last_accessed_on
        self.immutability_policy_expires_on = immutability_policy_expires_on
        self.immutability_policy_mode = immutability_policy_mode
        self.legal_hold = legal_hold


class BlobTag(_serialization.Model):
    """BlobTag.

    All required parameters must be populated in order to send to server.

    :ivar key: Required.
    :vartype key: str
    :ivar value: Required.
    :vartype value: str
    """

    _validation = {
        "key": {"required": True},
        "value": {"required": True},
    }

    _attribute_map = {
        "key": {"key": "Key", "type": "str"},
        "value": {"key": "Value", "type": "str"},
    }
    _xml_map = {"name": "Tag"}

    def __init__(self, *, key: str, value: str, **kwargs: Any) -> None:
        """
        :keyword key: Required.
        :paramtype key: str
        :keyword value: Required.
        :paramtype value: str
        """
        super().__init__(**kwargs)
        self.key = key
        self.value = value


class BlobTags(_serialization.Model):
    """Blob tags.

    All required parameters must be populated in order to send to server.

    :ivar blob_tag_set: Required.
    :vartype blob_tag_set: list[~azure.storage.blob.models.BlobTag]
    """

    _validation = {
        "blob_tag_set": {"required": True},
    }

    _attribute_map = {
        "blob_tag_set": {
            "key": "BlobTagSet",
            "type": "[BlobTag]",
            "xml": {"name": "TagSet", "wrapped": True, "itemsName": "Tag"},
        },
    }
    _xml_map = {"name": "Tags"}

    def __init__(self, *, blob_tag_set: List["_models.BlobTag"], **kwargs: Any) -> None:
        """
        :keyword blob_tag_set: Required.
        :paramtype blob_tag_set: list[~azure.storage.blob.models.BlobTag]
        """
        super().__init__(**kwargs)
        self.blob_tag_set = blob_tag_set


class Block(_serialization.Model):
    """Represents a single block in a block blob.  It describes the block's ID and size.

    All required parameters must be populated in order to send to server.

    :ivar name: The base64 encoded block ID. Required.
    :vartype name: str
    :ivar size: The block size in bytes. Required.
    :vartype size: int
    """

    _validation = {
        "name": {"required": True},
        "size": {"required": True},
    }

    _attribute_map = {
        "name": {"key": "Name", "type": "str"},
        "size": {"key": "Size", "type": "int"},
    }

    def __init__(self, *, name: str, size: int, **kwargs: Any) -> None:
        """
        :keyword name: The base64 encoded block ID. Required.
        :paramtype name: str
        :keyword size: The block size in bytes. Required.
        :paramtype size: int
        """
        super().__init__(**kwargs)
        self.name = name
        self.size = size


class BlockList(_serialization.Model):
    """BlockList.

    :ivar committed_blocks:
    :vartype committed_blocks: list[~azure.storage.blob.models.Block]
    :ivar uncommitted_blocks:
    :vartype uncommitted_blocks: list[~azure.storage.blob.models.Block]
    """

    _attribute_map = {
        "committed_blocks": {"key": "CommittedBlocks", "type": "[Block]", "xml": {"wrapped": True}},
        "uncommitted_blocks": {"key": "UncommittedBlocks", "type": "[Block]", "xml": {"wrapped": True}},
    }

    def __init__(
        self,
        *,
        committed_blocks: Optional[List["_models.Block"]] = None,
        uncommitted_blocks: Optional[List["_models.Block"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword committed_blocks:
        :paramtype committed_blocks: list[~azure.storage.blob.models.Block]
        :keyword uncommitted_blocks:
        :paramtype uncommitted_blocks: list[~azure.storage.blob.models.Block]
        """
        super().__init__(**kwargs)
        self.committed_blocks = committed_blocks
        self.uncommitted_blocks = uncommitted_blocks


class BlockLookupList(_serialization.Model):
    """BlockLookupList.

    :ivar committed:
    :vartype committed: list[str]
    :ivar uncommitted:
    :vartype uncommitted: list[str]
    :ivar latest:
    :vartype latest: list[str]
    """

    _attribute_map = {
        "committed": {"key": "Committed", "type": "[str]", "xml": {"itemsName": "Committed"}},
        "uncommitted": {"key": "Uncommitted", "type": "[str]", "xml": {"itemsName": "Uncommitted"}},
        "latest": {"key": "Latest", "type": "[str]", "xml": {"itemsName": "Latest"}},
    }
    _xml_map = {"name": "BlockList"}

    def __init__(
        self,
        *,
        committed: Optional[List[str]] = None,
        uncommitted: Optional[List[str]] = None,
        latest: Optional[List[str]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword committed:
        :paramtype committed: list[str]
        :keyword uncommitted:
        :paramtype uncommitted: list[str]
        :keyword latest:
        :paramtype latest: list[str]
        """
        super().__init__(**kwargs)
        self.committed = committed
        self.uncommitted = uncommitted
        self.latest = latest


class ClearRange(_serialization.Model):
    """ClearRange.

    All required parameters must be populated in order to send to server.

    :ivar start: Required.
    :vartype start: int
    :ivar end: Required.
    :vartype end: int
    """

    _validation = {
        "start": {"required": True},
        "end": {"required": True},
    }

    _attribute_map = {
        "start": {"key": "Start", "type": "int", "xml": {"name": "Start"}},
        "end": {"key": "End", "type": "int", "xml": {"name": "End"}},
    }
    _xml_map = {"name": "ClearRange"}

    def __init__(self, *, start: int, end: int, **kwargs: Any) -> None:
        """
        :keyword start: Required.
        :paramtype start: int
        :keyword end: Required.
        :paramtype end: int
        """
        super().__init__(**kwargs)
        self.start = start
        self.end = end


class ContainerCpkScopeInfo(_serialization.Model):
    """Parameter group.

    :ivar default_encryption_scope: Optional.  Version 2019-07-07 and later.  Specifies the default
     encryption scope to set on the container and use for all future writes.
    :vartype default_encryption_scope: str
    :ivar prevent_encryption_scope_override: Optional.  Version 2019-07-07 and newer.  If true,
     prevents any request from specifying a different encryption scope than the scope set on the
     container.
    :vartype prevent_encryption_scope_override: bool
    """

    _attribute_map = {
        "default_encryption_scope": {"key": "DefaultEncryptionScope", "type": "str"},
        "prevent_encryption_scope_override": {"key": "PreventEncryptionScopeOverride", "type": "bool"},
    }

    def __init__(
        self,
        *,
        default_encryption_scope: Optional[str] = None,
        prevent_encryption_scope_override: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword default_encryption_scope: Optional.  Version 2019-07-07 and later.  Specifies the
         default encryption scope to set on the container and use for all future writes.
        :paramtype default_encryption_scope: str
        :keyword prevent_encryption_scope_override: Optional.  Version 2019-07-07 and newer.  If true,
         prevents any request from specifying a different encryption scope than the scope set on the
         container.
        :paramtype prevent_encryption_scope_override: bool
        """
        super().__init__(**kwargs)
        self.default_encryption_scope = default_encryption_scope
        self.prevent_encryption_scope_override = prevent_encryption_scope_override


class ContainerItem(_serialization.Model):
    """An Azure Storage container.

    All required parameters must be populated in order to send to server.

    :ivar name: Required.
    :vartype name: str
    :ivar deleted:
    :vartype deleted: bool
    :ivar version:
    :vartype version: str
    :ivar properties: Properties of a container. Required.
    :vartype properties: ~azure.storage.blob.models.ContainerProperties
    :ivar metadata: Dictionary of :code:`<string>`.
    :vartype metadata: dict[str, str]
    """

    _validation = {
        "name": {"required": True},
        "properties": {"required": True},
    }

    _attribute_map = {
        "name": {"key": "Name", "type": "str"},
        "deleted": {"key": "Deleted", "type": "bool"},
        "version": {"key": "Version", "type": "str"},
        "properties": {"key": "Properties", "type": "ContainerProperties"},
        "metadata": {"key": "Metadata", "type": "{str}"},
    }
    _xml_map = {"name": "Container"}

    def __init__(
        self,
        *,
        name: str,
        properties: "_models.ContainerProperties",
        deleted: Optional[bool] = None,
        version: Optional[str] = None,
        metadata: Optional[Dict[str, str]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword name: Required.
        :paramtype name: str
        :keyword deleted:
        :paramtype deleted: bool
        :keyword version:
        :paramtype version: str
        :keyword properties: Properties of a container. Required.
        :paramtype properties: ~azure.storage.blob.models.ContainerProperties
        :keyword metadata: Dictionary of :code:`<string>`.
        :paramtype metadata: dict[str, str]
        """
        super().__init__(**kwargs)
        self.name = name
        self.deleted = deleted
        self.version = version
        self.properties = properties
        self.metadata = metadata


class ContainerProperties(_serialization.Model):
    """Properties of a container.

    All required parameters must be populated in order to send to server.

    :ivar last_modified: Required.
    :vartype last_modified: ~datetime.datetime
    :ivar etag: Required.
    :vartype etag: str
    :ivar lease_status: Known values are: "locked" and "unlocked".
    :vartype lease_status: str or ~azure.storage.blob.models.LeaseStatusType
    :ivar lease_state: Known values are: "available", "leased", "expired", "breaking", and
     "broken".
    :vartype lease_state: str or ~azure.storage.blob.models.LeaseStateType
    :ivar lease_duration: Known values are: "infinite" and "fixed".
    :vartype lease_duration: str or ~azure.storage.blob.models.LeaseDurationType
    :ivar public_access: Known values are: "container" and "blob".
    :vartype public_access: str or ~azure.storage.blob.models.PublicAccessType
    :ivar has_immutability_policy:
    :vartype has_immutability_policy: bool
    :ivar has_legal_hold:
    :vartype has_legal_hold: bool
    :ivar default_encryption_scope:
    :vartype default_encryption_scope: str
    :ivar prevent_encryption_scope_override:
    :vartype prevent_encryption_scope_override: bool
    :ivar deleted_time:
    :vartype deleted_time: ~datetime.datetime
    :ivar remaining_retention_days:
    :vartype remaining_retention_days: int
    :ivar is_immutable_storage_with_versioning_enabled: Indicates if version level worm is enabled
     on this container.
    :vartype is_immutable_storage_with_versioning_enabled: bool
    """

    _validation = {
        "last_modified": {"required": True},
        "etag": {"required": True},
    }

    _attribute_map = {
        "last_modified": {"key": "Last-Modified", "type": "rfc-1123"},
        "etag": {"key": "Etag", "type": "str"},
        "lease_status": {"key": "LeaseStatus", "type": "str"},
        "lease_state": {"key": "LeaseState", "type": "str"},
        "lease_duration": {"key": "LeaseDuration", "type": "str"},
        "public_access": {"key": "PublicAccess", "type": "str"},
        "has_immutability_policy": {"key": "HasImmutabilityPolicy", "type": "bool"},
        "has_legal_hold": {"key": "HasLegalHold", "type": "bool"},
        "default_encryption_scope": {"key": "DefaultEncryptionScope", "type": "str"},
        "prevent_encryption_scope_override": {"key": "DenyEncryptionScopeOverride", "type": "bool"},
        "deleted_time": {"key": "DeletedTime", "type": "rfc-1123"},
        "remaining_retention_days": {"key": "RemainingRetentionDays", "type": "int"},
        "is_immutable_storage_with_versioning_enabled": {
            "key": "ImmutableStorageWithVersioningEnabled",
            "type": "bool",
        },
    }

    def __init__(
        self,
        *,
        last_modified: datetime.datetime,
        etag: str,
        lease_status: Optional[Union[str, "_models.LeaseStatusType"]] = None,
        lease_state: Optional[Union[str, "_models.LeaseStateType"]] = None,
        lease_duration: Optional[Union[str, "_models.LeaseDurationType"]] = None,
        public_access: Optional[Union[str, "_models.PublicAccessType"]] = None,
        has_immutability_policy: Optional[bool] = None,
        has_legal_hold: Optional[bool] = None,
        default_encryption_scope: Optional[str] = None,
        prevent_encryption_scope_override: Optional[bool] = None,
        deleted_time: Optional[datetime.datetime] = None,
        remaining_retention_days: Optional[int] = None,
        is_immutable_storage_with_versioning_enabled: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword last_modified: Required.
        :paramtype last_modified: ~datetime.datetime
        :keyword etag: Required.
        :paramtype etag: str
        :keyword lease_status: Known values are: "locked" and "unlocked".
        :paramtype lease_status: str or ~azure.storage.blob.models.LeaseStatusType
        :keyword lease_state: Known values are: "available", "leased", "expired", "breaking", and
         "broken".
        :paramtype lease_state: str or ~azure.storage.blob.models.LeaseStateType
        :keyword lease_duration: Known values are: "infinite" and "fixed".
        :paramtype lease_duration: str or ~azure.storage.blob.models.LeaseDurationType
        :keyword public_access: Known values are: "container" and "blob".
        :paramtype public_access: str or ~azure.storage.blob.models.PublicAccessType
        :keyword has_immutability_policy:
        :paramtype has_immutability_policy: bool
        :keyword has_legal_hold:
        :paramtype has_legal_hold: bool
        :keyword default_encryption_scope:
        :paramtype default_encryption_scope: str
        :keyword prevent_encryption_scope_override:
        :paramtype prevent_encryption_scope_override: bool
        :keyword deleted_time:
        :paramtype deleted_time: ~datetime.datetime
        :keyword remaining_retention_days:
        :paramtype remaining_retention_days: int
        :keyword is_immutable_storage_with_versioning_enabled: Indicates if version level worm is
         enabled on this container.
        :paramtype is_immutable_storage_with_versioning_enabled: bool
        """
        super().__init__(**kwargs)
        self.last_modified = last_modified
        self.etag = etag
        self.lease_status = lease_status
        self.lease_state = lease_state
        self.lease_duration = lease_duration
        self.public_access = public_access
        self.has_immutability_policy = has_immutability_policy
        self.has_legal_hold = has_legal_hold
        self.default_encryption_scope = default_encryption_scope
        self.prevent_encryption_scope_override = prevent_encryption_scope_override
        self.deleted_time = deleted_time
        self.remaining_retention_days = remaining_retention_days
        self.is_immutable_storage_with_versioning_enabled = is_immutable_storage_with_versioning_enabled


class CorsRule(_serialization.Model):
    """CORS is an HTTP feature that enables a web application running under one domain to access
    resources in another domain. Web browsers implement a security restriction known as same-origin
    policy that prevents a web page from calling APIs in a different domain; CORS provides a secure
    way to allow one domain (the origin domain) to call APIs in another domain.

    All required parameters must be populated in order to send to server.

    :ivar allowed_origins: The origin domains that are permitted to make a request against the
     storage service via CORS. The origin domain is the domain from which the request originates.
     Note that the origin must be an exact case-sensitive match with the origin that the user age
     sends to the service. You can also use the wildcard character '*' to allow all origin domains
     to make requests via CORS. Required.
    :vartype allowed_origins: str
    :ivar allowed_methods: The methods (HTTP request verbs) that the origin domain may use for a
     CORS request. (comma separated). Required.
    :vartype allowed_methods: str
    :ivar allowed_headers: the request headers that the origin domain may specify on the CORS
     request. Required.
    :vartype allowed_headers: str
    :ivar exposed_headers: The response headers that may be sent in the response to the CORS
     request and exposed by the browser to the request issuer. Required.
    :vartype exposed_headers: str
    :ivar max_age_in_seconds: The maximum amount time that a browser should cache the preflight
     OPTIONS request. Required.
    :vartype max_age_in_seconds: int
    """

    _validation = {
        "allowed_origins": {"required": True},
        "allowed_methods": {"required": True},
        "allowed_headers": {"required": True},
        "exposed_headers": {"required": True},
        "max_age_in_seconds": {"required": True, "minimum": 0},
    }

    _attribute_map = {
        "allowed_origins": {"key": "AllowedOrigins", "type": "str"},
        "allowed_methods": {"key": "AllowedMethods", "type": "str"},
        "allowed_headers": {"key": "AllowedHeaders", "type": "str"},
        "exposed_headers": {"key": "ExposedHeaders", "type": "str"},
        "max_age_in_seconds": {"key": "MaxAgeInSeconds", "type": "int"},
    }

    def __init__(
        self,
        *,
        allowed_origins: str,
        allowed_methods: str,
        allowed_headers: str,
        exposed_headers: str,
        max_age_in_seconds: int,
        **kwargs: Any
    ) -> None:
        """
        :keyword allowed_origins: The origin domains that are permitted to make a request against the
         storage service via CORS. The origin domain is the domain from which the request originates.
         Note that the origin must be an exact case-sensitive match with the origin that the user age
         sends to the service. You can also use the wildcard character '*' to allow all origin domains
         to make requests via CORS. Required.
        :paramtype allowed_origins: str
        :keyword allowed_methods: The methods (HTTP request verbs) that the origin domain may use for a
         CORS request. (comma separated). Required.
        :paramtype allowed_methods: str
        :keyword allowed_headers: the request headers that the origin domain may specify on the CORS
         request. Required.
        :paramtype allowed_headers: str
        :keyword exposed_headers: The response headers that may be sent in the response to the CORS
         request and exposed by the browser to the request issuer. Required.
        :paramtype exposed_headers: str
        :keyword max_age_in_seconds: The maximum amount time that a browser should cache the preflight
         OPTIONS request. Required.
        :paramtype max_age_in_seconds: int
        """
        super().__init__(**kwargs)
        self.allowed_origins = allowed_origins
        self.allowed_methods = allowed_methods
        self.allowed_headers = allowed_headers
        self.exposed_headers = exposed_headers
        self.max_age_in_seconds = max_age_in_seconds


class CpkInfo(_serialization.Model):
    """Parameter group.

    :ivar encryption_key: Optional. Specifies the encryption key to use to encrypt the data
     provided in the request. If not specified, encryption is performed with the root account
     encryption key.  For more information, see Encryption at Rest for Azure Storage Services.
    :vartype encryption_key: str
    :ivar encryption_key_sha256: The SHA-256 hash of the provided encryption key. Must be provided
     if the x-ms-encryption-key header is provided.
    :vartype encryption_key_sha256: str
    :ivar encryption_algorithm: The algorithm used to produce the encryption key hash. Currently,
     the only accepted value is "AES256". Must be provided if the x-ms-encryption-key header is
     provided. Known values are: "None" and "AES256".
    :vartype encryption_algorithm: str or ~azure.storage.blob.models.EncryptionAlgorithmType
    """

    _attribute_map = {
        "encryption_key": {"key": "encryptionKey", "type": "str"},
        "encryption_key_sha256": {"key": "encryptionKeySha256", "type": "str"},
        "encryption_algorithm": {"key": "encryptionAlgorithm", "type": "str"},
    }

    def __init__(
        self,
        *,
        encryption_key: Optional[str] = None,
        encryption_key_sha256: Optional[str] = None,
        encryption_algorithm: Optional[Union[str, "_models.EncryptionAlgorithmType"]] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword encryption_key: Optional. Specifies the encryption key to use to encrypt the data
         provided in the request. If not specified, encryption is performed with the root account
         encryption key.  For more information, see Encryption at Rest for Azure Storage Services.
        :paramtype encryption_key: str
        :keyword encryption_key_sha256: The SHA-256 hash of the provided encryption key. Must be
         provided if the x-ms-encryption-key header is provided.
        :paramtype encryption_key_sha256: str
        :keyword encryption_algorithm: The algorithm used to produce the encryption key hash.
         Currently, the only accepted value is "AES256". Must be provided if the x-ms-encryption-key
         header is provided. Known values are: "None" and "AES256".
        :paramtype encryption_algorithm: str or ~azure.storage.blob.models.EncryptionAlgorithmType
        """
        super().__init__(**kwargs)
        self.encryption_key = encryption_key
        self.encryption_key_sha256 = encryption_key_sha256
        self.encryption_algorithm = encryption_algorithm


class CpkScopeInfo(_serialization.Model):
    """Parameter group.

    :ivar encryption_scope: Optional. Version 2019-07-07 and later.  Specifies the name of the
     encryption scope to use to encrypt the data provided in the request. If not specified,
     encryption is performed with the default account encryption scope.  For more information, see
     Encryption at Rest for Azure Storage Services.
    :vartype encryption_scope: str
    """

    _attribute_map = {
        "encryption_scope": {"key": "encryptionScope", "type": "str"},
    }

    def __init__(self, *, encryption_scope: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword encryption_scope: Optional. Version 2019-07-07 and later.  Specifies the name of the
         encryption scope to use to encrypt the data provided in the request. If not specified,
         encryption is performed with the default account encryption scope.  For more information, see
         Encryption at Rest for Azure Storage Services.
        :paramtype encryption_scope: str
        """
        super().__init__(**kwargs)
        self.encryption_scope = encryption_scope


class DelimitedTextConfiguration(_serialization.Model):
    """Groups the settings used for interpreting the blob data if the blob is delimited text
    formatted.

    :ivar column_separator: The string used to separate columns.
    :vartype column_separator: str
    :ivar field_quote: The string used to quote a specific field.
    :vartype field_quote: str
    :ivar record_separator: The string used to separate records.
    :vartype record_separator: str
    :ivar escape_char: The string used as an escape character.
    :vartype escape_char: str
    :ivar headers_present: Represents whether the data has headers.
    :vartype headers_present: bool
    """

    _attribute_map = {
        "column_separator": {"key": "ColumnSeparator", "type": "str", "xml": {"name": "ColumnSeparator"}},
        "field_quote": {"key": "FieldQuote", "type": "str", "xml": {"name": "FieldQuote"}},
        "record_separator": {"key": "RecordSeparator", "type": "str", "xml": {"name": "RecordSeparator"}},
        "escape_char": {"key": "EscapeChar", "type": "str", "xml": {"name": "EscapeChar"}},
        "headers_present": {"key": "HeadersPresent", "type": "bool", "xml": {"name": "HasHeaders"}},
    }
    _xml_map = {"name": "DelimitedTextConfiguration"}

    def __init__(
        self,
        *,
        column_separator: Optional[str] = None,
        field_quote: Optional[str] = None,
        record_separator: Optional[str] = None,
        escape_char: Optional[str] = None,
        headers_present: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword column_separator: The string used to separate columns.
        :paramtype column_separator: str
        :keyword field_quote: The string used to quote a specific field.
        :paramtype field_quote: str
        :keyword record_separator: The string used to separate records.
        :paramtype record_separator: str
        :keyword escape_char: The string used as an escape character.
        :paramtype escape_char: str
        :keyword headers_present: Represents whether the data has headers.
        :paramtype headers_present: bool
        """
        super().__init__(**kwargs)
        self.column_separator = column_separator
        self.field_quote = field_quote
        self.record_separator = record_separator
        self.escape_char = escape_char
        self.headers_present = headers_present


class FilterBlobItem(_serialization.Model):
    """Blob info from a Filter Blobs API call.

    All required parameters must be populated in order to send to server.

    :ivar name: Required.
    :vartype name: str
    :ivar container_name: Required.
    :vartype container_name: str
    :ivar tags: Blob tags.
    :vartype tags: ~azure.storage.blob.models.BlobTags
    :ivar version_id:
    :vartype version_id: str
    :ivar is_current_version:
    :vartype is_current_version: bool
    """

    _validation = {
        "name": {"required": True},
        "container_name": {"required": True},
    }

    _attribute_map = {
        "name": {"key": "Name", "type": "str"},
        "container_name": {"key": "ContainerName", "type": "str"},
        "tags": {"key": "Tags", "type": "BlobTags"},
        "version_id": {"key": "VersionId", "type": "str"},
        "is_current_version": {"key": "IsCurrentVersion", "type": "bool"},
    }
    _xml_map = {"name": "Blob"}

    def __init__(
        self,
        *,
        name: str,
        container_name: str,
        tags: Optional["_models.BlobTags"] = None,
        version_id: Optional[str] = None,
        is_current_version: Optional[bool] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword name: Required.
        :paramtype name: str
        :keyword container_name: Required.
        :paramtype container_name: str
        :keyword tags: Blob tags.
        :paramtype tags: ~azure.storage.blob.models.BlobTags
        :keyword version_id:
        :paramtype version_id: str
        :keyword is_current_version:
        :paramtype is_current_version: bool
        """
        super().__init__(**kwargs)
        self.name = name
        self.container_name = container_name
        self.tags = tags
        self.version_id = version_id
        self.is_current_version = is_current_version


class FilterBlobSegment(_serialization.Model):
    """The result of a Filter Blobs API call.

    All required parameters must be populated in order to send to server.

    :ivar service_endpoint: Required.
    :vartype service_endpoint: str
    :ivar where: Required.
    :vartype where: str
    :ivar blobs: Required.
    :vartype blobs: list[~azure.storage.blob.models.FilterBlobItem]
    :ivar next_marker:
    :vartype next_marker: str
    """

    _validation = {
        "service_endpoint": {"required": True},
        "where": {"required": True},
        "blobs": {"required": True},
    }

    _attribute_map = {
        "service_endpoint": {"key": "ServiceEndpoint", "type": "str", "xml": {"attr": True}},
        "where": {"key": "Where", "type": "str"},
        "blobs": {
            "key": "Blobs",
            "type": "[FilterBlobItem]",
            "xml": {"name": "Blobs", "wrapped": True, "itemsName": "Blob"},
        },
        "next_marker": {"key": "NextMarker", "type": "str"},
    }
    _xml_map = {"name": "EnumerationResults"}

    def __init__(
        self,
        *,
        service_endpoint: str,
        where: str,
        blobs: List["_models.FilterBlobItem"],
        next_marker: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword service_endpoint: Required.
        :paramtype service_endpoint: str
        :keyword where: Required.
        :paramtype where: str
        :keyword blobs: Required.
        :paramtype blobs: list[~azure.storage.blob.models.FilterBlobItem]
        :keyword next_marker:
        :paramtype next_marker: str
        """
        super().__init__(**kwargs)
        self.service_endpoint = service_endpoint
        self.where = where
        self.blobs = blobs
        self.next_marker = next_marker


class GeoReplication(_serialization.Model):
    """Geo-Replication information for the Secondary Storage Service.

    All required parameters must be populated in order to send to server.

    :ivar status: The status of the secondary location. Required. Known values are: "live",
     "bootstrap", and "unavailable".
    :vartype status: str or ~azure.storage.blob.models.GeoReplicationStatusType
    :ivar last_sync_time: A GMT date/time value, to the second. All primary writes preceding this
     value are guaranteed to be available for read operations at the secondary. Primary writes after
     this point in time may or may not be available for reads. Required.
    :vartype last_sync_time: ~datetime.datetime
    """

    _validation = {
        "status": {"required": True},
        "last_sync_time": {"required": True},
    }

    _attribute_map = {
        "status": {"key": "Status", "type": "str"},
        "last_sync_time": {"key": "LastSyncTime", "type": "rfc-1123"},
    }

    def __init__(
        self,
        *,
        status: Union[str, "_models.GeoReplicationStatusType"],
        last_sync_time: datetime.datetime,
        **kwargs: Any
    ) -> None:
        """
        :keyword status: The status of the secondary location. Required. Known values are: "live",
         "bootstrap", and "unavailable".
        :paramtype status: str or ~azure.storage.blob.models.GeoReplicationStatusType
        :keyword last_sync_time: A GMT date/time value, to the second. All primary writes preceding
         this value are guaranteed to be available for read operations at the secondary. Primary writes
         after this point in time may or may not be available for reads. Required.
        :paramtype last_sync_time: ~datetime.datetime
        """
        super().__init__(**kwargs)
        self.status = status
        self.last_sync_time = last_sync_time


class JsonTextConfiguration(_serialization.Model):
    """json text configuration.

    :ivar record_separator: The string used to separate records.
    :vartype record_separator: str
    """

    _attribute_map = {
        "record_separator": {"key": "RecordSeparator", "type": "str", "xml": {"name": "RecordSeparator"}},
    }
    _xml_map = {"name": "JsonTextConfiguration"}

    def __init__(self, *, record_separator: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword record_separator: The string used to separate records.
        :paramtype record_separator: str
        """
        super().__init__(**kwargs)
        self.record_separator = record_separator


class KeyInfo(_serialization.Model):
    """Key information.

    All required parameters must be populated in order to send to server.

    :ivar start: The date-time the key is active in ISO 8601 UTC time. Required.
    :vartype start: str
    :ivar expiry: The date-time the key expires in ISO 8601 UTC time. Required.
    :vartype expiry: str
    """

    _validation = {
        "start": {"required": True},
        "expiry": {"required": True},
    }

    _attribute_map = {
        "start": {"key": "Start", "type": "str"},
        "expiry": {"key": "Expiry", "type": "str"},
    }

    def __init__(self, *, start: str, expiry: str, **kwargs: Any) -> None:
        """
        :keyword start: The date-time the key is active in ISO 8601 UTC time. Required.
        :paramtype start: str
        :keyword expiry: The date-time the key expires in ISO 8601 UTC time. Required.
        :paramtype expiry: str
        """
        super().__init__(**kwargs)
        self.start = start
        self.expiry = expiry


class LeaseAccessConditions(_serialization.Model):
    """Parameter group.

    :ivar lease_id: If specified, the operation only succeeds if the resource's lease is active and
     matches this ID.
    :vartype lease_id: str
    """

    _attribute_map = {
        "lease_id": {"key": "leaseId", "type": "str"},
    }

    def __init__(self, *, lease_id: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword lease_id: If specified, the operation only succeeds if the resource's lease is active
         and matches this ID.
        :paramtype lease_id: str
        """
        super().__init__(**kwargs)
        self.lease_id = lease_id


class ListBlobsFlatSegmentResponse(_serialization.Model):
    """An enumeration of blobs.

    All required parameters must be populated in order to send to server.

    :ivar service_endpoint: Required.
    :vartype service_endpoint: str
    :ivar container_name: Required.
    :vartype container_name: str
    :ivar prefix:
    :vartype prefix: str
    :ivar marker:
    :vartype marker: str
    :ivar max_results:
    :vartype max_results: int
    :ivar segment: Required.
    :vartype segment: ~azure.storage.blob.models.BlobFlatListSegment
    :ivar next_marker:
    :vartype next_marker: str
    """

    _validation = {
        "service_endpoint": {"required": True},
        "container_name": {"required": True},
        "segment": {"required": True},
    }

    _attribute_map = {
        "service_endpoint": {"key": "ServiceEndpoint", "type": "str", "xml": {"attr": True}},
        "container_name": {"key": "ContainerName", "type": "str", "xml": {"attr": True}},
        "prefix": {"key": "Prefix", "type": "str"},
        "marker": {"key": "Marker", "type": "str"},
        "max_results": {"key": "MaxResults", "type": "int"},
        "segment": {"key": "Segment", "type": "BlobFlatListSegment"},
        "next_marker": {"key": "NextMarker", "type": "str"},
    }
    _xml_map = {"name": "EnumerationResults"}

    def __init__(
        self,
        *,
        service_endpoint: str,
        container_name: str,
        segment: "_models.BlobFlatListSegment",
        prefix: Optional[str] = None,
        marker: Optional[str] = None,
        max_results: Optional[int] = None,
        next_marker: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword service_endpoint: Required.
        :paramtype service_endpoint: str
        :keyword container_name: Required.
        :paramtype container_name: str
        :keyword prefix:
        :paramtype prefix: str
        :keyword marker:
        :paramtype marker: str
        :keyword max_results:
        :paramtype max_results: int
        :keyword segment: Required.
        :paramtype segment: ~azure.storage.blob.models.BlobFlatListSegment
        :keyword next_marker:
        :paramtype next_marker: str
        """
        super().__init__(**kwargs)
        self.service_endpoint = service_endpoint
        self.container_name = container_name
        self.prefix = prefix
        self.marker = marker
        self.max_results = max_results
        self.segment = segment
        self.next_marker = next_marker


class ListBlobsHierarchySegmentResponse(_serialization.Model):
    """An enumeration of blobs.

    All required parameters must be populated in order to send to server.

    :ivar service_endpoint: Required.
    :vartype service_endpoint: str
    :ivar container_name: Required.
    :vartype container_name: str
    :ivar prefix:
    :vartype prefix: str
    :ivar marker:
    :vartype marker: str
    :ivar max_results:
    :vartype max_results: int
    :ivar delimiter:
    :vartype delimiter: str
    :ivar segment: Required.
    :vartype segment: ~azure.storage.blob.models.BlobHierarchyListSegment
    :ivar next_marker:
    :vartype next_marker: str
    """

    _validation = {
        "service_endpoint": {"required": True},
        "container_name": {"required": True},
        "segment": {"required": True},
    }

    _attribute_map = {
        "service_endpoint": {"key": "ServiceEndpoint", "type": "str", "xml": {"attr": True}},
        "container_name": {"key": "ContainerName", "type": "str", "xml": {"attr": True}},
        "prefix": {"key": "Prefix", "type": "str"},
        "marker": {"key": "Marker", "type": "str"},
        "max_results": {"key": "MaxResults", "type": "int"},
        "delimiter": {"key": "Delimiter", "type": "str"},
        "segment": {"key": "Segment", "type": "BlobHierarchyListSegment"},
        "next_marker": {"key": "NextMarker", "type": "str"},
    }
    _xml_map = {"name": "EnumerationResults"}

    def __init__(
        self,
        *,
        service_endpoint: str,
        container_name: str,
        segment: "_models.BlobHierarchyListSegment",
        prefix: Optional[str] = None,
        marker: Optional[str] = None,
        max_results: Optional[int] = None,
        delimiter: Optional[str] = None,
        next_marker: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword service_endpoint: Required.
        :paramtype service_endpoint: str
        :keyword container_name: Required.
        :paramtype container_name: str
        :keyword prefix:
        :paramtype prefix: str
        :keyword marker:
        :paramtype marker: str
        :keyword max_results:
        :paramtype max_results: int
        :keyword delimiter:
        :paramtype delimiter: str
        :keyword segment: Required.
        :paramtype segment: ~azure.storage.blob.models.BlobHierarchyListSegment
        :keyword next_marker:
        :paramtype next_marker: str
        """
        super().__init__(**kwargs)
        self.service_endpoint = service_endpoint
        self.container_name = container_name
        self.prefix = prefix
        self.marker = marker
        self.max_results = max_results
        self.delimiter = delimiter
        self.segment = segment
        self.next_marker = next_marker


class ListContainersSegmentResponse(_serialization.Model):
    """An enumeration of containers.

    All required parameters must be populated in order to send to server.

    :ivar service_endpoint: Required.
    :vartype service_endpoint: str
    :ivar prefix:
    :vartype prefix: str
    :ivar marker:
    :vartype marker: str
    :ivar max_results:
    :vartype max_results: int
    :ivar container_items: Required.
    :vartype container_items: list[~azure.storage.blob.models.ContainerItem]
    :ivar next_marker:
    :vartype next_marker: str
    """

    _validation = {
        "service_endpoint": {"required": True},
        "container_items": {"required": True},
    }

    _attribute_map = {
        "service_endpoint": {"key": "ServiceEndpoint", "type": "str", "xml": {"attr": True}},
        "prefix": {"key": "Prefix", "type": "str"},
        "marker": {"key": "Marker", "type": "str"},
        "max_results": {"key": "MaxResults", "type": "int"},
        "container_items": {
            "key": "ContainerItems",
            "type": "[ContainerItem]",
            "xml": {"name": "Containers", "wrapped": True, "itemsName": "Container"},
        },
        "next_marker": {"key": "NextMarker", "type": "str"},
    }
    _xml_map = {"name": "EnumerationResults"}

    def __init__(
        self,
        *,
        service_endpoint: str,
        container_items: List["_models.ContainerItem"],
        prefix: Optional[str] = None,
        marker: Optional[str] = None,
        max_results: Optional[int] = None,
        next_marker: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword service_endpoint: Required.
        :paramtype service_endpoint: str
        :keyword prefix:
        :paramtype prefix: str
        :keyword marker:
        :paramtype marker: str
        :keyword max_results:
        :paramtype max_results: int
        :keyword container_items: Required.
        :paramtype container_items: list[~azure.storage.blob.models.ContainerItem]
        :keyword next_marker:
        :paramtype next_marker: str
        """
        super().__init__(**kwargs)
        self.service_endpoint = service_endpoint
        self.prefix = prefix
        self.marker = marker
        self.max_results = max_results
        self.container_items = container_items
        self.next_marker = next_marker


class Logging(_serialization.Model):
    """Azure Analytics Logging settings.

    All required parameters must be populated in order to send to server.

    :ivar version: The version of Storage Analytics to configure. Required.
    :vartype version: str
    :ivar delete: Indicates whether all delete requests should be logged. Required.
    :vartype delete: bool
    :ivar read: Indicates whether all read requests should be logged. Required.
    :vartype read: bool
    :ivar write: Indicates whether all write requests should be logged. Required.
    :vartype write: bool
    :ivar retention_policy: the retention policy which determines how long the associated data
     should persist. Required.
    :vartype retention_policy: ~azure.storage.blob.models.RetentionPolicy
    """

    _validation = {
        "version": {"required": True},
        "delete": {"required": True},
        "read": {"required": True},
        "write": {"required": True},
        "retention_policy": {"required": True},
    }

    _attribute_map = {
        "version": {"key": "Version", "type": "str"},
        "delete": {"key": "Delete", "type": "bool"},
        "read": {"key": "Read", "type": "bool"},
        "write": {"key": "Write", "type": "bool"},
        "retention_policy": {"key": "RetentionPolicy", "type": "RetentionPolicy"},
    }

    def __init__(
        self,
        *,
        version: str,
        delete: bool,
        read: bool,
        write: bool,
        retention_policy: "_models.RetentionPolicy",
        **kwargs: Any
    ) -> None:
        """
        :keyword version: The version of Storage Analytics to configure. Required.
        :paramtype version: str
        :keyword delete: Indicates whether all delete requests should be logged. Required.
        :paramtype delete: bool
        :keyword read: Indicates whether all read requests should be logged. Required.
        :paramtype read: bool
        :keyword write: Indicates whether all write requests should be logged. Required.
        :paramtype write: bool
        :keyword retention_policy: the retention policy which determines how long the associated data
         should persist. Required.
        :paramtype retention_policy: ~azure.storage.blob.models.RetentionPolicy
        """
        super().__init__(**kwargs)
        self.version = version
        self.delete = delete
        self.read = read
        self.write = write
        self.retention_policy = retention_policy


class Metrics(_serialization.Model):
    """a summary of request statistics grouped by API in hour or minute aggregates for blobs.

    All required parameters must be populated in order to send to server.

    :ivar version: The version of Storage Analytics to configure.
    :vartype version: str
    :ivar enabled: Indicates whether metrics are enabled for the Blob service. Required.
    :vartype enabled: bool
    :ivar include_apis: Indicates whether metrics should generate summary statistics for called API
     operations.
    :vartype include_apis: bool
    :ivar retention_policy: the retention policy which determines how long the associated data
     should persist.
    :vartype retention_policy: ~azure.storage.blob.models.RetentionPolicy
    """

    _validation = {
        "enabled": {"required": True},
    }

    _attribute_map = {
        "version": {"key": "Version", "type": "str"},
        "enabled": {"key": "Enabled", "type": "bool"},
        "include_apis": {"key": "IncludeAPIs", "type": "bool"},
        "retention_policy": {"key": "RetentionPolicy", "type": "RetentionPolicy"},
    }

    def __init__(
        self,
        *,
        enabled: bool,
        version: Optional[str] = None,
        include_apis: Optional[bool] = None,
        retention_policy: Optional["_models.RetentionPolicy"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword version: The version of Storage Analytics to configure.
        :paramtype version: str
        :keyword enabled: Indicates whether metrics are enabled for the Blob service. Required.
        :paramtype enabled: bool
        :keyword include_apis: Indicates whether metrics should generate summary statistics for called
         API operations.
        :paramtype include_apis: bool
        :keyword retention_policy: the retention policy which determines how long the associated data
         should persist.
        :paramtype retention_policy: ~azure.storage.blob.models.RetentionPolicy
        """
        super().__init__(**kwargs)
        self.version = version
        self.enabled = enabled
        self.include_apis = include_apis
        self.retention_policy = retention_policy


class ModifiedAccessConditions(_serialization.Model):
    """Parameter group.

    :ivar if_modified_since: Specify this header value to operate only on a blob if it has been
     modified since the specified date/time.
    :vartype if_modified_since: ~datetime.datetime
    :ivar if_unmodified_since: Specify this header value to operate only on a blob if it has not
     been modified since the specified date/time.
    :vartype if_unmodified_since: ~datetime.datetime
    :ivar if_match: Specify an ETag value to operate only on blobs with a matching value.
    :vartype if_match: str
    :ivar if_none_match: Specify an ETag value to operate only on blobs without a matching value.
    :vartype if_none_match: str
    :ivar if_tags: Specify a SQL where clause on blob tags to operate only on blobs with a matching
     value.
    :vartype if_tags: str
    """

    _attribute_map = {
        "if_modified_since": {"key": "ifModifiedSince", "type": "rfc-1123"},
        "if_unmodified_since": {"key": "ifUnmodifiedSince", "type": "rfc-1123"},
        "if_match": {"key": "ifMatch", "type": "str"},
        "if_none_match": {"key": "ifNoneMatch", "type": "str"},
        "if_tags": {"key": "ifTags", "type": "str"},
    }

    def __init__(
        self,
        *,
        if_modified_since: Optional[datetime.datetime] = None,
        if_unmodified_since: Optional[datetime.datetime] = None,
        if_match: Optional[str] = None,
        if_none_match: Optional[str] = None,
        if_tags: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword if_modified_since: Specify this header value to operate only on a blob if it has been
         modified since the specified date/time.
        :paramtype if_modified_since: ~datetime.datetime
        :keyword if_unmodified_since: Specify this header value to operate only on a blob if it has not
         been modified since the specified date/time.
        :paramtype if_unmodified_since: ~datetime.datetime
        :keyword if_match: Specify an ETag value to operate only on blobs with a matching value.
        :paramtype if_match: str
        :keyword if_none_match: Specify an ETag value to operate only on blobs without a matching
         value.
        :paramtype if_none_match: str
        :keyword if_tags: Specify a SQL where clause on blob tags to operate only on blobs with a
         matching value.
        :paramtype if_tags: str
        """
        super().__init__(**kwargs)
        self.if_modified_since = if_modified_since
        self.if_unmodified_since = if_unmodified_since
        self.if_match = if_match
        self.if_none_match = if_none_match
        self.if_tags = if_tags


class PageList(_serialization.Model):
    """the list of pages.

    :ivar page_range:
    :vartype page_range: list[~azure.storage.blob.models.PageRange]
    :ivar clear_range:
    :vartype clear_range: list[~azure.storage.blob.models.ClearRange]
    :ivar next_marker:
    :vartype next_marker: str
    """

    _attribute_map = {
        "page_range": {"key": "PageRange", "type": "[PageRange]", "xml": {"itemsName": "PageRange"}},
        "clear_range": {"key": "ClearRange", "type": "[ClearRange]", "xml": {"itemsName": "ClearRange"}},
        "next_marker": {"key": "NextMarker", "type": "str"},
    }

    def __init__(
        self,
        *,
        page_range: Optional[List["_models.PageRange"]] = None,
        clear_range: Optional[List["_models.ClearRange"]] = None,
        next_marker: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword page_range:
        :paramtype page_range: list[~azure.storage.blob.models.PageRange]
        :keyword clear_range:
        :paramtype clear_range: list[~azure.storage.blob.models.ClearRange]
        :keyword next_marker:
        :paramtype next_marker: str
        """
        super().__init__(**kwargs)
        self.page_range = page_range
        self.clear_range = clear_range
        self.next_marker = next_marker


class PageRange(_serialization.Model):
    """PageRange.

    All required parameters must be populated in order to send to server.

    :ivar start: Required.
    :vartype start: int
    :ivar end: Required.
    :vartype end: int
    """

    _validation = {
        "start": {"required": True},
        "end": {"required": True},
    }

    _attribute_map = {
        "start": {"key": "Start", "type": "int", "xml": {"name": "Start"}},
        "end": {"key": "End", "type": "int", "xml": {"name": "End"}},
    }
    _xml_map = {"name": "PageRange"}

    def __init__(self, *, start: int, end: int, **kwargs: Any) -> None:
        """
        :keyword start: Required.
        :paramtype start: int
        :keyword end: Required.
        :paramtype end: int
        """
        super().__init__(**kwargs)
        self.start = start
        self.end = end


class QueryFormat(_serialization.Model):
    """QueryFormat.

    All required parameters must be populated in order to send to server.

    :ivar type: The quick query format type. Required. Known values are: "delimited", "json",
     "arrow", and "parquet".
    :vartype type: str or ~azure.storage.blob.models.QueryFormatType
    :ivar delimited_text_configuration: Groups the settings used for interpreting the blob data if
     the blob is delimited text formatted.
    :vartype delimited_text_configuration: ~azure.storage.blob.models.DelimitedTextConfiguration
    :ivar json_text_configuration: json text configuration.
    :vartype json_text_configuration: ~azure.storage.blob.models.JsonTextConfiguration
    :ivar arrow_configuration: Groups the settings used for formatting the response if the response
     should be Arrow formatted.
    :vartype arrow_configuration: ~azure.storage.blob.models.ArrowConfiguration
    :ivar parquet_text_configuration: parquet configuration.
    :vartype parquet_text_configuration: JSON
    """

    _validation = {
        "type": {"required": True},
    }

    _attribute_map = {
        "type": {"key": "Type", "type": "str", "xml": {"name": "Type"}},
        "delimited_text_configuration": {"key": "DelimitedTextConfiguration", "type": "DelimitedTextConfiguration"},
        "json_text_configuration": {"key": "JsonTextConfiguration", "type": "JsonTextConfiguration"},
        "arrow_configuration": {"key": "ArrowConfiguration", "type": "ArrowConfiguration"},
        "parquet_text_configuration": {"key": "ParquetTextConfiguration", "type": "object"},
    }

    def __init__(
        self,
        *,
        type: Union[str, "_models.QueryFormatType"],
        delimited_text_configuration: Optional["_models.DelimitedTextConfiguration"] = None,
        json_text_configuration: Optional["_models.JsonTextConfiguration"] = None,
        arrow_configuration: Optional["_models.ArrowConfiguration"] = None,
        parquet_text_configuration: Optional[JSON] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword type: The quick query format type. Required. Known values are: "delimited", "json",
         "arrow", and "parquet".
        :paramtype type: str or ~azure.storage.blob.models.QueryFormatType
        :keyword delimited_text_configuration: Groups the settings used for interpreting the blob data
         if the blob is delimited text formatted.
        :paramtype delimited_text_configuration: ~azure.storage.blob.models.DelimitedTextConfiguration
        :keyword json_text_configuration: json text configuration.
        :paramtype json_text_configuration: ~azure.storage.blob.models.JsonTextConfiguration
        :keyword arrow_configuration: Groups the settings used for formatting the response if the
         response should be Arrow formatted.
        :paramtype arrow_configuration: ~azure.storage.blob.models.ArrowConfiguration
        :keyword parquet_text_configuration: parquet configuration.
        :paramtype parquet_text_configuration: JSON
        """
        super().__init__(**kwargs)
        self.type = type
        self.delimited_text_configuration = delimited_text_configuration
        self.json_text_configuration = json_text_configuration
        self.arrow_configuration = arrow_configuration
        self.parquet_text_configuration = parquet_text_configuration


class QueryRequest(_serialization.Model):
    """Groups the set of query request settings.

    Variables are only populated by the server, and will be ignored when sending a request.

    All required parameters must be populated in order to send to server.

    :ivar query_type: Required. The type of the provided query expression. Required. Default value
     is "SQL".
    :vartype query_type: str
    :ivar expression: The query expression in SQL. The maximum size of the query expression is
     256KiB. Required.
    :vartype expression: str
    :ivar input_serialization:
    :vartype input_serialization: ~azure.storage.blob.models.QuerySerialization
    :ivar output_serialization:
    :vartype output_serialization: ~azure.storage.blob.models.QuerySerialization
    """

    _validation = {
        "query_type": {"required": True, "constant": True},
        "expression": {"required": True},
    }

    _attribute_map = {
        "query_type": {"key": "QueryType", "type": "str", "xml": {"name": "QueryType"}},
        "expression": {"key": "Expression", "type": "str", "xml": {"name": "Expression"}},
        "input_serialization": {"key": "InputSerialization", "type": "QuerySerialization"},
        "output_serialization": {"key": "OutputSerialization", "type": "QuerySerialization"},
    }
    _xml_map = {"name": "QueryRequest"}

    query_type = "SQL"

    def __init__(
        self,
        *,
        expression: str,
        input_serialization: Optional["_models.QuerySerialization"] = None,
        output_serialization: Optional["_models.QuerySerialization"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword expression: The query expression in SQL. The maximum size of the query expression is
         256KiB. Required.
        :paramtype expression: str
        :keyword input_serialization:
        :paramtype input_serialization: ~azure.storage.blob.models.QuerySerialization
        :keyword output_serialization:
        :paramtype output_serialization: ~azure.storage.blob.models.QuerySerialization
        """
        super().__init__(**kwargs)
        self.expression = expression
        self.input_serialization = input_serialization
        self.output_serialization = output_serialization


class QuerySerialization(_serialization.Model):
    """QuerySerialization.

    All required parameters must be populated in order to send to server.

    :ivar format: Required.
    :vartype format: ~azure.storage.blob.models.QueryFormat
    """

    _validation = {
        "format": {"required": True},
    }

    _attribute_map = {
        "format": {"key": "Format", "type": "QueryFormat"},
    }

    def __init__(self, *, format: "_models.QueryFormat", **kwargs: Any) -> None:
        """
        :keyword format: Required.
        :paramtype format: ~azure.storage.blob.models.QueryFormat
        """
        super().__init__(**kwargs)
        self.format = format


class RetentionPolicy(_serialization.Model):
    """the retention policy which determines how long the associated data should persist.

    All required parameters must be populated in order to send to server.

    :ivar enabled: Indicates whether a retention policy is enabled for the storage service.
     Required.
    :vartype enabled: bool
    :ivar days: Indicates the number of days that metrics or logging or soft-deleted data should be
     retained. All data older than this value will be deleted.
    :vartype days: int
    :ivar allow_permanent_delete: Indicates whether permanent delete is allowed on this storage
     account.
    :vartype allow_permanent_delete: bool
    """

    _validation = {
        "enabled": {"required": True},
        "days": {"minimum": 1},
    }

    _attribute_map = {
        "enabled": {"key": "Enabled", "type": "bool"},
        "days": {"key": "Days", "type": "int"},
        "allow_permanent_delete": {"key": "AllowPermanentDelete", "type": "bool"},
    }

    def __init__(
        self, *, enabled: bool, days: Optional[int] = None, allow_permanent_delete: Optional[bool] = None, **kwargs: Any
    ) -> None:
        """
        :keyword enabled: Indicates whether a retention policy is enabled for the storage service.
         Required.
        :paramtype enabled: bool
        :keyword days: Indicates the number of days that metrics or logging or soft-deleted data should
         be retained. All data older than this value will be deleted.
        :paramtype days: int
        :keyword allow_permanent_delete: Indicates whether permanent delete is allowed on this storage
         account.
        :paramtype allow_permanent_delete: bool
        """
        super().__init__(**kwargs)
        self.enabled = enabled
        self.days = days
        self.allow_permanent_delete = allow_permanent_delete


class SequenceNumberAccessConditions(_serialization.Model):
    """Parameter group.

    :ivar if_sequence_number_less_than_or_equal_to: Specify this header value to operate only on a
     blob if it has a sequence number less than or equal to the specified.
    :vartype if_sequence_number_less_than_or_equal_to: int
    :ivar if_sequence_number_less_than: Specify this header value to operate only on a blob if it
     has a sequence number less than the specified.
    :vartype if_sequence_number_less_than: int
    :ivar if_sequence_number_equal_to: Specify this header value to operate only on a blob if it
     has the specified sequence number.
    :vartype if_sequence_number_equal_to: int
    """

    _attribute_map = {
        "if_sequence_number_less_than_or_equal_to": {"key": "ifSequenceNumberLessThanOrEqualTo", "type": "int"},
        "if_sequence_number_less_than": {"key": "ifSequenceNumberLessThan", "type": "int"},
        "if_sequence_number_equal_to": {"key": "ifSequenceNumberEqualTo", "type": "int"},
    }

    def __init__(
        self,
        *,
        if_sequence_number_less_than_or_equal_to: Optional[int] = None,
        if_sequence_number_less_than: Optional[int] = None,
        if_sequence_number_equal_to: Optional[int] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword if_sequence_number_less_than_or_equal_to: Specify this header value to operate only on
         a blob if it has a sequence number less than or equal to the specified.
        :paramtype if_sequence_number_less_than_or_equal_to: int
        :keyword if_sequence_number_less_than: Specify this header value to operate only on a blob if
         it has a sequence number less than the specified.
        :paramtype if_sequence_number_less_than: int
        :keyword if_sequence_number_equal_to: Specify this header value to operate only on a blob if it
         has the specified sequence number.
        :paramtype if_sequence_number_equal_to: int
        """
        super().__init__(**kwargs)
        self.if_sequence_number_less_than_or_equal_to = if_sequence_number_less_than_or_equal_to
        self.if_sequence_number_less_than = if_sequence_number_less_than
        self.if_sequence_number_equal_to = if_sequence_number_equal_to


class SignedIdentifier(_serialization.Model):
    """signed identifier.

    All required parameters must be populated in order to send to server.

    :ivar id: a unique id. Required.
    :vartype id: str
    :ivar access_policy: An Access policy.
    :vartype access_policy: ~azure.storage.blob.models.AccessPolicy
    """

    _validation = {
        "id": {"required": True},
    }

    _attribute_map = {
        "id": {"key": "Id", "type": "str"},
        "access_policy": {"key": "AccessPolicy", "type": "AccessPolicy"},
    }
    _xml_map = {"name": "SignedIdentifier"}

    def __init__(
        self,
        *,
        id: str,  # pylint: disable=redefined-builtin
        access_policy: Optional["_models.AccessPolicy"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword id: a unique id. Required.
        :paramtype id: str
        :keyword access_policy: An Access policy.
        :paramtype access_policy: ~azure.storage.blob.models.AccessPolicy
        """
        super().__init__(**kwargs)
        self.id = id
        self.access_policy = access_policy


class SourceModifiedAccessConditions(_serialization.Model):
    """Parameter group.

    :ivar source_if_modified_since: Specify this header value to operate only on a blob if it has
     been modified since the specified date/time.
    :vartype source_if_modified_since: ~datetime.datetime
    :ivar source_if_unmodified_since: Specify this header value to operate only on a blob if it has
     not been modified since the specified date/time.
    :vartype source_if_unmodified_since: ~datetime.datetime
    :ivar source_if_match: Specify an ETag value to operate only on blobs with a matching value.
    :vartype source_if_match: str
    :ivar source_if_none_match: Specify an ETag value to operate only on blobs without a matching
     value.
    :vartype source_if_none_match: str
    :ivar source_if_tags: Specify a SQL where clause on blob tags to operate only on blobs with a
     matching value.
    :vartype source_if_tags: str
    """

    _attribute_map = {
        "source_if_modified_since": {"key": "sourceIfModifiedSince", "type": "rfc-1123"},
        "source_if_unmodified_since": {"key": "sourceIfUnmodifiedSince", "type": "rfc-1123"},
        "source_if_match": {"key": "sourceIfMatch", "type": "str"},
        "source_if_none_match": {"key": "sourceIfNoneMatch", "type": "str"},
        "source_if_tags": {"key": "sourceIfTags", "type": "str"},
    }

    def __init__(
        self,
        *,
        source_if_modified_since: Optional[datetime.datetime] = None,
        source_if_unmodified_since: Optional[datetime.datetime] = None,
        source_if_match: Optional[str] = None,
        source_if_none_match: Optional[str] = None,
        source_if_tags: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword source_if_modified_since: Specify this header value to operate only on a blob if it
         has been modified since the specified date/time.
        :paramtype source_if_modified_since: ~datetime.datetime
        :keyword source_if_unmodified_since: Specify this header value to operate only on a blob if it
         has not been modified since the specified date/time.
        :paramtype source_if_unmodified_since: ~datetime.datetime
        :keyword source_if_match: Specify an ETag value to operate only on blobs with a matching value.
        :paramtype source_if_match: str
        :keyword source_if_none_match: Specify an ETag value to operate only on blobs without a
         matching value.
        :paramtype source_if_none_match: str
        :keyword source_if_tags: Specify a SQL where clause on blob tags to operate only on blobs with
         a matching value.
        :paramtype source_if_tags: str
        """
        super().__init__(**kwargs)
        self.source_if_modified_since = source_if_modified_since
        self.source_if_unmodified_since = source_if_unmodified_since
        self.source_if_match = source_if_match
        self.source_if_none_match = source_if_none_match
        self.source_if_tags = source_if_tags


class StaticWebsite(_serialization.Model):
    """The properties that enable an account to host a static website.

    All required parameters must be populated in order to send to server.

    :ivar enabled: Indicates whether this account is hosting a static website. Required.
    :vartype enabled: bool
    :ivar index_document: The default name of the index page under each directory.
    :vartype index_document: str
    :ivar error_document404_path: The absolute path of the custom 404 page.
    :vartype error_document404_path: str
    :ivar default_index_document_path: Absolute path of the default index page.
    :vartype default_index_document_path: str
    """

    _validation = {
        "enabled": {"required": True},
    }

    _attribute_map = {
        "enabled": {"key": "Enabled", "type": "bool"},
        "index_document": {"key": "IndexDocument", "type": "str"},
        "error_document404_path": {"key": "ErrorDocument404Path", "type": "str"},
        "default_index_document_path": {"key": "DefaultIndexDocumentPath", "type": "str"},
    }

    def __init__(
        self,
        *,
        enabled: bool,
        index_document: Optional[str] = None,
        error_document404_path: Optional[str] = None,
        default_index_document_path: Optional[str] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword enabled: Indicates whether this account is hosting a static website. Required.
        :paramtype enabled: bool
        :keyword index_document: The default name of the index page under each directory.
        :paramtype index_document: str
        :keyword error_document404_path: The absolute path of the custom 404 page.
        :paramtype error_document404_path: str
        :keyword default_index_document_path: Absolute path of the default index page.
        :paramtype default_index_document_path: str
        """
        super().__init__(**kwargs)
        self.enabled = enabled
        self.index_document = index_document
        self.error_document404_path = error_document404_path
        self.default_index_document_path = default_index_document_path


class StorageError(_serialization.Model):
    """StorageError.

    :ivar message:
    :vartype message: str
    """

    _attribute_map = {
        "message": {"key": "Message", "type": "str"},
    }

    def __init__(self, *, message: Optional[str] = None, **kwargs: Any) -> None:
        """
        :keyword message:
        :paramtype message: str
        """
        super().__init__(**kwargs)
        self.message = message


class StorageServiceProperties(_serialization.Model):
    """Storage Service Properties.

    :ivar logging: Azure Analytics Logging settings.
    :vartype logging: ~azure.storage.blob.models.Logging
    :ivar hour_metrics: a summary of request statistics grouped by API in hour or minute aggregates
     for blobs.
    :vartype hour_metrics: ~azure.storage.blob.models.Metrics
    :ivar minute_metrics: a summary of request statistics grouped by API in hour or minute
     aggregates for blobs.
    :vartype minute_metrics: ~azure.storage.blob.models.Metrics
    :ivar cors: The set of CORS rules.
    :vartype cors: list[~azure.storage.blob.models.CorsRule]
    :ivar default_service_version: The default version to use for requests to the Blob service if
     an incoming request's version is not specified. Possible values include version 2008-10-27 and
     all more recent versions.
    :vartype default_service_version: str
    :ivar delete_retention_policy: the retention policy which determines how long the associated
     data should persist.
    :vartype delete_retention_policy: ~azure.storage.blob.models.RetentionPolicy
    :ivar static_website: The properties that enable an account to host a static website.
    :vartype static_website: ~azure.storage.blob.models.StaticWebsite
    """

    _attribute_map = {
        "logging": {"key": "Logging", "type": "Logging"},
        "hour_metrics": {"key": "HourMetrics", "type": "Metrics"},
        "minute_metrics": {"key": "MinuteMetrics", "type": "Metrics"},
        "cors": {"key": "Cors", "type": "[CorsRule]", "xml": {"wrapped": True}},
        "default_service_version": {"key": "DefaultServiceVersion", "type": "str"},
        "delete_retention_policy": {"key": "DeleteRetentionPolicy", "type": "RetentionPolicy"},
        "static_website": {"key": "StaticWebsite", "type": "StaticWebsite"},
    }

    def __init__(
        self,
        *,
        logging: Optional["_models.Logging"] = None,
        hour_metrics: Optional["_models.Metrics"] = None,
        minute_metrics: Optional["_models.Metrics"] = None,
        cors: Optional[List["_models.CorsRule"]] = None,
        default_service_version: Optional[str] = None,
        delete_retention_policy: Optional["_models.RetentionPolicy"] = None,
        static_website: Optional["_models.StaticWebsite"] = None,
        **kwargs: Any
    ) -> None:
        """
        :keyword logging: Azure Analytics Logging settings.
        :paramtype logging: ~azure.storage.blob.models.Logging
        :keyword hour_metrics: a summary of request statistics grouped by API in hour or minute
         aggregates for blobs.
        :paramtype hour_metrics: ~azure.storage.blob.models.Metrics
        :keyword minute_metrics: a summary of request statistics grouped by API in hour or minute
         aggregates for blobs.
        :paramtype minute_metrics: ~azure.storage.blob.models.Metrics
        :keyword cors: The set of CORS rules.
        :paramtype cors: list[~azure.storage.blob.models.CorsRule]
        :keyword default_service_version: The default version to use for requests to the Blob service
         if an incoming request's version is not specified. Possible values include version 2008-10-27
         and all more recent versions.
        :paramtype default_service_version: str
        :keyword delete_retention_policy: the retention policy which determines how long the associated
         data should persist.
        :paramtype delete_retention_policy: ~azure.storage.blob.models.RetentionPolicy
        :keyword static_website: The properties that enable an account to host a static website.
        :paramtype static_website: ~azure.storage.blob.models.StaticWebsite
        """
        super().__init__(**kwargs)
        self.logging = logging
        self.hour_metrics = hour_metrics
        self.minute_metrics = minute_metrics
        self.cors = cors
        self.default_service_version = default_service_version
        self.delete_retention_policy = delete_retention_policy
        self.static_website = static_website


class StorageServiceStats(_serialization.Model):
    """Stats for the storage service.

    :ivar geo_replication: Geo-Replication information for the Secondary Storage Service.
    :vartype geo_replication: ~azure.storage.blob.models.GeoReplication
    """

    _attribute_map = {
        "geo_replication": {"key": "GeoReplication", "type": "GeoReplication"},
    }

    def __init__(self, *, geo_replication: Optional["_models.GeoReplication"] = None, **kwargs: Any) -> None:
        """
        :keyword geo_replication: Geo-Replication information for the Secondary Storage Service.
        :paramtype geo_replication: ~azure.storage.blob.models.GeoReplication
        """
        super().__init__(**kwargs)
        self.geo_replication = geo_replication


class UserDelegationKey(_serialization.Model):
    """A user delegation key.

    All required parameters must be populated in order to send to server.

    :ivar signed_oid: The Azure Active Directory object ID in GUID format. Required.
    :vartype signed_oid: str
    :ivar signed_tid: The Azure Active Directory tenant ID in GUID format. Required.
    :vartype signed_tid: str
    :ivar signed_start: The date-time the key is active. Required.
    :vartype signed_start: ~datetime.datetime
    :ivar signed_expiry: The date-time the key expires. Required.
    :vartype signed_expiry: ~datetime.datetime
    :ivar signed_service: Abbreviation of the Azure Storage service that accepts the key. Required.
    :vartype signed_service: str
    :ivar signed_version: The service version that created the key. Required.
    :vartype signed_version: str
    :ivar value: The key as a base64 string. Required.
    :vartype value: str
    """

    _validation = {
        "signed_oid": {"required": True},
        "signed_tid": {"required": True},
        "signed_start": {"required": True},
        "signed_expiry": {"required": True},
        "signed_service": {"required": True},
        "signed_version": {"required": True},
        "value": {"required": True},
    }

    _attribute_map = {
        "signed_oid": {"key": "SignedOid", "type": "str"},
        "signed_tid": {"key": "SignedTid", "type": "str"},
        "signed_start": {"key": "SignedStart", "type": "iso-8601"},
        "signed_expiry": {"key": "SignedExpiry", "type": "iso-8601"},
        "signed_service": {"key": "SignedService", "type": "str"},
        "signed_version": {"key": "SignedVersion", "type": "str"},
        "value": {"key": "Value", "type": "str"},
    }

    def __init__(
        self,
        *,
        signed_oid: str,
        signed_tid: str,
        signed_start: datetime.datetime,
        signed_expiry: datetime.datetime,
        signed_service: str,
        signed_version: str,
        value: str,
        **kwargs: Any
    ) -> None:
        """
        :keyword signed_oid: The Azure Active Directory object ID in GUID format. Required.
        :paramtype signed_oid: str
        :keyword signed_tid: The Azure Active Directory tenant ID in GUID format. Required.
        :paramtype signed_tid: str
        :keyword signed_start: The date-time the key is active. Required.
        :paramtype signed_start: ~datetime.datetime
        :keyword signed_expiry: The date-time the key expires. Required.
        :paramtype signed_expiry: ~datetime.datetime
        :keyword signed_service: Abbreviation of the Azure Storage service that accepts the key.
         Required.
        :paramtype signed_service: str
        :keyword signed_version: The service version that created the key. Required.
        :paramtype signed_version: str
        :keyword value: The key as a base64 string. Required.
        :paramtype value: str
        """
        super().__init__(**kwargs)
        self.signed_oid = signed_oid
        self.signed_tid = signed_tid
        self.signed_start = signed_start
        self.signed_expiry = signed_expiry
        self.signed_service = signed_service
        self.signed_version = signed_version
        self.value = value
