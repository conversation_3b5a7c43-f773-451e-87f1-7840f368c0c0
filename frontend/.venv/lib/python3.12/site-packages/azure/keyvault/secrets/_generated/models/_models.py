# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) Python Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------
# pylint: disable=useless-super-delegation

import datetime
from typing import Any, Dict, Mapping, Optional, TYPE_CHECKING, Union, overload

from .._utils.model_base import Model as _Model, rest_field

if TYPE_CHECKING:
    from .. import models as _models


class BackupSecretResult(_Model):
    """The backup secret result, containing the backup blob.

    :ivar value: The backup blob containing the backed up secret.
    :vartype value: bytes
    """

    value: Optional[bytes] = rest_field(visibility=["read"], format="base64url")
    """The backup blob containing the backed up secret."""


class DeletedSecretBundle(_Model):
    """A Deleted Secret consisting of its previous id, attributes and its tags, as well as information
    on when it will be purged.

    :ivar value: The secret value.
    :vartype value: str
    :ivar id: The secret id.
    :vartype id: str
    :ivar content_type: The content type of the secret.
    :vartype content_type: str
    :ivar attributes: The secret management attributes.
    :vartype attributes: ~azure.keyvault.secrets._generated.models.SecretAttributes
    :ivar tags: Application specific metadata in the form of key-value pairs.
    :vartype tags: dict[str, str]
    :ivar kid: If this is a secret backing a KV certificate, then this field specifies the
     corresponding key backing the KV certificate.
    :vartype kid: str
    :ivar managed: True if the secret's lifetime is managed by key vault. If this is a secret
     backing a certificate, then managed will be true.
    :vartype managed: bool
    :ivar recovery_id: The url of the recovery object, used to identify and recover the deleted
     secret.
    :vartype recovery_id: str
    :ivar scheduled_purge_date: The time when the secret is scheduled to be purged, in UTC.
    :vartype scheduled_purge_date: ~datetime.datetime
    :ivar deleted_date: The time when the secret was deleted, in UTC.
    :vartype deleted_date: ~datetime.datetime
    """

    value: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The secret value."""
    id: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The secret id."""
    content_type: Optional[str] = rest_field(
        name="contentType", visibility=["read", "create", "update", "delete", "query"]
    )
    """The content type of the secret."""
    attributes: Optional["_models.SecretAttributes"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The secret management attributes."""
    tags: Optional[Dict[str, str]] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Application specific metadata in the form of key-value pairs."""
    kid: Optional[str] = rest_field(visibility=["read"])
    """If this is a secret backing a KV certificate, then this field specifies the corresponding key
     backing the KV certificate."""
    managed: Optional[bool] = rest_field(visibility=["read"])
    """True if the secret's lifetime is managed by key vault. If this is a secret backing a
     certificate, then managed will be true."""
    recovery_id: Optional[str] = rest_field(
        name="recoveryId", visibility=["read", "create", "update", "delete", "query"]
    )
    """The url of the recovery object, used to identify and recover the deleted secret."""
    scheduled_purge_date: Optional[datetime.datetime] = rest_field(
        name="scheduledPurgeDate", visibility=["read"], format="unix-timestamp"
    )
    """The time when the secret is scheduled to be purged, in UTC."""
    deleted_date: Optional[datetime.datetime] = rest_field(
        name="deletedDate", visibility=["read"], format="unix-timestamp"
    )
    """The time when the secret was deleted, in UTC."""

    @overload
    def __init__(
        self,
        *,
        value: Optional[str] = None,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        content_type: Optional[str] = None,
        attributes: Optional["_models.SecretAttributes"] = None,
        tags: Optional[Dict[str, str]] = None,
        recovery_id: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DeletedSecretItem(_Model):
    """The deleted secret item containing metadata about the deleted secret.

    :ivar id: Secret identifier.
    :vartype id: str
    :ivar attributes: The secret management attributes.
    :vartype attributes: ~azure.keyvault.secrets._generated.models.SecretAttributes
    :ivar tags: Application specific metadata in the form of key-value pairs.
    :vartype tags: dict[str, str]
    :ivar content_type: Type of the secret value such as a password.
    :vartype content_type: str
    :ivar managed: True if the secret's lifetime is managed by key vault. If this is a key backing
     a certificate, then managed will be true.
    :vartype managed: bool
    :ivar recovery_id: The url of the recovery object, used to identify and recover the deleted
     secret.
    :vartype recovery_id: str
    :ivar scheduled_purge_date: The time when the secret is scheduled to be purged, in UTC.
    :vartype scheduled_purge_date: ~datetime.datetime
    :ivar deleted_date: The time when the secret was deleted, in UTC.
    :vartype deleted_date: ~datetime.datetime
    """

    id: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Secret identifier."""
    attributes: Optional["_models.SecretAttributes"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The secret management attributes."""
    tags: Optional[Dict[str, str]] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Application specific metadata in the form of key-value pairs."""
    content_type: Optional[str] = rest_field(
        name="contentType", visibility=["read", "create", "update", "delete", "query"]
    )
    """Type of the secret value such as a password."""
    managed: Optional[bool] = rest_field(visibility=["read"])
    """True if the secret's lifetime is managed by key vault. If this is a key backing a certificate,
     then managed will be true."""
    recovery_id: Optional[str] = rest_field(
        name="recoveryId", visibility=["read", "create", "update", "delete", "query"]
    )
    """The url of the recovery object, used to identify and recover the deleted secret."""
    scheduled_purge_date: Optional[datetime.datetime] = rest_field(
        name="scheduledPurgeDate", visibility=["read"], format="unix-timestamp"
    )
    """The time when the secret is scheduled to be purged, in UTC."""
    deleted_date: Optional[datetime.datetime] = rest_field(
        name="deletedDate", visibility=["read"], format="unix-timestamp"
    )
    """The time when the secret was deleted, in UTC."""

    @overload
    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        attributes: Optional["_models.SecretAttributes"] = None,
        tags: Optional[Dict[str, str]] = None,
        content_type: Optional[str] = None,
        recovery_id: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class KeyVaultError(_Model):
    """The key vault error exception.

    :ivar error: The key vault server error.
    :vartype error: ~azure.keyvault.secrets._generated.models.KeyVaultErrorError
    """

    error: Optional["_models.KeyVaultErrorError"] = rest_field(visibility=["read"])
    """The key vault server error."""


class KeyVaultErrorError(_Model):
    """KeyVaultErrorError.

    :ivar code: The error code.
    :vartype code: str
    :ivar message: The error message.
    :vartype message: str
    :ivar inner_error: The key vault server error.
    :vartype inner_error: ~azure.keyvault.secrets._generated.models.KeyVaultErrorError
    """

    code: Optional[str] = rest_field(visibility=["read"])
    """The error code."""
    message: Optional[str] = rest_field(visibility=["read"])
    """The error message."""
    inner_error: Optional["_models.KeyVaultErrorError"] = rest_field(name="innererror", visibility=["read"])
    """The key vault server error."""


class SecretAttributes(_Model):
    """The secret management attributes.

    :ivar enabled: Determines whether the object is enabled.
    :vartype enabled: bool
    :ivar not_before: Not before date in UTC.
    :vartype not_before: ~datetime.datetime
    :ivar expires: Expiry date in UTC.
    :vartype expires: ~datetime.datetime
    :ivar created: Creation time in UTC.
    :vartype created: ~datetime.datetime
    :ivar updated: Last updated time in UTC.
    :vartype updated: ~datetime.datetime
    :ivar recoverable_days: softDelete data retention days. Value should be >=7 and <=90 when
     softDelete enabled, otherwise 0.
    :vartype recoverable_days: int
    :ivar recovery_level: Reflects the deletion recovery level currently in effect for secrets in
     the current vault. If it contains 'Purgeable', the secret can be permanently deleted by a
     privileged user; otherwise, only the system can purge the secret, at the end of the retention
     interval. Known values are: "Purgeable", "Recoverable+Purgeable", "Recoverable",
     "Recoverable+ProtectedSubscription", "CustomizedRecoverable+Purgeable",
     "CustomizedRecoverable", and "CustomizedRecoverable+ProtectedSubscription".
    :vartype recovery_level: str or ~azure.keyvault.secrets._generated.models.DeletionRecoveryLevel
    """

    enabled: Optional[bool] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Determines whether the object is enabled."""
    not_before: Optional[datetime.datetime] = rest_field(
        name="nbf", visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """Not before date in UTC."""
    expires: Optional[datetime.datetime] = rest_field(
        name="exp", visibility=["read", "create", "update", "delete", "query"], format="unix-timestamp"
    )
    """Expiry date in UTC."""
    created: Optional[datetime.datetime] = rest_field(visibility=["read"], format="unix-timestamp")
    """Creation time in UTC."""
    updated: Optional[datetime.datetime] = rest_field(visibility=["read"], format="unix-timestamp")
    """Last updated time in UTC."""
    recoverable_days: Optional[int] = rest_field(name="recoverableDays", visibility=["read"])
    """softDelete data retention days. Value should be >=7 and <=90 when softDelete enabled, otherwise
     0."""
    recovery_level: Optional[Union[str, "_models.DeletionRecoveryLevel"]] = rest_field(
        name="recoveryLevel", visibility=["read"]
    )
    """Reflects the deletion recovery level currently in effect for secrets in the current vault. If
     it contains 'Purgeable', the secret can be permanently deleted by a privileged user; otherwise,
     only the system can purge the secret, at the end of the retention interval. Known values are:
     \"Purgeable\", \"Recoverable+Purgeable\", \"Recoverable\",
     \"Recoverable+ProtectedSubscription\", \"CustomizedRecoverable+Purgeable\",
     \"CustomizedRecoverable\", and \"CustomizedRecoverable+ProtectedSubscription\"."""

    @overload
    def __init__(
        self,
        *,
        enabled: Optional[bool] = None,
        not_before: Optional[datetime.datetime] = None,
        expires: Optional[datetime.datetime] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class SecretBundle(_Model):
    """A secret consisting of a value, id and its attributes.

    :ivar value: The secret value.
    :vartype value: str
    :ivar id: The secret id.
    :vartype id: str
    :ivar content_type: The content type of the secret.
    :vartype content_type: str
    :ivar attributes: The secret management attributes.
    :vartype attributes: ~azure.keyvault.secrets._generated.models.SecretAttributes
    :ivar tags: Application specific metadata in the form of key-value pairs.
    :vartype tags: dict[str, str]
    :ivar kid: If this is a secret backing a KV certificate, then this field specifies the
     corresponding key backing the KV certificate.
    :vartype kid: str
    :ivar managed: True if the secret's lifetime is managed by key vault. If this is a secret
     backing a certificate, then managed will be true.
    :vartype managed: bool
    """

    value: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The secret value."""
    id: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The secret id."""
    content_type: Optional[str] = rest_field(
        name="contentType", visibility=["read", "create", "update", "delete", "query"]
    )
    """The content type of the secret."""
    attributes: Optional["_models.SecretAttributes"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The secret management attributes."""
    tags: Optional[Dict[str, str]] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Application specific metadata in the form of key-value pairs."""
    kid: Optional[str] = rest_field(visibility=["read"])
    """If this is a secret backing a KV certificate, then this field specifies the corresponding key
     backing the KV certificate."""
    managed: Optional[bool] = rest_field(visibility=["read"])
    """True if the secret's lifetime is managed by key vault. If this is a secret backing a
     certificate, then managed will be true."""

    @overload
    def __init__(
        self,
        *,
        value: Optional[str] = None,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        content_type: Optional[str] = None,
        attributes: Optional["_models.SecretAttributes"] = None,
        tags: Optional[Dict[str, str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class SecretItem(_Model):
    """The secret item containing secret metadata.

    :ivar id: Secret identifier.
    :vartype id: str
    :ivar attributes: The secret management attributes.
    :vartype attributes: ~azure.keyvault.secrets._generated.models.SecretAttributes
    :ivar tags: Application specific metadata in the form of key-value pairs.
    :vartype tags: dict[str, str]
    :ivar content_type: Type of the secret value such as a password.
    :vartype content_type: str
    :ivar managed: True if the secret's lifetime is managed by key vault. If this is a key backing
     a certificate, then managed will be true.
    :vartype managed: bool
    """

    id: Optional[str] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Secret identifier."""
    attributes: Optional["_models.SecretAttributes"] = rest_field(
        visibility=["read", "create", "update", "delete", "query"]
    )
    """The secret management attributes."""
    tags: Optional[Dict[str, str]] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Application specific metadata in the form of key-value pairs."""
    content_type: Optional[str] = rest_field(
        name="contentType", visibility=["read", "create", "update", "delete", "query"]
    )
    """Type of the secret value such as a password."""
    managed: Optional[bool] = rest_field(visibility=["read"])
    """True if the secret's lifetime is managed by key vault. If this is a key backing a certificate,
     then managed will be true."""

    @overload
    def __init__(
        self,
        *,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
        attributes: Optional["_models.SecretAttributes"] = None,
        tags: Optional[Dict[str, str]] = None,
        content_type: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class SecretRestoreParameters(_Model):
    """The secret restore parameters.

    :ivar secret_bundle_backup: The backup blob associated with a secret bundle. Required.
    :vartype secret_bundle_backup: bytes
    """

    secret_bundle_backup: bytes = rest_field(
        name="value", visibility=["read", "create", "update", "delete", "query"], format="base64url"
    )
    """The backup blob associated with a secret bundle. Required."""

    @overload
    def __init__(
        self,
        *,
        secret_bundle_backup: bytes,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class SecretSetParameters(_Model):
    """The secret set parameters.

    :ivar value: The value of the secret. Required.
    :vartype value: str
    :ivar tags: Application specific metadata in the form of key-value pairs.
    :vartype tags: dict[str, str]
    :ivar content_type: Type of the secret value such as a password.
    :vartype content_type: str
    :ivar secret_attributes: The secret management attributes.
    :vartype secret_attributes: ~azure.keyvault.secrets._generated.models.SecretAttributes
    """

    value: str = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """The value of the secret. Required."""
    tags: Optional[Dict[str, str]] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Application specific metadata in the form of key-value pairs."""
    content_type: Optional[str] = rest_field(
        name="contentType", visibility=["read", "create", "update", "delete", "query"]
    )
    """Type of the secret value such as a password."""
    secret_attributes: Optional["_models.SecretAttributes"] = rest_field(
        name="attributes", visibility=["read", "create", "update", "delete", "query"]
    )
    """The secret management attributes."""

    @overload
    def __init__(
        self,
        *,
        value: str,
        tags: Optional[Dict[str, str]] = None,
        content_type: Optional[str] = None,
        secret_attributes: Optional["_models.SecretAttributes"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class SecretUpdateParameters(_Model):
    """The secret update parameters.

    :ivar content_type: Type of the secret value such as a password.
    :vartype content_type: str
    :ivar secret_attributes: The secret management attributes.
    :vartype secret_attributes: ~azure.keyvault.secrets._generated.models.SecretAttributes
    :ivar tags: Application specific metadata in the form of key-value pairs.
    :vartype tags: dict[str, str]
    """

    content_type: Optional[str] = rest_field(
        name="contentType", visibility=["read", "create", "update", "delete", "query"]
    )
    """Type of the secret value such as a password."""
    secret_attributes: Optional["_models.SecretAttributes"] = rest_field(
        name="attributes", visibility=["read", "create", "update", "delete", "query"]
    )
    """The secret management attributes."""
    tags: Optional[Dict[str, str]] = rest_field(visibility=["read", "create", "update", "delete", "query"])
    """Application specific metadata in the form of key-value pairs."""

    @overload
    def __init__(
        self,
        *,
        content_type: Optional[str] = None,
        secret_attributes: Optional["_models.SecretAttributes"] = None,
        tags: Optional[Dict[str, str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
