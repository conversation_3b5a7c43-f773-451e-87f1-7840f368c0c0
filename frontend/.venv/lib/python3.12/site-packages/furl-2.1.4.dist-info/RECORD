furl-2.1.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
furl-2.1.4.dist-info/LICENSE.md,sha256=D1-lh4_jTOTZNMAgexlMjrI10sqimnm_bVdvMJEpgSA,1254
furl-2.1.4.dist-info/METADATA,sha256=mm8mN9X2r_-_9r2JdSBJDkq0bR7yQzgA4POwMMZFxd4,25268
furl-2.1.4.dist-info/RECORD,,
furl-2.1.4.dist-info/WHEEL,sha256=z9j0xAa_JmUKMpmz72K0ZGALSM_n-wQVmGbleXx2VHg,110
furl-2.1.4.dist-info/top_level.txt,sha256=GNSBGmkATNKvpqi-f2RCAW92oY-S8Nn9GnXuxddjYVQ,5
furl/__init__.py,sha256=BUnuqowF_L_Fjp3rIM_3mSmTF3tl1D8TiyNv3nhhAaI,365
furl/__pycache__/__init__.cpython-312.pyc,,
furl/__pycache__/__version__.cpython-312.pyc,,
furl/__pycache__/common.cpython-312.pyc,,
furl/__pycache__/compat.cpython-312.pyc,,
furl/__pycache__/furl.cpython-312.pyc,,
furl/__pycache__/omdict1D.cpython-312.pyc,,
furl/__version__.py,sha256=JVuDo2AVGCFTthKJ2zr4dCMthQ1xo5r06u6oPGHX8NA,440
furl/common.py,sha256=8gK0ASKFVM2y9CS438DyaBA9mJWMYyKfC2UXOQx9IME,432
furl/compat.py,sha256=uvsiGjaK0wdS62fKXv2th8VC3PaILuxQVpPg6TzAero,768
furl/furl.py,sha256=nQFiLtWAMtDKJhi3I74YZecD5G6zhYqDYMyNieN_scg,63808
furl/omdict1D.py,sha256=K8ikleZnscsfWo5ephlURLD6lP3yqJwRdqh6MSQk0cY,3652
