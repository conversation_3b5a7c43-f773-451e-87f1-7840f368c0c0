pyarrow-18.1.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pyarrow-18.1.0.dist-info/LICENSE.txt,sha256=Ip2-KeThNE6VFy9vOkJ37A2lx4UMsDiXxH86JLevzgg,110423
pyarrow-18.1.0.dist-info/METADATA,sha256=1khsr21oevo12MWgCroHP3HsH8tF1YBunmiHf0S3RLE,3292
pyarrow-18.1.0.dist-info/NOTICE.txt,sha256=ti6iQmQtOhjx4psMH-CCQVppQ_4VjuIrSM_zdi81QAk,3032
pyarrow-18.1.0.dist-info/RECORD,,
pyarrow-18.1.0.dist-info/WHEEL,sha256=GAEhkxm77H2WVEyE70oaJna7cr1z-ZyZC29APwfwcpg,113
pyarrow-18.1.0.dist-info/top_level.txt,sha256=Zuk_c1WeinXdMz20fXlEtGC67zfKOWuwU8adpEEU_nI,18
pyarrow/__init__.pxd,sha256=Wnar1phFqM_ZHnZmtbuqm6wJHsXlBoYKhV7Qmo2jUHA,2195
pyarrow/__init__.py,sha256=KdUkzmN0NJf1AKTskHDoH3OwZoc0Ih2PRu3BqtdjHbY,18119
pyarrow/__pycache__/__init__.cpython-312.pyc,,
pyarrow/__pycache__/_compute_docstrings.cpython-312.pyc,,
pyarrow/__pycache__/_generated_version.cpython-312.pyc,,
pyarrow/__pycache__/acero.cpython-312.pyc,,
pyarrow/__pycache__/benchmark.cpython-312.pyc,,
pyarrow/__pycache__/cffi.cpython-312.pyc,,
pyarrow/__pycache__/compute.cpython-312.pyc,,
pyarrow/__pycache__/conftest.cpython-312.pyc,,
pyarrow/__pycache__/csv.cpython-312.pyc,,
pyarrow/__pycache__/cuda.cpython-312.pyc,,
pyarrow/__pycache__/dataset.cpython-312.pyc,,
pyarrow/__pycache__/feather.cpython-312.pyc,,
pyarrow/__pycache__/flight.cpython-312.pyc,,
pyarrow/__pycache__/fs.cpython-312.pyc,,
pyarrow/__pycache__/ipc.cpython-312.pyc,,
pyarrow/__pycache__/json.cpython-312.pyc,,
pyarrow/__pycache__/jvm.cpython-312.pyc,,
pyarrow/__pycache__/orc.cpython-312.pyc,,
pyarrow/__pycache__/pandas_compat.cpython-312.pyc,,
pyarrow/__pycache__/substrait.cpython-312.pyc,,
pyarrow/__pycache__/types.cpython-312.pyc,,
pyarrow/__pycache__/util.cpython-312.pyc,,
pyarrow/_acero.cpython-312-x86_64-linux-gnu.so,sha256=2LiJPwCpXcwgl03cP88sEfUCxLcFUvaiel4RO-EVt4Y,327160
pyarrow/_acero.pxd,sha256=5ish_GgGWvit4ebhzoZil7b-m0r2RuG5JwYoxsH34FI,1440
pyarrow/_acero.pyx,sha256=56orFsG2ksoP4C0DPIa-ruQxQRCC489lYlHkGJIh1zY,21301
pyarrow/_azurefs.cpython-312-x86_64-linux-gnu.so,sha256=HDzOW5lGRh-EiXhuHe7sXbPhIKrVfPeJio9QzvrhG0U,102240
pyarrow/_azurefs.pyx,sha256=ezGU_z3kIw2r14YAB4yogaxMssJXikcxajz4dZSez1k,5909
pyarrow/_compute.cpython-312-x86_64-linux-gnu.so,sha256=o3oAfiZSw1gv-XRZvMDq1ms_SRJiUV0eHJHYuoq1KUo,1369056
pyarrow/_compute.pxd,sha256=nmjgwV2KCGFfxZj5ruDwM4oH1ITqF0rDQS0yDvcaXBA,1949
pyarrow/_compute.pyx,sha256=dmGXzWOdzMfrj-nWV8RWBRypswmtKZSEwRZ64KZsuuY,107233
pyarrow/_compute_docstrings.py,sha256=7Vg8jt1aCsWrpTxsdqR7gY6M0faxXNX31c1RZdq9CFw,1707
pyarrow/_csv.cpython-312-x86_64-linux-gnu.so,sha256=8SMBPeE50lIEAYJDmLUNX8ujOMI-6BOhHdaEYoUWuOg,371640
pyarrow/_csv.pxd,sha256=1Zk3Zpvvhy-Tb7c79Aqd4e7bBM21kc1JxWJkl02Y4DE,1638
pyarrow/_csv.pyx,sha256=aqWLHgfZ-nrqKHfVVVXeGbtY2ZChE6Oi9qIa5jP4m1M,54705
pyarrow/_cuda.pxd,sha256=VzhM6j9dpNgrABlvFJKoMpRC0As55im-M3tgPTOuwEk,1922
pyarrow/_cuda.pyx,sha256=YSlswn4Tj1H24SL_iPIGqT3v3JmofE7NwCunuOLMNwY,35136
pyarrow/_dataset.cpython-312-x86_64-linux-gnu.so,sha256=uaj2xuvEpa-7xV-lEl5r6QSzVYxOhxtaEsRCWM2diAI,1075160
pyarrow/_dataset.pxd,sha256=Ag9rUhoBySU6ba3wFLeuZyWMJnz9VkAf9TQEzWG4hUU,4944
pyarrow/_dataset.pyx,sha256=WGqJ4LCtbpxkU4bdIjLR0MAxE1WgmrsCvSQlr1M-emc,157400
pyarrow/_dataset_orc.cpython-312-x86_64-linux-gnu.so,sha256=tS-ogQ1tNWosGtWtyrXDf7DTLCapEUrw5byK6msglZg,80032
pyarrow/_dataset_orc.pyx,sha256=JSFoRI0pfHtL2jeIuPg5TJHodcfuCNYmj_iEZ4xY87w,1499
pyarrow/_dataset_parquet.cpython-312-x86_64-linux-gnu.so,sha256=6uYTN8viaNDGe3lEjjpsP0PKPBwU5QvCf8xMc6EJi1M,368808
pyarrow/_dataset_parquet.pxd,sha256=y-3iKehyB_eB_oeqjtt4aQRbUpVGVN1oUMFGIY13brE,1572
pyarrow/_dataset_parquet.pyx,sha256=x_VvTpuF9vxfAAUGkLZrr0N_TCxYWlqMahlkg0201gQ,38916
pyarrow/_dataset_parquet_encryption.cpython-312-x86_64-linux-gnu.so,sha256=v_cl0aj1XlwksEMYGRWcdTeQiZTpcnhNpB0i6OqJbTY,122296
pyarrow/_dataset_parquet_encryption.pyx,sha256=p7LDNUsp3jMVcWDcbOFp8a3CYJjASVPI_tfATpY-ePg,7229
pyarrow/_dlpack.pxi,sha256=clw0FkGoyZMEtUU8zPpO_DMtl2X-27kb2UtyhQuIc1s,1832
pyarrow/_feather.cpython-312-x86_64-linux-gnu.so,sha256=n1zysBq49-ogEBacOr4XT_5_ynUqimE0XhGoZn60_W8,115552
pyarrow/_feather.pyx,sha256=DWQI4U0uAWE1ZYUwPreBPJg1TGLEGmF3wPEIRL-PhPw,3773
pyarrow/_flight.cpython-312-x86_64-linux-gnu.so,sha256=Ez4yw8mceuOPVlLiC8-z8C0qziM22ATf4_5LsIfxxkU,1297440
pyarrow/_flight.pyx,sha256=siQR9YhOPLToP5dnHtkm-aCjgPfBLYq8d777RHY_MsY,110592
pyarrow/_fs.cpython-312-x86_64-linux-gnu.so,sha256=IWjC1zY2OxEabiVoaxabo4BTKbpiSxwaff25OhCUMmo,499960
pyarrow/_fs.pxd,sha256=SmHS31eyYU7VUZlVuP613HKgpd7bENnQGApvX_g2Lfw,2439
pyarrow/_fs.pyx,sha256=ZyfvmWary8XTHaCoeSYtjI-b0SK0lCsznerI8cGh4K8,52479
pyarrow/_gcsfs.cpython-312-x86_64-linux-gnu.so,sha256=WGwFHKoODrG0ThHKLuoS44gHLfOjp_GbqD9P48vZVIw,127128
pyarrow/_gcsfs.pyx,sha256=fa1QmTQII9sFKtjtfeZPQfTEntAh3IGyJJ1w116OCA4,9121
pyarrow/_generated_version.py,sha256=Soy0A65IyGohu8QOyRrzPypaVt4qqH1YV1839Jm0jc4,413
pyarrow/_hdfs.cpython-312-x86_64-linux-gnu.so,sha256=rvlU5q0WnFeQVzwPSUNv1zP2n24PMSTmo-UQ7tluYNs,127872
pyarrow/_hdfs.pyx,sha256=HA0KkZa6aVRmwg3ku3U7lZo_8COn1cLwylfc6nEJUlg,5885
pyarrow/_json.cpython-312-x86_64-linux-gnu.so,sha256=t6_6ztsraOrkZI7HRZMRwRr62aRA969saIYBh23si8w,117816
pyarrow/_json.pxd,sha256=tECTP14M12-b_ja5QI3snQbd0uWPWmmC9FwkWq23Vg0,1206
pyarrow/_json.pyx,sha256=RmaWaSTG61u2Qcmc_fLsTns_awLJDls3_SdlaCAg53Y,9860
pyarrow/_orc.cpython-312-x86_64-linux-gnu.so,sha256=LJcWFpgwHJkTvuIR1DTNkPzUexLlA6TdKanG7_Qo1WA,216104
pyarrow/_orc.pxd,sha256=6hL0cq1RufqQD-B_bV3ne1rhu2g-h4rDOFNQsSb6qps,5689
pyarrow/_orc.pyx,sha256=Pn7r4dzagWaqMf8rymbXBIWisxonBaStZgXCi7pfrZI,15556
pyarrow/_parquet.cpython-312-x86_64-linux-gnu.so,sha256=QF3Y-vwC03qhtRnM4GOh13gNgNWIXSFh1VxMcxnlr7U,617992
pyarrow/_parquet.pxd,sha256=bLdSgSZg1bn-ZsQbAKOWH44esooFHQOiO9wqmrQuvn4,26805
pyarrow/_parquet.pyx,sha256=xpIBXt3xv9S1H9G6CqSpwl_ZokhJpy09yShxuntqMMg,74388
pyarrow/_parquet_encryption.cpython-312-x86_64-linux-gnu.so,sha256=mOiZW5uW2IgJ0CHXe_2qqqSDBKuTU4y3xNsa5tKZ_J8,280608
pyarrow/_parquet_encryption.pxd,sha256=1vQnkyS1rLrSNMlmuW62PxkOmCsYpzC60L9mqD0_LYc,2586
pyarrow/_parquet_encryption.pyx,sha256=CaTiq5EjTVGYQnxDEmpYcItSBiEencV-pNEu-lBAiOk,18625
pyarrow/_pyarrow_cpp_tests.cpython-312-x86_64-linux-gnu.so,sha256=fgsy0AXGReo39PxrMfREhjdy-cBjTblUGUCxmykeBt4,89488
pyarrow/_pyarrow_cpp_tests.pxd,sha256=nPyRmNtFbOUvSXCwegAApQFfh8UI_K9Hq5dN4oPAxdo,1199
pyarrow/_pyarrow_cpp_tests.pyx,sha256=gLeMzB9RWodZgXEpipX65_0aqWu12SjMld0JZmZVRP0,1753
pyarrow/_s3fs.cpython-312-x86_64-linux-gnu.so,sha256=rSQshWw4go8j5h1vRC-UOtUdXyQtODgXunusm62YmVY,224896
pyarrow/_s3fs.pyx,sha256=VFuZkBV8rt44JrYtAwbPxGI1YlZJ9gfl1U91JQgJEMU,19706
pyarrow/_substrait.cpython-312-x86_64-linux-gnu.so,sha256=NIizsiu305suPCDgPEuWjZIjces3muDrofFJ-FIXSrw,192776
pyarrow/_substrait.pyx,sha256=CA2kxzxJUVPL7lMn8_XSAa9jt1Alq4IbhcI3sHGvsxw,11630
pyarrow/acero.py,sha256=_P7DcFTmhgW4-EAyM67luFMWcp4t1iUX1pKBIkVe7cM,15108
pyarrow/array.pxi,sha256=jP6v7Y3YUVggdyKmfNAa7hGNd2T-1xONCTxK-2a-IOI,151046
pyarrow/benchmark.pxi,sha256=DYXdu-jMSH7XcTohbc8x8NiKRLtpX9IULfY20ohkffA,869
pyarrow/benchmark.py,sha256=k9Z3yQyoojpYz4lTA6DkCfqT6fPG3N2fJtsHKjpbYFo,856
pyarrow/builder.pxi,sha256=9QE4KAiA4JpA7-2JLgX3xo32jRtuWZ3YqC-T9GzUVDc,4634
pyarrow/cffi.py,sha256=hEcrPH9KeG6NES3ZCpSbOVYhOgDOuBB_2LgMMucgw-8,2396
pyarrow/compat.pxi,sha256=Sq5c3CKq0uj5aDyOoHHkPEO_VsSpZ90JRaL2rAKHk5I,1920
pyarrow/compute.py,sha256=MyrZk7PTX-8pYlUu5PLbuXjDMTRpyCcgdFWi2BPVK0I,23181
pyarrow/config.pxi,sha256=E6QOFjdlw3H1a5BOAevYNJJEmmm6FblfaaeyspnWBWw,3092
pyarrow/conftest.py,sha256=afosSyVsVRsJdDXRXOFQEyj4qVO39OtZYVb_wbvvadU,9811
pyarrow/csv.py,sha256=S6tm31Bra9HPf9IsYwBLltZBLMvNzypWfeCLySsjmds,974
pyarrow/cuda.py,sha256=j--8HcBAm5Ib-kbhK4d2M6SVQmDWkr7Mt5fnwU2LzdQ,1087
pyarrow/dataset.py,sha256=4ibGh9x36jEYI7VMxTdZc-XDg8VfNx6RPbA2L-bLJbA,40232
pyarrow/device.pxi,sha256=CtVBXp68zNXrrPwehh56igfLsMSlYRo5rWFcKkEP_gY,5569
pyarrow/error.pxi,sha256=Wj7-NGUfdvlEwAwd8Ta_JqRC8IUOUpm_PmpvizCFvfY,8909
pyarrow/feather.py,sha256=9rWL-TYK_qc0FW3vIyYyd6Xt86ApJWLqo-2cK3F5vGQ,9959
pyarrow/flight.py,sha256=HLB04A0SZ35MZJumPIuBu5I2dpetjEc-CGMEdjQeQRQ,2177
pyarrow/fs.py,sha256=M-cSbS2bBR4MwbJqpz9Q7VxHY8fa89StEw2J0XMMF7E,14899
pyarrow/gandiva.pyx,sha256=bF23rkq6e45i-CePDZeTy9iFwoeg8ElrNjz9VK97QRs,24503
pyarrow/include/arrow/acero/accumulation_queue.h,sha256=_HoTuKEkZodmrwXF9CeWGsmpT7jIM0FrrYZSPMTMMr8,5856
pyarrow/include/arrow/acero/aggregate_node.h,sha256=9HdFxR6tzSfx_UaUHZtS1I2FCbm3PvfF8FdekVpBO34,2155
pyarrow/include/arrow/acero/api.h,sha256=fRuKEHbKDYWRCwSHLc7vSD-6mQavyOsztluCR7evFCk,1151
pyarrow/include/arrow/acero/asof_join_node.h,sha256=Ko6r1wDjxg01FE9-xKkttx7WzCAzf43GxbpvGHgKZp8,1490
pyarrow/include/arrow/acero/backpressure_handler.h,sha256=CsSWRenrtbZYiNnf-cdYCgMLmu5KUAPUKNKMDWttoD4,2810
pyarrow/include/arrow/acero/benchmark_util.h,sha256=T5bNabF1TDAp28S7V_vt_VIDn6l5Be0zOVCHhcTcFf8,1943
pyarrow/include/arrow/acero/bloom_filter.h,sha256=bFzzAzQrs9ePp2tCPQIuk1Oa9gG_Nyp72M_HM0dhakM,11978
pyarrow/include/arrow/acero/exec_plan.h,sha256=U0KA3tnNvVb75G0XQFLVbGzXCGdddGyRhW3zMa8oWJc,35909
pyarrow/include/arrow/acero/hash_join.h,sha256=Ji0k5z778QtNQ0MwU6xBP6z7ajLk79Va-vgCqrlApso,3003
pyarrow/include/arrow/acero/hash_join_dict.h,sha256=_BKJmK3Z_KdJuYHh4KQCuT_1rXlUohrtEgGLtEJ4fgQ,15360
pyarrow/include/arrow/acero/hash_join_node.h,sha256=FXT-aeXL7nNTuV75f9oXgdGyqMK_72GnqGUm9cmBnko,4378
pyarrow/include/arrow/acero/map_node.h,sha256=Bd1HcW0N5azoIVth2ATeHxgTKd9XmmEkz42YBNw5eK0,2628
pyarrow/include/arrow/acero/options.h,sha256=r-GnLElNJAAdFoJ7k0Q1TOfvGSOdgT9BrWbdMcS_SF0,37262
pyarrow/include/arrow/acero/order_by_impl.h,sha256=dQqplP-AZWPZRKio8LmTjYWlCYz9VmW-usUrtaLpd_w,1691
pyarrow/include/arrow/acero/partition_util.h,sha256=bs_zxok-qng8jsHmVBlfJ7Ts2uBEmovEb27knqQmT-Q,7411
pyarrow/include/arrow/acero/pch.h,sha256=8VXXI10rUHzlQiAthx-yjHMQCpGL3dgAiVaGzTubPPE,1094
pyarrow/include/arrow/acero/query_context.h,sha256=D364aGRS3uWe8lgYqCNRjVvs5sKetLOOXzACdp5GZeg,6212
pyarrow/include/arrow/acero/schema_util.h,sha256=KA_hV2xy2TRccMyksSzQrdH9_rdGo3tQyHOIvrWWYBQ,7961
pyarrow/include/arrow/acero/task_util.h,sha256=6pqILuYfcVwt9HqVhRfXFVJoOC-Q_dtk8mQ5SxjgwbY,3706
pyarrow/include/arrow/acero/test_nodes.h,sha256=xKeLWZZC8iokveVXPjseO1MOvWMcby-0xiMISy0qw8E,2877
pyarrow/include/arrow/acero/time_series_util.h,sha256=W9yzoaTGkB2jtYm8w2CYknSw1EjMbsdTfmEuuL2zMtk,1210
pyarrow/include/arrow/acero/tpch_node.h,sha256=l3zocxHTfGmXTjywJxwoXCIk9tjzURgWdYKSgSk8DAQ,2671
pyarrow/include/arrow/acero/type_fwd.h,sha256=4zLhtLJf_7MSXgrhQIZVGeLxjT7JrEDAn9yW75DTFlc,1103
pyarrow/include/arrow/acero/util.h,sha256=byhMEj5XoAUy-93AjLrx_p9_iUZdYn5uJ_cDkCJQt5Q,6121
pyarrow/include/arrow/acero/visibility.h,sha256=E-4G2O4F2YabXnFNJYnsI2VbVoKBtO7AXqh_SPuJi6k,1616
pyarrow/include/arrow/adapters/orc/adapter.h,sha256=G5SSGGYMSREILC43kqL5fqo94c4tKgukitO15m217tY,11031
pyarrow/include/arrow/adapters/orc/options.h,sha256=FMxda5YSskRrB6h9FvcAuMxl5qdavWrNYHPlanjtk48,3696
pyarrow/include/arrow/adapters/tensorflow/convert.h,sha256=ZGFAodnwTJK0ZoXfgYJdjgi_F4vfEhI9E87zejxVb6E,3465
pyarrow/include/arrow/api.h,sha256=Gs6HiRBYU5N7-a79hjTl9WMSda551XdUKpWthFY2v1s,2491
pyarrow/include/arrow/array.h,sha256=P5oW6hvD2j97bLaSTE4_UHuV6Y38DTwJVww3Eb3xdTQ,1981
pyarrow/include/arrow/array/array_base.h,sha256=14RULo7wEJze9IY2psySGtBlBsnCErnqY4lBO4ckU6g,12123
pyarrow/include/arrow/array/array_binary.h,sha256=JvtB8DoR0_tqfSFS_9nMRrJ39lt1cTm5yXh-DLkhqjU,11247
pyarrow/include/arrow/array/array_decimal.h,sha256=xRfrZ1IFO09EmkHEolCwrJ4lsXjLo5DXdfH5_v2gSyw,3105
pyarrow/include/arrow/array/array_dict.h,sha256=6AMbSnZoMj-nhQhZhG4RNnxy9VVPk2DvZjVblwIUhgY,7611
pyarrow/include/arrow/array/array_nested.h,sha256=xySiF5b1ab97GifKMx6FuYZWb2_6e3YvSMfOORGe3J4,37605
pyarrow/include/arrow/array/array_primitive.h,sha256=anek7WkjubNBTRz8wOHyZ0_UuE3BExj02P-PCs3F5To,7719
pyarrow/include/arrow/array/array_run_end.h,sha256=4zs3tcUrIgDOhSEOywJ1vGY2lsH-5QuEBn87mxnDbi8,5101
pyarrow/include/arrow/array/builder_adaptive.h,sha256=92DpiIZDXSI_yOrMftj7P60zlCLjNmwfGM5ubdbXWM4,6861
pyarrow/include/arrow/array/builder_base.h,sha256=CP9kS8pDFd4XyJQdgIlBp3pTIX9mND1Lvh85re4IC8w,13723
pyarrow/include/arrow/array/builder_binary.h,sha256=01BrSwkFQNAEy4FVYi8Esbd2CaeyxN04GDUoXsQUFhU,32718
pyarrow/include/arrow/array/builder_decimal.h,sha256=DFxyFlpzWRZS9zdBhsjII5fFUOMY9bXHn3EIrIvmOMo,5051
pyarrow/include/arrow/array/builder_dict.h,sha256=FZjvCRIDmVuwmzx_HCcDK6ZjNoZKCEsSV-fGI0K974Y,27899
pyarrow/include/arrow/array/builder_nested.h,sha256=O8r6v9n2l9dUFmRPtm1WuP0zFAewzSeoosLmfACB1kA,31270
pyarrow/include/arrow/array/builder_primitive.h,sha256=OOfGI-zDM7BMWIBv-Tko_8pJDkpw-ttQM76JldlUOvc,20808
pyarrow/include/arrow/array/builder_run_end.h,sha256=SZIdsUKK1qAc9pdonPGf0A_aikZHcxxzicezRGR5hLs,11416
pyarrow/include/arrow/array/builder_time.h,sha256=8M2ifZnDgujSItXKsevyBdtM6Iky3ImyeIdAqZV3fec,2548
pyarrow/include/arrow/array/builder_union.h,sha256=8BF532sAMc7JxWIbSN-yX6Z9fqY9jmmsIa054DPvbWE,10144
pyarrow/include/arrow/array/concatenate.h,sha256=wBy-CBTz9MeRCmcnfXGvkXnvSRApvPOcfCf64A42ys8,2059
pyarrow/include/arrow/array/data.h,sha256=BuYmkq11BUas2FvufTRZkg_aoWVd-rLX1sBQIwB5HuE,25147
pyarrow/include/arrow/array/diff.h,sha256=bYNKy2oLAxtt6VYDWvCfq2bnJTVNjG5KMTsGl-gT_kM,3344
pyarrow/include/arrow/array/statistics.h,sha256=JYPb5hAHmJTQ9cDHcEhhHGRBZurt6CcVbUOlp54UWSU,2498
pyarrow/include/arrow/array/util.h,sha256=qVHvCaVlALz8WJwAjyMwsBm5J2iN89CSgj7NpmmqlkI,3652
pyarrow/include/arrow/array/validate.h,sha256=JdDb3XJg4TmAfpv_zgu2ITfL2H9no10TQit-HPj9Myw,1710
pyarrow/include/arrow/buffer.h,sha256=EfXDyFegRdva4rv4nf0jtErnIrt9_FWoXSHk6OPk_G8,23092
pyarrow/include/arrow/buffer_builder.h,sha256=tXWILwHW0MKpve7NIU2ElElPY0y0ooISa82Dq6UdhVU,17371
pyarrow/include/arrow/builder.h,sha256=mBxMko271lJ7Xbku0hCixj943Yx-d2i4Q5Hm2WfwiGM,1546
pyarrow/include/arrow/c/abi.h,sha256=ZohWkqHoTBeIIGYs2iv2VLL8I4G5lP8MAWgbtpWKLVM,7917
pyarrow/include/arrow/c/bridge.h,sha256=D9W-vKI_Ko6_INcMAdUx15foV08UbBvL48R8RRcL5cM,18132
pyarrow/include/arrow/c/dlpack.h,sha256=_HIa9AKR2mwbhf1aChIpMF_XDpFrPaf58Lt3fVxWRWc,1817
pyarrow/include/arrow/c/dlpack_abi.h,sha256=mjp9WWq8qv6gkGirT4y0o3BL_ZI9VyHQpJ5aEpPFetI,9920
pyarrow/include/arrow/c/helpers.h,sha256=f0Q519PwoliFHpxsHp-QvbP6fpVMN2Ha35Tk-RBK6Ws,6279
pyarrow/include/arrow/chunk_resolver.h,sha256=lR4Drywh_3K32dDm0LYDQLp6AeQx7cswspVjmgzgsns,12519
pyarrow/include/arrow/chunked_array.h,sha256=z6LA9OB3uhtmn7ZZe5wfi3Am3icVQ-L_e8s3KEMuq18,10647
pyarrow/include/arrow/compare.h,sha256=U5craXnXACCUzQ8HmGYyhTehNrOezcVUP1ABAlxI62E,5555
pyarrow/include/arrow/compute/api.h,sha256=IQKXz_6YBBfHKOkuqkXIh9ZTZYyVgq7aEBTIzMkZEiI,2071
pyarrow/include/arrow/compute/api_aggregate.h,sha256=cgXomjDDHoAK_ddzyH1NSqWAewzEYPD7qJBj4x5Rkhk,17173
pyarrow/include/arrow/compute/api_scalar.h,sha256=xtRsJg11WgE5RXV9gZZHfhlEElLEpWUUWnbZXTKw4j8,66540
pyarrow/include/arrow/compute/api_vector.h,sha256=6jxDvg_Zz14_63SfVlWnfUff135kls1aGGK_d9h3bj8,29122
pyarrow/include/arrow/compute/cast.h,sha256=Xw9j03AIAMU_hZiqk9d2ZD4xTmESkfXaDsuZkiTypLs,4245
pyarrow/include/arrow/compute/exec.h,sha256=0ZAA9_tzcQEr364sjJ3SwgTtURTwtCjRLzo_LOdn960,17969
pyarrow/include/arrow/compute/expression.h,sha256=llX_81uUIyJ8vPmP8-2mAippyw4cVNhCGfqHRY37FOM,11184
pyarrow/include/arrow/compute/function.h,sha256=krTXaLowvT1cKhecs70urPQcx74vQCJ4jswtBE4Xs5A,16345
pyarrow/include/arrow/compute/function_options.h,sha256=Q9rjkXPrU9-Xi64_fMLPbBbW_byhjJFsvHppP1CumdA,3088
pyarrow/include/arrow/compute/kernel.h,sha256=ywsxF87w2eI4li8be7Wiua5bXp0NYhMb7LS8IzPFO3U,31406
pyarrow/include/arrow/compute/ordering.h,sha256=8Vw3VzDi1mGgVwKGQZakz9TVj0A40wxcL13EvuqNVjU,4129
pyarrow/include/arrow/compute/registry.h,sha256=x7LHiaNEVvZ0VUssZFsasB52Z1AxRflkdI5tR1hhzqc,4837
pyarrow/include/arrow/compute/row/grouper.h,sha256=m-XUADUbpC2wSYmea8rFMbooh0gJQtdTBoF81ywhhjY,7319
pyarrow/include/arrow/compute/type_fwd.h,sha256=-O63QUbsxWws8TBi55x6u9FweUSSOOfizhE4pTczLd4,1537
pyarrow/include/arrow/compute/util.h,sha256=eF_BX2aftTa3qUJwaZA3QGTajrDv4nf6HKXs6dOmjug,8863
pyarrow/include/arrow/config.h,sha256=8liyKI0CJO0G-Fz5I--QjIAwh0m4hosfyAOwvVVs0sU,3044
pyarrow/include/arrow/csv/api.h,sha256=LbwWhPyIsi_73hvsSr77RNR9uUxrVyXM__hp7QcSom0,907
pyarrow/include/arrow/csv/chunker.h,sha256=nTs8hdy4D3Nz3oZWm2JMuA02noY_0pWRYWq_RptqzHY,1171
pyarrow/include/arrow/csv/column_builder.h,sha256=7oa9YCg2Uc2mB7ExHIyYIvbdt555qLXiU0y4FepkISU,2890
pyarrow/include/arrow/csv/column_decoder.h,sha256=10idcPJE2V_TbvgjzPqmFy1dd_qSGWvu9eDkenTuCz0,2358
pyarrow/include/arrow/csv/converter.h,sha256=cjtnz_hZFxm_dWjAMjr1iqqk1egXI2Yb8Bd0xC8md5E,2789
pyarrow/include/arrow/csv/invalid_row.h,sha256=gTHjEbjkpee6syLGA8hFY7spx1ROMJmtMcwhXv21x5Q,1889
pyarrow/include/arrow/csv/options.h,sha256=_HkjSoiAPW77z5AHVVnTa452y1KfJgnXWXz2NoPPAYw,7980
pyarrow/include/arrow/csv/parser.h,sha256=8PplRh3Qxckk8VPyM70P_f1MBb4WMGnNVpoeJ9kOdHU,8616
pyarrow/include/arrow/csv/reader.h,sha256=416pt3yNQsgn4RhIyRMsmSJmvv1sw3ouQotubXG91gQ,4606
pyarrow/include/arrow/csv/test_common.h,sha256=uEYzw8EROvd1QMBQ98d4MaZ7BqMlw2e0flAyz-du0Z4,1972
pyarrow/include/arrow/csv/type_fwd.h,sha256=ptVbengmY_a7Yz1w0SKmKL16yyw9yEeym0Q0cnRCSV4,984
pyarrow/include/arrow/csv/writer.h,sha256=Y1zErZ5H1r2QzjAta3TXpFrdl2btoardCF8USCAGtGg,3549
pyarrow/include/arrow/dataset/api.h,sha256=p7i-bncJLhmfBkfjJWS7684vD9Lke1m6tb7HQq7Tpn4,1322
pyarrow/include/arrow/dataset/dataset.h,sha256=sDkJg42vSE05FwRmYi9pes3jD9932X3J8cyYZ3SY2jI,19830
pyarrow/include/arrow/dataset/dataset_writer.h,sha256=TQV75b_UigfGjIpBnPk8teOncM5WroKfKV15oicBRRY,4589
pyarrow/include/arrow/dataset/discovery.h,sha256=x7-5NBAyEeQWGlWanJDLZAoWksKiMwM96tlDx_M6n5c,11236
pyarrow/include/arrow/dataset/file_base.h,sha256=2oe5v8Qy6v_UthJavg9rjU_WuQvwXcJengWwc3sWLqk,20203
pyarrow/include/arrow/dataset/file_csv.h,sha256=7PlvQW_2FJ5RRN-VH4-OBw5cZ6nkd0KE0sj1TQvCZeo,5016
pyarrow/include/arrow/dataset/file_ipc.h,sha256=6-btvXhflZsAH90T3wMkwzZkte6T4ixzeCEUn_5uYW8,4083
pyarrow/include/arrow/dataset/file_json.h,sha256=sPjOeMOtbZZbvOivnOdb4MvYKHltpTnY8fONkhB9PZs,3523
pyarrow/include/arrow/dataset/file_orc.h,sha256=P7nAD9nacVngDEjH8ChQRt0AQmDg4Z1wBx360LDOoSg,2452
pyarrow/include/arrow/dataset/file_parquet.h,sha256=bzArl0XrmtTNvWhs6YTkLFxtD8TLbTIJwYmWz3YRm38,16708
pyarrow/include/arrow/dataset/parquet_encryption_config.h,sha256=Upo0k5MijZaMaRZjPp5Xg8TRt1p8Zwh2c2tdimjVe1A,3425
pyarrow/include/arrow/dataset/partition.h,sha256=3wrNekD_-fPO1YW91Za-T4muCfQeAX7SZRIcsCN_czI,16815
pyarrow/include/arrow/dataset/pch.h,sha256=iAE_PbVtKHfhygz7Ox9Z2nlhsIrfageGixGKjlzNRvg,1194
pyarrow/include/arrow/dataset/plan.h,sha256=IjuR9K2sWD85_2HpVVoJ-3YUCq--UPblHU46exX5qRg,1181
pyarrow/include/arrow/dataset/projector.h,sha256=KfZijq09Ht0Z2cJHsrjg-sE3SiZ4TKainflReK-39cg,1135
pyarrow/include/arrow/dataset/scanner.h,sha256=9Ats-ejc6exp3alGUhq0Sw8fww3kJj4ssi8FOKK7SDk,24598
pyarrow/include/arrow/dataset/type_fwd.h,sha256=YOUSRwdNAlXJ7meFLolpAFQ_mSlObs2F81zcOy0DoI4,3170
pyarrow/include/arrow/dataset/visibility.h,sha256=ckmf_sEI0WBo4W7DIgH1QrOq82skOHtoksl9B3yYvzU,1586
pyarrow/include/arrow/datum.h,sha256=XYaZ_URrAtVqHMq-_2YtXk_ETeQ4yZWLVAnsi-k2Mac,11511
pyarrow/include/arrow/device.h,sha256=mLz99tb74VdjxXtKt6RZCYKJQ8TYz93uaCFJ1ZiItMw,15344
pyarrow/include/arrow/device_allocation_type_set.h,sha256=ynoZ-XyFlOAjh01PU-R11mE_EOxuw3xzc94v5OXa0u4,3306
pyarrow/include/arrow/engine/api.h,sha256=ORM0M5KQeurjEG8Eoa5IeV_ZgKBRPlWyicyv3ORWkAY,886
pyarrow/include/arrow/engine/pch.h,sha256=8VXXI10rUHzlQiAthx-yjHMQCpGL3dgAiVaGzTubPPE,1094
pyarrow/include/arrow/engine/substrait/api.h,sha256=W9NB1RAm0ZVxztRXYA-GD7H8XLQNXFoYT7TdGFHoNTE,1079
pyarrow/include/arrow/engine/substrait/extension_set.h,sha256=FE6cceycuQv7CCe_Fl4t6tIMRyfoJfWClUhSvHgcm90,21552
pyarrow/include/arrow/engine/substrait/extension_types.h,sha256=x5ZIuynNh6WFt3wRjW--zUsuC3SeDLk1qRg9_xhswWM,3075
pyarrow/include/arrow/engine/substrait/options.h,sha256=dtvUty_zoDmcFwVflppiDzelYkeOhCO74uRF6izQSzk,5820
pyarrow/include/arrow/engine/substrait/relation.h,sha256=V3VKFlDdE61e1OS8LbJiwvm5w0uq5bzBLhKqmgmKaws,2385
pyarrow/include/arrow/engine/substrait/serde.h,sha256=mjxfuFo4aPhCiwefpKAJMIlknF4UOHSr6gWU__1SwCc,16528
pyarrow/include/arrow/engine/substrait/test_plan_builder.h,sha256=REFa79D1AOIIjp2Iez73iw5gEnzG9Rac9t8WwiGLsuI,3003
pyarrow/include/arrow/engine/substrait/test_util.h,sha256=IHZeYrk50Sx9anJfC25DWP6XesItKEywDWUqvUJcjEQ,1517
pyarrow/include/arrow/engine/substrait/type_fwd.h,sha256=P9YRjAQpSgoIjDC0siYyxoQzcPVo3r9y85qjiMtudBs,1028
pyarrow/include/arrow/engine/substrait/util.h,sha256=_dRiQBaIMWNbsYG7kuXhs3dMk4dI63-pM0uSxYPOvgE,3570
pyarrow/include/arrow/engine/substrait/visibility.h,sha256=GRzH6U-UCPT8d60cywOkFfcanPSgiZKCDP6X2rIpbMs,1740
pyarrow/include/arrow/extension/bool8.h,sha256=VsHTtVyrqk6UKgvifad7LouuieoAZuZs_uVvegdGq4Q,2145
pyarrow/include/arrow/extension/fixed_shape_tensor.h,sha256=VOqvTSnwDIvnhbstYX5nnqWfhtZ7MaD-lSF89BEqlhE,5610
pyarrow/include/arrow/extension/json.h,sha256=gnJSzCVni_oJKxKMoSNBwsuBg1BJzk_goGIE_uTSMJY,2109
pyarrow/include/arrow/extension/opaque.h,sha256=uMVqSScey_13Ho6V86vfkuoByZni9ufh5BGKgX4bTZk,2920
pyarrow/include/arrow/extension/uuid.h,sha256=E_Bnp5KNKSxVuvdhQHjYT-0HKa9mzVPbSAQjuZ9N3Pc,2278
pyarrow/include/arrow/extension_type.h,sha256=5rDE_IuEMAQg05k6wnbo6pu8hOW3-jp9Ab89souwcds,6628
pyarrow/include/arrow/filesystem/api.h,sha256=Xgy2GOZtBVwDjTaXPDyPPlS9Bwt9gjWXm5I_QbyRbFo,1383
pyarrow/include/arrow/filesystem/azurefs.h,sha256=urXoeGp29R42-0ILfkKBhzSa3U2DjjVaFmol2kOsb3g,15223
pyarrow/include/arrow/filesystem/filesystem.h,sha256=H7MEX1259aVrWMsgsWX26tuCEPSJF-iI51J3sKsYec0,29585
pyarrow/include/arrow/filesystem/filesystem_library.h,sha256=axaof-G9GxBjzXhRIt4azB7HB8VJ49MtGYsL7pSO0A0,1725
pyarrow/include/arrow/filesystem/gcsfs.h,sha256=wzVfIkqhUp-aw6NFNhMbvl0bczty3HmdiYG36oPCDS8,10533
pyarrow/include/arrow/filesystem/hdfs.h,sha256=Jn91pjfk6RMx-MuAWsEAKLTyKQ7bDPNA5jMEVzafSgc,4133
pyarrow/include/arrow/filesystem/localfs.h,sha256=eIhPrpABheQz21WE845ULleTk83e4EtJnES4jALW6mM,4972
pyarrow/include/arrow/filesystem/mockfs.h,sha256=kohu7s9s9xtd75sGTE2K_rsHW89swDOtSSSFxBixMcc,4768
pyarrow/include/arrow/filesystem/path_util.h,sha256=hrDVHk4F9M7oGABB4x2wKfQMjSlSAIS0IaLVv2jHrl4,5698
pyarrow/include/arrow/filesystem/s3_test_util.h,sha256=ffeqZmR8G8YyzbpUWws2oSEchYPBt254jwOHWdkcWQo,2767
pyarrow/include/arrow/filesystem/s3fs.h,sha256=0C98nH3MLI-lq0FW3mWufnY8z43GWCl4BVOnhgDsFhw,16217
pyarrow/include/arrow/filesystem/test_util.h,sha256=MFwd6ljnwR8q1smTSpVRLk_15Ch_v1hEQWkRL3lAo-s,11412
pyarrow/include/arrow/filesystem/type_fwd.h,sha256=zztDER55Wbt4rVnkd-ReeDO-YnrpemftFeFtZ7ZGidY,1462
pyarrow/include/arrow/flight/api.h,sha256=YotLTQn-KCl6y5BIg8coEFZ9n7PMtJ02ly7Pc5gmX7U,1257
pyarrow/include/arrow/flight/client.h,sha256=NtFquWOaafBcmdIB4en9ua5xSEJaCBkC1ZHhAU_Gg60,17798
pyarrow/include/arrow/flight/client_auth.h,sha256=a3Dkm_jPOuqzNsDA4eejuMUwCEBMavM8uS7w81ihbRY,2216
pyarrow/include/arrow/flight/client_cookie_middleware.h,sha256=5zkCP2SxMFQuTX8N9NHxOve5J_ef2rFO6-xY4Tfnygk,1204
pyarrow/include/arrow/flight/client_middleware.h,sha256=aAZwCahuiBhP85iMPe7xNWvidBR9KeHGto2YAqJioI4,2948
pyarrow/include/arrow/flight/client_tracing_middleware.h,sha256=d0sTmUOfq5M9FMliIKK-flJkR6-7r69NjU2TpxhfqWo,1217
pyarrow/include/arrow/flight/middleware.h,sha256=JPQd8JnIVcwjTH6yOBck4BWR-WV95fpnAdhHyEYvfKE,2254
pyarrow/include/arrow/flight/otel_logging.h,sha256=riS9sZM2C3mH6VMbESizJ6lGmudqdJhfdCY9_cJJqMA,1139
pyarrow/include/arrow/flight/pch.h,sha256=Dp2nrZ3t_KPjm0cIMyu913BbCorJG5rmbtpfyDN09bo,1192
pyarrow/include/arrow/flight/platform.h,sha256=1ZfzVaollAZosGyH_1JvzEA8iNR0hi9cUGz5eyLT1zc,1209
pyarrow/include/arrow/flight/server.h,sha256=GAcV0-THuBuj-bXfwqYrZ1P2bwZgKQSJLbu8ToltRvU,13185
pyarrow/include/arrow/flight/server_auth.h,sha256=zKQ8lvkMBuMYiIfT1sU0MPXqVPQikaOS3npBgytcaKk,5429
pyarrow/include/arrow/flight/server_middleware.h,sha256=ITKjCNTT2qnX7JeqWdaweC_QpCX_ytW9PFucQYmPkFo,4317
pyarrow/include/arrow/flight/server_tracing_middleware.h,sha256=zR0FFZYGwAAqhzVhPVDjyXfZda9zmLteqauwA5dgR_w,2186
pyarrow/include/arrow/flight/test_auth_handlers.h,sha256=XkvMWucv9GQjlt2ttvYxshym4kUubUdMh-timlQIt1I,3315
pyarrow/include/arrow/flight/test_definitions.h,sha256=esAWPIVJxTQqGpPTxa4Dm_HdAnzK-4DoJAb3zFtQBiM,13022
pyarrow/include/arrow/flight/test_flight_server.h,sha256=SbRhZP0U4ILnbg7lYQvGeXmvPM_B6bai12FTM_HD4RQ,3930
pyarrow/include/arrow/flight/test_util.h,sha256=E0OlDLwcknevKf4LzzqdU3jfxUMV_mcIJxy4U_up77Q,6860
pyarrow/include/arrow/flight/transport.h,sha256=ZDXc-f8o00TFWESwsGU1My7rR9OfM3X7OZjDcGXTwIA,12181
pyarrow/include/arrow/flight/transport_server.h,sha256=iVdXmrb2pemh4o6BxwvB7OZAV4UeoWrbhe4ePZ5Pi4s,5268
pyarrow/include/arrow/flight/type_fwd.h,sha256=tQFAM3QNKPdzB4VqUGdEUFjNPYXVZLApwGnSus2GQx8,1797
pyarrow/include/arrow/flight/types.h,sha256=b_HQAdmPTh8sZsk5KI7diTMlfm5TmnPFgc8sHE9KFWs,46638
pyarrow/include/arrow/flight/types_async.h,sha256=3nIQqwCYO4Ir3Mt2bG7BNntXxuNHYQNNpz-Yl3EaFTQ,2599
pyarrow/include/arrow/flight/visibility.h,sha256=N1k74cwyRvOaYFa_tCjdgUjiSdPBhmy20UuVGu0wTg0,1596
pyarrow/include/arrow/io/api.h,sha256=Pn4jZSTsLW8MAlMyXUokmJdupX54u154GYI5AvD5ByA,996
pyarrow/include/arrow/io/buffered.h,sha256=YFKKAHStUFncnfpwnk0XSZAZLeLX-LAXV1qH9VGaE1k,5845
pyarrow/include/arrow/io/caching.h,sha256=AAjoyKwQ06m2XiglFS6Ch_cdg2p4-wkA7GakGI_eX1E,6708
pyarrow/include/arrow/io/compressed.h,sha256=3JxIOo1q8VhjIErfwVM5ZLVkwwQKXd-FT5517j58etA,3774
pyarrow/include/arrow/io/concurrency.h,sha256=7BSmXQGTJKKMpJVtR4hxpp62KPTIKU1W3DfC17SrlmA,7960
pyarrow/include/arrow/io/file.h,sha256=-ZEklW1Q0sj3pYCQLQ1ebirKd3s2GI3vUEIszFr8mVU,7625
pyarrow/include/arrow/io/hdfs.h,sha256=2s3f49ggAYgSCsX5SoqnomwsXd24_IZhW-VSBJclqTg,8559
pyarrow/include/arrow/io/interfaces.h,sha256=QIBHTJUobEkwcqnKMT_GEKu5ArzpeGmK-8v7z4qGHIQ,13428
pyarrow/include/arrow/io/memory.h,sha256=htc3MmEbEvwc28bLjCtTtt9QcYp-10WKLmX0V9TnwRM,7048
pyarrow/include/arrow/io/mman.h,sha256=qoLBAGFcvpYTy96Ga7FNWDJKT3uhxpFAF3hbXIaDSiY,4111
pyarrow/include/arrow/io/slow.h,sha256=8-ZjQJq49EQJ4esQ6qHHjlKCeZNg4BSND7ire-ZtLYQ,3942
pyarrow/include/arrow/io/stdio.h,sha256=dqMTHoJbmiXcyNa2fN60tSWQsx0GPphZVCLdGiZNt8I,2095
pyarrow/include/arrow/io/test_common.h,sha256=Rj8mwgcUkzksrlBALiAldtr_6JGHJFLh2SztGVkRiSA,2112
pyarrow/include/arrow/io/transform.h,sha256=W9XWonw69VymQAaQptfW7jD-6ry7VCpfPXlkB7aZzOE,1890
pyarrow/include/arrow/io/type_fwd.h,sha256=Pi7EFpFvBXsFN1xKOyZjTSP95xNDs6W5hxb5GucoVVE,2315
pyarrow/include/arrow/ipc/api.h,sha256=olkdu82mTS8hmwD53DBJJL6QQ0YBplhs-s-m4uOInSQ,1007
pyarrow/include/arrow/ipc/dictionary.h,sha256=UTjZPIG8mLZOk9IW2QnR9RZGr1npexZOp103fv-O70E,6104
pyarrow/include/arrow/ipc/feather.h,sha256=uCnxwO7eUH18kJ-lWz9IWwSj6AjfejqqLdoifJ-UBDo,4918
pyarrow/include/arrow/ipc/json_simple.h,sha256=IjFjx6Z7h_WLXt1paVIJboUOTR5GFBhWUhCbm_m9lNk,2455
pyarrow/include/arrow/ipc/message.h,sha256=KtMCbIC2J4-5iyPG5Sijqu_MALxiuKWBYZhGnw0jxOQ,20011
pyarrow/include/arrow/ipc/options.h,sha256=X2BbCaQ03S1uqedgLRbvLyfb1PHZ7WGRBjDLLCbQMGE,6888
pyarrow/include/arrow/ipc/reader.h,sha256=NqdrqqAEItO1ecYUINRO7-qhKlYy-CHSJKGI2hdXlRQ,24106
pyarrow/include/arrow/ipc/test_common.h,sha256=_kWOR_-YKtilcCIWK6I4WYo8fcRt6eBMfxEM4kDtY20,6351
pyarrow/include/arrow/ipc/type_fwd.h,sha256=Ty8ET7nLI4JJeTqDMyP0pEH9QVj9xs7BpJkZrnrpaPY,1440
pyarrow/include/arrow/ipc/util.h,sha256=wTkfC9YFKZlAAjyzlmQVZcW90oOj_JatjDN4qz0IxHg,1414
pyarrow/include/arrow/ipc/writer.h,sha256=hum8E_orkG_X38vgyfyKhGbyvcLJ3AkXEykyBjAXIYg,18870
pyarrow/include/arrow/json/api.h,sha256=XRW1fP43zVqwy1yabaKctNK9MDZqnxkoHDH1fx5B3Y4,879
pyarrow/include/arrow/json/chunked_builder.h,sha256=DDuMwrImMECw6Mhfncn2xMOjkFcKUV1O1597_fSFSAs,2365
pyarrow/include/arrow/json/chunker.h,sha256=dkZOcxsF1Q3ek58P7IoA8f3lQyBQpFvGSFeynNV2Olc,1119
pyarrow/include/arrow/json/converter.h,sha256=3lXsP3BSdpLPIkFAJnYW9vP8BbX3neVYR_W0zFKClQ0,3134
pyarrow/include/arrow/json/object_parser.h,sha256=Y_6Oceya06aUyeo-1k047dm2-JUMJa2_w9iyZ-goIRQ,1627
pyarrow/include/arrow/json/object_writer.h,sha256=UrIrjCkIz7Q5n_FpV5NNPD96gHHdTkvTJaekuGBHwTo,1428
pyarrow/include/arrow/json/options.h,sha256=EypQgDwLZQbrPnAh45nSPfpGGYrxvLgfp1eAG_l0p3Q,2227
pyarrow/include/arrow/json/parser.h,sha256=3oIzO5kUs2Takc7t_d5mH7bp1uIcc1M-qbuHmPoSI34,3383
pyarrow/include/arrow/json/rapidjson_defs.h,sha256=lBJlfuYWIeQQ8awPd3bk4jJc81efr_KzKwG8Klw7t1s,1474
pyarrow/include/arrow/json/reader.h,sha256=KNO9dCyc2RZs7WxUSEW7bpCYBh_h1C3U52YHYxBnP0M,5212
pyarrow/include/arrow/json/test_common.h,sha256=YiiY_jswpp7Nu6IW1Y2lBhqWSFRoNaNEy1jHd5qkYHQ,10874
pyarrow/include/arrow/json/type_fwd.h,sha256=o9aigB5losknJFFei1k25pDVYZgkC2elmRMX1C6aTjo,942
pyarrow/include/arrow/memory_pool.h,sha256=SjPtWz1tx6Lotr2WeOKCCIw9NQc50Zjez3yzgfr7SDw,11064
pyarrow/include/arrow/memory_pool_test.h,sha256=qv7csk6hZiO2ELFF-1yukpppjETDDX0nuBFBbPFHtMU,3350
pyarrow/include/arrow/pch.h,sha256=MaR9bqy2cFZDbjq8Aekq9Gh1vzLTlWZOSHu-GhWP1g8,1286
pyarrow/include/arrow/pretty_print.h,sha256=ZDlroPRr9_ryCk7h_rjA8pL7BNgaJQ9HnRb2PZU63lg,5529
pyarrow/include/arrow/python/api.h,sha256=W76VAxYqOxi9BHJddji1B62CmaWDFuBhqI65YOhUnGQ,1222
pyarrow/include/arrow/python/arrow_to_pandas.h,sha256=jUBEUMKXw70oJdMlgkSf6HitaNweQcc7hxI75_C9WSI,5561
pyarrow/include/arrow/python/async.h,sha256=C0f8YYmgwBGgDau4xEFsdjukiZB4YvpylETHEZryHOo,2352
pyarrow/include/arrow/python/benchmark.h,sha256=f-kzyMOlPKDse2bcLWhyMrDEMZrG_JHAPpDJgGW0bXU,1192
pyarrow/include/arrow/python/common.h,sha256=yjljfJK1f7slZ7DBQ4LTo_pob70zioswJNWazy0p-uM,14412
pyarrow/include/arrow/python/csv.h,sha256=QxU3B-Hv_RsoEcMGS9-1434ugouL2ygC64Lq6FgviNM,1397
pyarrow/include/arrow/python/datetime.h,sha256=Bny_THGi2tyUeHxcOuw01O7hNE8B_gave5ABAZQtwTQ,7931
pyarrow/include/arrow/python/decimal.h,sha256=kDDjLzW07D7d7omWSR4CBF1Ocskp4YSZu4Dtxu-gRUg,4726
pyarrow/include/arrow/python/deserialize.h,sha256=Q4L1qPCra8-Wzl6oLm44cPOUMVuK1FX01LeGzwNUtK4,4260
pyarrow/include/arrow/python/extension_type.h,sha256=0gzb42y_mbw4fsYs3u8cwPFLBRlG-kkHQLgbvGtrY0U,3181
pyarrow/include/arrow/python/filesystem.h,sha256=FG0AcLekqaDf9IQPqKixAfIcY_ZLgIKP5NvvXdtBVUM,5126
pyarrow/include/arrow/python/flight.h,sha256=u5UnulNJqMuXQLlODUWuoyxq-GtL1HuHmVGNzobUVGc,14311
pyarrow/include/arrow/python/gdb.h,sha256=H-qvM-nU8a_3Z5tk8PvppTwQtBMSZhQKQIVgRAsRfFg,972
pyarrow/include/arrow/python/helpers.h,sha256=jVNFEbvJXmCceJti3J3-MnZkNlJoynQNq334tt29bbs,5489
pyarrow/include/arrow/python/inference.h,sha256=FUFvB4Zy7V-tueXdmbDcqTeLK4xj5GZEeRW5yhiJlsU,2038
pyarrow/include/arrow/python/io.h,sha256=4jGnodpSUlnVqAVh9fWId7H4WldlLPkXyroABpdaW6w,3858
pyarrow/include/arrow/python/ipc.h,sha256=SZbw6jCCqLiLNCY3k632GmwHeD_r_xrDS0dhqV49VhY,2259
pyarrow/include/arrow/python/iterators.h,sha256=Ugfm3JvetAH0l-oAjjpZfhrUBqRimVMaw4-xusvqLSg,7327
pyarrow/include/arrow/python/lib.h,sha256=UNSuhntc2NTo9y8txHS8MqB10IQN41UuXjb5dGtstfw,4631
pyarrow/include/arrow/python/lib_api.h,sha256=SCXALS0e94-_uXt9ZlqlUlvU-cclpx7xT8LpxAU1nbM,19487
pyarrow/include/arrow/python/numpy_convert.h,sha256=y13eHwfe1lJKzadoTr2-GyX6xPsE6Z7FN31s7PN-2Rk,4870
pyarrow/include/arrow/python/numpy_init.h,sha256=FniVHP7W2YBlenoMYhQrODvoqqvDMSls2JANGtNPQts,999
pyarrow/include/arrow/python/numpy_interop.h,sha256=rI6ek8JTOYtjo7gEADSDBS6QuAOHa2A0YQPZ2GeypFw,3418
pyarrow/include/arrow/python/numpy_to_arrow.h,sha256=z9KapsuoOSpWILPt9bea7GR4BL6AQ28T6DUO0mSkh3k,2760
pyarrow/include/arrow/python/parquet_encryption.h,sha256=Mc8tZ8gIfkH0AckNiIOt6hesP_MVKeKhcytT24ZOLdQ,4861
pyarrow/include/arrow/python/pch.h,sha256=vkbgStQjq820YeHlXBPdzQ-W9LyzJrTGfMBpnMMqahk,1129
pyarrow/include/arrow/python/platform.h,sha256=XYS5IqiMUejxN2COzu60Zs8b_wAaGTBw4M-zKVqqs5U,1422
pyarrow/include/arrow/python/pyarrow.h,sha256=TK3BtD9n3QKOQ9dX3LXbQc0hu9alWcufV0O93iQW7B0,2761
pyarrow/include/arrow/python/pyarrow_api.h,sha256=7l0G4-_m9yALYoifsY8Z6qh3HHD0PgkpVSgCn_JaGU4,867
pyarrow/include/arrow/python/pyarrow_lib.h,sha256=-70_Ckj3_0ImlzaXSJOE_d3w9pGM66lXiGPyln9c96Y,863
pyarrow/include/arrow/python/python_test.h,sha256=ea32mM20uHySlygi9MtVxr26O-ydTZHCUQIlxaIMjT4,1195
pyarrow/include/arrow/python/python_to_arrow.h,sha256=BoVytf6P7PBYXyznchElKZSFvEsFyimB-tLFdw0AUNo,2521
pyarrow/include/arrow/python/serialize.h,sha256=HVBhIKgc7A4YOmwYfjE2Hqj1Yxl9suCJb940KxrVcrs,4630
pyarrow/include/arrow/python/type_traits.h,sha256=B_NsRT_hZG8D91sTcihJyKF5SrslPcFmj12QfbpHuLI,10093
pyarrow/include/arrow/python/udf.h,sha256=de3R8PhNJO5lT9oCqRxe8e2_SE3jBpHOkwbNqCrlgjQ,3104
pyarrow/include/arrow/python/vendored/pythoncapi_compat.h,sha256=bzMnlHTCfjk5DQRIxwytunYh5aQxU3iSElaaDyNnAY8,40900
pyarrow/include/arrow/python/visibility.h,sha256=hwJw5sGrWJckQkNaAuLe4Tf-VDjQbXknyzNOVgZI3FI,1381
pyarrow/include/arrow/record_batch.h,sha256=qk-6MakursNrRIec5MZeCfjUSYyXPQsyYbB1FJcYb7g,17835
pyarrow/include/arrow/result.h,sha256=1NmZkkVhjVe1CAI7dFXRFdNQefEtk1lxMCF92o41ROE,17739
pyarrow/include/arrow/scalar.h,sha256=syIPKehmg60wmde_ySRUZqMTxJ1rEBrIQ9erRTSZbzg,36595
pyarrow/include/arrow/sparse_tensor.h,sha256=dd6eQmCjfCmmI76hgsC37R-qPJ11IMhafVaxSo2XJFs,25205
pyarrow/include/arrow/status.h,sha256=2D-uFQpe83Yja8Qygm1cXvWAybuiibyxlavOxFuPEjs,16417
pyarrow/include/arrow/stl.h,sha256=yGoKi-YUq6DgxkIW27S5B0_rXd2YiUrdzA1YdvHNCHQ,18164
pyarrow/include/arrow/stl_allocator.h,sha256=TBbvjbuQIH9y88FI2SaqAL7pOIt3wZ1xMKwXqeKNiJE,4956
pyarrow/include/arrow/stl_iterator.h,sha256=RelNQrADHupKWTuFBCCkqVlyuGHXU3yB6gcsDpQpra8,9953
pyarrow/include/arrow/table.h,sha256=UoixXGk5S1ckV35utXjbA-KUBQrSeqvfrhSmk22k760,14647
pyarrow/include/arrow/table_builder.h,sha256=LRcLCL2iUrj6vF4f9AjPswVjqtqlMw7z_8VBAfUJeCo,3763
pyarrow/include/arrow/tensor.h,sha256=mgPkJ5f5ngl0qDkeYf-uk-BtX7Gyr-0DUuX1qB6YadE,9093
pyarrow/include/arrow/tensor/converter.h,sha256=RZq0Try_kiZ085_d_CvhewMsd57InGb2TCeiveaf-Oo,2891
pyarrow/include/arrow/testing/async_test_util.h,sha256=IrHWfPeIyhrgeTGHUPLt92LdsofmFX6khjngWsZv3dY,2262
pyarrow/include/arrow/testing/builder.h,sha256=4x0bWOedaVomWU0m7dF99irOv3flR-_p-IMofTDZtwo,8556
pyarrow/include/arrow/testing/executor_util.h,sha256=38_rF-V_9zF1ttJMspkPiI-34VU1RDjg1ADBS8lUFHk,1885
pyarrow/include/arrow/testing/extension_type.h,sha256=5l_28-SdoO0r6r-nVqkXsfSRFWTLTPgOFEpXzZiqh6U,7430
pyarrow/include/arrow/testing/fixed_width_test_util.h,sha256=g6yB7RkziU7HEhNJnxOhkn2nE5HeXaFX3tbBX3q9_sE,3091
pyarrow/include/arrow/testing/future_util.h,sha256=qIhi417OGMWSMUSDHjkGTYd-ihZbqw8ZSIRwJ01vbKg,6246
pyarrow/include/arrow/testing/generator.h,sha256=h9Kw9GfDnCHDLl7IsEgaLCi8UDu7R6MHL7Au2TWfMVc,12024
pyarrow/include/arrow/testing/gtest_compat.h,sha256=0NqH39my7m1FMpsrQYnxQx4bdEE10SCXZaysN6yjQFA,1311
pyarrow/include/arrow/testing/gtest_util.h,sha256=jnVGbM53nnXO433aUNmZHlMyiQ1ftENITLbtqRF6R08,24496
pyarrow/include/arrow/testing/matchers.h,sha256=3ys7UI6YpFeMvFCgjmF_VWn1w7Hzhqbr2c-_EuJBpnU,16852
pyarrow/include/arrow/testing/pch.h,sha256=wKPN4rZnVcQbmpn02Sx5tSa7-MEhpUR1w-YJ6drtyRM,1164
pyarrow/include/arrow/testing/process.h,sha256=AzPW3Lh2R4sTm-RUUi4Od3aSba9zoLcS_zHBxztv4zI,1372
pyarrow/include/arrow/testing/random.h,sha256=UMxioQORvoZOsodZM6T-ujza5WuYKwAndbvnOImDsqQ,37046
pyarrow/include/arrow/testing/uniform_real.h,sha256=-G_2J9cvevoCtB55vsCsWtJkMUHLIMyOwdT6G8ZW45Y,2970
pyarrow/include/arrow/testing/util.h,sha256=Vr_F5jZQo6kd2-PBq5M0IjODeuaY7cNU7dDovpnPtLQ,5391
pyarrow/include/arrow/testing/visibility.h,sha256=-wjc00QIhygXJa7tknbIL685AQ1wnyCPr-EtVzkzmq0,1606
pyarrow/include/arrow/type.h,sha256=SJVslP638byBgmRg3xk3wptgYrQ_Gvj-_s78Yx2G8bY,96785
pyarrow/include/arrow/type_fwd.h,sha256=2stweTjQZvCwuWYBFI_QJu2369tT6Y1Az4AIien0NVU,23442
pyarrow/include/arrow/type_traits.h,sha256=5XS-cpIzY1DQmNIwzhL7zd4ItxPfOgCwEqWfVG-zU80,54725
pyarrow/include/arrow/util/algorithm.h,sha256=045EVzsC9rThlRVFaCoBmmtWZmFy5y28PR9yapn9sXY,1229
pyarrow/include/arrow/util/align_util.h,sha256=DG2L24KReTiU8nFpXLigbflkKouKWTPUf6osQs6mxiY,10669
pyarrow/include/arrow/util/aligned_storage.h,sha256=ZsAqIA3DV3jIhCnC8mmA4J7FCnnQ-CV-gJj_T_pTmsI,4987
pyarrow/include/arrow/util/async_generator.h,sha256=dMfy3t58k9zQ82LeD002LZT0uEce_QWoDRfwjIapwKk,77704
pyarrow/include/arrow/util/async_generator_fwd.h,sha256=Y7EZ4VXdvqp7DnzG5I6rTt123_8kQhAgYIOhNcLvBdA,1737
pyarrow/include/arrow/util/async_util.h,sha256=1nnAJZ22iK7wSzmvZDo3PMhuWqJIt2qKdlXzTyhoCK4,19759
pyarrow/include/arrow/util/base64.h,sha256=qzcBE98cg8Tx5iPJAvQ4Pdf2yc6R2r-4yGJS1_DEIeY,1095
pyarrow/include/arrow/util/basic_decimal.h,sha256=Y6l2AliCqGzeavJq7pD55WS9wVbznkxbjJzMBLXoll4,33136
pyarrow/include/arrow/util/benchmark_util.h,sha256=SG3gfwE-wGNZAwpL3TvffnSiZGM2cztV5xRBnbqy2Mw,7641
pyarrow/include/arrow/util/binary_view_util.h,sha256=-sFAQX9cnfWmmZJo8stFX5vkJki7T2UloAvDzYO0MN8,4625
pyarrow/include/arrow/util/bit_block_counter.h,sha256=iSIemzizxVokwC0Ze6SjSi-al_nrP2ViXF6JPoIVUWc,20162
pyarrow/include/arrow/util/bit_run_reader.h,sha256=IWDww6Dm8OFsCRlJ0hEpJKiHMK3nUM3pqbd09mZhcIQ,16616
pyarrow/include/arrow/util/bit_util.h,sha256=S0TbReZet8MpPFZk9wjfYzfKpkBquthkkFk2QtxzB7U,12108
pyarrow/include/arrow/util/bitmap.h,sha256=qDoNl-S8QFoZ220HsAtAN-s-Xm5JcnjOXNOGdaIssL0,17462
pyarrow/include/arrow/util/bitmap_builders.h,sha256=zOb7Q-eX9vm9rkgu0Z3ftUDsI1xPthxJ_iC4qDYR1is,1563
pyarrow/include/arrow/util/bitmap_generate.h,sha256=m6ZsNwx1GhsEktQr63NxXHQkX2B7Nti011XYsPg2xfo,3661
pyarrow/include/arrow/util/bitmap_ops.h,sha256=87_SXoqmVPRC6umXFitektDCIeI8yOalYWUonzdWjt8,10750
pyarrow/include/arrow/util/bitmap_reader.h,sha256=pLrMDWhVo-Qb3V1mLASAz_aI6QZxDHRr37EtqxqGd9E,8353
pyarrow/include/arrow/util/bitmap_visit.h,sha256=myn8k66VrvZnL6R6VW6IDPTfO68VxjbJ8Up5IuSjFL4,3470
pyarrow/include/arrow/util/bitmap_writer.h,sha256=a4goXhLlY0qcfvYxbfbGD_HZ8Au1wFcbV1tVF3BPaXs,9383
pyarrow/include/arrow/util/bitset_stack.h,sha256=D49IZZSzZOM2hqh6b-fT0vgRISf1mQnl4oG5nnLBZ4A,2776
pyarrow/include/arrow/util/bpacking.h,sha256=qiiYXgZLWZcYX6sm75_vBQ6qpHtS1AwasL59YQL2Ptk,1175
pyarrow/include/arrow/util/bpacking64_default.h,sha256=q7kf_BW62k45v1qMtnJtLIPk8VtJIALc5nXkYmISy3w,196990
pyarrow/include/arrow/util/bpacking_avx2.h,sha256=ymQJGQc54W3zbrSoktjbAcBnWwbq_SphiXLLI-G6fHg,1009
pyarrow/include/arrow/util/bpacking_avx512.h,sha256=Z_rAQpiKJEH-9QSHUXpbDmZiAgIm7CPCHfPnwlIZDAE,1011
pyarrow/include/arrow/util/bpacking_default.h,sha256=nDi4g5JdyWwXa_J3EqE22bG9R4G7Czd6W75F9spRU5U,103760
pyarrow/include/arrow/util/bpacking_neon.h,sha256=vE-V4E8dpqSjk7dq8kagD07-nhRQKGvcYMhc_dE4nqg,1009
pyarrow/include/arrow/util/byte_size.h,sha256=Pd2c_3a0IeSOUevhPIlXNkDmgoB06g4c9YCsuRwwSKM,3997
pyarrow/include/arrow/util/cancel.h,sha256=oW33c4AXSKLHUc5R_1mZ4ssjmLXU_P0Jk6GDO3IwZUo,3651
pyarrow/include/arrow/util/checked_cast.h,sha256=SR9Qg8NuLSBJw2w1UfgeGvCfT8k7wrbN7BzADQOZfAU,2076
pyarrow/include/arrow/util/compare.h,sha256=OLrSSyllkY4Sv00IK-37A2d68gr4OwnWJsxn1aF9xTU,1982
pyarrow/include/arrow/util/compression.h,sha256=fvlURoWJsgO8Hr6Xs_VNaqiOatmIGn9ktVUkYv7pIu4,8427
pyarrow/include/arrow/util/concurrent_map.h,sha256=wMi9WDHfRuJ_aSFgcJPpsVwGJ9vIJ5agaZ3rVUlwGe4,1775
pyarrow/include/arrow/util/config.h,sha256=sPqy_ZPB3evV_btgboJMWhuLM5KvS7MElff-VL4u5X8,2269
pyarrow/include/arrow/util/converter.h,sha256=PILfos6VlnLK6fOFMfLIUhiKl3o1dJo9T4HJXeR7V5E,14637
pyarrow/include/arrow/util/counting_semaphore.h,sha256=iXHYagqi_-ay73T1uPmv7pG334SY34DUQLSdtD_4_tA,2251
pyarrow/include/arrow/util/cpu_info.h,sha256=MqLdJabBZkzDjiScaQ7if9dmoAGvXT2QavGoGkho3lU,3964
pyarrow/include/arrow/util/crc32.h,sha256=4gN0M-SRnxaGKci2ATPbMWZG2TG3YULXjaTpadV0Udk,1337
pyarrow/include/arrow/util/debug.h,sha256=CPB_oDOuZ_u89e9wM8bGn88mGvClgfa7UDxDph6v9sY,971
pyarrow/include/arrow/util/decimal.h,sha256=ozY_pRsBgftG73qz0KKEPchFQ5HRTb5oxCcTIdWEL7g,20831
pyarrow/include/arrow/util/delimiting.h,sha256=JYe9YcWMeFT_ISuojx_VgVqOYLvZ2TiiR2sNn-WdeBQ,7317
pyarrow/include/arrow/util/dict_util.h,sha256=HipvAVlQ1Q6zNneu9tYOwVUv6NLklBu2IfZ1eoeSpVg,986
pyarrow/include/arrow/util/dispatch.h,sha256=g6R9w8asCTRyDTFoxUipvdOeh6Ye_FvZBGP6Zwg2t3M,3235
pyarrow/include/arrow/util/double_conversion.h,sha256=23QU2TFX4hpBZnoqMDyTKxZoH7mU9qkY2vkF1KL8bW4,1243
pyarrow/include/arrow/util/endian.h,sha256=jp4QoQ9r2vb-oigrlb9AhQW7Lxgxjj7desQjzkEre7g,8176
pyarrow/include/arrow/util/float16.h,sha256=RaJBIWnDdqj7uw2YskxBM0Wlpnrq7QRbMCiTZLr7gJY,7418
pyarrow/include/arrow/util/formatting.h,sha256=782wKN6ZKlHO7cQLC8CKCF9STixvLGjXrp_CwRqXyVs,22554
pyarrow/include/arrow/util/functional.h,sha256=4ljKXSWX3G_lBT2BfLXuG44pzZwVKeaojpLWCniqKyc,5612
pyarrow/include/arrow/util/future.h,sha256=tsSVDEH2dhXKyvIKl6R9BVBolpPdZXoRRf2-YRbtdxg,32296
pyarrow/include/arrow/util/hash_util.h,sha256=CjiNVPUJPxXvVJy7ys79aIb7YB6Bm-5nTJAR4DHsxcs,1918
pyarrow/include/arrow/util/hashing.h,sha256=baLrNZVhO0choWat_Bie2OV821WSTiutqIVfDMjYO6o,32892
pyarrow/include/arrow/util/int_util.h,sha256=zTOAq57M4pUe469WpnW6I5hNtxe3vGRHlZWhngA1DzM,4859
pyarrow/include/arrow/util/int_util_overflow.h,sha256=AtvkG7v3-1gVzW5SrFrdVkYuXFtT76_nxrKtzIbz_9U,4895
pyarrow/include/arrow/util/io_util.h,sha256=U6VTCh0yKUmYPaw2oG-CllJd4J02Gce6b0qTfqFi9E4,13709
pyarrow/include/arrow/util/iterator.h,sha256=nprqdPs6wrrgi6RHIJ2VMQI1YFya-57wBQfOEmHoKUc,18087
pyarrow/include/arrow/util/key_value_metadata.h,sha256=wjU6uQGcSmy-YFqMs6rwLP7E4X-0IFBjPrWZstistzQ,3590
pyarrow/include/arrow/util/launder.h,sha256=C3rNBRh4reuUp8YuRdGQU95WPc8vl4bAY-z5LXgDiuA,1046
pyarrow/include/arrow/util/list_util.h,sha256=_OmtsDqe-mnZ_7tVWxB2yHdgCJhpiME_RP3nXHzKbdI,2028
pyarrow/include/arrow/util/logger.h,sha256=p9i4dNgne36LWpFmNSYBYgTQ4kFSao20dJ40LgRRZKQ,6693
pyarrow/include/arrow/util/logging.h,sha256=eY1sZ1QCcvy5lpJwfOCL2rtRgLjc8V8yDf9usSa9-d4,9694
pyarrow/include/arrow/util/macros.h,sha256=dqnFiDUrFUyqHyNP4xEr54WgaAEXX8gE4ZG7-i3nfZQ,9336
pyarrow/include/arrow/util/map.h,sha256=KbKB3QNc3aWR_0YU1S7aF9fdI0VCABGxEF1VES2oOqU,2476
pyarrow/include/arrow/util/math_constants.h,sha256=2sfWoVc8syHz8X26XgBmejzXStl7hmvKiOh9622oUZA,1112
pyarrow/include/arrow/util/memory.h,sha256=qsxFgvj_wozO5OxIs6fHdcam7aifpozqc1aE81P91Yo,1566
pyarrow/include/arrow/util/mutex.h,sha256=n4bsrHK2Q8zbYsQEyNaFqNu__vvqgwo1AfrLLCxfkpU,2554
pyarrow/include/arrow/util/parallel.h,sha256=iZBn0C7HkQhGNKET5WTXCJ2FftcryCZAyBGwcg7qRvo,3616
pyarrow/include/arrow/util/pcg_random.h,sha256=nbXowfCJFiy4GjVfF9I8VvB6fxkyR5zNB1FKdnFsYTQ,1252
pyarrow/include/arrow/util/prefetch.h,sha256=vaE4FPdscbtO0cPbzl8F1PzB1NDO18ytYlEmZCHDjHs,1251
pyarrow/include/arrow/util/print.h,sha256=X0CfuWzDkq8CNHaEUH3I27Yi4v_zdoOo7sdrTad8Wr0,2444
pyarrow/include/arrow/util/queue.h,sha256=X9vRZQX3YL_a2Lzwe-zcNNHguR7FoGYmD-Q0THqsCBM,1017
pyarrow/include/arrow/util/range.h,sha256=yhe5pJiZIiLUO8tYr408Y9yEsFrFd7FrBMeTL2hAOKY,8526
pyarrow/include/arrow/util/ree_util.h,sha256=waTBOQfwWGHhoAYHTyyhUnM2BSwOqsof_H_akHvUgno,22395
pyarrow/include/arrow/util/regex.h,sha256=Tj92CttOh2HxS0EKQ_9-sxMBAsQrDOUKNP0ngIJFdP8,1742
pyarrow/include/arrow/util/rows_to_batches.h,sha256=PZNoLeMCfJJdeHVvUny0UHc5AtS0hctUCi7zUztJpeE,7120
pyarrow/include/arrow/util/simd.h,sha256=PpKm-aWpZYIYP0NnyGrQceOO9m3_7JbN4uro0IhIT9w,1679
pyarrow/include/arrow/util/small_vector.h,sha256=dDNNMFpNdtIbxLP3L-h_bv3A8raYv4IVuyLEzUVMgck,14421
pyarrow/include/arrow/util/sort.h,sha256=cXZvBN_EcXkN5j0xhX2oNisbChT2QKXP9KzDgjXW2_M,2466
pyarrow/include/arrow/util/spaced.h,sha256=790FFCTdZA-z6qKuEJM5_wG24SqTTVtyj7PKnLBe7_Q,3567
pyarrow/include/arrow/util/span.h,sha256=2zDPUc5ciTQovM-T32EZt4iMpqcsoL7Y46ovKjo-7ro,5551
pyarrow/include/arrow/util/stopwatch.h,sha256=ADGbEEU1x-fvp_NsIdTHH5BW0b9jDB8rTAj1WOgkClc,1401
pyarrow/include/arrow/util/string.h,sha256=hYtg4d3kGQBHdd0vGuKJTlVeueCCgfyD3iq-feMA3p8,5756
pyarrow/include/arrow/util/string_builder.h,sha256=UwOKPz8BQjtl9ecBZ0INoYWMWUkAVQOd_aC8xZZMCgo,2446
pyarrow/include/arrow/util/task_group.h,sha256=fI330NoJT8u84AEUA6pSxWrE7UBKn2LaM4DfPFoalqA,4362
pyarrow/include/arrow/util/tdigest.h,sha256=L6nSj-FVlYLtwKJ94WX9qps9YU6Yg-e3xwP6C0qE7pw,3058
pyarrow/include/arrow/util/test_common.h,sha256=ZniLT8TvAUdCE2T2YrtlDKdwDNPBpT5e9V1EiPHH9LU,2837
pyarrow/include/arrow/util/thread_pool.h,sha256=4ztLwkJHQJQmTmqwy8IGDmAo8X4N-o3qi6f91agzkkQ,24426
pyarrow/include/arrow/util/time.h,sha256=4Xi8JzaYlWFxVaenmCJ7orMgu4cuKELvbtMiszuJHUA,2988
pyarrow/include/arrow/util/tracing.h,sha256=sVfC_Rj2gwkWKVSKT0l0FOO5c2EGsfYwlkZX4d9ncxA,1286
pyarrow/include/arrow/util/trie.h,sha256=WBvryYO2sNdoPc-UB-XmQ3WzSed79qIsSg7YWCrvwNY,7121
pyarrow/include/arrow/util/type_fwd.h,sha256=aC3ZZR2FniFUR3InlZDXH8dknZKvmM0RBocHwFKU_Us,1521
pyarrow/include/arrow/util/type_traits.h,sha256=F0Gdg_3faM0MmZBOXOspRzUwuxnjKbFaVpJiTEaOXGU,1731
pyarrow/include/arrow/util/ubsan.h,sha256=dJNGOe0smDe1akrYLdYcIbAWDJNS6Z7NRgqgDnr2emc,2765
pyarrow/include/arrow/util/union_util.h,sha256=PSssBiw-v-PDen_q75c6OkNO5PwyIPhGbf9PMJj7P2M,1211
pyarrow/include/arrow/util/unreachable.h,sha256=O1TG4ozCYT3_xvDpJouKWrlFADIEpIemQ28y4DqIwu4,1070
pyarrow/include/arrow/util/uri.h,sha256=D24zebazFcrKGt7iGpkcGQ87DuF-2AbjPKVkDlq9Nuk,3886
pyarrow/include/arrow/util/utf8.h,sha256=flGZ786kHo33Xg_zw0zVA9GAT8jYdPUHTVhIPHGjOj8,2031
pyarrow/include/arrow/util/value_parsing.h,sha256=ypbnIIxfFDfDmELinEiS2RYSkeabYDAfuKPW5YsmfRw,29995
pyarrow/include/arrow/util/vector.h,sha256=w1lxZG3CU0gq2ZrByeU8QX2A0JeTtooGdaZONUsVlfs,5697
pyarrow/include/arrow/util/visibility.h,sha256=DFEdl8TCr30r3b7vlpgzJIiA5NsK7eW9UmeL47PgcLk,2835
pyarrow/include/arrow/util/windows_compatibility.h,sha256=Chme9fWRqYRzfIbLw7V_yeiIWd3F4dFeG6ImHHr4Xqw,1255
pyarrow/include/arrow/util/windows_fixup.h,sha256=hjoh6zvB8u8OVUQqLtdcrmohMzoAoLy6XJFLxcfFhK0,1435
pyarrow/include/arrow/vendored/ProducerConsumerQueue.h,sha256=Bz1ks3NDgXXLfT8TMUkE38RpMOSwKRRtwU1e37Y1CUw,6101
pyarrow/include/arrow/vendored/datetime.h,sha256=tsFbz8LKBFzRzTEOAKZyWRbdFLfnCnZRCK9Tyi1PANs,1103
pyarrow/include/arrow/vendored/datetime/date.h,sha256=WNhAT0LIHIl-5t27CT4NjHZxQFl9TANLlGTjlUfHPaE,237812
pyarrow/include/arrow/vendored/datetime/ios.h,sha256=Qnu0iuy2-ein9KkVoSL1t71_W_VFZkdjDVsOnYTnP38,1641
pyarrow/include/arrow/vendored/datetime/tz.h,sha256=m5JJv7LE7Vukp8h50r90sCfbOSAD2bMVIVQUUxNZeDQ,85347
pyarrow/include/arrow/vendored/datetime/tz_private.h,sha256=pDkKXYdzfzQ5uh-jcUhURBLqHo00t0UnlimUdiM53Cs,10706
pyarrow/include/arrow/vendored/datetime/visibility.h,sha256=VCGKzhQOgL1zwGXKl_7lLULfSy0OsPt8FLWHwA4sOtU,1002
pyarrow/include/arrow/vendored/double-conversion/bignum-dtoa.h,sha256=imGhcg0RywMsFNMYTqp6rlXw2HZCIAla8SC_n92gCqE,4358
pyarrow/include/arrow/vendored/double-conversion/bignum.h,sha256=RnQ2CPL8Pt6fVCGh_8VDF11e_GyrrwO0IH0uMnTcsEs,5949
pyarrow/include/arrow/vendored/double-conversion/cached-powers.h,sha256=jjwfR3bue7mNlE5lbTrFR2KlgjRew2OkmjBa7oQO0Qg,3079
pyarrow/include/arrow/vendored/double-conversion/diy-fp.h,sha256=J-RgqH27jspT5Ubth9pTA9NAZH6e7n1OVhxModgi8Sc,5088
pyarrow/include/arrow/vendored/double-conversion/double-conversion.h,sha256=J1Tl5-8aFY0A9SnaA9z5Q90jnMxw55illPIuE-jdD5Q,1804
pyarrow/include/arrow/vendored/double-conversion/double-to-string.h,sha256=C-tKRi0IuLycXgS6CC1oiFkCroOo_-AO0VOjmfe0tlE,23925
pyarrow/include/arrow/vendored/double-conversion/fast-dtoa.h,sha256=ZAho25fqeP3t2RM0XgqfhTBXQIIicACLpdyHHMRX3JU,4122
pyarrow/include/arrow/vendored/double-conversion/fixed-dtoa.h,sha256=HLnpxkHjKldm-FBiDRbADYljJBSYbQGP4Gz-sVbiSJU,2828
pyarrow/include/arrow/vendored/double-conversion/ieee.h,sha256=CVKA9RXSjv4ZygqDHMiF-H2hUh3QHQvp1GZYC3MAhkE,15281
pyarrow/include/arrow/vendored/double-conversion/string-to-double.h,sha256=Ul6b-2R0pjUaAWNM3Ki4kH933LqrW6_XfPz4BSiE2v8,10906
pyarrow/include/arrow/vendored/double-conversion/strtod.h,sha256=6xCRm47vmcghYJug5mhhTVbsZ3m3Y6tQfMehEyVZNx0,3096
pyarrow/include/arrow/vendored/double-conversion/utils.h,sha256=wFRb5cGABiNoUSCnvKmdv_KIMcBtX1PX89tPFfvgbQI,15614
pyarrow/include/arrow/vendored/pcg/pcg_extras.hpp,sha256=FEYzq8NFxPfdJyLs4kVtTBLkaD6iO71INz9EJnaxTdc,19784
pyarrow/include/arrow/vendored/pcg/pcg_random.hpp,sha256=7TaV3nZhcwpf6XxlZ6cod1GaW5gm-iUn67t2fiMPNbA,73501
pyarrow/include/arrow/vendored/pcg/pcg_uint128.hpp,sha256=r8exMtH21S8pjizyZZvP8Q8lAdxkKF22ZEiurSTFtzM,28411
pyarrow/include/arrow/vendored/portable-snippets/debug-trap.h,sha256=9KphJ9gRtDT9DXR9iZ7aS23xa2T8tLmLsFEJMg0pLDQ,3081
pyarrow/include/arrow/vendored/portable-snippets/safe-math.h,sha256=q9yWh34bsFu1vSqLTuI3n_cIU4TlY98Lk1elxKHvZP0,48167
pyarrow/include/arrow/vendored/strptime.h,sha256=q1IZi5CvyUp_PNzbQ4_XLroAV24VEovBEz2TkpwUJ9c,1212
pyarrow/include/arrow/vendored/xxhash.h,sha256=MUwtyzu7xjkx9mBcS65SaDcCK7tgeqQgj-KYEMxcHWc,844
pyarrow/include/arrow/vendored/xxhash/xxhash.h,sha256=videnbIaUDw38kaDzbSQjyNwo-NauW4CxOpz3I45nEM,253096
pyarrow/include/arrow/visit_array_inline.h,sha256=XuQjuME8XZeJp7W86YuCsuoVVgmG1NulXAA0KJkmmB0,2446
pyarrow/include/arrow/visit_data_inline.h,sha256=4MkdFVsrjhMyTDNrScQtOYV_nwzqR2ddSS2yYnbyLt0,12460
pyarrow/include/arrow/visit_scalar_inline.h,sha256=KvNY0j8nE9gs_805LXMV3ATgvxvUqW4UeKpXUxR3rMA,2419
pyarrow/include/arrow/visit_type_inline.h,sha256=45aoF8APn8hm909nLBngls669o2yKCn24WlL5XdDpa4,4397
pyarrow/include/arrow/visitor.h,sha256=NKos98j54uY9tdXzctI_n_nwFRrXNOwanxLDqDZONw4,8690
pyarrow/include/arrow/visitor_generate.h,sha256=n2YKZW-5hY7ICQSwEUBZIYh2eg9ZoTfD54XRd9OlNDo,3324
pyarrow/include/parquet/api/io.h,sha256=Ricq0d2R4QXHiGZCbjxZ_0F_QmKq0IrfTidNu5NoXPI,847
pyarrow/include/parquet/api/reader.h,sha256=vnM5XDPn1TVsDJk4SDgb3ZU2Ta4vdrRzCpDWO90rYHk,1204
pyarrow/include/parquet/api/schema.h,sha256=KsNJ529pEh7bGUa0rLUCcfanI9rW2uSTirgpvKq0hdc,855
pyarrow/include/parquet/api/writer.h,sha256=UJZbY8QGVRMtAmozzjoM9TnI4gssqlNFUKCXBw2IfuI,1007
pyarrow/include/parquet/arrow/reader.h,sha256=l4R351BVOWpYJOv_vyqWmXdJUErm2z_ztvTAv537q0w,15305
pyarrow/include/parquet/arrow/schema.h,sha256=Mi56ul7itNS6NDbMpKOJCufjHVqaSY5_rbsNRNLE560,6204
pyarrow/include/parquet/arrow/test_util.h,sha256=Edb5eSSEwkIExpHZ9Q0LJgPzggWNry4WMQ_i4q9z1uo,20540
pyarrow/include/parquet/arrow/writer.h,sha256=XicHPFeGb92AcsNRDblJ7V4Hmst2qSPGYYT9MTSNNmI,7095
pyarrow/include/parquet/benchmark_util.h,sha256=RhFvoDBVyfd5Sv0fm9JO4JrXWJRGYYmIIrHXi0cSJP0,1756
pyarrow/include/parquet/bloom_filter.h,sha256=TC3OxK0J2v6tHxT_Bbw7mlYtM0603KXgBoHRvmzM9aA,14999
pyarrow/include/parquet/bloom_filter_reader.h,sha256=63kpHYKs5TPrbRamkBLZsDYbD-I9UeVhF-R8d7JHeLg,2892
pyarrow/include/parquet/column_page.h,sha256=_BbPcMfSa52JmteUMdsc7BW6KWoGXn9aQepDgr0veSE,6526
pyarrow/include/parquet/column_reader.h,sha256=3QwlHlpiS5e5jtWmI_kRmD4jrrC8ljfpqF0ilf5JgNI,19299
pyarrow/include/parquet/column_scanner.h,sha256=HecBvh-z0n_1HJsD-GIdcGHQAvDOHKlLzppB9RBsD9s,8863
pyarrow/include/parquet/column_writer.h,sha256=Y9VN1eJtsYmQVhpL9UPiWGrHbgSDbDds19Z1nv_yfOA,12294
pyarrow/include/parquet/encoding.h,sha256=jSYqNVLnsKFu95Mb3uhTP06-7La5_6kNJwn00VqSK_Q,16341
pyarrow/include/parquet/encryption/crypto_factory.h,sha256=RT4iznr6uvSIPbUzh_7s6Cexe8uMbQkzgrjCTGYBC6I,7057
pyarrow/include/parquet/encryption/encryption.h,sha256=bHJ7USckzezXfydqjJstljcjuR15r8U6zh8z3IoINCo,19842
pyarrow/include/parquet/encryption/file_key_material_store.h,sha256=YzAVO3M2H5v5Fz2b_WlmB3GE5wVbMEnFTL3S9XPH6k0,2200
pyarrow/include/parquet/encryption/file_key_unwrapper.h,sha256=pB30St8lGEaEAxNcwnDnlGtATTvc1muMzNOusfgqzT8,4635
pyarrow/include/parquet/encryption/file_key_wrapper.h,sha256=d2W4xICbSRAy7aPe5RKahhPhiJDfvxHY_v_lifq7wqY,3762
pyarrow/include/parquet/encryption/file_system_key_material_store.h,sha256=9H1ey0O3LL4dg9VVeFLNxlZ7Vr263JVaZHKVSu4s8MI,3573
pyarrow/include/parquet/encryption/key_encryption_key.h,sha256=0c3ZrRud2vrCu5z513ocyPYxlsP2kg1fQ8m0Jqr701g,2232
pyarrow/include/parquet/encryption/key_material.h,sha256=kPTSIuRFYOnH4BCPIB33zG9hp5D2Ba-5kZVlq3rFnRI,6221
pyarrow/include/parquet/encryption/key_metadata.h,sha256=Pc0nA9LW3Fc9NLMMxz7osbw8si2jSiOVTES-J-9R0y0,4003
pyarrow/include/parquet/encryption/key_toolkit.h,sha256=HPabI8qFnIMgxZYhHgXCzYV0LU1c5yJ16xjUx21I9b0,4577
pyarrow/include/parquet/encryption/kms_client.h,sha256=D34pVHzkCbWqKnPIBYfs6cONxmuYzyLSS9-C52ZFhz0,3151
pyarrow/include/parquet/encryption/kms_client_factory.h,sha256=VZ97CMgDQxx5oZWFGprjXsaM1hZ0wNudPmFU1_lniAc,1293
pyarrow/include/parquet/encryption/local_wrap_kms_client.h,sha256=XZxkEct0-Tv93VDpda9sDou1kp9qkTKMxr36bpVcI8s,3954
pyarrow/include/parquet/encryption/test_encryption_util.h,sha256=zIGeULeTOCU1N-XYHdvIppth5wnnTYEwf2h-OuTcQZQ,5209
pyarrow/include/parquet/encryption/test_in_memory_kms.h,sha256=jYc5WPsrh_wcaaaWcjf23Gbiye3a_bdg2royUfukWEs,3521
pyarrow/include/parquet/encryption/two_level_cache_with_expiration.h,sha256=cuHbX9gBWWyd0IPXNVjMmHxjPw7omYTns4If4YhBgSM,5075
pyarrow/include/parquet/encryption/type_fwd.h,sha256=dL8snyUwNjhTQE2FQ2dXAUjTboEXhH2JOehQovHfixc,955
pyarrow/include/parquet/exception.h,sha256=yc5A3iMqM9P59hnjuY8VXUIoF_JvbZVPHM6_wPtg4cI,5599
pyarrow/include/parquet/file_reader.h,sha256=OFRKhwAww2N24aZOZcznzral1Or1TGIFGRd1aACARLQ,9664
pyarrow/include/parquet/file_writer.h,sha256=6fK6Mn-MdiQ-J4oo8BTi_eVVVshlffoQiJzFaLRrqco,9343
pyarrow/include/parquet/hasher.h,sha256=HSY1EjPD2xx_dB9HtAg-lXL7hB4j9MDE0cAlR7u0NOc,5227
pyarrow/include/parquet/level_comparison.h,sha256=5z4fUJJPWq9W60l2CsAI7T7E2auGYD7m0fpR5rfLmsw,1306
pyarrow/include/parquet/level_comparison_inc.h,sha256=r20_6Rv5L7UmFGJ68f-JaZ5hLXb87wvZa80hZNQoF-I,2494
pyarrow/include/parquet/level_conversion.h,sha256=OsuqK1xiUnEnOLPKwfm9X-pXTaXRMlDIkj3lwGb2ggI,9432
pyarrow/include/parquet/level_conversion_inc.h,sha256=0r2Gfd_FMidLGFC_a8kgpC9bnUt2-IBbAn9QbQFTrTo,14161
pyarrow/include/parquet/metadata.h,sha256=ORXKWkfSM-64vTrZ-qrsQ5naKx_pk8XbjJEPwtct7wI,20751
pyarrow/include/parquet/page_index.h,sha256=qBKqiq131jCUrtFCfwlBkeb8PL96yOPKg7AqkslnM60,16399
pyarrow/include/parquet/parquet_version.h,sha256=7Xw9wd-fT-B6cgEw2r4N_obNTRsgsqYKZMnK52pe1t4,1164
pyarrow/include/parquet/pch.h,sha256=zIdkjZS4kuFYra3woGMjmvYXCwB4IaXdpm_nR5Nz8hk,1249
pyarrow/include/parquet/platform.h,sha256=VS0zEUC4d37LQmlQLQZ5aHNaiwRf8QrxixXdWf73m5Q,3898
pyarrow/include/parquet/printer.h,sha256=_sJ5IoEj4naSTWxlhbq2Pc6WkNG3wMuxRy8zfKfsAJ8,1540
pyarrow/include/parquet/properties.h,sha256=X5zn-xdztONv4QfK-gcfdh1CBAuq27cVj9jZQgQNqfA,46415
pyarrow/include/parquet/schema.h,sha256=CjZh2i9WN5VeoDbLqy7M1AZtopZ43_C9blWG3OT2IfU,18222
pyarrow/include/parquet/statistics.h,sha256=0sk7koXslu-KuVC6CsTiFVD1Fu_ZWPD_FLhcXALas_g,15176
pyarrow/include/parquet/stream_reader.h,sha256=1WmN0vYCqTz1Lwb_Di4xPWTE-VbCQQuzZralSpWQm3U,8791
pyarrow/include/parquet/stream_writer.h,sha256=nw_v3nhrL682ozZ2KZKVkHnOsjwexbmBXTV2CKcq4YQ,7505
pyarrow/include/parquet/test_util.h,sha256=gkJoOl_N4cG3L56uXVJi1RLiDVBl73yX01Dkx2Plt9g,31180
pyarrow/include/parquet/type_fwd.h,sha256=qx6Dhg1HO0U99jdiUfu3rC7zhmQ-3i7WXsfEhrza3rE,3046
pyarrow/include/parquet/types.h,sha256=IFbKlP0aZzW8Cn4U0QCIGboVb8hOnD6UvSGi6EqpvvE,25482
pyarrow/include/parquet/windows_compatibility.h,sha256=xIEGHW354URgdIP9A4V303TJL8A1IkCEvp08bMKsHTU,897
pyarrow/include/parquet/windows_fixup.h,sha256=DpyWCywx8YIqouun6BJcgMrHFMTCBgowWdJ1mnJnQ2s,1052
pyarrow/include/parquet/xxhasher.h,sha256=QAa7ZE7S3UFtU_Voz3oi3YclIYhbhviJkafLOYgiuWg,2074
pyarrow/includes/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyarrow/includes/common.pxd,sha256=tYI1M3gk_d-uzNUpLcIxhNG5W67ycFSVb36Tv7hyN30,5452
pyarrow/includes/libarrow.pxd,sha256=zOwU6egOTsU45dV0z0sEVLMu6N2iNXXxNB4R3R2QlyA,114590
pyarrow/includes/libarrow_acero.pxd,sha256=c84RdYfIuFWW_36-1RELJsowfQwXhgUxbdC_xKQyFCI,5298
pyarrow/includes/libarrow_cuda.pxd,sha256=0fRcHbCZY_gFdwIXIElnpGvTxeA5xVxZH1-vwZh16SM,4942
pyarrow/includes/libarrow_dataset.pxd,sha256=LVHtNouC3ZWMmyD48JkYGXajf22Wax-FgzAV4URqySs,16993
pyarrow/includes/libarrow_dataset_parquet.pxd,sha256=4me_u82JiInHNRvoazLXUTOO5sxVnyCk-BdfsYQZyWQ,4536
pyarrow/includes/libarrow_feather.pxd,sha256=MTJUDQbfKP8Ir700Fobl7xcbjX7WcrsUV4mxFXlfwn0,2140
pyarrow/includes/libarrow_flight.pxd,sha256=pcVtpB4Rx81RZoG3afIizmyQuTnckrqIPZyjvsIYYKE,24860
pyarrow/includes/libarrow_fs.pxd,sha256=jG1sBGyTkU3X_XZKBMC-n3YsY8Po_2dIQdXyK9vXtHY,14973
pyarrow/includes/libarrow_python.pxd,sha256=Fs9hNJZ-_fdVmqkNu3zGRUXy8Azt6_zniX_p1SKqM64,12387
pyarrow/includes/libarrow_substrait.pxd,sha256=5ZJ0yHhM54I1GfmUaPMy5nRxLFsr-A625qUSmOhnQO8,3196
pyarrow/includes/libgandiva.pxd,sha256=FLBd99IeU67Db9SnHS7oe6FgBZ1aIHuRc0pOiDv7hQc,11538
pyarrow/includes/libparquet_encryption.pxd,sha256=fi3QrLpHN1_IaYRXvVMJdIgp7F_6aaLu1owP0I3BD5g,5898
pyarrow/interchange/__init__.py,sha256=DH0bwbKpdjD1WCW1VinnXEuVLY098uHKkirv7DFc9JM,845
pyarrow/interchange/__pycache__/__init__.cpython-312.pyc,,
pyarrow/interchange/__pycache__/buffer.cpython-312.pyc,,
pyarrow/interchange/__pycache__/column.cpython-312.pyc,,
pyarrow/interchange/__pycache__/dataframe.cpython-312.pyc,,
pyarrow/interchange/__pycache__/from_dataframe.cpython-312.pyc,,
pyarrow/interchange/buffer.py,sha256=NF_GU1uQ6INqHqCwzY6XQQqRxKDh6znEeDHiRqaEIQ0,3359
pyarrow/interchange/column.py,sha256=afU794n3H7yf4gDQDuFLbtyDlgVnLk9iZ6sugb0h8_4,19370
pyarrow/interchange/dataframe.py,sha256=tmSMmBvBAc-ZSUzE8tBNbvQLHuuxLuBkMkK6KYwtS8M,8405
pyarrow/interchange/from_dataframe.py,sha256=JfkP4wuY_9x76H6RDtmsOzs6B6qe-1WS7zxpKeD481s,19709
pyarrow/io.pxi,sha256=LcEqNanwQD7dr0XVHu52dnhlUUH25bhjDGGPO6Wet34,86616
pyarrow/ipc.pxi,sha256=Reakb_rHbBipOr9QPEC0D2jBvQ87ORpVb5kasDdeY_4,41081
pyarrow/ipc.py,sha256=Hb3qCPKRr_wth5u4WrkZHJyAZmIK5SoVSezfBOI97Ww,10107
pyarrow/json.py,sha256=N9Y7_3TSrOEDy2OrmgQ8UKqUPMx1Bm9dYgot-brJ8Xw,858
pyarrow/jvm.py,sha256=tzAsIrMSCIeNAtSC8lZWjQS0rq7kjaQDPlePDmvpqDw,9593
pyarrow/lib.cpython-312-x86_64-linux-gnu.so,sha256=OL5efJ4O7Cxz9VYPuFJK9otuS87VijO2ruTDy4gQ3IE,4843384
pyarrow/lib.h,sha256=UNSuhntc2NTo9y8txHS8MqB10IQN41UuXjb5dGtstfw,4631
pyarrow/lib.pxd,sha256=repMfzMLwO9NOjVyJbVn5R_vBQJJhD507YcD1wvaB8g,17964
pyarrow/lib.pyx,sha256=Pe9ERxojd9KzxzqWJ60B8OJHH8Z1fFYg3bUx8ZDFUtk,6016
pyarrow/lib_api.h,sha256=SCXALS0e94-_uXt9ZlqlUlvU-cclpx7xT8LpxAU1nbM,19487
pyarrow/libarrow.so.1801,sha256=aHuFALmvAEpjlir30YV7hkCaPiZJUUDQbd9kDdgOfvA,63816496
pyarrow/libarrow_acero.so.1801,sha256=ucWlroFOdfuifavQ_NAy9mXg3oVbOLlW_eXa0aInnEk,2086504
pyarrow/libarrow_dataset.so.1801,sha256=ccKk-lUbYp8PDc2sGUVBY_h475Dncc53My5HOlYBhsk,2763968
pyarrow/libarrow_flight.so.1801,sha256=QHYz55mvA7SKYYVZchFzgatdvU7pRNgIPDxW0Ok-KRg,20274112
pyarrow/libarrow_python.so,sha256=F-dVWXCK5YqQ-GhcG9irbkSWIDTdNPgxdaxBg8Lzv4M,2877712
pyarrow/libarrow_python_flight.so,sha256=BmHU04tcWv4OH9OsG9NUVzqHqUgyHoKkOEEI2GHz84A,117984
pyarrow/libarrow_python_parquet_encryption.so,sha256=DQqSIzXtoZhiWng1SUPxhXfX2wczfUebzQf1WwdxA1w,41648
pyarrow/libarrow_substrait.so.1801,sha256=2OsEN65_y962IRTnrsK4NNPZ4A9s10V4U4sWRnnMqjI,5348016
pyarrow/libparquet.so.1801,sha256=EqZcHoVDUsMU59EOqgLc-tB7ngVShVkH0xBJdRn25YY,11042272
pyarrow/memory.pxi,sha256=9AVMENxqaV0Ndf9tYSiakunEpMRRCZNT9d-PnrY8r14,8229
pyarrow/orc.py,sha256=IjjeGAEZl0KhHvwy3YsSGfTWlx7Ilb54P0tFKPvwcfk,12618
pyarrow/pandas-shim.pxi,sha256=d3Z0mki6n3QUTzCOJoEhvgUBcCIcWPsuBli65ZQ_gBg,8178
pyarrow/pandas_compat.py,sha256=sMLsO2ufQeRxpZadNHv4AEG2FGP8EstyOglL38sqAeA,42775
pyarrow/parquet/__init__.py,sha256=4W64CbvwvO60tG58nfNtyCwMVCfuPumtu82p-kiGPaE,822
pyarrow/parquet/__pycache__/__init__.cpython-312.pyc,,
pyarrow/parquet/__pycache__/core.cpython-312.pyc,,
pyarrow/parquet/__pycache__/encryption.cpython-312.pyc,,
pyarrow/parquet/core.py,sha256=SA1zMIm-0cnTPMCjgWe_Bu6bFbjBbTWBpfYauGcHpW8,90440
pyarrow/parquet/encryption.py,sha256=-XW7Qcbl-jQhpZsR610uQ8-z9ZVE_NL045Jdnp1TZ9M,1153
pyarrow/public-api.pxi,sha256=EO0_0FZz0JK9_SfuHBPN0ljwwAU7Gv6jGl1WG_BSGsE,13781
pyarrow/scalar.pxi,sha256=hRcUS1nHQILBp8eL3vfhRXp4yXrvVRPBBoD8ALVdhZ8,35388
pyarrow/src/arrow/python/CMakeLists.txt,sha256=D4Ypror_508aAd_juYkrS9Qu2maeirK4QXzwGEZEj0M,855
pyarrow/src/arrow/python/api.h,sha256=W76VAxYqOxi9BHJddji1B62CmaWDFuBhqI65YOhUnGQ,1222
pyarrow/src/arrow/python/arrow_to_pandas.cc,sha256=z22z8UmNl69KGbmbZLwgZhApNyD9x7xolCSC_3_g6oE,95737
pyarrow/src/arrow/python/arrow_to_pandas.h,sha256=jUBEUMKXw70oJdMlgkSf6HitaNweQcc7hxI75_C9WSI,5561
pyarrow/src/arrow/python/arrow_to_python_internal.h,sha256=nQXPZTL3xa4Sm-a-Gv-8bpFs-qAOZHkqWmA_m-dSLVw,1740
pyarrow/src/arrow/python/async.h,sha256=C0f8YYmgwBGgDau4xEFsdjukiZB4YvpylETHEZryHOo,2352
pyarrow/src/arrow/python/benchmark.cc,sha256=z6qYRx4qMuNXPaC8fuPJlQd92aosMN85u1aD50R1-UU,1293
pyarrow/src/arrow/python/benchmark.h,sha256=f-kzyMOlPKDse2bcLWhyMrDEMZrG_JHAPpDJgGW0bXU,1192
pyarrow/src/arrow/python/common.cc,sha256=_9ozIRo_WTDWovBKqOVyX28d0IttHvwW9MG-PkTzmKc,7591
pyarrow/src/arrow/python/common.h,sha256=yjljfJK1f7slZ7DBQ4LTo_pob70zioswJNWazy0p-uM,14412
pyarrow/src/arrow/python/csv.cc,sha256=ql5AY76AqiFksWsrmzSl551k5s9vS8YcmypM2A9rhw8,1803
pyarrow/src/arrow/python/csv.h,sha256=QxU3B-Hv_RsoEcMGS9-1434ugouL2ygC64Lq6FgviNM,1397
pyarrow/src/arrow/python/datetime.cc,sha256=_VKRKeyFqR7Xzay2wazcveb7mgOv8K37ebMomNY__lQ,23001
pyarrow/src/arrow/python/datetime.h,sha256=Bny_THGi2tyUeHxcOuw01O7hNE8B_gave5ABAZQtwTQ,7931
pyarrow/src/arrow/python/decimal.cc,sha256=66Hy-u-_fcZtm_0v7npDtPNoiX-mkRJTwCj3FpSyIqc,8848
pyarrow/src/arrow/python/decimal.h,sha256=kDDjLzW07D7d7omWSR4CBF1Ocskp4YSZu4Dtxu-gRUg,4726
pyarrow/src/arrow/python/deserialize.cc,sha256=ogtBX7OzGuDvyj_LepFkaG7m53-wenf3duG0WF8Ooa0,19185
pyarrow/src/arrow/python/deserialize.h,sha256=Q4L1qPCra8-Wzl6oLm44cPOUMVuK1FX01LeGzwNUtK4,4260
pyarrow/src/arrow/python/extension_type.cc,sha256=eU5P7pufWjcEcmVeOyu1jtEZ08AWd9tkTSMfx8ph0rQ,6860
pyarrow/src/arrow/python/extension_type.h,sha256=0gzb42y_mbw4fsYs3u8cwPFLBRlG-kkHQLgbvGtrY0U,3181
pyarrow/src/arrow/python/filesystem.cc,sha256=0twavI91TE20Otq5kkVUwnN5sindU_mBWoVAvz1ZMgI,6152
pyarrow/src/arrow/python/filesystem.h,sha256=FG0AcLekqaDf9IQPqKixAfIcY_ZLgIKP5NvvXdtBVUM,5126
pyarrow/src/arrow/python/flight.cc,sha256=Iz4wAyhX7mksabELtRljCOsXRRzuYzu38Rv_yQKJarw,13995
pyarrow/src/arrow/python/flight.h,sha256=u5UnulNJqMuXQLlODUWuoyxq-GtL1HuHmVGNzobUVGc,14311
pyarrow/src/arrow/python/gdb.cc,sha256=Z0WLBYHWBzc4uExNG7nWJeRnUBAVSqo_DFpKYry0aAE,22667
pyarrow/src/arrow/python/gdb.h,sha256=H-qvM-nU8a_3Z5tk8PvppTwQtBMSZhQKQIVgRAsRfFg,972
pyarrow/src/arrow/python/helpers.cc,sha256=zrrUI56RGrZ8VBzR2dJFJoRq7L6chlX7289HK7tjoOA,16627
pyarrow/src/arrow/python/helpers.h,sha256=jVNFEbvJXmCceJti3J3-MnZkNlJoynQNq334tt29bbs,5489
pyarrow/src/arrow/python/inference.cc,sha256=Gm-lOXDzqcbef6gdgCQa5eXPuh8uvYqz9iUjKS2_yO4,24350
pyarrow/src/arrow/python/inference.h,sha256=FUFvB4Zy7V-tueXdmbDcqTeLK4xj5GZEeRW5yhiJlsU,2038
pyarrow/src/arrow/python/io.cc,sha256=ZARQCv4WQmHDQrA1dlNZt6mJuPhyK8wNuGm7zoL6V78,11936
pyarrow/src/arrow/python/io.h,sha256=4jGnodpSUlnVqAVh9fWId7H4WldlLPkXyroABpdaW6w,3858
pyarrow/src/arrow/python/ipc.cc,sha256=3D9iMbOFHlhNXX4432wsfbfjWvDryZWgdA0Ak19V_8Q,4472
pyarrow/src/arrow/python/ipc.h,sha256=SZbw6jCCqLiLNCY3k632GmwHeD_r_xrDS0dhqV49VhY,2259
pyarrow/src/arrow/python/iterators.h,sha256=Ugfm3JvetAH0l-oAjjpZfhrUBqRimVMaw4-xusvqLSg,7327
pyarrow/src/arrow/python/numpy_convert.cc,sha256=166BIW7zVTRMKogxLUuhV4e5jOevmonvRtXDydNujgc,21194
pyarrow/src/arrow/python/numpy_convert.h,sha256=y13eHwfe1lJKzadoTr2-GyX6xPsE6Z7FN31s7PN-2Rk,4870
pyarrow/src/arrow/python/numpy_init.cc,sha256=cJKOH946T7VCcB-gVIoGgfbWTrbj3FPkI4TgnsLTf7s,1178
pyarrow/src/arrow/python/numpy_init.h,sha256=FniVHP7W2YBlenoMYhQrODvoqqvDMSls2JANGtNPQts,999
pyarrow/src/arrow/python/numpy_internal.h,sha256=F9p-hzTKCIhRqgtZbsoyPox7RR85YcEw6FYkFF8KqfM,5314
pyarrow/src/arrow/python/numpy_interop.h,sha256=rI6ek8JTOYtjo7gEADSDBS6QuAOHa2A0YQPZ2GeypFw,3418
pyarrow/src/arrow/python/numpy_to_arrow.cc,sha256=55-VSQlg10MAZTR0G3I7maErexO8-FDk_27SYdvVlk8,30238
pyarrow/src/arrow/python/numpy_to_arrow.h,sha256=z9KapsuoOSpWILPt9bea7GR4BL6AQ28T6DUO0mSkh3k,2760
pyarrow/src/arrow/python/parquet_encryption.cc,sha256=RNupwaySaVHKX_iCYOPK0yJWkTUpqbrpbCW2duWJ3kU,3567
pyarrow/src/arrow/python/parquet_encryption.h,sha256=Mc8tZ8gIfkH0AckNiIOt6hesP_MVKeKhcytT24ZOLdQ,4861
pyarrow/src/arrow/python/pch.h,sha256=vkbgStQjq820YeHlXBPdzQ-W9LyzJrTGfMBpnMMqahk,1129
pyarrow/src/arrow/python/platform.h,sha256=XYS5IqiMUejxN2COzu60Zs8b_wAaGTBw4M-zKVqqs5U,1422
pyarrow/src/arrow/python/pyarrow.cc,sha256=Pul4lmF7n5Q9cSzgBSvPArWfZY_qDyAq1a_tyMIQGRA,3677
pyarrow/src/arrow/python/pyarrow.h,sha256=TK3BtD9n3QKOQ9dX3LXbQc0hu9alWcufV0O93iQW7B0,2761
pyarrow/src/arrow/python/pyarrow_api.h,sha256=7l0G4-_m9yALYoifsY8Z6qh3HHD0PgkpVSgCn_JaGU4,867
pyarrow/src/arrow/python/pyarrow_lib.h,sha256=-70_Ckj3_0ImlzaXSJOE_d3w9pGM66lXiGPyln9c96Y,863
pyarrow/src/arrow/python/python_test.cc,sha256=Jg35rRR7BtXOS1012RFOdLViFlVC3zlXV--w8aEzf8I,32397
pyarrow/src/arrow/python/python_test.h,sha256=ea32mM20uHySlygi9MtVxr26O-ydTZHCUQIlxaIMjT4,1195
pyarrow/src/arrow/python/python_to_arrow.cc,sha256=K6tVQK1phrrJQzz_TJVmEdfcX-fJfBAkPIeQlypRirY,47145
pyarrow/src/arrow/python/python_to_arrow.h,sha256=BoVytf6P7PBYXyznchElKZSFvEsFyimB-tLFdw0AUNo,2521
pyarrow/src/arrow/python/serialize.cc,sha256=FOAsdyfRETe_bCSxC1vc3-oq9Rs9SsU4kDQFTwrdvQM,32667
pyarrow/src/arrow/python/serialize.h,sha256=HVBhIKgc7A4YOmwYfjE2Hqj1Yxl9suCJb940KxrVcrs,4630
pyarrow/src/arrow/python/type_traits.h,sha256=B_NsRT_hZG8D91sTcihJyKF5SrslPcFmj12QfbpHuLI,10093
pyarrow/src/arrow/python/udf.cc,sha256=69DuHRjV6rUAbZqkWEKEUG3ODuHg9ym52lnH7A_lM5Y,29814
pyarrow/src/arrow/python/udf.h,sha256=de3R8PhNJO5lT9oCqRxe8e2_SE3jBpHOkwbNqCrlgjQ,3104
pyarrow/src/arrow/python/vendored/CMakeLists.txt,sha256=02XvDJAdKiajCEBOmnMKBpmzbRU7FPkNdlNXtw0-A24,837
pyarrow/src/arrow/python/vendored/pythoncapi_compat.h,sha256=bzMnlHTCfjk5DQRIxwytunYh5aQxU3iSElaaDyNnAY8,40900
pyarrow/src/arrow/python/visibility.h,sha256=hwJw5sGrWJckQkNaAuLe4Tf-VDjQbXknyzNOVgZI3FI,1381
pyarrow/substrait.py,sha256=ugd_UrjkUIrwSvqFxLl9WkVtBZ2-hcgt5XiSVYvDLnQ,1151
pyarrow/table.pxi,sha256=Dfujf9nDQ9R--F5cybcUxB126Hu1mBuARWvgOTuFl3o,203868
pyarrow/tensor.pxi,sha256=CXlMcTRWh_n_FTzIIx8SpHCmYlV0IBA69toQ-3Evs5o,42071
pyarrow/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pyarrow/tests/__pycache__/__init__.cpython-312.pyc,,
pyarrow/tests/__pycache__/arrow_16597.cpython-312.pyc,,
pyarrow/tests/__pycache__/arrow_39313.cpython-312.pyc,,
pyarrow/tests/__pycache__/arrow_7980.cpython-312.pyc,,
pyarrow/tests/__pycache__/conftest.cpython-312.pyc,,
pyarrow/tests/__pycache__/pandas_examples.cpython-312.pyc,,
pyarrow/tests/__pycache__/pandas_threaded_import.cpython-312.pyc,,
pyarrow/tests/__pycache__/read_record_batch.cpython-312.pyc,,
pyarrow/tests/__pycache__/strategies.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_acero.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_adhoc_memory_leak.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_array.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_builder.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_cffi.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_compute.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_convert_builtin.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_cpp_internals.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_csv.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_cuda.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_cuda_numba_interop.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_cython.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_dataset.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_dataset_encryption.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_deprecations.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_device.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_dlpack.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_exec_plan.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_extension_type.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_feather.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_flight.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_flight_async.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_fs.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_gandiva.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_gdb.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_io.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_ipc.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_json.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_jvm.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_memory.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_misc.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_orc.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_pandas.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_scalars.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_schema.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_sparse_tensor.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_strategies.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_substrait.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_table.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_tensor.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_types.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_udf.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_util.cpython-312.pyc,,
pyarrow/tests/__pycache__/test_without_numpy.cpython-312.pyc,,
pyarrow/tests/__pycache__/util.cpython-312.pyc,,
pyarrow/tests/__pycache__/wsgi_examples.cpython-312.pyc,,
pyarrow/tests/arrow_16597.py,sha256=DNb41h9E3ITGvAJJu86i5SfsKrwstQJ0E5gT_bpTS_k,1354
pyarrow/tests/arrow_39313.py,sha256=0pyBixoX38fldTPO1Vwshi_H0XBACrz8esYoL4o71KI,1431
pyarrow/tests/arrow_7980.py,sha256=tZKb_tRLfxHaosDk9Yu2GLEsJjMaruXD5CKhbK_6Hq8,1094
pyarrow/tests/bound_function_visit_strings.pyx,sha256=vDEFoNYR8BWNkCntKDuBUT8sXNRBex_5G2bFKogr1Bs,2026
pyarrow/tests/conftest.py,sha256=PwqCO9vIgMUc2W9gCwcDvEz4hcp2eIYHDZ_fwddhqJ4,9904
pyarrow/tests/data/feather/v0.17.0.version.2-compression.lz4.feather,sha256=qzcc7Bo4OWBXYsyyKdDJwdTRstMqB1Zz0GiGYtndBnE,594
pyarrow/tests/data/orc/README.md,sha256=_4X5XszZqQtWAVEz5N1Va4VyyayGQgNDKrcdMX2Ib4s,932
pyarrow/tests/data/orc/TestOrcFile.emptyFile.jsn.gz,sha256=xLjAXd-3scx3DCyeAsmxTO3dv1cj9KRvYopKe5rQNiI,50
pyarrow/tests/data/orc/TestOrcFile.emptyFile.orc,sha256=zj0579dQBXhF7JuB-ZphkmQ81ybLo6Ca4zPV4HXoImY,523
pyarrow/tests/data/orc/TestOrcFile.test1.jsn.gz,sha256=kLxmwMVHtfzpHqBztFjfY_PTCloaXpfHq9DDDszb8Wk,323
pyarrow/tests/data/orc/TestOrcFile.test1.orc,sha256=A4JxgMCffTkz9-XT1QT1tg2TlYZRRz1g7iIMmqzovqA,1711
pyarrow/tests/data/orc/TestOrcFile.testDate1900.jsn.gz,sha256=oWf7eBR3ZtOA91OTvdeQJYos1an56msGsJwhGOan3lo,182453
pyarrow/tests/data/orc/TestOrcFile.testDate1900.orc,sha256=nYsVYhUGGOL80gHj37si_vX0dh8QhIMSeU4sHjNideM,30941
pyarrow/tests/data/orc/decimal.jsn.gz,sha256=kTEyYdPDAASFUX8Niyry5mRDF-Y-LsrhSAjbu453mvA,19313
pyarrow/tests/data/orc/decimal.orc,sha256=W5cV2WdLy4OrSTnd_Qv5ntphG4TcB-MyG4UpRFwSxJY,16337
pyarrow/tests/data/parquet/v0.7.1.all-named-index.parquet,sha256=YPGUXtw-TsOPbiNDieZHobNp3or7nHhAxJGjmIDAyqE,3948
pyarrow/tests/data/parquet/v0.7.1.column-metadata-handling.parquet,sha256=7sebZgpfdcP37QksT3FhDL6vOA9gR6GBaq44NCVtOYw,2012
pyarrow/tests/data/parquet/v0.7.1.parquet,sha256=vmdzhIzpBbmRkq3Gjww7KqurfSFNtQuSpSIDeQVmqys,4372
pyarrow/tests/data/parquet/v0.7.1.some-named-index.parquet,sha256=VGgSjqihCRtdBxlUcfP5s3BSR7aUQKukW-bGgJLf_HY,4008
pyarrow/tests/extensions.pyx,sha256=05S652zNGxwMFwuyMbP0RP4VNJLSMlzvoxH8iYIvSNk,3054
pyarrow/tests/interchange/__init__.py,sha256=9hdXHABrVpkbpjZgUft39kOFL2xSGeG4GEua0Hmelus,785
pyarrow/tests/interchange/__pycache__/__init__.cpython-312.pyc,,
pyarrow/tests/interchange/__pycache__/test_conversion.cpython-312.pyc,,
pyarrow/tests/interchange/__pycache__/test_interchange_spec.cpython-312.pyc,,
pyarrow/tests/interchange/test_conversion.py,sha256=23e5tpKBL-ekA5uWpM6-f6HVPF937Hnzfune0Ty9moo,18609
pyarrow/tests/interchange/test_interchange_spec.py,sha256=5hwwCG6f7yf72PfUG0iLIk2bARsZU5EJeRjDxSQrkKI,9320
pyarrow/tests/pandas_examples.py,sha256=RFKCW-Rn0Qz-ncd4pZWWSeUoPq63kemE3lFiVdv2dBs,5115
pyarrow/tests/pandas_threaded_import.py,sha256=b_ubLr5dj4dWJht9552qc3S3Yt3fQQgaUH6208oZvHg,1429
pyarrow/tests/parquet/__init__.py,sha256=dKsXU9M-sJyz2wYIuqwsKM9meOlK_qY6qhmQzIvEpCE,931
pyarrow/tests/parquet/__pycache__/__init__.cpython-312.pyc,,
pyarrow/tests/parquet/__pycache__/common.cpython-312.pyc,,
pyarrow/tests/parquet/__pycache__/conftest.cpython-312.pyc,,
pyarrow/tests/parquet/__pycache__/encryption.cpython-312.pyc,,
pyarrow/tests/parquet/__pycache__/test_basic.cpython-312.pyc,,
pyarrow/tests/parquet/__pycache__/test_compliant_nested_type.cpython-312.pyc,,
pyarrow/tests/parquet/__pycache__/test_data_types.cpython-312.pyc,,
pyarrow/tests/parquet/__pycache__/test_dataset.cpython-312.pyc,,
pyarrow/tests/parquet/__pycache__/test_datetime.cpython-312.pyc,,
pyarrow/tests/parquet/__pycache__/test_encryption.cpython-312.pyc,,
pyarrow/tests/parquet/__pycache__/test_metadata.cpython-312.pyc,,
pyarrow/tests/parquet/__pycache__/test_pandas.cpython-312.pyc,,
pyarrow/tests/parquet/__pycache__/test_parquet_file.cpython-312.pyc,,
pyarrow/tests/parquet/__pycache__/test_parquet_writer.cpython-312.pyc,,
pyarrow/tests/parquet/common.py,sha256=-kckaOVj9P9BvL1vlvyHlsPUtysBoAYVL98Nwc9wmGo,5894
pyarrow/tests/parquet/conftest.py,sha256=mJNQal0VYsGFhHglhSt-F9CYHy_i8hB8MXaq3SxFBvk,3082
pyarrow/tests/parquet/encryption.py,sha256=Oi3QbixApvWGoGImiW7PAjR28cTQqlRXZKMI3O7E4UY,2521
pyarrow/tests/parquet/test_basic.py,sha256=SdLPuZ02NaBUEtpr18SzA90I7pK-WRMM1r3ApbQI5ps,36492
pyarrow/tests/parquet/test_compliant_nested_type.py,sha256=Lz7tCPrSpv9GrKPMS-eu1LehsCTwz7KdUdCYJ8tF8dE,3901
pyarrow/tests/parquet/test_data_types.py,sha256=tdYodveHBksDjM7DjSc7x1IEqMAZv0y6z2GsnpDdriM,15778
pyarrow/tests/parquet/test_dataset.py,sha256=UhjjQGO2kki9Q50zush0VGU3OMXHZncL_3uEQse4Lx8,42218
pyarrow/tests/parquet/test_datetime.py,sha256=A3ZaRj88u0IrlhCNp2KY_A8txrb7y2pKPgEVvI7e7bU,16398
pyarrow/tests/parquet/test_encryption.py,sha256=XMVlIcEurlzcPN2rlaNqbdZbGhF9hjz5ZhWY5Bz4Fxo,22099
pyarrow/tests/parquet/test_metadata.py,sha256=0sbEUEEal4dEczJHk77KzCk3q9P_1JD61Ayw6HBXFzo,27158
pyarrow/tests/parquet/test_pandas.py,sha256=dXXcaRBZXIt2HervJLC1gCxDLlhxu6MM_M3gxcaV1Rw,22821
pyarrow/tests/parquet/test_parquet_file.py,sha256=xm5ZUCf5xmpKh7s5nTIrEiis53mfv2NqZWVRiYOTfAg,9909
pyarrow/tests/parquet/test_parquet_writer.py,sha256=xwedRwRYtw5n_OMhLPGnJurcvlo4ooROsSalYL-ZVCM,11733
pyarrow/tests/pyarrow_cython_example.pyx,sha256=fx6zT1bUb2-cDnwKoG71K3ozpmrNJ53kKQHHJTExGz8,2115
pyarrow/tests/read_record_batch.py,sha256=9Y0X0h03hUXwOKZz7jBBZSwgIrjxT-FkWIw6pu38Frc,953
pyarrow/tests/strategies.py,sha256=ygkKPSV8CM8IMU8uW8d_RuDZEbwyj8bhD0Bv-ZwvaRk,13926
pyarrow/tests/test_acero.py,sha256=jgSkIAGhrVffShaD0ZAm50sY-f4u9jfjCimK8ezUbbA,15003
pyarrow/tests/test_adhoc_memory_leak.py,sha256=Pn4PcIbOBRtSJuz9Ar_ocubco0QOMZ-eAE9Bs7Wp4mA,1453
pyarrow/tests/test_array.py,sha256=p3JPYOvP6zJgNI2vuQ_ah9p5w126d9HRFeHN6Z5q894,139832
pyarrow/tests/test_builder.py,sha256=zNEcslLwyb40oYbG7lInQcI81QHMKDOzi1zthw1Je7c,2803
pyarrow/tests/test_cffi.py,sha256=Fbs1dFCxdnvXYLgO5oaxm_h8KV3vefE9jc3nI1JZNxw,26385
pyarrow/tests/test_compute.py,sha256=ajHKKGCpw92ZgdJl2pfdVF1UW4xGQB3EPELxXt-CnNw,142525
pyarrow/tests/test_convert_builtin.py,sha256=QTTX4KcmfZ5keLpSjfPnft9Eim4FeYnBpvPDwnOMGP0,80894
pyarrow/tests/test_cpp_internals.py,sha256=Xg4CUB6zohQkcYG64Lj_Uf2BscI27Vv0JC_CqNkDQuE,2006
pyarrow/tests/test_csv.py,sha256=GKNYAGis5TsiDJMIu0L6bH2_cIOpWDviRwxCfPN9Pv8,77313
pyarrow/tests/test_cuda.py,sha256=qCbVbYOokzpEef-e0_Fus36xQR9Y---9MLCYquI3shE,36163
pyarrow/tests/test_cuda_numba_interop.py,sha256=iHP_FE4sWbsKwNNXRcYnVozp3Wd1o0Mg6BDymx710G4,8794
pyarrow/tests/test_cython.py,sha256=IJVELKXBD89xoCcxscMfUpwvkk9SL_kNT4cccLjDww4,7115
pyarrow/tests/test_dataset.py,sha256=nbTfPH338ZqDstL1FuYpD7HefNMvbDi_zPF_zd4lFew,210420
pyarrow/tests/test_dataset_encryption.py,sha256=mA8ipIlOBSA4eKc6xnRz-IFyM7fu_kIQ5FV2r4vE2rs,7593
pyarrow/tests/test_deprecations.py,sha256=W_rneq4jC6zqCNoGhBDf1F28Q-0LHI7YKLgtsbV6LHM,891
pyarrow/tests/test_device.py,sha256=qe9Wiwo-XVazt9pdxyqQJUz6fNR0jTs9CHiyaoppNA4,2550
pyarrow/tests/test_dlpack.py,sha256=3s23cDst8TaUdum_v4XrWBJ9Ny5q4-b20vJJlHJLI8o,4937
pyarrow/tests/test_exec_plan.py,sha256=pjOkSaWeqjN6celKxUEH3tBGXLh8kKbmSSsvKOWsbQQ,10096
pyarrow/tests/test_extension_type.py,sha256=gKukBp0ial_3-iBeLsLIJKN-4ayn1K7P7auil2luH1U,65617
pyarrow/tests/test_feather.py,sha256=Rw8J4upIZhR0GMe17n84IFGItlBUk9qpHOCWmDWyCuw,25074
pyarrow/tests/test_flight.py,sha256=9kJlmuwCSzKrilP3UMeA20cyZJwlRB_pqGavbRM0Y7E,87152
pyarrow/tests/test_flight_async.py,sha256=g_mNqrnNBp7GWNOWZgnVklZcVKV_vvAAChDgcQICNdo,2873
pyarrow/tests/test_fs.py,sha256=n-RuiqvfK9zWkmmuhHLSZp3v5pRR1f258YKB6R5DsdI,65418
pyarrow/tests/test_gandiva.py,sha256=AEf9ln-j5MmIMQ0JTQPhnZwbNh82ynSURsWPaKaNing,15623
pyarrow/tests/test_gdb.py,sha256=OJzMfZtev3YOKJBm2QxnE-q-9-exy2JLhxpiVhY3T_0,44938
pyarrow/tests/test_io.py,sha256=T9Vdg1rPGjdAp7nd5U9TAc3mN0N4oWvlG-F8TKmMVS4,63768
pyarrow/tests/test_ipc.py,sha256=JPW2Q3pXKi8Y4adbCkpGZeNjdP8C6Ot1TqapinKeO_Q,42746
pyarrow/tests/test_json.py,sha256=P60OhNO7MqHWmppL7cKPmvFEYNMj0XdztxiNGxvjhkM,13169
pyarrow/tests/test_jvm.py,sha256=pIrHUgnDdmwDoLgM2TFvdgfcEJTGtBGsPgzYIRU6jYY,15507
pyarrow/tests/test_memory.py,sha256=FqCTUSCqZvKx4k-JDY3M83MvxQ15iNdMUbyUxACfS7w,8874
pyarrow/tests/test_misc.py,sha256=5-P4nWTZXB7ObuCiVwsQgCjNJ8883tZh03EY4aWea4I,7227
pyarrow/tests/test_orc.py,sha256=oijYMqsxPLYbpEy1NTwqlz-wiTd8aKttaZH6npXNXoY,19321
pyarrow/tests/test_pandas.py,sha256=_X9K5EQuAMff5vjj0CPlw-Yoa2syFbjXAfWOoZKPPIA,188352
pyarrow/tests/test_scalars.py,sha256=cKsl6QSB68aKTcHRI_sVXXonA-OgIOrkGjW3iAEIDT4,27654
pyarrow/tests/test_schema.py,sha256=3ed2GtcKqio7XJMbl9HTN2XxqCLlhiVJBze7VIwxn8Q,21814
pyarrow/tests/test_sparse_tensor.py,sha256=6Hp-X6PLqcUzTZCRSB-TyaAWR7ZyWf5YsWuZgixmd64,17500
pyarrow/tests/test_strategies.py,sha256=HEL8T94h8VyukGKNRVAW_RyQ3m36REc2P4q2BQZ_PfY,1811
pyarrow/tests/test_substrait.py,sha256=yGijuSlKRUndT80QMvuqfCx4135uAI7UjN89RYYiFCI,30634
pyarrow/tests/test_table.py,sha256=WAYwyPK8jiGWd6H8BKjJsQGcFAsgT0zW-vO5d-7iyo8,120500
pyarrow/tests/test_tensor.py,sha256=LYSEYGUjtdnsbL0WAir9jFindo-r0bLySiDA1uAXL8E,6643
pyarrow/tests/test_types.py,sha256=KH-BLjbSuQ17ySb0qr8ZUsYiHNfy_fGHuTsA-Ypr4Og,42056
pyarrow/tests/test_udf.py,sha256=WA9E5skUqh7uMr_zH3rQ11LRx0SK2G3WO8HjVHGWyQY,29792
pyarrow/tests/test_util.py,sha256=ozTlooHBMOP3nbX5b3dG2aanrXwxXHx1giicm0QQyPM,5030
pyarrow/tests/test_without_numpy.py,sha256=ysbB-jML318I04ViQT4Ok7iMg1cI-NU8kguPu-FTSl4,1855
pyarrow/tests/util.py,sha256=YeH8RovBtKY4L1SJqcOOObEZx0Yf6HSpkkq4xJdKL5U,13275
pyarrow/tests/wsgi_examples.py,sha256=vQIDb5989sRVLsELw-fRHhfX-dE96sTl5J2lEuEKup8,1348
pyarrow/types.pxi,sha256=dPCKGp91crrmtwOfkotcsh0QNVPrmOdQQVqOuaHbCao,157764
pyarrow/types.py,sha256=Woixb8A_OzBNtolWwwFGhbEWn10gavaB7S0wGMoFakQ,7240
pyarrow/util.py,sha256=W0LXUR7nsrA5N-l3THD283bxCibS0sM1q6WLcfbFFz8,7970
pyarrow/vendored/__init__.py,sha256=9hdXHABrVpkbpjZgUft39kOFL2xSGeG4GEua0Hmelus,785
pyarrow/vendored/__pycache__/__init__.cpython-312.pyc,,
pyarrow/vendored/__pycache__/docscrape.cpython-312.pyc,,
pyarrow/vendored/__pycache__/version.cpython-312.pyc,,
pyarrow/vendored/docscrape.py,sha256=phTjwuzoO5hB88QerZk3uGu9c5OrZwjFzI7vEIIbCUQ,22975
pyarrow/vendored/version.py,sha256=5-Vo4Q3kPJrm1DSGusnMlTxuA8ynI4hAryApBd6MnpQ,14345
