Metadata-Version: 2.1
Name: azure-functions-durable
Version: 1.3.2
Summary: Durable Functions For Python
Home-page: https://github.com/Azure/azure-functions-durable-python
Author: Azure Functions team at Microsoft Corp.
Author-email: <EMAIL>
License: MIT
Keywords: azure functions azurefunctions python serverless workflows durablefunctions
Platform: UNKNOWN
Classifier: License :: OSI Approved :: MIT License
Classifier: Intended Audience :: Developers
Classifier: Programming Language :: Python :: 3
Classifier: Operating System :: Microsoft :: Windows
Classifier: Operating System :: POSIX
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Environment :: Web Environment
Classifier: Development Status :: 5 - Production/Stable
Requires-Python: >=3.9,<4
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: azure-functions>=1.12.0
Requires-Dist: aiohttp>=3.12.9
Requires-Dist: requests==2.*
Requires-Dist: python-dateutil>=2.8.0
Requires-Dist: furl>=2.1.0
Requires-Dist: opentelemetry-api>=1.32.1
Requires-Dist: opentelemetry-sdk>=1.32.1

|Branch|Status|
|---|---|
|main|[![Build Status](https://azfunc.visualstudio.com/Azure%20Functions/_apis/build/status/Azure.azure-functions-durable-python?branchName=main)](https://azfunc.visualstudio.com/Azure%20Functions/_build/latest?definitionId=58&branchName=main)|
|dev|[![Build Status](https://azfunc.visualstudio.com/Azure%20Functions/_apis/build/status/Azure.azure-functions-durable-python?branchName=dev)](https://azfunc.visualstudio.com/Azure%20Functions/_build/latest?definitionId=58&branchName=dev)|

# Durable Functions for Python

 [Durable Functions](https://docs.microsoft.com/en-us/azure/azure-functions/durable/durable-functions-overview) is an extension of [Azure Functions](https://docs.microsoft.com/en-us/azure/azure-functions/functions-overview) that lets you write stateful functions in a serverless compute environment. The extension lets you define stateful workflows by writing orchestrator functions and stateful entities by writing entity functions using the Azure Functions programming model. Behind the scenes, the extension manages state, checkpoints, and restarts for you, allowing you to focus on your business logic.

 🐍  Find us on PyPi [here](https://pypi.org/project/azure-functions-durable/) 🐍   


You can find more information at the following links:

* [Azure Functions overview](https://docs.microsoft.com/en-us/azure/azure-functions/functions-overview)
* [Azure Functions Python developers guide](https://docs.microsoft.com/en-us/azure/azure-functions/functions-reference-python)
* [Durable Functions overview](https://docs.microsoft.com/en-us/azure/azure-functions/durable/durable-functions-overview?tabs=python)
* [Core concepts and features overview](https://docs.microsoft.com/en-us/azure/azure-functions/durable/durable-functions-types-features-overview).

> Durable Functions expects certain programming constraints to be followed. Please read the documentation linked above for more information.

## Getting Started

Follow these instructions to get started with Durable Functions in Python:

**🚀 [Python Durable Functions quickstart](https://docs.microsoft.com/azure/azure-functions/durable/quickstart-python-vscode)**

## Tooling

* Python Durable Functions requires [Azure Functions Core Tools](https://docs.microsoft.com/en-us/azure/azure-functions/functions-run-local) version 3.0.2630 or higher.


