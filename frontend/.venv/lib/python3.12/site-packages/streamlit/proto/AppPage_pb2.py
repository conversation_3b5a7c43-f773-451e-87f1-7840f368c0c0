# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/AppPage.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1dstreamlit/proto/AppPage.proto\"\x86\x01\n\x07\x41ppPage\x12\x18\n\x10page_script_hash\x18\x01 \x01(\t\x12\x11\n\tpage_name\x18\x02 \x01(\t\x12\x0c\n\x04icon\x18\x03 \x01(\t\x12\x12\n\nis_default\x18\x04 \x01(\x08\x12\x16\n\x0esection_header\x18\x05 \x01(\t\x12\x14\n\x0curl_pathname\x18\x06 \x01(\tB,\n\x1c\x63om.snowflake.apps.streamlitB\x0c\x41ppPageProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.AppPage_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\014AppPageProto'
  _globals['_APPPAGE']._serialized_start=34
  _globals['_APPPAGE']._serialized_end=168
# @@protoc_insertion_point(module_scope)
