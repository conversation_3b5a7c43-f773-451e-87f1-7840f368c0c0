# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/Common.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1cstreamlit/proto/Common.proto\"\x1b\n\x0bStringArray\x12\x0c\n\x04\x64\x61ta\x18\x01 \x03(\t\"\x1b\n\x0b\x44oubleArray\x12\x0c\n\x04\x64\x61ta\x18\x01 \x03(\x01\"\x1a\n\nInt32Array\x12\x0c\n\x04\x64\x61ta\x18\x01 \x03(\x05\"\x1a\n\nInt64Array\x12\x0c\n\x04\x64\x61ta\x18\x01 \x03(\x03\"\x1b\n\x0bSInt64Array\x12\x0c\n\x04\x64\x61ta\x18\x01 \x03(\x12\"\x1b\n\x0bUInt32Array\x12\x0c\n\x04\x64\x61ta\x18\x01 \x03(\r\"0\n\x12StringTriggerValue\x12\x11\n\x04\x64\x61ta\x18\x01 \x01(\tH\x00\x88\x01\x01\x42\x07\n\x05_data\"M\n\x0f\x46ileURLsRequest\x12\x12\n\nrequest_id\x18\x01 \x01(\t\x12\x12\n\nfile_names\x18\x02 \x03(\t\x12\x12\n\nsession_id\x18\x03 \x01(\t\"C\n\x08\x46ileURLs\x12\x0f\n\x07\x66ile_id\x18\x01 \x01(\t\x12\x12\n\nupload_url\x18\x02 \x01(\t\x12\x12\n\ndelete_url\x18\x03 \x01(\t\"X\n\x10\x46ileURLsResponse\x12\x13\n\x0bresponse_id\x18\x01 \x01(\t\x12\x1c\n\tfile_urls\x18\x02 \x03(\x0b\x32\t.FileURLs\x12\x11\n\terror_msg\x18\x03 \x01(\t\"i\n\x10UploadedFileInfo\x12\n\n\x02id\x18\x01 \x01(\x12\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0c\n\x04size\x18\x03 \x01(\x04\x12\x0f\n\x07\x66ile_id\x18\x04 \x01(\t\x12\x1c\n\tfile_urls\x18\x05 \x01(\x0b\x32\t.FileURLs\"W\n\x11\x46ileUploaderState\x12\x13\n\x0bmax_file_id\x18\x01 \x01(\x12\x12-\n\x12uploaded_file_info\x18\x02 \x03(\x0b\x32\x11.UploadedFileInfo\"z\n\x0e\x43hatInputValue\x12\x11\n\x04\x64\x61ta\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x34\n\x13\x66ile_uploader_state\x18\x02 \x01(\x0b\x32\x12.FileUploaderStateH\x01\x88\x01\x01\x42\x07\n\x05_dataB\x16\n\x14_file_uploader_stateB+\n\x1c\x63om.snowflake.apps.streamlitB\x0b\x43ommonProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.Common_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\013CommonProto'
  _globals['_STRINGARRAY']._serialized_start=32
  _globals['_STRINGARRAY']._serialized_end=59
  _globals['_DOUBLEARRAY']._serialized_start=61
  _globals['_DOUBLEARRAY']._serialized_end=88
  _globals['_INT32ARRAY']._serialized_start=90
  _globals['_INT32ARRAY']._serialized_end=116
  _globals['_INT64ARRAY']._serialized_start=118
  _globals['_INT64ARRAY']._serialized_end=144
  _globals['_SINT64ARRAY']._serialized_start=146
  _globals['_SINT64ARRAY']._serialized_end=173
  _globals['_UINT32ARRAY']._serialized_start=175
  _globals['_UINT32ARRAY']._serialized_end=202
  _globals['_STRINGTRIGGERVALUE']._serialized_start=204
  _globals['_STRINGTRIGGERVALUE']._serialized_end=252
  _globals['_FILEURLSREQUEST']._serialized_start=254
  _globals['_FILEURLSREQUEST']._serialized_end=331
  _globals['_FILEURLS']._serialized_start=333
  _globals['_FILEURLS']._serialized_end=400
  _globals['_FILEURLSRESPONSE']._serialized_start=402
  _globals['_FILEURLSRESPONSE']._serialized_end=490
  _globals['_UPLOADEDFILEINFO']._serialized_start=492
  _globals['_UPLOADEDFILEINFO']._serialized_end=597
  _globals['_FILEUPLOADERSTATE']._serialized_start=599
  _globals['_FILEUPLOADERSTATE']._serialized_end=686
  _globals['_CHATINPUTVALUE']._serialized_start=688
  _globals['_CHATINPUTVALUE']._serialized_end=810
# @@protoc_insertion_point(module_scope)
