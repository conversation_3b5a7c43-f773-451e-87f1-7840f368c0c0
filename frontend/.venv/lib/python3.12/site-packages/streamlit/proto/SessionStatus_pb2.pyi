"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
*!
Copyright (c) Streamlit Inc. (2018-2022) Snowflake Inc. (2022-2025)

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import builtins
import google.protobuf.descriptor
import google.protobuf.message
import typing

DESCRIPTOR: google.protobuf.descriptor.FileDescriptor

@typing.final
class SessionStatus(google.protobuf.message.Message):
    """Status for a session. Sent as part of the Initialize message, and also
    on AppSession status change events.

    NOTE: This proto type is used by some external services so needs to remain
    relatively stable. While it isn't entirely set in stone, changing it
    may require a good amount of effort so should be avoided if possible.
    """

    DESCRIPTOR: google.protobuf.descriptor.Descriptor

    RUN_ON_SAVE_FIELD_NUMBER: builtins.int
    SCRIPT_IS_RUNNING_FIELD_NUMBER: builtins.int
    run_on_save: builtins.bool
    """If true, streamlit will re-run the script if it detects that the script
    has been changed. This value comes from the server.runOnSave config.
    The browser can change this option; it's sent here so that the browser
    shows the correct initial value in its Settings dialog.
    """
    script_is_running: builtins.bool
    """True if the script is being run by a client right now."""
    def __init__(
        self,
        *,
        run_on_save: builtins.bool = ...,
        script_is_running: builtins.bool = ...,
    ) -> None: ...
    def ClearField(self, field_name: typing.Literal["run_on_save", b"run_on_save", "script_is_running", b"script_is_running"]) -> None: ...

global___SessionStatus = SessionStatus
