# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/HeightConfig.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\"streamlit/proto/HeightConfig.proto\x12\tstreamlit\"c\n\x0cHeightConfig\x12\x15\n\x0buse_stretch\x18\x01 \x01(\x08H\x00\x12\x15\n\x0buse_content\x18\x02 \x01(\x08H\x00\x12\x16\n\x0cpixel_height\x18\x03 \x01(\rH\x00\x42\r\n\x0bheight_specB1\n\x1c\x63om.snowflake.apps.streamlitB\x11HeightConfigProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.HeightConfig_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\021HeightConfigProto'
  _globals['_HEIGHTCONFIG']._serialized_start=49
  _globals['_HEIGHTCONFIG']._serialized_end=148
# @@protoc_insertion_point(module_scope)
