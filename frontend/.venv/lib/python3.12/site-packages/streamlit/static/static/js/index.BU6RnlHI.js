import{r as h,E as $,_ as H,b8 as mt,bh as $t,bi as Ht,bj as Vt,M as X,O as q,aE as jt,n as P,bk as Gt,C as N,j as v,bl as Xt,b2 as qt,bm as Yt,bn as Kt,l as Jt,z as Qt,bo as tt,H as Zt,aA as te,bp as ee,bq as ie,b7 as se,br as ne}from"./index.C1NIn1Y2.js";import{u as re,F as oe}from"./FormClearHelper.D1M9GM_c.js";import{T as ae,a as ft}from"./Toolbar.KhlcEc0K.js";import{u as le}from"./Hooks.BGwHKeUc.js";import{c as ce}from"./createDownloadLinkElement.DZMwyjvU.js";import{F as de,D as he}from"./FileDownload.esm.AI3watX9.js";var yt=h.forwardRef(function(s,t){var e={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return h.createElement($,H({iconAttrs:e,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},s,{ref:t}),h.createElement("g",{fill:"none"},h.createElement("rect",{width:24,height:24}),h.createElement("rect",{width:24,height:24}),h.createElement("rect",{width:24,height:24})),h.createElement("path",{d:"M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"}),h.createElement("path",{d:"M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"}))});yt.displayName="Mic";var wt=h.forwardRef(function(s,t){var e={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return h.createElement($,H({iconAttrs:e,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},s,{ref:t}),h.createElement("rect",{width:24,height:24,fill:"none"}),h.createElement("path",{d:"M8 19c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2s-2 .9-2 2v10c0 1.1.9 2 2 2zm6-12v10c0 1.1.9 2 2 2s2-.9 2-2V7c0-1.1-.9-2-2-2s-2 .9-2 2z"}))});wt.displayName="Pause";var St=h.forwardRef(function(s,t){var e={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return h.createElement($,H({iconAttrs:e,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},s,{ref:t}),h.createElement("rect",{width:24,height:24,fill:"none"}),h.createElement("path",{d:"M8 6.82v10.36c0 .79.87 1.27 1.54.84l8.14-5.18a1 1 0 000-1.69L9.54 5.98A.998.998 0 008 6.82z"}))});St.displayName="PlayArrow";var Ct=h.forwardRef(function(s,t){var e={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return h.createElement($,H({iconAttrs:e,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},s,{ref:t}),h.createElement("path",{fill:"none",d:"M0 0h24v24H0V0z"}),h.createElement("path",{d:"M17.65 6.35a7.95 7.95 0 00-6.48-2.31c-3.67.37-6.69 3.35-7.1 7.02C3.52 15.91 7.27 20 12 20a7.98 7.98 0 007.21-4.56c.32-.67-.16-1.44-.9-1.44-.37 0-.72.2-.88.53a5.994 5.994 0 01-6.8 3.31c-2.22-.49-4.01-2.3-4.48-4.52A6.002 6.002 0 0112 6c1.66 0 3.14.69 4.22 1.78l-1.51 1.51c-.63.63-.19 1.71.7 1.71H19c.55 0 1-.45 1-1V6.41c0-.89-1.08-1.34-1.71-.71l-.64.65z"}))});Ct.displayName="Refresh";var Et=h.forwardRef(function(s,t){var e={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return h.createElement($,H({iconAttrs:e,iconVerticalAlign:"middle",iconViewBox:"0 0 24 24"},s,{ref:t}),h.createElement("g",{fill:"none"},h.createElement("rect",{width:24,height:24}),h.createElement("rect",{width:24,height:24})),h.createElement("path",{fillRule:"evenodd",d:"M9 16h6c.55 0 1-.45 1-1V9c0-.55-.45-1-1-1H9c-.55 0-1 .45-1 1v6c0 .55.45 1 1 1zm3-14C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z"}))});Et.displayName="StopCircle";function R(s,t,e,i){return new(e||(e=Promise))(function(n,r){function a(c){try{d(i.next(c))}catch(l){r(l)}}function o(c){try{d(i.throw(c))}catch(l){r(l)}}function d(c){var l;c.done?n(c.value):(l=c.value,l instanceof e?l:new e(function(p){p(l)})).then(a,o)}d((i=i.apply(s,t||[])).next())})}let V=class{constructor(){this.listeners={}}on(t,e,i){if(this.listeners[t]||(this.listeners[t]=new Set),this.listeners[t].add(e),i==null?void 0:i.once){const n=()=>{this.un(t,n),this.un(t,e)};return this.on(t,n),n}return()=>this.un(t,e)}un(t,e){var i;(i=this.listeners[t])===null||i===void 0||i.delete(e)}once(t,e){return this.on(t,e,{once:!0})}unAll(){this.listeners={}}emit(t,...e){this.listeners[t]&&this.listeners[t].forEach(i=>i(...e))}};const j={decode:function(s,t){return R(this,void 0,void 0,function*(){const e=new AudioContext({sampleRate:t});return e.decodeAudioData(s).finally(()=>e.close())})},createBuffer:function(s,t){return typeof s[0]=="number"&&(s=[s]),function(e){const i=e[0];if(i.some(n=>n>1||n<-1)){const n=i.length;let r=0;for(let a=0;a<n;a++){const o=Math.abs(i[a]);o>r&&(r=o)}for(const a of e)for(let o=0;o<n;o++)a[o]/=r}}(s),{duration:t,length:s[0].length,sampleRate:s[0].length/t,numberOfChannels:s.length,getChannelData:e=>s==null?void 0:s[e],copyFromChannel:AudioBuffer.prototype.copyFromChannel,copyToChannel:AudioBuffer.prototype.copyToChannel}}};function Rt(s,t){const e=t.xmlns?document.createElementNS(t.xmlns,s):document.createElement(s);for(const[i,n]of Object.entries(t))if(i==="children")for(const[r,a]of Object.entries(t))typeof a=="string"?e.appendChild(document.createTextNode(a)):e.appendChild(Rt(r,a));else i==="style"?Object.assign(e.style,n):i==="textContent"?e.textContent=n:e.setAttribute(i,n.toString());return e}function gt(s,t,e){const i=Rt(s,t||{});return e==null||e.appendChild(i),i}var ue=Object.freeze({__proto__:null,createElement:gt,default:gt});const pe={fetchBlob:function(s,t,e){return R(this,void 0,void 0,function*(){const i=yield fetch(s,e);if(i.status>=400)throw new Error(`Failed to fetch ${s}: ${i.status} (${i.statusText})`);return function(n,r){R(this,void 0,void 0,function*(){if(!n.body||!n.headers)return;const a=n.body.getReader(),o=Number(n.headers.get("Content-Length"))||0;let d=0;const c=p=>R(this,void 0,void 0,function*(){d+=(p==null?void 0:p.length)||0;const u=Math.round(d/o*100);r(u)}),l=()=>R(this,void 0,void 0,function*(){let p;try{p=yield a.read()}catch{return}p.done||(c(p.value),yield l())});l()})}(i.clone(),t),i.blob()})}};class me extends V{constructor(t){super(),this.isExternalMedia=!1,t.media?(this.media=t.media,this.isExternalMedia=!0):this.media=document.createElement("audio"),t.mediaControls&&(this.media.controls=!0),t.autoplay&&(this.media.autoplay=!0),t.playbackRate!=null&&this.onMediaEvent("canplay",()=>{t.playbackRate!=null&&(this.media.playbackRate=t.playbackRate)},{once:!0})}onMediaEvent(t,e,i){return this.media.addEventListener(t,e,i),()=>this.media.removeEventListener(t,e,i)}getSrc(){return this.media.currentSrc||this.media.src||""}revokeSrc(){const t=this.getSrc();t.startsWith("blob:")&&URL.revokeObjectURL(t)}canPlayType(t){return this.media.canPlayType(t)!==""}setSrc(t,e){const i=this.getSrc();if(t&&i===t)return;this.revokeSrc();const n=e instanceof Blob&&(this.canPlayType(e.type)||!t)?URL.createObjectURL(e):t;i&&(this.media.src="");try{this.media.src=n}catch{this.media.src=t}}destroy(){this.isExternalMedia||(this.media.pause(),this.media.remove(),this.revokeSrc(),this.media.src="",this.media.load())}setMediaElement(t){this.media=t}play(){return R(this,void 0,void 0,function*(){return this.media.play()})}pause(){this.media.pause()}isPlaying(){return!this.media.paused&&!this.media.ended}setTime(t){this.media.currentTime=Math.max(0,Math.min(t,this.getDuration()))}getDuration(){return this.media.duration}getCurrentTime(){return this.media.currentTime}getVolume(){return this.media.volume}setVolume(t){this.media.volume=t}getMuted(){return this.media.muted}setMuted(t){this.media.muted=t}getPlaybackRate(){return this.media.playbackRate}isSeeking(){return this.media.seeking}setPlaybackRate(t,e){e!=null&&(this.media.preservesPitch=e),this.media.playbackRate=t}getMediaElement(){return this.media}setSinkId(t){return this.media.setSinkId(t)}}class U extends V{constructor(t,e){super(),this.timeouts=[],this.isScrollable=!1,this.audioData=null,this.resizeObserver=null,this.lastContainerWidth=0,this.isDragging=!1,this.subscriptions=[],this.unsubscribeOnScroll=[],this.subscriptions=[],this.options=t;const i=this.parentFromOptionsContainer(t.container);this.parent=i;const[n,r]=this.initHtml();i.appendChild(n),this.container=n,this.scrollContainer=r.querySelector(".scroll"),this.wrapper=r.querySelector(".wrapper"),this.canvasWrapper=r.querySelector(".canvases"),this.progressWrapper=r.querySelector(".progress"),this.cursor=r.querySelector(".cursor"),e&&r.appendChild(e),this.initEvents()}parentFromOptionsContainer(t){let e;if(typeof t=="string"?e=document.querySelector(t):t instanceof HTMLElement&&(e=t),!e)throw new Error("Container not found");return e}initEvents(){const t=e=>{const i=this.wrapper.getBoundingClientRect(),n=e.clientX-i.left,r=e.clientY-i.top;return[n/i.width,r/i.height]};if(this.wrapper.addEventListener("click",e=>{const[i,n]=t(e);this.emit("click",i,n)}),this.wrapper.addEventListener("dblclick",e=>{const[i,n]=t(e);this.emit("dblclick",i,n)}),this.options.dragToSeek!==!0&&typeof this.options.dragToSeek!="object"||this.initDrag(),this.scrollContainer.addEventListener("scroll",()=>{const{scrollLeft:e,scrollWidth:i,clientWidth:n}=this.scrollContainer,r=e/i,a=(e+n)/i;this.emit("scroll",r,a,e,e+n)}),typeof ResizeObserver=="function"){const e=this.createDelay(100);this.resizeObserver=new ResizeObserver(()=>{e().then(()=>this.onContainerResize()).catch(()=>{})}),this.resizeObserver.observe(this.scrollContainer)}}onContainerResize(){const t=this.parent.clientWidth;t===this.lastContainerWidth&&this.options.height!=="auto"||(this.lastContainerWidth=t,this.reRender())}initDrag(){this.subscriptions.push(function(t,e,i,n,r=3,a=0,o=100){if(!t)return()=>{};const d=matchMedia("(pointer: coarse)").matches;let c=()=>{};const l=p=>{if(p.button!==a)return;p.preventDefault(),p.stopPropagation();let u=p.clientX,m=p.clientY,f=!1;const b=Date.now(),g=w=>{if(w.preventDefault(),w.stopPropagation(),d&&Date.now()-b<o)return;const D=w.clientX,T=w.clientY,k=D-u,W=T-m;if(f||Math.abs(k)>r||Math.abs(W)>r){const O=t.getBoundingClientRect(),{left:F,top:L}=O;f||(i==null||i(u-F,m-L),f=!0),e(k,W,D-F,T-L),u=D,m=T}},S=w=>{if(f){const D=w.clientX,T=w.clientY,k=t.getBoundingClientRect(),{left:W,top:O}=k;n==null||n(D-W,T-O)}c()},y=w=>{w.relatedTarget&&w.relatedTarget!==document.documentElement||S(w)},x=w=>{f&&(w.stopPropagation(),w.preventDefault())},A=w=>{f&&w.preventDefault()};document.addEventListener("pointermove",g),document.addEventListener("pointerup",S),document.addEventListener("pointerout",y),document.addEventListener("pointercancel",y),document.addEventListener("touchmove",A,{passive:!1}),document.addEventListener("click",x,{capture:!0}),c=()=>{document.removeEventListener("pointermove",g),document.removeEventListener("pointerup",S),document.removeEventListener("pointerout",y),document.removeEventListener("pointercancel",y),document.removeEventListener("touchmove",A),setTimeout(()=>{document.removeEventListener("click",x,{capture:!0})},10)}};return t.addEventListener("pointerdown",l),()=>{c(),t.removeEventListener("pointerdown",l)}}(this.wrapper,(t,e,i)=>{this.emit("drag",Math.max(0,Math.min(1,i/this.wrapper.getBoundingClientRect().width)))},t=>{this.isDragging=!0,this.emit("dragstart",Math.max(0,Math.min(1,t/this.wrapper.getBoundingClientRect().width)))},t=>{this.isDragging=!1,this.emit("dragend",Math.max(0,Math.min(1,t/this.wrapper.getBoundingClientRect().width)))}))}getHeight(t,e){var i;const n=((i=this.audioData)===null||i===void 0?void 0:i.numberOfChannels)||1;if(t==null)return 128;if(!isNaN(Number(t)))return Number(t);if(t==="auto"){const r=this.parent.clientHeight||128;return e!=null&&e.every(a=>!a.overlay)?r/n:r}return 128}initHtml(){const t=document.createElement("div"),e=t.attachShadow({mode:"open"}),i=this.options.cspNonce&&typeof this.options.cspNonce=="string"?this.options.cspNonce.replace(/"/g,""):"";return e.innerHTML=`
      <style${i?` nonce="${i}"`:""}>
        :host {
          user-select: none;
          min-width: 1px;
        }
        :host audio {
          display: block;
          width: 100%;
        }
        :host .scroll {
          overflow-x: auto;
          overflow-y: hidden;
          width: 100%;
          position: relative;
        }
        :host .noScrollbar {
          scrollbar-color: transparent;
          scrollbar-width: none;
        }
        :host .noScrollbar::-webkit-scrollbar {
          display: none;
          -webkit-appearance: none;
        }
        :host .wrapper {
          position: relative;
          overflow: visible;
          z-index: 2;
        }
        :host .canvases {
          min-height: ${this.getHeight(this.options.height,this.options.splitChannels)}px;
        }
        :host .canvases > div {
          position: relative;
        }
        :host canvas {
          display: block;
          position: absolute;
          top: 0;
          image-rendering: pixelated;
        }
        :host .progress {
          pointer-events: none;
          position: absolute;
          z-index: 2;
          top: 0;
          left: 0;
          width: 0;
          height: 100%;
          overflow: hidden;
        }
        :host .progress > div {
          position: relative;
        }
        :host .cursor {
          pointer-events: none;
          position: absolute;
          z-index: 5;
          top: 0;
          left: 0;
          height: 100%;
          border-radius: 2px;
        }
      </style>

      <div class="scroll" part="scroll">
        <div class="wrapper" part="wrapper">
          <div class="canvases" part="canvases"></div>
          <div class="progress" part="progress"></div>
          <div class="cursor" part="cursor"></div>
        </div>
      </div>
    `,[t,e]}setOptions(t){if(this.options.container!==t.container){const e=this.parentFromOptionsContainer(t.container);e.appendChild(this.container),this.parent=e}t.dragToSeek!==!0&&typeof this.options.dragToSeek!="object"||this.initDrag(),this.options=t,this.reRender()}getWrapper(){return this.wrapper}getWidth(){return this.scrollContainer.clientWidth}getScroll(){return this.scrollContainer.scrollLeft}setScroll(t){this.scrollContainer.scrollLeft=t}setScrollPercentage(t){const{scrollWidth:e}=this.scrollContainer,i=e*t;this.setScroll(i)}destroy(){var t,e;this.subscriptions.forEach(i=>i()),this.container.remove(),(t=this.resizeObserver)===null||t===void 0||t.disconnect(),(e=this.unsubscribeOnScroll)===null||e===void 0||e.forEach(i=>i()),this.unsubscribeOnScroll=[]}createDelay(t=10){let e,i;const n=()=>{e&&clearTimeout(e),i&&i()};return this.timeouts.push(n),()=>new Promise((r,a)=>{n(),i=a,e=setTimeout(()=>{e=void 0,i=void 0,r()},t)})}convertColorValues(t){if(!Array.isArray(t))return t||"";if(t.length<2)return t[0]||"";const e=document.createElement("canvas"),i=e.getContext("2d"),n=e.height*(window.devicePixelRatio||1),r=i.createLinearGradient(0,0,0,n),a=1/(t.length-1);return t.forEach((o,d)=>{const c=d*a;r.addColorStop(c,o)}),r}getPixelRatio(){return Math.max(1,window.devicePixelRatio||1)}renderBarWaveform(t,e,i,n){const r=t[0],a=t[1]||t[0],o=r.length,{width:d,height:c}=i.canvas,l=c/2,p=this.getPixelRatio(),u=e.barWidth?e.barWidth*p:1,m=e.barGap?e.barGap*p:e.barWidth?u/2:0,f=e.barRadius||0,b=d/(u+m)/o,g=f&&"roundRect"in i?"roundRect":"rect";i.beginPath();let S=0,y=0,x=0;for(let A=0;A<=o;A++){const w=Math.round(A*b);if(w>S){const k=Math.round(y*l*n),W=k+Math.round(x*l*n)||1;let O=l-k;e.barAlign==="top"?O=0:e.barAlign==="bottom"&&(O=c-W),i[g](S*(u+m),O,u,W,f),S=w,y=0,x=0}const D=Math.abs(r[A]||0),T=Math.abs(a[A]||0);D>y&&(y=D),T>x&&(x=T)}i.fill(),i.closePath()}renderLineWaveform(t,e,i,n){const r=a=>{const o=t[a]||t[0],d=o.length,{height:c}=i.canvas,l=c/2,p=i.canvas.width/d;i.moveTo(0,l);let u=0,m=0;for(let f=0;f<=d;f++){const b=Math.round(f*p);if(b>u){const S=l+(Math.round(m*l*n)||1)*(a===0?-1:1);i.lineTo(u,S),u=b,m=0}const g=Math.abs(o[f]||0);g>m&&(m=g)}i.lineTo(u,l)};i.beginPath(),r(0),r(1),i.fill(),i.closePath()}renderWaveform(t,e,i){if(i.fillStyle=this.convertColorValues(e.waveColor),e.renderFunction)return void e.renderFunction(t,i);let n=e.barHeight||1;if(e.normalize){const r=Array.from(t[0]).reduce((a,o)=>Math.max(a,Math.abs(o)),0);n=r?1/r:1}e.barWidth||e.barGap||e.barAlign?this.renderBarWaveform(t,e,i,n):this.renderLineWaveform(t,e,i,n)}renderSingleCanvas(t,e,i,n,r,a,o){const d=this.getPixelRatio(),c=document.createElement("canvas");c.width=Math.round(i*d),c.height=Math.round(n*d),c.style.width=`${i}px`,c.style.height=`${n}px`,c.style.left=`${Math.round(r)}px`,a.appendChild(c);const l=c.getContext("2d");if(this.renderWaveform(t,e,l),c.width>0&&c.height>0){const p=c.cloneNode(),u=p.getContext("2d");u.drawImage(c,0,0),u.globalCompositeOperation="source-in",u.fillStyle=this.convertColorValues(e.progressColor),u.fillRect(0,0,c.width,c.height),o.appendChild(p)}}renderMultiCanvas(t,e,i,n,r,a){const o=this.getPixelRatio(),{clientWidth:d}=this.scrollContainer,c=i/o;let l=Math.min(U.MAX_CANVAS_WIDTH,d,c),p={};if(l===0)return;if(e.barWidth||e.barGap){const g=e.barWidth||.5,S=g+(e.barGap||g/2);l%S!=0&&(l=Math.floor(l/S)*S)}const u=g=>{if(g<0||g>=m||p[g])return;p[g]=!0;const S=g*l,y=Math.min(c-S,l);if(y<=0)return;const x=t.map(A=>{const w=Math.floor(S/c*A.length),D=Math.floor((S+y)/c*A.length);return A.slice(w,D)});this.renderSingleCanvas(x,e,y,n,S,r,a)},m=Math.ceil(c/l);if(!this.isScrollable){for(let g=0;g<m;g++)u(g);return}const f=this.scrollContainer.scrollLeft/c,b=Math.floor(f*m);if(u(b-1),u(b),u(b+1),m>1){const g=this.on("scroll",()=>{const{scrollLeft:S}=this.scrollContainer,y=Math.floor(S/c*m);Object.keys(p).length>U.MAX_NODES&&(r.innerHTML="",a.innerHTML="",p={}),u(y-1),u(y),u(y+1)});this.unsubscribeOnScroll.push(g)}}renderChannel(t,e,i,n){var{overlay:r}=e,a=function(l,p){var u={};for(var m in l)Object.prototype.hasOwnProperty.call(l,m)&&p.indexOf(m)<0&&(u[m]=l[m]);if(l!=null&&typeof Object.getOwnPropertySymbols=="function"){var f=0;for(m=Object.getOwnPropertySymbols(l);f<m.length;f++)p.indexOf(m[f])<0&&Object.prototype.propertyIsEnumerable.call(l,m[f])&&(u[m[f]]=l[m[f]])}return u}(e,["overlay"]);const o=document.createElement("div"),d=this.getHeight(a.height,a.splitChannels);o.style.height=`${d}px`,r&&n>0&&(o.style.marginTop=`-${d}px`),this.canvasWrapper.style.minHeight=`${d}px`,this.canvasWrapper.appendChild(o);const c=o.cloneNode();this.progressWrapper.appendChild(c),this.renderMultiCanvas(t,a,i,d,o,c)}render(t){return R(this,void 0,void 0,function*(){var e;this.timeouts.forEach(d=>d()),this.timeouts=[],this.canvasWrapper.innerHTML="",this.progressWrapper.innerHTML="",this.options.width!=null&&(this.scrollContainer.style.width=typeof this.options.width=="number"?`${this.options.width}px`:this.options.width);const i=this.getPixelRatio(),n=this.scrollContainer.clientWidth,r=Math.ceil(t.duration*(this.options.minPxPerSec||0));this.isScrollable=r>n;const a=this.options.fillParent&&!this.isScrollable,o=(a?n:r)*i;if(this.wrapper.style.width=a?"100%":`${r}px`,this.scrollContainer.style.overflowX=this.isScrollable?"auto":"hidden",this.scrollContainer.classList.toggle("noScrollbar",!!this.options.hideScrollbar),this.cursor.style.backgroundColor=`${this.options.cursorColor||this.options.progressColor}`,this.cursor.style.width=`${this.options.cursorWidth}px`,this.audioData=t,this.emit("render"),this.options.splitChannels)for(let d=0;d<t.numberOfChannels;d++){const c=Object.assign(Object.assign({},this.options),(e=this.options.splitChannels)===null||e===void 0?void 0:e[d]);this.renderChannel([t.getChannelData(d)],c,o,d)}else{const d=[t.getChannelData(0)];t.numberOfChannels>1&&d.push(t.getChannelData(1)),this.renderChannel(d,this.options,o,0)}Promise.resolve().then(()=>this.emit("rendered"))})}reRender(){if(this.unsubscribeOnScroll.forEach(i=>i()),this.unsubscribeOnScroll=[],!this.audioData)return;const{scrollWidth:t}=this.scrollContainer,{right:e}=this.progressWrapper.getBoundingClientRect();if(this.render(this.audioData),this.isScrollable&&t!==this.scrollContainer.scrollWidth){const{right:i}=this.progressWrapper.getBoundingClientRect();let n=i-e;n*=2,n=n<0?Math.floor(n):Math.ceil(n),n/=2,this.scrollContainer.scrollLeft+=n}}zoom(t){this.options.minPxPerSec=t,this.reRender()}scrollIntoView(t,e=!1){const{scrollLeft:i,scrollWidth:n,clientWidth:r}=this.scrollContainer,a=t*n,o=i,d=i+r,c=r/2;if(this.isDragging)a+30>d?this.scrollContainer.scrollLeft+=30:a-30<o&&(this.scrollContainer.scrollLeft-=30);else{(a<o||a>d)&&(this.scrollContainer.scrollLeft=a-(this.options.autoCenter?c:0));const l=a-i-c;e&&this.options.autoCenter&&l>0&&(this.scrollContainer.scrollLeft+=Math.min(l,10))}{const l=this.scrollContainer.scrollLeft,p=l/n,u=(l+r)/n;this.emit("scroll",p,u,l,l+r)}}renderProgress(t,e){if(isNaN(t))return;const i=100*t;this.canvasWrapper.style.clipPath=`polygon(${i}% 0, 100% 0, 100% 100%, ${i}% 100%)`,this.progressWrapper.style.width=`${i}%`,this.cursor.style.left=`${i}%`,this.cursor.style.transform=`translateX(-${Math.round(i)===100?this.options.cursorWidth:0}px)`,this.isScrollable&&this.options.autoScroll&&this.scrollIntoView(t,e)}exportImage(t,e,i){return R(this,void 0,void 0,function*(){const n=this.canvasWrapper.querySelectorAll("canvas");if(!n.length)throw new Error("No waveform data");if(i==="dataURL"){const r=Array.from(n).map(a=>a.toDataURL(t,e));return Promise.resolve(r)}return Promise.all(Array.from(n).map(r=>new Promise((a,o)=>{r.toBlob(d=>{d?a(d):o(new Error("Could not export image"))},t,e)})))})}}U.MAX_CANVAS_WIDTH=8e3,U.MAX_NODES=10;class fe extends V{constructor(){super(...arguments),this.unsubscribe=()=>{}}start(){this.unsubscribe=this.on("tick",()=>{requestAnimationFrame(()=>{this.emit("tick")})}),this.emit("tick")}stop(){this.unsubscribe()}destroy(){this.unsubscribe()}}class et extends V{constructor(t=new AudioContext){super(),this.bufferNode=null,this.playStartTime=0,this.playedDuration=0,this._muted=!1,this._playbackRate=1,this._duration=void 0,this.buffer=null,this.currentSrc="",this.paused=!0,this.crossOrigin=null,this.seeking=!1,this.autoplay=!1,this.addEventListener=this.on,this.removeEventListener=this.un,this.audioContext=t,this.gainNode=this.audioContext.createGain(),this.gainNode.connect(this.audioContext.destination)}load(){return R(this,void 0,void 0,function*(){})}get src(){return this.currentSrc}set src(t){if(this.currentSrc=t,this._duration=void 0,!t)return this.buffer=null,void this.emit("emptied");fetch(t).then(e=>{if(e.status>=400)throw new Error(`Failed to fetch ${t}: ${e.status} (${e.statusText})`);return e.arrayBuffer()}).then(e=>this.currentSrc!==t?null:this.audioContext.decodeAudioData(e)).then(e=>{this.currentSrc===t&&(this.buffer=e,this.emit("loadedmetadata"),this.emit("canplay"),this.autoplay&&this.play())})}_play(){var t;if(!this.paused)return;this.paused=!1,(t=this.bufferNode)===null||t===void 0||t.disconnect(),this.bufferNode=this.audioContext.createBufferSource(),this.buffer&&(this.bufferNode.buffer=this.buffer),this.bufferNode.playbackRate.value=this._playbackRate,this.bufferNode.connect(this.gainNode);let e=this.playedDuration*this._playbackRate;(e>=this.duration||e<0)&&(e=0,this.playedDuration=0),this.bufferNode.start(this.audioContext.currentTime,e),this.playStartTime=this.audioContext.currentTime,this.bufferNode.onended=()=>{this.currentTime>=this.duration&&(this.pause(),this.emit("ended"))}}_pause(){var t;this.paused=!0,(t=this.bufferNode)===null||t===void 0||t.stop(),this.playedDuration+=this.audioContext.currentTime-this.playStartTime}play(){return R(this,void 0,void 0,function*(){this.paused&&(this._play(),this.emit("play"))})}pause(){this.paused||(this._pause(),this.emit("pause"))}stopAt(t){const e=t-this.currentTime,i=this.bufferNode;i==null||i.stop(this.audioContext.currentTime+e),i==null||i.addEventListener("ended",()=>{i===this.bufferNode&&(this.bufferNode=null,this.pause())},{once:!0})}setSinkId(t){return R(this,void 0,void 0,function*(){return this.audioContext.setSinkId(t)})}get playbackRate(){return this._playbackRate}set playbackRate(t){this._playbackRate=t,this.bufferNode&&(this.bufferNode.playbackRate.value=t)}get currentTime(){return(this.paused?this.playedDuration:this.playedDuration+(this.audioContext.currentTime-this.playStartTime))*this._playbackRate}set currentTime(t){const e=!this.paused;e&&this._pause(),this.playedDuration=t/this._playbackRate,e&&this._play(),this.emit("seeking"),this.emit("timeupdate")}get duration(){var t,e;return(t=this._duration)!==null&&t!==void 0?t:((e=this.buffer)===null||e===void 0?void 0:e.duration)||0}set duration(t){this._duration=t}get volume(){return this.gainNode.gain.value}set volume(t){this.gainNode.gain.value=t,this.emit("volumechange")}get muted(){return this._muted}set muted(t){this._muted!==t&&(this._muted=t,this._muted?this.gainNode.disconnect():this.gainNode.connect(this.audioContext.destination))}canPlayType(t){return/^(audio|video)\//.test(t)}getGainNode(){return this.gainNode}getChannelData(){const t=[];if(!this.buffer)return t;const e=this.buffer.numberOfChannels;for(let i=0;i<e;i++)t.push(this.buffer.getChannelData(i));return t}}const ge={waveColor:"#999",progressColor:"#555",cursorWidth:1,minPxPerSec:0,fillParent:!0,interact:!0,dragToSeek:!1,autoScroll:!0,autoCenter:!0,sampleRate:8e3};class z extends me{static create(t){return new z(t)}constructor(t){const e=t.media||(t.backend==="WebAudio"?new et:void 0);super({media:e,mediaControls:t.mediaControls,autoplay:t.autoplay,playbackRate:t.audioRate}),this.plugins=[],this.decodedData=null,this.stopAtPosition=null,this.subscriptions=[],this.mediaSubscriptions=[],this.abortController=null,this.options=Object.assign({},ge,t),this.timer=new fe;const i=e?void 0:this.getMediaElement();this.renderer=new U(this.options,i),this.initPlayerEvents(),this.initRendererEvents(),this.initTimerEvents(),this.initPlugins();const n=this.options.url||this.getSrc()||"";Promise.resolve().then(()=>{this.emit("init");const{peaks:r,duration:a}=this.options;(n||r&&a)&&this.load(n,r,a).catch(()=>null)})}updateProgress(t=this.getCurrentTime()){return this.renderer.renderProgress(t/this.getDuration(),this.isPlaying()),t}initTimerEvents(){this.subscriptions.push(this.timer.on("tick",()=>{if(!this.isSeeking()){const t=this.updateProgress();this.emit("timeupdate",t),this.emit("audioprocess",t),this.stopAtPosition!=null&&this.isPlaying()&&t>=this.stopAtPosition&&this.pause()}}))}initPlayerEvents(){this.isPlaying()&&(this.emit("play"),this.timer.start()),this.mediaSubscriptions.push(this.onMediaEvent("timeupdate",()=>{const t=this.updateProgress();this.emit("timeupdate",t)}),this.onMediaEvent("play",()=>{this.emit("play"),this.timer.start()}),this.onMediaEvent("pause",()=>{this.emit("pause"),this.timer.stop(),this.stopAtPosition=null}),this.onMediaEvent("emptied",()=>{this.timer.stop(),this.stopAtPosition=null}),this.onMediaEvent("ended",()=>{this.emit("timeupdate",this.getDuration()),this.emit("finish"),this.stopAtPosition=null}),this.onMediaEvent("seeking",()=>{this.emit("seeking",this.getCurrentTime())}),this.onMediaEvent("error",()=>{var t;this.emit("error",(t=this.getMediaElement().error)!==null&&t!==void 0?t:new Error("Media error")),this.stopAtPosition=null}))}initRendererEvents(){this.subscriptions.push(this.renderer.on("click",(t,e)=>{this.options.interact&&(this.seekTo(t),this.emit("interaction",t*this.getDuration()),this.emit("click",t,e))}),this.renderer.on("dblclick",(t,e)=>{this.emit("dblclick",t,e)}),this.renderer.on("scroll",(t,e,i,n)=>{const r=this.getDuration();this.emit("scroll",t*r,e*r,i,n)}),this.renderer.on("render",()=>{this.emit("redraw")}),this.renderer.on("rendered",()=>{this.emit("redrawcomplete")}),this.renderer.on("dragstart",t=>{this.emit("dragstart",t)}),this.renderer.on("dragend",t=>{this.emit("dragend",t)}));{let t;this.subscriptions.push(this.renderer.on("drag",e=>{if(!this.options.interact)return;let i;this.renderer.renderProgress(e),clearTimeout(t),this.isPlaying()?i=0:this.options.dragToSeek===!0?i=200:typeof this.options.dragToSeek=="object"&&this.options.dragToSeek!==void 0&&(i=this.options.dragToSeek.debounceTime),t=setTimeout(()=>{this.seekTo(e)},i),this.emit("interaction",e*this.getDuration()),this.emit("drag",e)}))}}initPlugins(){var t;!((t=this.options.plugins)===null||t===void 0)&&t.length&&this.options.plugins.forEach(e=>{this.registerPlugin(e)})}unsubscribePlayerEvents(){this.mediaSubscriptions.forEach(t=>t()),this.mediaSubscriptions=[]}setOptions(t){this.options=Object.assign({},this.options,t),t.duration&&!t.peaks&&(this.decodedData=j.createBuffer(this.exportPeaks(),t.duration)),t.peaks&&t.duration&&(this.decodedData=j.createBuffer(t.peaks,t.duration)),this.renderer.setOptions(this.options),t.audioRate&&this.setPlaybackRate(t.audioRate),t.mediaControls!=null&&(this.getMediaElement().controls=t.mediaControls)}registerPlugin(t){return t._init(this),this.plugins.push(t),this.subscriptions.push(t.once("destroy",()=>{this.plugins=this.plugins.filter(e=>e!==t)})),t}getWrapper(){return this.renderer.getWrapper()}getWidth(){return this.renderer.getWidth()}getScroll(){return this.renderer.getScroll()}setScroll(t){return this.renderer.setScroll(t)}setScrollTime(t){const e=t/this.getDuration();this.renderer.setScrollPercentage(e)}getActivePlugins(){return this.plugins}loadAudio(t,e,i,n){return R(this,void 0,void 0,function*(){var r;if(this.emit("load",t),!this.options.media&&this.isPlaying()&&this.pause(),this.decodedData=null,this.stopAtPosition=null,!e&&!i){const o=this.options.fetchParams||{};window.AbortController&&!o.signal&&(this.abortController=new AbortController,o.signal=(r=this.abortController)===null||r===void 0?void 0:r.signal);const d=l=>this.emit("loading",l);e=yield pe.fetchBlob(t,d,o);const c=this.options.blobMimeType;c&&(e=new Blob([e],{type:c}))}this.setSrc(t,e);const a=yield new Promise(o=>{const d=n||this.getDuration();d?o(d):this.mediaSubscriptions.push(this.onMediaEvent("loadedmetadata",()=>o(this.getDuration()),{once:!0}))});if(!t&&!e){const o=this.getMediaElement();o instanceof et&&(o.duration=a)}if(i)this.decodedData=j.createBuffer(i,a||0);else if(e){const o=yield e.arrayBuffer();this.decodedData=yield j.decode(o,this.options.sampleRate)}this.decodedData&&(this.emit("decode",this.getDuration()),this.renderer.render(this.decodedData)),this.emit("ready",this.getDuration())})}load(t,e,i){return R(this,void 0,void 0,function*(){try{return yield this.loadAudio(t,void 0,e,i)}catch(n){throw this.emit("error",n),n}})}loadBlob(t,e,i){return R(this,void 0,void 0,function*(){try{return yield this.loadAudio("",t,e,i)}catch(n){throw this.emit("error",n),n}})}zoom(t){if(!this.decodedData)throw new Error("No audio loaded");this.renderer.zoom(t),this.emit("zoom",t)}getDecodedData(){return this.decodedData}exportPeaks({channels:t=2,maxLength:e=8e3,precision:i=1e4}={}){if(!this.decodedData)throw new Error("The audio has not been decoded yet");const n=Math.min(t,this.decodedData.numberOfChannels),r=[];for(let a=0;a<n;a++){const o=this.decodedData.getChannelData(a),d=[],c=o.length/e;for(let l=0;l<e;l++){const p=o.slice(Math.floor(l*c),Math.ceil((l+1)*c));let u=0;for(let m=0;m<p.length;m++){const f=p[m];Math.abs(f)>Math.abs(u)&&(u=f)}d.push(Math.round(u*i)/i)}r.push(d)}return r}getDuration(){let t=super.getDuration()||0;return t!==0&&t!==1/0||!this.decodedData||(t=this.decodedData.duration),t}toggleInteraction(t){this.options.interact=t}setTime(t){this.stopAtPosition=null,super.setTime(t),this.updateProgress(t),this.emit("timeupdate",t)}seekTo(t){const e=this.getDuration()*t;this.setTime(e)}play(t,e){const i=Object.create(null,{play:{get:()=>super.play}});return R(this,void 0,void 0,function*(){t!=null&&this.setTime(t);const n=yield i.play.call(this);return e!=null&&(this.media instanceof et?this.media.stopAt(e):this.stopAtPosition=e),n})}playPause(){return R(this,void 0,void 0,function*(){return this.isPlaying()?this.pause():this.play()})}stop(){this.pause(),this.setTime(0)}skip(t){this.setTime(this.getCurrentTime()+t)}empty(){this.load("",[[0]],.001)}setMediaElement(t){this.unsubscribePlayerEvents(),super.setMediaElement(t),this.initPlayerEvents()}exportImage(){return R(this,arguments,void 0,function*(t="image/png",e=1,i="dataURL"){return this.renderer.exportImage(t,e,i)})}destroy(){var t;this.emit("destroy"),(t=this.abortController)===null||t===void 0||t.abort(),this.plugins.forEach(e=>e.destroy()),this.subscriptions.forEach(e=>e()),this.unsubscribePlayerEvents(),this.timer.destroy(),this.renderer.destroy(),super.destroy()}}z.BasePlugin=class extends V{constructor(s){super(),this.subscriptions=[],this.options=s}onInit(){}_init(s){this.wavesurfer=s,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach(s=>s())}},z.dom=ue;function it(s,t,e,i){return new(e||(e=Promise))(function(n,r){function a(c){try{d(i.next(c))}catch(l){r(l)}}function o(c){try{d(i.throw(c))}catch(l){r(l)}}function d(c){var l;c.done?n(c.value):(l=c.value,l instanceof e?l:new e(function(p){p(l)})).then(a,o)}d((i=i.apply(s,[])).next())})}class Pt{constructor(){this.listeners={}}on(t,e,i){if(this.listeners[t]||(this.listeners[t]=new Set),this.listeners[t].add(e),i==null?void 0:i.once){const n=()=>{this.un(t,n),this.un(t,e)};return this.on(t,n),n}return()=>this.un(t,e)}un(t,e){var i;(i=this.listeners[t])===null||i===void 0||i.delete(e)}once(t,e){return this.on(t,e,{once:!0})}unAll(){this.listeners={}}emit(t,...e){this.listeners[t]&&this.listeners[t].forEach(i=>i(...e))}}class ve extends Pt{constructor(t){super(),this.subscriptions=[],this.options=t}onInit(){}_init(t){this.wavesurfer=t,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach(t=>t())}}class be extends Pt{constructor(){super(...arguments),this.unsubscribe=()=>{}}start(){this.unsubscribe=this.on("tick",()=>{requestAnimationFrame(()=>{this.emit("tick")})}),this.emit("tick")}stop(){this.unsubscribe()}destroy(){this.unsubscribe()}}const ye=["audio/webm","audio/wav","audio/mpeg","audio/mp4","audio/mp3"];class Y extends ve{constructor(t){var e,i,n,r,a,o;super(Object.assign(Object.assign({},t),{audioBitsPerSecond:(e=t.audioBitsPerSecond)!==null&&e!==void 0?e:128e3,scrollingWaveform:(i=t.scrollingWaveform)!==null&&i!==void 0&&i,scrollingWaveformWindow:(n=t.scrollingWaveformWindow)!==null&&n!==void 0?n:5,continuousWaveform:(r=t.continuousWaveform)!==null&&r!==void 0&&r,renderRecordedAudio:(a=t.renderRecordedAudio)===null||a===void 0||a,mediaRecorderTimeslice:(o=t.mediaRecorderTimeslice)!==null&&o!==void 0?o:void 0})),this.stream=null,this.mediaRecorder=null,this.dataWindow=null,this.isWaveformPaused=!1,this.lastStartTime=0,this.lastDuration=0,this.duration=0,this.timer=new be,this.subscriptions.push(this.timer.on("tick",()=>{const d=performance.now()-this.lastStartTime;this.duration=this.isPaused()?this.duration:this.lastDuration+d,this.emit("record-progress",this.duration)}))}static create(t){return new Y(t||{})}renderMicStream(t){var e;const i=new AudioContext,n=i.createMediaStreamSource(t),r=i.createAnalyser();n.connect(r),this.options.continuousWaveform&&(r.fftSize=32);const a=r.frequencyBinCount,o=new Float32Array(a);let d=0;this.wavesurfer&&((e=this.originalOptions)!==null&&e!==void 0||(this.originalOptions=Object.assign({},this.wavesurfer.options)),this.wavesurfer.options.interact=!1,this.options.scrollingWaveform&&(this.wavesurfer.options.cursorWidth=0));const c=setInterval(()=>{var l,p,u,m;if(!this.isWaveformPaused){if(r.getFloatTimeDomainData(o),this.options.scrollingWaveform){const f=Math.floor((this.options.scrollingWaveformWindow||0)*i.sampleRate),b=Math.min(f,this.dataWindow?this.dataWindow.length+a:a),g=new Float32Array(f);if(this.dataWindow){const S=Math.max(0,f-this.dataWindow.length);g.set(this.dataWindow.slice(-b+a),S)}g.set(o,f-a),this.dataWindow=g}else if(this.options.continuousWaveform){if(!this.dataWindow){const b=this.options.continuousWaveformDuration?Math.round(100*this.options.continuousWaveformDuration):((p=(l=this.wavesurfer)===null||l===void 0?void 0:l.getWidth())!==null&&p!==void 0?p:0)*window.devicePixelRatio;this.dataWindow=new Float32Array(b)}let f=0;for(let b=0;b<a;b++){const g=Math.abs(o[b]);g>f&&(f=g)}if(d+1>this.dataWindow.length){const b=new Float32Array(2*this.dataWindow.length);b.set(this.dataWindow,0),this.dataWindow=b}this.dataWindow[d]=f,d++}else this.dataWindow=o;if(this.wavesurfer){const f=((m=(u=this.dataWindow)===null||u===void 0?void 0:u.length)!==null&&m!==void 0?m:0)/100;this.wavesurfer.load("",[this.dataWindow],this.options.scrollingWaveform?this.options.scrollingWaveformWindow:f).then(()=>{this.wavesurfer&&this.options.continuousWaveform&&(this.wavesurfer.setTime(this.getDuration()/1e3),this.wavesurfer.options.minPxPerSec||this.wavesurfer.setOptions({minPxPerSec:this.wavesurfer.getWidth()/this.wavesurfer.getDuration()}))}).catch(b=>{console.error("Error rendering real-time recording data:",b)})}}},10);return{onDestroy:()=>{clearInterval(c),n==null||n.disconnect(),i==null||i.close()},onEnd:()=>{this.isWaveformPaused=!0,clearInterval(c),this.stopMic()}}}startMic(t){return it(this,void 0,void 0,function*(){let e;try{e=yield navigator.mediaDevices.getUserMedia({audio:t==null||t})}catch(r){throw new Error("Error accessing the microphone: "+r.message)}const{onDestroy:i,onEnd:n}=this.renderMicStream(e);return this.subscriptions.push(this.once("destroy",i)),this.subscriptions.push(this.once("record-end",n)),this.stream=e,e})}stopMic(){this.stream&&(this.stream.getTracks().forEach(t=>t.stop()),this.stream=null,this.mediaRecorder=null)}startRecording(t){return it(this,void 0,void 0,function*(){const e=this.stream||(yield this.startMic(t));this.dataWindow=null;const i=this.mediaRecorder||new MediaRecorder(e,{mimeType:this.options.mimeType||ye.find(a=>MediaRecorder.isTypeSupported(a)),audioBitsPerSecond:this.options.audioBitsPerSecond});this.mediaRecorder=i,this.stopRecording();const n=[];i.ondataavailable=a=>{a.data.size>0&&n.push(a.data),this.emit("record-data-available",a.data)};const r=a=>{var o;const d=new Blob(n,{type:i.mimeType});this.emit(a,d),this.options.renderRecordedAudio&&(this.applyOriginalOptionsIfNeeded(),(o=this.wavesurfer)===null||o===void 0||o.load(URL.createObjectURL(d)))};i.onpause=()=>r("record-pause"),i.onstop=()=>r("record-end"),i.start(this.options.mediaRecorderTimeslice),this.lastStartTime=performance.now(),this.lastDuration=0,this.duration=0,this.isWaveformPaused=!1,this.timer.start(),this.emit("record-start")})}getDuration(){return this.duration}isRecording(){var t;return((t=this.mediaRecorder)===null||t===void 0?void 0:t.state)==="recording"}isPaused(){var t;return((t=this.mediaRecorder)===null||t===void 0?void 0:t.state)==="paused"}isActive(){var t;return((t=this.mediaRecorder)===null||t===void 0?void 0:t.state)!=="inactive"}stopRecording(){var t;this.isActive()&&((t=this.mediaRecorder)===null||t===void 0||t.stop(),this.timer.stop())}pauseRecording(){var t,e;this.isRecording()&&(this.isWaveformPaused=!0,(t=this.mediaRecorder)===null||t===void 0||t.requestData(),(e=this.mediaRecorder)===null||e===void 0||e.pause(),this.timer.stop(),this.lastDuration=this.duration)}resumeRecording(){var t;this.isPaused()&&(this.isWaveformPaused=!1,(t=this.mediaRecorder)===null||t===void 0||t.resume(),this.timer.start(),this.lastStartTime=performance.now(),this.emit("record-resume"))}static getAvailableAudioDevices(){return it(this,void 0,void 0,function*(){return navigator.mediaDevices.enumerateDevices().then(t=>t.filter(e=>e.kind==="audioinput"))})}destroy(){this.applyOriginalOptionsIfNeeded(),super.destroy(),this.stopRecording(),this.stopMic()}applyOriginalOptionsIfNeeded(){this.wavesurfer&&this.originalOptions&&(this.wavesurfer.setOptions(this.originalOptions),delete this.originalOptions)}}const we=async({files:s,uploadClient:t,widgetMgr:e,widgetInfo:i,fragmentId:n})=>{let r=[];try{r=await t.fetchFileURLs(s)}catch(c){return{successfulUploads:[],failedUploads:s.map(l=>({file:l,error:mt(c)}))}}const a=$t(s,r),o=[],d=[];return await Promise.all(a.map(async([c,l])=>{if(!c||!l||!l.uploadUrl||!l.fileId)return{file:c,fileUrl:l,error:new Error("No upload URL found")};try{await t.uploadFile({id:l.fileId,formId:i.formId||""},l.uploadUrl,c),o.push({fileUrl:l,file:c})}catch(p){const u=mt(p);d.push({file:c,error:u})}})),e.setFileUploaderStateValue(i,new Ht({uploadedFileInfo:o.map(({file:c,fileUrl:l})=>new Vt({fileId:l.fileId,fileUrls:l,name:c.name,size:c.size}))}),{fromUi:!0},n),{successfulUploads:o,failedUploads:d}},st=({widgetMgr:s,id:t,formId:e,key:i,defaultValue:n})=>{h.useEffect(()=>{const l=s.getElementState(t,i);X(l)&&q(n)&&s.setElementState(t,i,n)},[s,t,i,n]);const[r,a]=h.useState(s.getElementState(t,i)??n),o=h.useCallback(l=>{s.setElementState(t,i,l),a(l)},[s,t,i]),d=h.useMemo(()=>({formId:e||""}),[e]),c=h.useCallback(()=>o(n),[n,o]);return re({element:d,widgetMgr:s,onFormCleared:c}),[r,o]},Se=(s,t)=>{const{libConfig:{enforceDownloadInNewTab:e=!1}}=h.useContext(jt);return h.useCallback(()=>{if(!s)return;const n=ce({enforceDownloadInNewTab:e,url:s,filename:t});n.style.display="none",document.body.appendChild(n),n.click(),document.body.removeChild(n)},[s,e,t])},Ce=P("div",{target:"e1f2vvgs0"})(),vt=P("div",{target:"e1f2vvgs1"})(({theme:s})=>({height:s.sizes.largestElementHeight,width:"100%",background:s.colors.secondaryBg,borderRadius:s.radii.default,marginBottom:s.spacing.twoXS,display:"flex",alignItems:"center",position:"relative",paddingLeft:s.spacing.xs,paddingRight:s.spacing.sm,border:s.colors.widgetBorderColor?`${s.sizes.borderWidth} solid ${s.colors.widgetBorderColor}`:void 0})),Ee=P("div",{target:"e1f2vvgs2"})({flex:1}),Re=P("div",{target:"e1f2vvgs3"})(({show:s})=>({display:s?"block":"none"})),Pe=P("span",{target:"e1f2vvgs4"})(({theme:s,isPlayingOrRecording:t})=>({margin:s.spacing.sm,fontFamily:s.fonts.monospace,color:t?s.colors.bodyText:s.colors.fadedText60,backgroundColor:s.colors.secondaryBg,fontSize:s.fontSizes.sm})),xt=P("div",{target:"e1f2vvgs5"})({width:"100%",textAlign:"center",overflow:"hidden"}),At=P("span",{target:"e1f2vvgs6"})(({theme:s})=>({color:s.colors.bodyText})),xe=P("a",{target:"e1f2vvgs7"})(({theme:s})=>({color:s.colors.link,textDecoration:"underline"})),Ae=P("div",{target:"e1f2vvgs8"})(({theme:s})=>({height:s.sizes.largestElementHeight,display:"flex",justifyContent:"center",alignItems:"center"})),De=P("div",{target:"e1f2vvgs9"})(({theme:s})=>{const t="0.625em";return{opacity:.2,width:"100%",height:t,backgroundSize:t,backgroundImage:`radial-gradient(${s.colors.fadedText10} 40%, transparent 40%)`,backgroundRepeat:"repeat"}}),Te=P("span",{target:"e1f2vvgs10"})(({theme:s})=>({"& > button":{color:s.colors.primary,padding:s.spacing.threeXS},"& > button:hover, & > button:focus":{color:s.colors.red}})),We=P("span",{target:"e1f2vvgs11"})(({theme:s})=>({"& > button":{padding:s.spacing.threeXS,color:s.colors.fadedText60},"& > button:hover, & > button:focus":{color:s.colors.bodyText}})),Dt=P("span",{target:"e1f2vvgs12"})(({theme:s})=>({"& > button":{padding:s.spacing.threeXS,color:s.colors.fadedText60},"& > button:hover, & > button:focus":{color:s.colors.bodyText}})),nt=P("div",{target:"e1f2vvgs13"})(({theme:s})=>({display:"flex",justifyContent:"center",alignItems:"center",flexGrow:0,flexShrink:1,padding:s.spacing.xs,gap:s.spacing.twoXS,marginRight:s.spacing.twoXS})),Me=P("div",{target:"e1f2vvgs14"})(({theme:s})=>({marginLeft:s.spacing.sm})),ke=P(Gt,{target:"e1f2vvgs15"})(({theme:s})=>({fontSize:s.fontSizes.sm,width:s.sizes.spinnerSize,height:s.sizes.spinnerSize,borderWidth:s.sizes.spinnerThickness,justifyContents:"center",padding:s.spacing.none,margin:s.spacing.none,borderColor:s.colors.borderColor,borderTopColor:s.colors.secondary,flexGrow:0,flexShrink:0})),Oe=()=>N(xt,{children:[v(At,{children:"This app would like to use your microphone."})," ",v(xe,{href:Xt,children:"Learn how to allow access."})]}),Ie=h.memo(Oe),Le=()=>v(Ae,{children:v(De,{})}),Be=h.memo(Le),Ne=4,Ue=4,Fe=4,ze=8,_e=0,G="00:00",bt=s=>{const t=Math.floor(s/1e3),e=Math.floor(t/60),i=Math.floor(e/60),n=t%60,r=e%60,a=n.toString().padStart(2,"0"),o=r.toString().padStart(2,"0"),d=i.toString().padStart(2,"0");return e<60?`${o}:${a}`:`${d}:${o}:${a}`},_=({onClick:s,disabled:t,ariaLabel:e,iconContent:i})=>v(Kt,{kind:qt.BORDERLESS_ICON,onClick:s,disabled:t,"aria-label":e,containerWidth:!0,"data-testid":"stAudioInputActionButton",children:v(Yt,{content:i,size:"lg",color:"inherit"})}),$e=({disabled:s,stopRecording:t})=>v(Te,{children:v(_,{onClick:t,disabled:s,ariaLabel:"Stop recording",iconContent:Et})}),He=({disabled:s,isPlaying:t,onClickPlayPause:e})=>v(Dt,{children:t?v(_,{onClick:e,disabled:s,ariaLabel:"Pause",iconContent:wt}):v(_,{onClick:e,disabled:s,ariaLabel:"Play",iconContent:St})}),Ve=({disabled:s,startRecording:t})=>v(We,{children:v(_,{onClick:t,disabled:s,ariaLabel:"Record",iconContent:yt})}),je=({onClick:s})=>v(Dt,{children:v(_,{disabled:!1,onClick:s,ariaLabel:"Reset",iconContent:Ct})}),Ge=({disabled:s,isRecording:t,isPlaying:e,isUploading:i,isError:n,recordingUrlExists:r,startRecording:a,stopRecording:o,onClickPlayPause:d,onClear:c})=>n?v(nt,{children:v(je,{onClick:c})}):i?v(nt,{children:v(ke,{"aria-label":"Uploading"})}):N(nt,{children:[t?v($e,{disabled:s,stopRecording:o}):v(Ve,{disabled:s,startRecording:a}),r&&v(He,{disabled:s,isPlaying:e,onClickPlayPause:d})]}),Xe=h.memo(Ge),qe=Jt.getLogger("convertAudioToWav");async function Ye(s){const t=new window.AudioContext,e=await s.arrayBuffer();let i;try{i=await t.decodeAudioData(e)}catch(m){qe.error(m);return}const n=44,r=i.numberOfChannels,a=i.sampleRate,o=i.length*r*2+n,d=new ArrayBuffer(o),c=new DataView(d),l={0:{type:"string",value:"RIFF"},4:{type:"uint32",value:o-8},8:{type:"string",value:"WAVE"},12:{type:"string",value:"fmt "},16:{type:"uint32",value:16},20:{type:"uint16",value:1},22:{type:"uint16",value:r},24:{type:"uint32",value:a},28:{type:"uint32",value:a*r*2},32:{type:"uint16",value:r*2},34:{type:"uint16",value:16},36:{type:"string",value:"data"},40:{type:"uint32",value:i.length*r*2}};Object.entries(l).forEach(([m,{type:f,value:b}])=>{const g=parseInt(m,10);f==="string"?Ke(c,g,b):f==="uint32"?c.setUint32(g,b,!0):f==="uint16"&&c.setUint16(g,b,!0)});let p=n;for(let m=0;m<i.length;m++)for(let f=0;f<r;f++){const b=Math.max(-1,Math.min(1,i.getChannelData(f)[m]));c.setInt16(p,b*32767,!0),p+=2}const u=new Uint8Array(d);return new Blob([u],{type:"audio/wav"})}function Ke(s,t,e){for(let i=0;i<e.length;i++)s.setUint8(t+i,e.charCodeAt(i))}const Je=()=>v(xt,{children:v(At,{children:"An error has occurred, please try again."})}),Qe=h.memo(Je),Ze=({element:s,uploadClient:t,widgetMgr:e,fragmentId:i,disabled:n})=>{var pt;const r=Qt(),a=le(r),[o,d]=h.useState(null),c=h.useRef(null),[l,p]=st({widgetMgr:e,id:s.id,key:"deleteFileUrl",defaultValue:null}),[u,m]=h.useState(null),[f,b]=h.useState([]),[g,S]=h.useState(null),[y,x]=st({widgetMgr:e,id:s.id,key:"recordingUrl",defaultValue:null}),[,A]=h.useState(0),w=()=>{A(C=>C+1)},[D,T]=h.useState(G),[k,W]=st({widgetMgr:e,id:s.id,formId:s.formId,key:"recordingTime",defaultValue:G}),[O,F]=h.useState(!1),[L,Tt]=h.useState(!1),[rt,Wt]=h.useState(!1),[Mt,ot]=h.useState(!1),[K,J]=h.useState(!1),at=s.id,I=s.formId,lt=h.useCallback(async C=>{ot(!0),q(I)&&e.setFormsWithUploadsInProgress(new Set([I]));let E;if(C.type==="audio/wav"?E=C:E=await Ye(C),!E){J(!0);return}const M=URL.createObjectURL(E),Ut=new Date().toISOString().slice(0,16).replace(":","-"),Ft=new File([E],`${Ut}_audio.wav`,{type:E.type});x(M),we({files:[Ft],uploadClient:t,widgetMgr:e,widgetInfo:{id:at,formId:I},fragmentId:i}).then(({successfulUploads:zt,failedUploads:_t})=>{if(_t.length>0){J(!0);return}const Z=zt[0];Z&&Z.fileUrl.deleteUrl&&p(Z.fileUrl.deleteUrl)}).finally(()=>{q(I)&&e.setFormsWithUploadsInProgress(new Set),ot(!1)})},[x,t,e,at,I,i,p]),B=h.useCallback(({updateWidgetManager:C,deleteFile:E})=>{X(o)||X(l)||(x(null),o.empty(),E&&t.deleteFile(l),p(null),T(G),W(G),C&&e.setFileUploaderStateValue(s,{},{fromUi:!0},i),F(!1),q(y)&&URL.revokeObjectURL(y))},[l,y,t,o,s,e,i,W,x,p]);h.useEffect(()=>{if(X(I))return;const C=new oe;return C.manageFormClearListener(e,I,()=>B({updateWidgetManager:!0,deleteFile:!1})),()=>C.disconnect()},[I,B,e]);const ct=h.useCallback(()=>{if(c.current===null)return;const C=z.create({container:c.current,waveColor:y?tt(r.colors.fadedText40,r.colors.secondaryBg):r.colors.primary,progressColor:r.colors.bodyText,height:Zt(r.sizes.largestElementHeight)-2*Ne,barWidth:Ue,barGap:Fe,barRadius:ze,cursorWidth:_e,url:y??void 0});C.on("timeupdate",M=>{T(bt(M*1e3))}),C.on("pause",()=>{w()});const E=C.registerPlugin(Y.create({scrollingWaveform:!1,renderRecordedAudio:!0}));return E.on("record-end",M=>{lt(M)}),E.on("record-progress",M=>{W(bt(M))}),d(C),m(E),()=>{C&&C.destroy(),E&&E.destroy()}},[lt]);h.useEffect(()=>ct(),[ct]),h.useEffect(()=>{te(a,r)||o==null||o.setOptions({waveColor:y?tt(r.colors.fadedText40,r.colors.secondaryBg):r.colors.primary,progressColor:r.colors.bodyText})},[r,a,y,o]);const kt=h.useCallback(()=>{o&&(o.playPause(),F(!0),w())},[o]),Ot=h.useCallback(async()=>{let C=g;rt||(await navigator.mediaDevices.getUserMedia({audio:!0}).then(()=>Y.getAvailableAudioDevices().then(E=>{if(b(E),E.length>0){const{deviceId:M}=E[0];S(M),C=M}})).catch(E=>{Tt(!0)}),Wt(!0)),!(!u||!C||!o)&&(o.setOptions({waveColor:r.colors.primary}),y&&B({updateWidgetManager:!1,deleteFile:!0}),u.startRecording({deviceId:C}).then(()=>{w()}))},[g,u,r,o,y,B,rt]),It=h.useCallback(()=>{u&&(u.stopRecording(),o==null||o.setOptions({waveColor:tt(r.colors.fadedText40,r.colors.secondaryBg)}))},[u,o,r]),Lt=Se(y,"recording.wav"),Q=!!(u!=null&&u.isRecording()),dt=!!(o!=null&&o.isPlaying()),Bt=Q||dt,ht=!Q&&!y&&!L,Nt=L||ht||K,ut=n||L;return N(Ce,{className:"stAudioInput","data-testid":"stAudioInput",children:[v(ne,{label:s.label,disabled:ut,labelVisibility:ee((pt=s.labelVisibility)==null?void 0:pt.value),children:s.help&&v(Me,{children:v(ie,{content:s.help,placement:se.TOP})})}),N(vt,{children:[N(ae,{isFullScreen:!1,disableFullscreenMode:!0,target:vt,children:[y&&v(ft,{label:"Download as WAV",icon:de,onClick:()=>Lt()}),l&&v(ft,{label:"Clear recording",icon:he,onClick:()=>B({updateWidgetManager:!0,deleteFile:!0})})]}),v(Xe,{isRecording:Q,isPlaying:dt,isUploading:Mt,isError:K,recordingUrlExists:!!y,startRecording:Ot,stopRecording:It,onClickPlayPause:kt,onClear:()=>{B({updateWidgetManager:!1,deleteFile:!0}),J(!1)},disabled:ut}),N(Ee,{children:[K&&v(Qe,{}),ht&&v(Be,{}),L&&v(Ie,{}),v(Re,{"data-testid":"stAudioInputWaveSurfer",ref:c,show:!Nt})]}),v(Pe,{isPlayingOrRecording:Bt,"data-testid":"stAudioInputWaveformTimeCode",children:O?D:k})]})]})},ai=h.memo(Ze);export{ai as default};
