import{r as o,aN as U,z as k,co as x,C as A,j as l,bp as D,bD as L,bq as P,b7 as B,br as j}from"./index.C1NIn1Y2.js";import{u as _}from"./uniqueId.O0UbJ2Bu.js";import{u as q,a as w,b as K}from"./useOnInputChange.Cxh6ExEn.js";import{I as N}from"./InputInstructions.DaZ89mzH.js";import{a as O}from"./useBasicWidgetState.Ci89jaH5.js";import{T as $}from"./textarea.QKjxR64N.js";import"./inputUtils.CQWz5UKz.js";import"./FormClearHelper.D1M9GM_c.js";import"./base-input.BJ4qsfSq.js";const T=(a,t)=>a.getStringValue(t)??t.default??null,G=a=>a.default??null,J=a=>a.value??null,Q=(a,t,s,u)=>{t.setStringValue(a,s.value,{fromUi:s.fromUi},u)},X=({disabled:a,element:t,widgetMgr:s,fragmentId:u})=>{var g;const h=o.useRef(_("text_area_")).current,[W,S]=U(),[r,n]=o.useState(!1),[C,b]=o.useState(!1),[i,d]=o.useState(()=>T(s,t)??null),I=o.useCallback(()=>{d(t.default??null),n(!0)},[t]),[V,c]=O({getStateFromWidgetMgr:T,getDefaultStateFromProto:G,getCurrStateFromProto:J,updateWidgetMgrState:Q,element:t,widgetMgr:s,fragmentId:u,onFormCleared:I});q(V,i,d,r);const e=k(),m=o.useCallback(()=>{n(!1),c({value:i,fromUi:!0})},[i,c]),y=o.useCallback(()=>{r&&m(),b(!1)},[r,m]),F=o.useCallback(()=>{b(!0)},[]),v=w({formId:t.formId,maxChars:t.maxChars,setDirty:n,setUiValue:d,setValueWithSource:c}),E=K(t.formId,m,r,s,u,!0),{height:f,placeholder:z,formId:p}=t,R=x({formId:p})?s.allowFormEnterToSubmit(p):r,H=C&&W>e.breakpoints.hideWidgetDetails;return A("div",{className:"stTextArea","data-testid":"stTextArea",ref:S,children:[l(j,{label:t.label,disabled:a,labelVisibility:D((g=t.labelVisibility)==null?void 0:g.value),htmlFor:h,children:t.help&&l(L,{children:l(P,{content:t.help,placement:B.TOP_RIGHT})})}),l($,{value:i??"",placeholder:z,onBlur:y,onFocus:F,onChange:v,onKeyDown:E,"aria-label":t.label,disabled:a,id:h,overrides:{Input:{style:{lineHeight:e.lineHeights.inputWidget,height:f?`${f}px`:"",minHeight:e.sizes.largestElementHeight,resize:"vertical",paddingRight:e.spacing.md,paddingLeft:e.spacing.md,paddingBottom:e.spacing.md,paddingTop:e.spacing.md,"::placeholder":{color:e.colors.fadedText60}}},Root:{props:{"data-testid":"stTextAreaRootElement"},style:{borderLeftWidth:e.sizes.borderWidth,borderRightWidth:e.sizes.borderWidth,borderTopWidth:e.sizes.borderWidth,borderBottomWidth:e.sizes.borderWidth}}}}),H&&l(N,{dirty:r,value:i??"",maxLength:t.maxChars,type:"multiline",inForm:x({formId:p}),allowEnterToSubmit:R})]})},it=o.memo(X);export{it as default};
