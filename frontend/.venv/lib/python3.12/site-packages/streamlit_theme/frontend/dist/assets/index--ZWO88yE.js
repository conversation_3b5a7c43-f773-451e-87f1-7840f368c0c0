(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))i(r);new MutationObserver(r=>{for(const s of r)if(s.type==="childList")for(const o of s.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&i(o)}).observe(document,{childList:!0,subtree:!0});function n(r){const s={};return r.integrity&&(s.integrity=r.integrity),r.referrerPolicy&&(s.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?s.credentials="include":r.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function i(r){if(r.ep)return;r.ep=!0;const s=n(r);fetch(r.href,s)}})();function Lo(e,t){const n=Object.create(null),i=e.split(",");for(let r=0;r<i.length;r++)n[i[r]]=!0;return t?r=>!!n[r.toLowerCase()]:r=>!!n[r]}const dt={},Gn=[],Ee=()=>{},nd=()=>!1,hs=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Uo=e=>e.startsWith("onUpdate:"),Ft=Object.assign,Po=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},id=Object.prototype.hasOwnProperty,q=(e,t)=>id.call(e,t),R=Array.isArray,ti=e=>ps(e)==="[object Map]",qc=e=>ps(e)==="[object Set]",V=e=>typeof e=="function",Bt=e=>typeof e=="string",bi=e=>typeof e=="symbol",gt=e=>e!==null&&typeof e=="object",Zc=e=>(gt(e)||V(e))&&V(e.then)&&V(e.catch),Xc=Object.prototype.toString,ps=e=>Xc.call(e),rd=e=>ps(e).slice(8,-1),Qc=e=>ps(e)==="[object Object]",jo=e=>Bt(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,wr=Lo(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),ys=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},sd=/-(\w)/g,Ce=ys(e=>e.replace(sd,(t,n)=>n?n.toUpperCase():"")),od=/\B([A-Z])/g,_i=ys(e=>e.replace(od,"-$1").toLowerCase()),ms=ys(e=>e.charAt(0).toUpperCase()+e.slice(1)),ks=ys(e=>e?`on${ms(e)}`:""),jn=(e,t)=>!Object.is(e,t),Ws=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},Nr=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},ad=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Ya;const so=()=>Ya||(Ya=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function $o(e){if(R(e)){const t={};for(let n=0;n<e.length;n++){const i=e[n],r=Bt(i)?fd(i):$o(i);if(r)for(const s in r)t[s]=r[s]}return t}else if(Bt(e)||gt(e))return e}const cd=/;(?![^(]*\))/g,ld=/:([^]+)/,ud=/\/\*[^]*?\*\//g;function fd(e){const t={};return e.replace(ud,"").split(cd).forEach(n=>{if(n){const i=n.split(ld);i.length>1&&(t[i[0].trim()]=i[1].trim())}}),t}function Vo(e){let t="";if(Bt(e))t=e;else if(R(e))for(let n=0;n<e.length;n++){const i=Vo(e[n]);i&&(t+=i+" ")}else if(gt(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const dd="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",hd=Lo(dd);function Gc(e){return!!e||e===""}const pd=e=>Bt(e)?e:e==null?"":R(e)||gt(e)&&(e.toString===Xc||!V(e.toString))?JSON.stringify(e,tl,2):String(e),tl=(e,t)=>t&&t.__v_isRef?tl(e,t.value):ti(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[i,r],s)=>(n[Hs(i,s)+" =>"]=r,n),{})}:qc(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Hs(n))}:bi(t)?Hs(t):gt(t)&&!R(t)&&!Qc(t)?String(t):t,Hs=(e,t="")=>{var n;return bi(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};let ae;class yd{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this.parent=ae,!t&&ae&&(this.index=(ae.scopes||(ae.scopes=[])).push(this)-1)}get active(){return this._active}run(t){if(this._active){const n=ae;try{return ae=this,t()}finally{ae=n}}}on(){ae=this}off(){ae=this.parent}stop(t){if(this._active){let n,i;for(n=0,i=this.effects.length;n<i;n++)this.effects[n].stop();for(n=0,i=this.cleanups.length;n<i;n++)this.cleanups[n]();if(this.scopes)for(n=0,i=this.scopes.length;n<i;n++)this.scopes[n].stop(!0);if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0,this._active=!1}}}function md(e,t=ae){t&&t.active&&t.effects.push(e)}function gd(){return ae}const zo=e=>{const t=new Set(e);return t.w=0,t.n=0,t},el=e=>(e.w&un)>0,nl=e=>(e.n&un)>0,bd=({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=un},_d=e=>{const{deps:t}=e;if(t.length){let n=0;for(let i=0;i<t.length;i++){const r=t[i];el(r)&&!nl(r)?r.delete(e):t[n++]=r,r.w&=~un,r.n&=~un}t.length=n}},oo=new WeakMap;let xi=0,un=1;const ao=30;let ue;const Rn=Symbol(""),co=Symbol("");class ko{constructor(t,n=null,i){this.fn=t,this.scheduler=n,this.active=!0,this.deps=[],this.parent=void 0,md(this,i)}run(){if(!this.active)return this.fn();let t=ue,n=cn;for(;t;){if(t===this)return;t=t.parent}try{return this.parent=ue,ue=this,cn=!0,un=1<<++xi,xi<=ao?bd(this):Ka(this),this.fn()}finally{xi<=ao&&_d(this),un=1<<--xi,ue=this.parent,cn=n,this.parent=void 0,this.deferStop&&this.stop()}}stop(){ue===this?this.deferStop=!0:this.active&&(Ka(this),this.onStop&&this.onStop(),this.active=!1)}}function Ka(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let cn=!0;const il=[];function vi(){il.push(cn),cn=!1}function wi(){const e=il.pop();cn=e===void 0?!0:e}function zt(e,t,n){if(cn&&ue){let i=oo.get(e);i||oo.set(e,i=new Map);let r=i.get(n);r||i.set(n,r=zo()),rl(r)}}function rl(e,t){let n=!1;xi<=ao?nl(e)||(e.n|=un,n=!el(e)):n=!e.has(ue),n&&(e.add(ue),ue.deps.push(e))}function Ke(e,t,n,i,r,s){const o=oo.get(e);if(!o)return;let a=[];if(t==="clear")a=[...o.values()];else if(n==="length"&&R(e)){const c=Number(i);o.forEach((l,f)=>{(f==="length"||!bi(f)&&f>=c)&&a.push(l)})}else switch(n!==void 0&&a.push(o.get(n)),t){case"add":R(e)?jo(n)&&a.push(o.get("length")):(a.push(o.get(Rn)),ti(e)&&a.push(o.get(co)));break;case"delete":R(e)||(a.push(o.get(Rn)),ti(e)&&a.push(o.get(co)));break;case"set":ti(e)&&a.push(o.get(Rn));break}if(a.length===1)a[0]&&lo(a[0]);else{const c=[];for(const l of a)l&&c.push(...l);lo(zo(c))}}function lo(e,t){const n=R(e)?e:[...e];for(const i of n)i.computed&&Ja(i);for(const i of n)i.computed||Ja(i)}function Ja(e,t){(e!==ue||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}const vd=Lo("__proto__,__v_isRef,__isVue"),sl=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(bi)),qa=wd();function wd(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...n){const i=Q(this);for(let s=0,o=this.length;s<o;s++)zt(i,"get",s+"");const r=i[t](...n);return r===-1||r===!1?i[t](...n.map(Q)):r}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...n){vi();const i=Q(this)[t].apply(this,n);return wi(),i}}),e}function Sd(e){const t=Q(this);return zt(t,"has",e),t.hasOwnProperty(e)}class ol{constructor(t=!1,n=!1){this._isReadonly=t,this._shallow=n}get(t,n,i){const r=this._isReadonly,s=this._shallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return s;if(n==="__v_raw")return i===(r?s?Rd:ul:s?ll:cl).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(i)?t:void 0;const o=R(t);if(!r){if(o&&q(qa,n))return Reflect.get(qa,n,i);if(n==="hasOwnProperty")return Sd}const a=Reflect.get(t,n,i);return(bi(n)?sl.has(n):vd(n))||(r||zt(t,"get",n),s)?a:kt(a)?o&&jo(n)?a:a.value:gt(a)?r?fl(a):Yo(a):a}}class al extends ol{constructor(t=!1){super(!1,t)}set(t,n,i,r){let s=t[n];if(!this._shallow){const c=ci(s);if(!Er(i)&&!ci(i)&&(s=Q(s),i=Q(i)),!R(t)&&kt(s)&&!kt(i))return c?!1:(s.value=i,!0)}const o=R(t)&&jo(n)?Number(n)<t.length:q(t,n),a=Reflect.set(t,n,i,r);return t===Q(r)&&(o?jn(i,s)&&Ke(t,"set",n,i):Ke(t,"add",n,i)),a}deleteProperty(t,n){const i=q(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&i&&Ke(t,"delete",n,void 0),r}has(t,n){const i=Reflect.has(t,n);return(!bi(n)||!sl.has(n))&&zt(t,"has",n),i}ownKeys(t){return zt(t,"iterate",R(t)?"length":Rn),Reflect.ownKeys(t)}}class Id extends ol{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Bd=new al,Od=new Id,Ad=new al(!0),Wo=e=>e,gs=e=>Reflect.getPrototypeOf(e);function dr(e,t,n=!1,i=!1){e=e.__v_raw;const r=Q(e),s=Q(t);n||(jn(t,s)&&zt(r,"get",t),zt(r,"get",s));const{has:o}=gs(r),a=i?Wo:n?Jo:$i;if(o.call(r,t))return a(e.get(t));if(o.call(r,s))return a(e.get(s));e!==r&&e.get(t)}function hr(e,t=!1){const n=this.__v_raw,i=Q(n),r=Q(e);return t||(jn(e,r)&&zt(i,"has",e),zt(i,"has",r)),e===r?n.has(e):n.has(e)||n.has(r)}function pr(e,t=!1){return e=e.__v_raw,!t&&zt(Q(e),"iterate",Rn),Reflect.get(e,"size",e)}function Za(e){e=Q(e);const t=Q(this);return gs(t).has.call(t,e)||(t.add(e),Ke(t,"add",e,e)),this}function Xa(e,t){t=Q(t);const n=Q(this),{has:i,get:r}=gs(n);let s=i.call(n,e);s||(e=Q(e),s=i.call(n,e));const o=r.call(n,e);return n.set(e,t),s?jn(t,o)&&Ke(n,"set",e,t):Ke(n,"add",e,t),this}function Qa(e){const t=Q(this),{has:n,get:i}=gs(t);let r=n.call(t,e);r||(e=Q(e),r=n.call(t,e)),i&&i.call(t,e);const s=t.delete(e);return r&&Ke(t,"delete",e,void 0),s}function Ga(){const e=Q(this),t=e.size!==0,n=e.clear();return t&&Ke(e,"clear",void 0,void 0),n}function yr(e,t){return function(i,r){const s=this,o=s.__v_raw,a=Q(o),c=t?Wo:e?Jo:$i;return!e&&zt(a,"iterate",Rn),o.forEach((l,f)=>i.call(r,c(l),c(f),s))}}function mr(e,t,n){return function(...i){const r=this.__v_raw,s=Q(r),o=ti(s),a=e==="entries"||e===Symbol.iterator&&o,c=e==="keys"&&o,l=r[e](...i),f=n?Wo:t?Jo:$i;return!t&&zt(s,"iterate",c?co:Rn),{next(){const{value:p,done:m}=l.next();return m?{value:p,done:m}:{value:a?[f(p[0]),f(p[1])]:f(p),done:m}},[Symbol.iterator](){return this}}}}function nn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Fd(){const e={get(s){return dr(this,s)},get size(){return pr(this)},has:hr,add:Za,set:Xa,delete:Qa,clear:Ga,forEach:yr(!1,!1)},t={get(s){return dr(this,s,!1,!0)},get size(){return pr(this)},has:hr,add:Za,set:Xa,delete:Qa,clear:Ga,forEach:yr(!1,!0)},n={get(s){return dr(this,s,!0)},get size(){return pr(this,!0)},has(s){return hr.call(this,s,!0)},add:nn("add"),set:nn("set"),delete:nn("delete"),clear:nn("clear"),forEach:yr(!0,!1)},i={get(s){return dr(this,s,!0,!0)},get size(){return pr(this,!0)},has(s){return hr.call(this,s,!0)},add:nn("add"),set:nn("set"),delete:nn("delete"),clear:nn("clear"),forEach:yr(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(s=>{e[s]=mr(s,!1,!1),n[s]=mr(s,!0,!1),t[s]=mr(s,!1,!0),i[s]=mr(s,!0,!0)}),[e,n,t,i]}const[Dd,Td,xd,Md]=Fd();function Ho(e,t){const n=t?e?Md:xd:e?Td:Dd;return(i,r,s)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?i:Reflect.get(q(n,r)&&r in i?n:i,r,s)}const Nd={get:Ho(!1,!1)},Ed={get:Ho(!1,!0)},Cd={get:Ho(!0,!1)},cl=new WeakMap,ll=new WeakMap,ul=new WeakMap,Rd=new WeakMap;function Ld(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Ud(e){return e.__v_skip||!Object.isExtensible(e)?0:Ld(rd(e))}function Yo(e){return ci(e)?e:Ko(e,!1,Bd,Nd,cl)}function Pd(e){return Ko(e,!1,Ad,Ed,ll)}function fl(e){return Ko(e,!0,Od,Cd,ul)}function Ko(e,t,n,i,r){if(!gt(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const s=r.get(e);if(s)return s;const o=Ud(e);if(o===0)return e;const a=new Proxy(e,o===2?i:n);return r.set(e,a),a}function ei(e){return ci(e)?ei(e.__v_raw):!!(e&&e.__v_isReactive)}function ci(e){return!!(e&&e.__v_isReadonly)}function Er(e){return!!(e&&e.__v_isShallow)}function dl(e){return ei(e)||ci(e)}function Q(e){const t=e&&e.__v_raw;return t?Q(t):e}function hl(e){return Nr(e,"__v_skip",!0),e}const $i=e=>gt(e)?Yo(e):e,Jo=e=>gt(e)?fl(e):e;function pl(e){cn&&ue&&(e=Q(e),rl(e.dep||(e.dep=zo())))}function yl(e,t){e=Q(e);const n=e.dep;n&&lo(n)}function kt(e){return!!(e&&e.__v_isRef===!0)}function tc(e){return jd(e,!1)}function jd(e,t){return kt(e)?e:new $d(e,t)}class $d{constructor(t,n){this.__v_isShallow=n,this.dep=void 0,this.__v_isRef=!0,this._rawValue=n?t:Q(t),this._value=n?t:$i(t)}get value(){return pl(this),this._value}set value(t){const n=this.__v_isShallow||Er(t)||ci(t);t=n?t:Q(t),jn(t,this._rawValue)&&(this._rawValue=t,this._value=n?t:$i(t),yl(this))}}function Vd(e){return kt(e)?e.value:e}const zd={get:(e,t,n)=>Vd(Reflect.get(e,t,n)),set:(e,t,n,i)=>{const r=e[t];return kt(r)&&!kt(n)?(r.value=n,!0):Reflect.set(e,t,n,i)}};function ml(e){return ei(e)?e:new Proxy(e,zd)}class kd{constructor(t,n,i,r){this._setter=n,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this._dirty=!0,this.effect=new ko(t,()=>{this._dirty||(this._dirty=!0,yl(this))}),this.effect.computed=this,this.effect.active=this._cacheable=!r,this.__v_isReadonly=i}get value(){const t=Q(this);return pl(t),(t._dirty||!t._cacheable)&&(t._dirty=!1,t._value=t.effect.run()),t._value}set value(t){this._setter(t)}}function Wd(e,t,n=!1){let i,r;const s=V(e);return s?(i=e,r=Ee):(i=e.get,r=e.set),new kd(i,r,s||!r,n)}function ln(e,t,n,i){let r;try{r=i?e(...i):e()}catch(s){bs(s,t,n)}return r}function de(e,t,n,i){if(V(e)){const s=ln(e,t,n,i);return s&&Zc(s)&&s.catch(o=>{bs(o,t,n)}),s}const r=[];for(let s=0;s<e.length;s++)r.push(de(e[s],t,n,i));return r}function bs(e,t,n,i=!0){const r=t?t.vnode:null;if(t){let s=t.parent;const o=t.proxy,a=n;for(;s;){const l=s.ec;if(l){for(let f=0;f<l.length;f++)if(l[f](e,o,a)===!1)return}s=s.parent}const c=t.appContext.config.errorHandler;if(c){ln(c,null,10,[e,o,a]);return}}Hd(e,n,r,i)}function Hd(e,t,n,i=!0){console.error(e)}let Vi=!1,uo=!1;const Tt=[];let Be=0;const ni=[];let $e=null,Fn=0;const gl=Promise.resolve();let qo=null;function Yd(e){const t=qo||gl;return e?t.then(this?e.bind(this):e):t}function Kd(e){let t=Be+1,n=Tt.length;for(;t<n;){const i=t+n>>>1,r=Tt[i],s=zi(r);s<e||s===e&&r.pre?t=i+1:n=i}return t}function Zo(e){(!Tt.length||!Tt.includes(e,Vi&&e.allowRecurse?Be+1:Be))&&(e.id==null?Tt.push(e):Tt.splice(Kd(e.id),0,e),bl())}function bl(){!Vi&&!uo&&(uo=!0,qo=gl.then(vl))}function Jd(e){const t=Tt.indexOf(e);t>Be&&Tt.splice(t,1)}function qd(e){R(e)?ni.push(...e):(!$e||!$e.includes(e,e.allowRecurse?Fn+1:Fn))&&ni.push(e),bl()}function ec(e,t,n=Vi?Be+1:0){for(;n<Tt.length;n++){const i=Tt[n];if(i&&i.pre){if(e&&i.id!==e.uid)continue;Tt.splice(n,1),n--,i()}}}function _l(e){if(ni.length){const t=[...new Set(ni)];if(ni.length=0,$e){$e.push(...t);return}for($e=t,$e.sort((n,i)=>zi(n)-zi(i)),Fn=0;Fn<$e.length;Fn++)$e[Fn]();$e=null,Fn=0}}const zi=e=>e.id==null?1/0:e.id,Zd=(e,t)=>{const n=zi(e)-zi(t);if(n===0){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function vl(e){uo=!1,Vi=!0,Tt.sort(Zd);try{for(Be=0;Be<Tt.length;Be++){const t=Tt[Be];t&&t.active!==!1&&ln(t,null,14)}}finally{Be=0,Tt.length=0,_l(),Vi=!1,qo=null,(Tt.length||ni.length)&&vl()}}function Xd(e,t,...n){if(e.isUnmounted)return;const i=e.vnode.props||dt;let r=n;const s=t.startsWith("update:"),o=s&&t.slice(7);if(o&&o in i){const f=`${o==="modelValue"?"model":o}Modifiers`,{number:p,trim:m}=i[f]||dt;m&&(r=n.map(I=>Bt(I)?I.trim():I)),p&&(r=n.map(ad))}let a,c=i[a=ks(t)]||i[a=ks(Ce(t))];!c&&s&&(c=i[a=ks(_i(t))]),c&&de(c,e,6,r);const l=i[a+"Once"];if(l){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,de(l,e,6,r)}}function wl(e,t,n=!1){const i=t.emitsCache,r=i.get(e);if(r!==void 0)return r;const s=e.emits;let o={},a=!1;if(!V(e)){const c=l=>{const f=wl(l,t,!0);f&&(a=!0,Ft(o,f))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!s&&!a?(gt(e)&&i.set(e,null),null):(R(s)?s.forEach(c=>o[c]=null):Ft(o,s),gt(e)&&i.set(e,o),o)}function _s(e,t){return!e||!hs(t)?!1:(t=t.slice(2).replace(/Once$/,""),q(e,t[0].toLowerCase()+t.slice(1))||q(e,_i(t))||q(e,t))}let Rt=null,vs=null;function Cr(e){const t=Rt;return Rt=e,vs=e&&e.type.__scopeId||null,t}function Qd(e){vs=e}function Gd(){vs=null}function Sl(e,t=Rt,n){if(!t||e._n)return e;const i=(...r)=>{i._d&&hc(-1);const s=Cr(t);let o;try{o=e(...r)}finally{Cr(s),i._d&&hc(1)}return o};return i._n=!0,i._c=!0,i._d=!0,i}function Ys(e){const{type:t,vnode:n,proxy:i,withProxy:r,props:s,propsOptions:[o],slots:a,attrs:c,emit:l,render:f,renderCache:p,data:m,setupState:I,ctx:k,inheritAttrs:W}=e;let bt,ht;const wt=Cr(e);try{if(n.shapeFlag&4){const H=r||i,me=H;bt=Ie(f.call(me,H,p,s,I,m,k)),ht=c}else{const H=t;bt=Ie(H.length>1?H(s,{attrs:c,slots:a,emit:l}):H(s,null)),ht=t.props?c:th(c)}}catch(H){Li.length=0,bs(H,e,1),bt=re(fn)}let Dt=bt;if(ht&&W!==!1){const H=Object.keys(ht),{shapeFlag:me}=Dt;H.length&&me&7&&(o&&H.some(Uo)&&(ht=eh(ht,o)),Dt=li(Dt,ht))}return n.dirs&&(Dt=li(Dt),Dt.dirs=Dt.dirs?Dt.dirs.concat(n.dirs):n.dirs),n.transition&&(Dt.transition=n.transition),bt=Dt,Cr(wt),bt}const th=e=>{let t;for(const n in e)(n==="class"||n==="style"||hs(n))&&((t||(t={}))[n]=e[n]);return t},eh=(e,t)=>{const n={};for(const i in e)(!Uo(i)||!(i.slice(9)in t))&&(n[i]=e[i]);return n};function nh(e,t,n){const{props:i,children:r,component:s}=e,{props:o,children:a,patchFlag:c}=t,l=s.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return i?nc(i,o,l):!!o;if(c&8){const f=t.dynamicProps;for(let p=0;p<f.length;p++){const m=f[p];if(o[m]!==i[m]&&!_s(l,m))return!0}}}else return(r||a)&&(!a||!a.$stable)?!0:i===o?!1:i?o?nc(i,o,l):!0:!!o;return!1}function nc(e,t,n){const i=Object.keys(t);if(i.length!==Object.keys(e).length)return!0;for(let r=0;r<i.length;r++){const s=i[r];if(t[s]!==e[s]&&!_s(n,s))return!0}return!1}function ih({vnode:e,parent:t},n){for(;t&&t.subTree===e;)(e=t.vnode).el=n,t=t.parent}const Il="components";function ic(e,t){return sh(Il,e,!0,t)||e}const rh=Symbol.for("v-ndc");function sh(e,t,n=!0,i=!1){const r=Rt||At;if(r){const s=r.type;if(e===Il){const a=Xh(s,!1);if(a&&(a===t||a===Ce(t)||a===ms(Ce(t))))return s}const o=rc(r[e]||s[e],t)||rc(r.appContext[e],t);return!o&&i?s:o}}function rc(e,t){return e&&(e[t]||e[Ce(t)]||e[ms(Ce(t))])}const oh=e=>e.__isSuspense;function ah(e,t){t&&t.pendingBranch?R(e)?t.effects.push(...e):t.effects.push(e):qd(e)}const gr={};function Sr(e,t,n){return Bl(e,t,n)}function Bl(e,t,{immediate:n,deep:i,flush:r,onTrack:s,onTrigger:o}=dt){var a;const c=gd()===((a=At)==null?void 0:a.scope)?At:null;let l,f=!1,p=!1;if(kt(e)?(l=()=>e.value,f=Er(e)):ei(e)?(l=()=>e,i=!0):R(e)?(p=!0,f=e.some(H=>ei(H)||Er(H)),l=()=>e.map(H=>{if(kt(H))return H.value;if(ei(H))return Xn(H);if(V(H))return ln(H,c,2)})):V(e)?t?l=()=>ln(e,c,2):l=()=>{if(!(c&&c.isUnmounted))return m&&m(),de(e,c,3,[I])}:l=Ee,t&&i){const H=l;l=()=>Xn(H())}let m,I=H=>{m=wt.onStop=()=>{ln(H,c,4),m=wt.onStop=void 0}},k;if(Hi)if(I=Ee,t?n&&de(t,c,3,[l(),p?[]:void 0,I]):l(),r==="sync"){const H=tp();k=H.__watcherHandles||(H.__watcherHandles=[])}else return Ee;let W=p?new Array(e.length).fill(gr):gr;const bt=()=>{if(wt.active)if(t){const H=wt.run();(i||f||(p?H.some((me,zn)=>jn(me,W[zn])):jn(H,W)))&&(m&&m(),de(t,c,3,[H,W===gr?void 0:p&&W[0]===gr?[]:W,I]),W=H)}else wt.run()};bt.allowRecurse=!!t;let ht;r==="sync"?ht=bt:r==="post"?ht=()=>Lt(bt,c&&c.suspense):(bt.pre=!0,c&&(bt.id=c.uid),ht=()=>Zo(bt));const wt=new ko(l,ht);t?n?bt():W=wt.run():r==="post"?Lt(wt.run.bind(wt),c&&c.suspense):wt.run();const Dt=()=>{wt.stop(),c&&c.scope&&Po(c.scope.effects,wt)};return k&&k.push(Dt),Dt}function ch(e,t,n){const i=this.proxy,r=Bt(e)?e.includes(".")?Ol(i,e):()=>i[e]:e.bind(i,i);let s;V(t)?s=t:(s=t.handler,n=t);const o=At;ui(this);const a=Bl(r,s.bind(i),n);return o?ui(o):Ln(),a}function Ol(e,t){const n=t.split(".");return()=>{let i=e;for(let r=0;r<n.length&&i;r++)i=i[n[r]];return i}}function Xn(e,t){if(!gt(e)||e.__v_skip||(t=t||new Set,t.has(e)))return e;if(t.add(e),kt(e))Xn(e.value,t);else if(R(e))for(let n=0;n<e.length;n++)Xn(e[n],t);else if(qc(e)||ti(e))e.forEach(n=>{Xn(n,t)});else if(Qc(e))for(const n in e)Xn(e[n],t);return e}function Bn(e,t,n,i){const r=e.dirs,s=t&&t.dirs;for(let o=0;o<r.length;o++){const a=r[o];s&&(a.oldValue=s[o].value);let c=a.dir[i];c&&(vi(),de(c,n,8,[e.el,a,e,t]),wi())}}/*! #__NO_SIDE_EFFECTS__ */function Al(e,t){return V(e)?Ft({name:e.name},t,{setup:e}):e}const Ci=e=>!!e.type.__asyncLoader,Fl=e=>e.type.__isKeepAlive;function lh(e,t){Dl(e,"a",t)}function uh(e,t){Dl(e,"da",t)}function Dl(e,t,n=At){const i=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(ws(t,i,n),n){let r=n.parent;for(;r&&r.parent;)Fl(r.parent.vnode)&&fh(i,t,n,r),r=r.parent}}function fh(e,t,n,i){const r=ws(t,e,i,!0);Xo(()=>{Po(i[t],r)},n)}function ws(e,t,n=At,i=!1){if(n){const r=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;vi(),ui(n);const a=de(t,n,e,o);return Ln(),wi(),a});return i?r.unshift(s):r.push(s),s}}const Xe=e=>(t,n=At)=>(!Hi||e==="sp")&&ws(e,(...i)=>t(...i),n),dh=Xe("bm"),Tl=Xe("m"),hh=Xe("bu"),xl=Xe("u"),ph=Xe("bum"),Xo=Xe("um"),yh=Xe("sp"),mh=Xe("rtg"),gh=Xe("rtc");function Ml(e,t=At){ws("ec",e,t)}function bh(e,t,n={},i,r){if(Rt.isCE||Rt.parent&&Ci(Rt.parent)&&Rt.parent.isCE)return t!=="default"&&(n.name=t),re("slot",n,i&&i());let s=e[t];s&&s._c&&(s._d=!1),ki();const o=s&&Nl(s(n)),a=zl(le,{key:n.key||o&&o.key||`_${t}`},o||(i?i():[]),o&&e._===1?64:-2);return!r&&a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),s&&s._c&&(s._d=!0),a}function Nl(e){return e.some(t=>kl(t)?!(t.type===fn||t.type===le&&!Nl(t.children)):!0)?e:null}const fo=e=>e?Hl(e)?na(e)||e.proxy:fo(e.parent):null,Ri=Ft(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>fo(e.parent),$root:e=>fo(e.root),$emit:e=>e.emit,$options:e=>Qo(e),$forceUpdate:e=>e.f||(e.f=()=>Zo(e.update)),$nextTick:e=>e.n||(e.n=Yd.bind(e.proxy)),$watch:e=>ch.bind(e)}),Ks=(e,t)=>e!==dt&&!e.__isScriptSetup&&q(e,t),_h={get({_:e},t){const{ctx:n,setupState:i,data:r,props:s,accessCache:o,type:a,appContext:c}=e;let l;if(t[0]!=="$"){const I=o[t];if(I!==void 0)switch(I){case 1:return i[t];case 2:return r[t];case 4:return n[t];case 3:return s[t]}else{if(Ks(i,t))return o[t]=1,i[t];if(r!==dt&&q(r,t))return o[t]=2,r[t];if((l=e.propsOptions[0])&&q(l,t))return o[t]=3,s[t];if(n!==dt&&q(n,t))return o[t]=4,n[t];ho&&(o[t]=0)}}const f=Ri[t];let p,m;if(f)return t==="$attrs"&&zt(e,"get",t),f(e);if((p=a.__cssModules)&&(p=p[t]))return p;if(n!==dt&&q(n,t))return o[t]=4,n[t];if(m=c.config.globalProperties,q(m,t))return m[t]},set({_:e},t,n){const{data:i,setupState:r,ctx:s}=e;return Ks(r,t)?(r[t]=n,!0):i!==dt&&q(i,t)?(i[t]=n,!0):q(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(s[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:i,appContext:r,propsOptions:s}},o){let a;return!!n[o]||e!==dt&&q(e,o)||Ks(t,o)||(a=s[0])&&q(a,o)||q(i,o)||q(Ri,o)||q(r.config.globalProperties,o)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:q(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function sc(e){return R(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let ho=!0;function vh(e){const t=Qo(e),n=e.proxy,i=e.ctx;ho=!1,t.beforeCreate&&oc(t.beforeCreate,e,"bc");const{data:r,computed:s,methods:o,watch:a,provide:c,inject:l,created:f,beforeMount:p,mounted:m,beforeUpdate:I,updated:k,activated:W,deactivated:bt,beforeDestroy:ht,beforeUnmount:wt,destroyed:Dt,unmounted:H,render:me,renderTracked:zn,renderTriggered:Oi,errorCaptured:en,serverPrefetch:js,expose:wn,inheritAttrs:Ai,components:cr,directives:lr,filters:$s}=t;if(l&&wh(l,i,null),o)for(const mt in o){const ct=o[mt];V(ct)&&(i[mt]=ct.bind(n))}if(r){const mt=r.call(n,n);gt(mt)&&(e.data=Yo(mt))}if(ho=!0,s)for(const mt in s){const ct=s[mt],Sn=V(ct)?ct.bind(n,n):V(ct.get)?ct.get.bind(n,n):Ee,ur=!V(ct)&&V(ct.set)?ct.set.bind(n):Ee,In=Kl({get:Sn,set:ur});Object.defineProperty(i,mt,{enumerable:!0,configurable:!0,get:()=>In.value,set:ge=>In.value=ge})}if(a)for(const mt in a)El(a[mt],i,n,mt);if(c){const mt=V(c)?c.call(n):c;Reflect.ownKeys(mt).forEach(ct=>{Fh(ct,mt[ct])})}f&&oc(f,e,"c");function xt(mt,ct){R(ct)?ct.forEach(Sn=>mt(Sn.bind(n))):ct&&mt(ct.bind(n))}if(xt(dh,p),xt(Tl,m),xt(hh,I),xt(xl,k),xt(lh,W),xt(uh,bt),xt(Ml,en),xt(gh,zn),xt(mh,Oi),xt(ph,wt),xt(Xo,H),xt(yh,js),R(wn))if(wn.length){const mt=e.exposed||(e.exposed={});wn.forEach(ct=>{Object.defineProperty(mt,ct,{get:()=>n[ct],set:Sn=>n[ct]=Sn})})}else e.exposed||(e.exposed={});me&&e.render===Ee&&(e.render=me),Ai!=null&&(e.inheritAttrs=Ai),cr&&(e.components=cr),lr&&(e.directives=lr)}function wh(e,t,n=Ee){R(e)&&(e=po(e));for(const i in e){const r=e[i];let s;gt(r)?"default"in r?s=Ir(r.from||i,r.default,!0):s=Ir(r.from||i):s=Ir(r),kt(s)?Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>s.value,set:o=>s.value=o}):t[i]=s}}function oc(e,t,n){de(R(e)?e.map(i=>i.bind(t.proxy)):e.bind(t.proxy),t,n)}function El(e,t,n,i){const r=i.includes(".")?Ol(n,i):()=>n[i];if(Bt(e)){const s=t[e];V(s)&&Sr(r,s)}else if(V(e))Sr(r,e.bind(n));else if(gt(e))if(R(e))e.forEach(s=>El(s,t,n,i));else{const s=V(e.handler)?e.handler.bind(n):t[e.handler];V(s)&&Sr(r,s,e)}}function Qo(e){const t=e.type,{mixins:n,extends:i}=t,{mixins:r,optionsCache:s,config:{optionMergeStrategies:o}}=e.appContext,a=s.get(t);let c;return a?c=a:!r.length&&!n&&!i?c=t:(c={},r.length&&r.forEach(l=>Rr(c,l,o,!0)),Rr(c,t,o)),gt(t)&&s.set(t,c),c}function Rr(e,t,n,i=!1){const{mixins:r,extends:s}=t;s&&Rr(e,s,n,!0),r&&r.forEach(o=>Rr(e,o,n,!0));for(const o in t)if(!(i&&o==="expose")){const a=Sh[o]||n&&n[o];e[o]=a?a(e[o],t[o]):t[o]}return e}const Sh={data:ac,props:cc,emits:cc,methods:Mi,computed:Mi,beforeCreate:Mt,created:Mt,beforeMount:Mt,mounted:Mt,beforeUpdate:Mt,updated:Mt,beforeDestroy:Mt,beforeUnmount:Mt,destroyed:Mt,unmounted:Mt,activated:Mt,deactivated:Mt,errorCaptured:Mt,serverPrefetch:Mt,components:Mi,directives:Mi,watch:Bh,provide:ac,inject:Ih};function ac(e,t){return t?e?function(){return Ft(V(e)?e.call(this,this):e,V(t)?t.call(this,this):t)}:t:e}function Ih(e,t){return Mi(po(e),po(t))}function po(e){if(R(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Mt(e,t){return e?[...new Set([].concat(e,t))]:t}function Mi(e,t){return e?Ft(Object.create(null),e,t):t}function cc(e,t){return e?R(e)&&R(t)?[...new Set([...e,...t])]:Ft(Object.create(null),sc(e),sc(t??{})):t}function Bh(e,t){if(!e)return t;if(!t)return e;const n=Ft(Object.create(null),e);for(const i in t)n[i]=Mt(e[i],t[i]);return n}function Cl(){return{app:null,config:{isNativeTag:nd,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Oh=0;function Ah(e,t){return function(i,r=null){V(i)||(i=Ft({},i)),r!=null&&!gt(r)&&(r=null);const s=Cl(),o=new WeakSet;let a=!1;const c=s.app={_uid:Oh++,_component:i,_props:r,_container:null,_context:s,_instance:null,version:ep,get config(){return s.config},set config(l){},use(l,...f){return o.has(l)||(l&&V(l.install)?(o.add(l),l.install(c,...f)):V(l)&&(o.add(l),l(c,...f))),c},mixin(l){return s.mixins.includes(l)||s.mixins.push(l),c},component(l,f){return f?(s.components[l]=f,c):s.components[l]},directive(l,f){return f?(s.directives[l]=f,c):s.directives[l]},mount(l,f,p){if(!a){const m=re(i,r);return m.appContext=s,f&&t?t(m,l):e(m,l,p),a=!0,c._container=l,l.__vue_app__=c,na(m.component)||m.component.proxy}},unmount(){a&&(e(null,c._container),delete c._container.__vue_app__)},provide(l,f){return s.provides[l]=f,c},runWithContext(l){Lr=c;try{return l()}finally{Lr=null}}};return c}}let Lr=null;function Fh(e,t){if(At){let n=At.provides;const i=At.parent&&At.parent.provides;i===n&&(n=At.provides=Object.create(i)),n[e]=t}}function Ir(e,t,n=!1){const i=At||Rt;if(i||Lr){const r=i?i.parent==null?i.vnode.appContext&&i.vnode.appContext.provides:i.parent.provides:Lr._context.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&V(t)?t.call(i&&i.proxy):t}}function Dh(e,t,n,i=!1){const r={},s={};Nr(s,Is,1),e.propsDefaults=Object.create(null),Rl(e,t,r,s);for(const o in e.propsOptions[0])o in r||(r[o]=void 0);n?e.props=i?r:Pd(r):e.type.props?e.props=r:e.props=s,e.attrs=s}function Th(e,t,n,i){const{props:r,attrs:s,vnode:{patchFlag:o}}=e,a=Q(r),[c]=e.propsOptions;let l=!1;if((i||o>0)&&!(o&16)){if(o&8){const f=e.vnode.dynamicProps;for(let p=0;p<f.length;p++){let m=f[p];if(_s(e.emitsOptions,m))continue;const I=t[m];if(c)if(q(s,m))I!==s[m]&&(s[m]=I,l=!0);else{const k=Ce(m);r[k]=yo(c,a,k,I,e,!1)}else I!==s[m]&&(s[m]=I,l=!0)}}}else{Rl(e,t,r,s)&&(l=!0);let f;for(const p in a)(!t||!q(t,p)&&((f=_i(p))===p||!q(t,f)))&&(c?n&&(n[p]!==void 0||n[f]!==void 0)&&(r[p]=yo(c,a,p,void 0,e,!0)):delete r[p]);if(s!==a)for(const p in s)(!t||!q(t,p))&&(delete s[p],l=!0)}l&&Ke(e,"set","$attrs")}function Rl(e,t,n,i){const[r,s]=e.propsOptions;let o=!1,a;if(t)for(let c in t){if(wr(c))continue;const l=t[c];let f;r&&q(r,f=Ce(c))?!s||!s.includes(f)?n[f]=l:(a||(a={}))[f]=l:_s(e.emitsOptions,c)||(!(c in i)||l!==i[c])&&(i[c]=l,o=!0)}if(s){const c=Q(n),l=a||dt;for(let f=0;f<s.length;f++){const p=s[f];n[p]=yo(r,c,p,l[p],e,!q(l,p))}}return o}function yo(e,t,n,i,r,s){const o=e[n];if(o!=null){const a=q(o,"default");if(a&&i===void 0){const c=o.default;if(o.type!==Function&&!o.skipFactory&&V(c)){const{propsDefaults:l}=r;n in l?i=l[n]:(ui(r),i=l[n]=c.call(null,t),Ln())}else i=c}o[0]&&(s&&!a?i=!1:o[1]&&(i===""||i===_i(n))&&(i=!0))}return i}function Ll(e,t,n=!1){const i=t.propsCache,r=i.get(e);if(r)return r;const s=e.props,o={},a=[];let c=!1;if(!V(e)){const f=p=>{c=!0;const[m,I]=Ll(p,t,!0);Ft(o,m),I&&a.push(...I)};!n&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}if(!s&&!c)return gt(e)&&i.set(e,Gn),Gn;if(R(s))for(let f=0;f<s.length;f++){const p=Ce(s[f]);lc(p)&&(o[p]=dt)}else if(s)for(const f in s){const p=Ce(f);if(lc(p)){const m=s[f],I=o[p]=R(m)||V(m)?{type:m}:Ft({},m);if(I){const k=dc(Boolean,I.type),W=dc(String,I.type);I[0]=k>-1,I[1]=W<0||k<W,(k>-1||q(I,"default"))&&a.push(p)}}}const l=[o,a];return gt(e)&&i.set(e,l),l}function lc(e){return e[0]!=="$"}function uc(e){const t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:e===null?"null":""}function fc(e,t){return uc(e)===uc(t)}function dc(e,t){return R(t)?t.findIndex(n=>fc(n,e)):V(t)&&fc(t,e)?0:-1}const Ul=e=>e[0]==="_"||e==="$stable",Go=e=>R(e)?e.map(Ie):[Ie(e)],xh=(e,t,n)=>{if(t._n)return t;const i=Sl((...r)=>Go(t(...r)),n);return i._c=!1,i},Pl=(e,t,n)=>{const i=e._ctx;for(const r in e){if(Ul(r))continue;const s=e[r];if(V(s))t[r]=xh(r,s,i);else if(s!=null){const o=Go(s);t[r]=()=>o}}},jl=(e,t)=>{const n=Go(t);e.slots.default=()=>n},Mh=(e,t)=>{if(e.vnode.shapeFlag&32){const n=t._;n?(e.slots=Q(t),Nr(t,"_",n)):Pl(t,e.slots={})}else e.slots={},t&&jl(e,t);Nr(e.slots,Is,1)},Nh=(e,t,n)=>{const{vnode:i,slots:r}=e;let s=!0,o=dt;if(i.shapeFlag&32){const a=t._;a?n&&a===1?s=!1:(Ft(r,t),!n&&a===1&&delete r._):(s=!t.$stable,Pl(t,r)),o=t}else t&&(jl(e,t),o={default:1});if(s)for(const a in r)!Ul(a)&&o[a]==null&&delete r[a]};function mo(e,t,n,i,r=!1){if(R(e)){e.forEach((m,I)=>mo(m,t&&(R(t)?t[I]:t),n,i,r));return}if(Ci(i)&&!r)return;const s=i.shapeFlag&4?na(i.component)||i.component.proxy:i.el,o=r?null:s,{i:a,r:c}=e,l=t&&t.r,f=a.refs===dt?a.refs={}:a.refs,p=a.setupState;if(l!=null&&l!==c&&(Bt(l)?(f[l]=null,q(p,l)&&(p[l]=null)):kt(l)&&(l.value=null)),V(c))ln(c,a,12,[o,f]);else{const m=Bt(c),I=kt(c);if(m||I){const k=()=>{if(e.f){const W=m?q(p,c)?p[c]:f[c]:c.value;r?R(W)&&Po(W,s):R(W)?W.includes(s)||W.push(s):m?(f[c]=[s],q(p,c)&&(p[c]=f[c])):(c.value=[s],e.k&&(f[e.k]=c.value))}else m?(f[c]=o,q(p,c)&&(p[c]=o)):I&&(c.value=o,e.k&&(f[e.k]=o))};o?(k.id=-1,Lt(k,n)):k()}}}const Lt=ah;function Eh(e){return Ch(e)}function Ch(e,t){const n=so();n.__VUE__=!0;const{insert:i,remove:r,patchProp:s,createElement:o,createText:a,createComment:c,setText:l,setElementText:f,parentNode:p,nextSibling:m,setScopeId:I=Ee,insertStaticContent:k}=e,W=(u,d,y,g=null,b=null,w=null,B=!1,v=null,S=!!d.dynamicChildren)=>{if(u===d)return;u&&!Di(u,d)&&(g=fr(u),ge(u,b,w,!0),u=null),d.patchFlag===-2&&(S=!1,d.dynamicChildren=null);const{type:_,ref:A,shapeFlag:O}=d;switch(_){case Ss:bt(u,d,y,g);break;case fn:ht(u,d,y,g);break;case Js:u==null&&wt(d,y,g,B);break;case le:cr(u,d,y,g,b,w,B,v,S);break;default:O&1?me(u,d,y,g,b,w,B,v,S):O&6?lr(u,d,y,g,b,w,B,v,S):(O&64||O&128)&&_.process(u,d,y,g,b,w,B,v,S,kn)}A!=null&&b&&mo(A,u&&u.ref,w,d||u,!d)},bt=(u,d,y,g)=>{if(u==null)i(d.el=a(d.children),y,g);else{const b=d.el=u.el;d.children!==u.children&&l(b,d.children)}},ht=(u,d,y,g)=>{u==null?i(d.el=c(d.children||""),y,g):d.el=u.el},wt=(u,d,y,g)=>{[u.el,u.anchor]=k(u.children,d,y,g,u.el,u.anchor)},Dt=({el:u,anchor:d},y,g)=>{let b;for(;u&&u!==d;)b=m(u),i(u,y,g),u=b;i(d,y,g)},H=({el:u,anchor:d})=>{let y;for(;u&&u!==d;)y=m(u),r(u),u=y;r(d)},me=(u,d,y,g,b,w,B,v,S)=>{B=B||d.type==="svg",u==null?zn(d,y,g,b,w,B,v,S):js(u,d,b,w,B,v,S)},zn=(u,d,y,g,b,w,B,v)=>{let S,_;const{type:A,props:O,shapeFlag:F,transition:C,dirs:Y}=u;if(S=u.el=o(u.type,w,O&&O.is,O),F&8?f(S,u.children):F&16&&en(u.children,S,null,g,b,w&&A!=="foreignObject",B,v),Y&&Bn(u,null,g,"created"),Oi(S,u,u.scopeId,B,g),O){for(const st in O)st!=="value"&&!wr(st)&&s(S,st,null,O[st],w,u.children,g,b,Pe);"value"in O&&s(S,"value",null,O.value),(_=O.onVnodeBeforeMount)&&_e(_,g,u)}Y&&Bn(u,null,g,"beforeMount");const lt=Rh(b,C);lt&&C.beforeEnter(S),i(S,d,y),((_=O&&O.onVnodeMounted)||lt||Y)&&Lt(()=>{_&&_e(_,g,u),lt&&C.enter(S),Y&&Bn(u,null,g,"mounted")},b)},Oi=(u,d,y,g,b)=>{if(y&&I(u,y),g)for(let w=0;w<g.length;w++)I(u,g[w]);if(b){let w=b.subTree;if(d===w){const B=b.vnode;Oi(u,B,B.scopeId,B.slotScopeIds,b.parent)}}},en=(u,d,y,g,b,w,B,v,S=0)=>{for(let _=S;_<u.length;_++){const A=u[_]=v?sn(u[_]):Ie(u[_]);W(null,A,d,y,g,b,w,B,v)}},js=(u,d,y,g,b,w,B)=>{const v=d.el=u.el;let{patchFlag:S,dynamicChildren:_,dirs:A}=d;S|=u.patchFlag&16;const O=u.props||dt,F=d.props||dt;let C;y&&On(y,!1),(C=F.onVnodeBeforeUpdate)&&_e(C,y,d,u),A&&Bn(d,u,y,"beforeUpdate"),y&&On(y,!0);const Y=b&&d.type!=="foreignObject";if(_?wn(u.dynamicChildren,_,v,y,g,Y,w):B||ct(u,d,v,null,y,g,Y,w,!1),S>0){if(S&16)Ai(v,d,O,F,y,g,b);else if(S&2&&O.class!==F.class&&s(v,"class",null,F.class,b),S&4&&s(v,"style",O.style,F.style,b),S&8){const lt=d.dynamicProps;for(let st=0;st<lt.length;st++){const _t=lt[st],oe=O[_t],Wn=F[_t];(Wn!==oe||_t==="value")&&s(v,_t,oe,Wn,b,u.children,y,g,Pe)}}S&1&&u.children!==d.children&&f(v,d.children)}else!B&&_==null&&Ai(v,d,O,F,y,g,b);((C=F.onVnodeUpdated)||A)&&Lt(()=>{C&&_e(C,y,d,u),A&&Bn(d,u,y,"updated")},g)},wn=(u,d,y,g,b,w,B)=>{for(let v=0;v<d.length;v++){const S=u[v],_=d[v],A=S.el&&(S.type===le||!Di(S,_)||S.shapeFlag&70)?p(S.el):y;W(S,_,A,null,g,b,w,B,!0)}},Ai=(u,d,y,g,b,w,B)=>{if(y!==g){if(y!==dt)for(const v in y)!wr(v)&&!(v in g)&&s(u,v,y[v],null,B,d.children,b,w,Pe);for(const v in g){if(wr(v))continue;const S=g[v],_=y[v];S!==_&&v!=="value"&&s(u,v,_,S,B,d.children,b,w,Pe)}"value"in g&&s(u,"value",y.value,g.value)}},cr=(u,d,y,g,b,w,B,v,S)=>{const _=d.el=u?u.el:a(""),A=d.anchor=u?u.anchor:a("");let{patchFlag:O,dynamicChildren:F,slotScopeIds:C}=d;C&&(v=v?v.concat(C):C),u==null?(i(_,y,g),i(A,y,g),en(d.children,y,A,b,w,B,v,S)):O>0&&O&64&&F&&u.dynamicChildren?(wn(u.dynamicChildren,F,y,b,w,B,v),(d.key!=null||b&&d===b.subTree)&&$l(u,d,!0)):ct(u,d,y,A,b,w,B,v,S)},lr=(u,d,y,g,b,w,B,v,S)=>{d.slotScopeIds=v,u==null?d.shapeFlag&512?b.ctx.activate(d,y,g,B,S):$s(d,y,g,b,w,B,S):$a(u,d,S)},$s=(u,d,y,g,b,w,B)=>{const v=u.component=Yh(u,g,b);if(Fl(u)&&(v.ctx.renderer=kn),Kh(v),v.asyncDep){if(b&&b.registerDep(v,xt),!u.el){const S=v.subTree=re(fn);ht(null,S,d,y)}return}xt(v,u,d,y,b,w,B)},$a=(u,d,y)=>{const g=d.component=u.component;if(nh(u,d,y))if(g.asyncDep&&!g.asyncResolved){mt(g,d,y);return}else g.next=d,Jd(g.update),g.update();else d.el=u.el,g.vnode=d},xt=(u,d,y,g,b,w,B)=>{const v=()=>{if(u.isMounted){let{next:A,bu:O,u:F,parent:C,vnode:Y}=u,lt=A,st;On(u,!1),A?(A.el=Y.el,mt(u,A,B)):A=Y,O&&Ws(O),(st=A.props&&A.props.onVnodeBeforeUpdate)&&_e(st,C,A,Y),On(u,!0);const _t=Ys(u),oe=u.subTree;u.subTree=_t,W(oe,_t,p(oe.el),fr(oe),u,b,w),A.el=_t.el,lt===null&&ih(u,_t.el),F&&Lt(F,b),(st=A.props&&A.props.onVnodeUpdated)&&Lt(()=>_e(st,C,A,Y),b)}else{let A;const{el:O,props:F}=d,{bm:C,m:Y,parent:lt}=u,st=Ci(d);if(On(u,!1),C&&Ws(C),!st&&(A=F&&F.onVnodeBeforeMount)&&_e(A,lt,d),On(u,!0),O&&zs){const _t=()=>{u.subTree=Ys(u),zs(O,u.subTree,u,b,null)};st?d.type.__asyncLoader().then(()=>!u.isUnmounted&&_t()):_t()}else{const _t=u.subTree=Ys(u);W(null,_t,y,g,u,b,w),d.el=_t.el}if(Y&&Lt(Y,b),!st&&(A=F&&F.onVnodeMounted)){const _t=d;Lt(()=>_e(A,lt,_t),b)}(d.shapeFlag&256||lt&&Ci(lt.vnode)&&lt.vnode.shapeFlag&256)&&u.a&&Lt(u.a,b),u.isMounted=!0,d=y=g=null}},S=u.effect=new ko(v,()=>Zo(_),u.scope),_=u.update=()=>S.run();_.id=u.uid,On(u,!0),_()},mt=(u,d,y)=>{d.component=u;const g=u.vnode.props;u.vnode=d,u.next=null,Th(u,d.props,g,y),Nh(u,d.children,y),vi(),ec(u),wi()},ct=(u,d,y,g,b,w,B,v,S=!1)=>{const _=u&&u.children,A=u?u.shapeFlag:0,O=d.children,{patchFlag:F,shapeFlag:C}=d;if(F>0){if(F&128){ur(_,O,y,g,b,w,B,v,S);return}else if(F&256){Sn(_,O,y,g,b,w,B,v,S);return}}C&8?(A&16&&Pe(_,b,w),O!==_&&f(y,O)):A&16?C&16?ur(_,O,y,g,b,w,B,v,S):Pe(_,b,w,!0):(A&8&&f(y,""),C&16&&en(O,y,g,b,w,B,v,S))},Sn=(u,d,y,g,b,w,B,v,S)=>{u=u||Gn,d=d||Gn;const _=u.length,A=d.length,O=Math.min(_,A);let F;for(F=0;F<O;F++){const C=d[F]=S?sn(d[F]):Ie(d[F]);W(u[F],C,y,null,b,w,B,v,S)}_>A?Pe(u,b,w,!0,!1,O):en(d,y,g,b,w,B,v,S,O)},ur=(u,d,y,g,b,w,B,v,S)=>{let _=0;const A=d.length;let O=u.length-1,F=A-1;for(;_<=O&&_<=F;){const C=u[_],Y=d[_]=S?sn(d[_]):Ie(d[_]);if(Di(C,Y))W(C,Y,y,null,b,w,B,v,S);else break;_++}for(;_<=O&&_<=F;){const C=u[O],Y=d[F]=S?sn(d[F]):Ie(d[F]);if(Di(C,Y))W(C,Y,y,null,b,w,B,v,S);else break;O--,F--}if(_>O){if(_<=F){const C=F+1,Y=C<A?d[C].el:g;for(;_<=F;)W(null,d[_]=S?sn(d[_]):Ie(d[_]),y,Y,b,w,B,v,S),_++}}else if(_>F)for(;_<=O;)ge(u[_],b,w,!0),_++;else{const C=_,Y=_,lt=new Map;for(_=Y;_<=F;_++){const Kt=d[_]=S?sn(d[_]):Ie(d[_]);Kt.key!=null&&lt.set(Kt.key,_)}let st,_t=0;const oe=F-Y+1;let Wn=!1,ka=0;const Fi=new Array(oe);for(_=0;_<oe;_++)Fi[_]=0;for(_=C;_<=O;_++){const Kt=u[_];if(_t>=oe){ge(Kt,b,w,!0);continue}let be;if(Kt.key!=null)be=lt.get(Kt.key);else for(st=Y;st<=F;st++)if(Fi[st-Y]===0&&Di(Kt,d[st])){be=st;break}be===void 0?ge(Kt,b,w,!0):(Fi[be-Y]=_+1,be>=ka?ka=be:Wn=!0,W(Kt,d[be],y,null,b,w,B,v,S),_t++)}const Wa=Wn?Lh(Fi):Gn;for(st=Wa.length-1,_=oe-1;_>=0;_--){const Kt=Y+_,be=d[Kt],Ha=Kt+1<A?d[Kt+1].el:g;Fi[_]===0?W(null,be,y,Ha,b,w,B,v,S):Wn&&(st<0||_!==Wa[st]?In(be,y,Ha,2):st--)}}},In=(u,d,y,g,b=null)=>{const{el:w,type:B,transition:v,children:S,shapeFlag:_}=u;if(_&6){In(u.component.subTree,d,y,g);return}if(_&128){u.suspense.move(d,y,g);return}if(_&64){B.move(u,d,y,kn);return}if(B===le){i(w,d,y);for(let O=0;O<S.length;O++)In(S[O],d,y,g);i(u.anchor,d,y);return}if(B===Js){Dt(u,d,y);return}if(g!==2&&_&1&&v)if(g===0)v.beforeEnter(w),i(w,d,y),Lt(()=>v.enter(w),b);else{const{leave:O,delayLeave:F,afterLeave:C}=v,Y=()=>i(w,d,y),lt=()=>{O(w,()=>{Y(),C&&C()})};F?F(w,Y,lt):lt()}else i(w,d,y)},ge=(u,d,y,g=!1,b=!1)=>{const{type:w,props:B,ref:v,children:S,dynamicChildren:_,shapeFlag:A,patchFlag:O,dirs:F}=u;if(v!=null&&mo(v,null,y,u,!0),A&256){d.ctx.deactivate(u);return}const C=A&1&&F,Y=!Ci(u);let lt;if(Y&&(lt=B&&B.onVnodeBeforeUnmount)&&_e(lt,d,u),A&6)ed(u.component,y,g);else{if(A&128){u.suspense.unmount(y,g);return}C&&Bn(u,null,d,"beforeUnmount"),A&64?u.type.remove(u,d,y,b,kn,g):_&&(w!==le||O>0&&O&64)?Pe(_,d,y,!1,!0):(w===le&&O&384||!b&&A&16)&&Pe(S,d,y),g&&Va(u)}(Y&&(lt=B&&B.onVnodeUnmounted)||C)&&Lt(()=>{lt&&_e(lt,d,u),C&&Bn(u,null,d,"unmounted")},y)},Va=u=>{const{type:d,el:y,anchor:g,transition:b}=u;if(d===le){td(y,g);return}if(d===Js){H(u);return}const w=()=>{r(y),b&&!b.persisted&&b.afterLeave&&b.afterLeave()};if(u.shapeFlag&1&&b&&!b.persisted){const{leave:B,delayLeave:v}=b,S=()=>B(y,w);v?v(u.el,w,S):S()}else w()},td=(u,d)=>{let y;for(;u!==d;)y=m(u),r(u),u=y;r(d)},ed=(u,d,y)=>{const{bum:g,scope:b,update:w,subTree:B,um:v}=u;g&&Ws(g),b.stop(),w&&(w.active=!1,ge(B,u,d,y)),v&&Lt(v,d),Lt(()=>{u.isUnmounted=!0},d),d&&d.pendingBranch&&!d.isUnmounted&&u.asyncDep&&!u.asyncResolved&&u.suspenseId===d.pendingId&&(d.deps--,d.deps===0&&d.resolve())},Pe=(u,d,y,g=!1,b=!1,w=0)=>{for(let B=w;B<u.length;B++)ge(u[B],d,y,g,b)},fr=u=>u.shapeFlag&6?fr(u.component.subTree):u.shapeFlag&128?u.suspense.next():m(u.anchor||u.el),za=(u,d,y)=>{u==null?d._vnode&&ge(d._vnode,null,null,!0):W(d._vnode||null,u,d,null,null,null,y),ec(),_l(),d._vnode=u},kn={p:W,um:ge,m:In,r:Va,mt:$s,mc:en,pc:ct,pbc:wn,n:fr,o:e};let Vs,zs;return t&&([Vs,zs]=t(kn)),{render:za,hydrate:Vs,createApp:Ah(za,Vs)}}function On({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Rh(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function $l(e,t,n=!1){const i=e.children,r=t.children;if(R(i)&&R(r))for(let s=0;s<i.length;s++){const o=i[s];let a=r[s];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=r[s]=sn(r[s]),a.el=o.el),n||$l(o,a)),a.type===Ss&&(a.el=o.el)}}function Lh(e){const t=e.slice(),n=[0];let i,r,s,o,a;const c=e.length;for(i=0;i<c;i++){const l=e[i];if(l!==0){if(r=n[n.length-1],e[r]<l){t[i]=r,n.push(i);continue}for(s=0,o=n.length-1;s<o;)a=s+o>>1,e[n[a]]<l?s=a+1:o=a;l<e[n[s]]&&(s>0&&(t[i]=n[s-1]),n[s]=i)}}for(s=n.length,o=n[s-1];s-- >0;)n[s]=o,o=t[o];return n}const Uh=e=>e.__isTeleport,le=Symbol.for("v-fgt"),Ss=Symbol.for("v-txt"),fn=Symbol.for("v-cmt"),Js=Symbol.for("v-stc"),Li=[];let fe=null;function ki(e=!1){Li.push(fe=e?null:[])}function Ph(){Li.pop(),fe=Li[Li.length-1]||null}let Wi=1;function hc(e){Wi+=e}function Vl(e){return e.dynamicChildren=Wi>0?fe||Gn:null,Ph(),Wi>0&&fe&&fe.push(e),e}function go(e,t,n,i,r,s){return Vl(Bs(e,t,n,i,r,s,!0))}function zl(e,t,n,i,r){return Vl(re(e,t,n,i,r,!0))}function kl(e){return e?e.__v_isVNode===!0:!1}function Di(e,t){return e.type===t.type&&e.key===t.key}const Is="__vInternal",Wl=({key:e})=>e??null,Br=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Bt(e)||kt(e)||V(e)?{i:Rt,r:e,k:t,f:!!n}:e:null);function Bs(e,t=null,n=null,i=0,r=null,s=e===le?0:1,o=!1,a=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Wl(t),ref:t&&Br(t),scopeId:vs,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:s,patchFlag:i,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Rt};return a?(ta(c,n),s&128&&e.normalize(c)):n&&(c.shapeFlag|=Bt(n)?8:16),Wi>0&&!o&&fe&&(c.patchFlag>0||s&6)&&c.patchFlag!==32&&fe.push(c),c}const re=jh;function jh(e,t=null,n=null,i=0,r=null,s=!1){if((!e||e===rh)&&(e=fn),kl(e)){const a=li(e,t,!0);return n&&ta(a,n),Wi>0&&!s&&fe&&(a.shapeFlag&6?fe[fe.indexOf(e)]=a:fe.push(a)),a.patchFlag|=-2,a}if(Qh(e)&&(e=e.__vccOpts),t){t=$h(t);let{class:a,style:c}=t;a&&!Bt(a)&&(t.class=Vo(a)),gt(c)&&(dl(c)&&!R(c)&&(c=Ft({},c)),t.style=$o(c))}const o=Bt(e)?1:oh(e)?128:Uh(e)?64:gt(e)?4:V(e)?2:0;return Bs(e,t,n,i,r,o,s,!0)}function $h(e){return e?dl(e)||Is in e?Ft({},e):e:null}function li(e,t,n=!1){const{props:i,ref:r,patchFlag:s,children:o}=e,a=t?kh(i||{},t):i;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&Wl(a),ref:t&&t.ref?n&&r?R(r)?r.concat(Br(t)):[r,Br(t)]:Br(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:o,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==le?s===-1?16:s|16:s,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&li(e.ssContent),ssFallback:e.ssFallback&&li(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function Vh(e=" ",t=0){return re(Ss,null,e,t)}function zh(e="",t=!1){return t?(ki(),zl(fn,null,e)):re(fn,null,e)}function Ie(e){return e==null||typeof e=="boolean"?re(fn):R(e)?re(le,null,e.slice()):typeof e=="object"?sn(e):re(Ss,null,String(e))}function sn(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:li(e)}function ta(e,t){let n=0;const{shapeFlag:i}=e;if(t==null)t=null;else if(R(t))n=16;else if(typeof t=="object")if(i&65){const r=t.default;r&&(r._c&&(r._d=!1),ta(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!(Is in t)?t._ctx=Rt:r===3&&Rt&&(Rt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else V(t)?(t={default:t,_ctx:Rt},n=32):(t=String(t),i&64?(n=16,t=[Vh(t)]):n=8);e.children=t,e.shapeFlag|=n}function kh(...e){const t={};for(let n=0;n<e.length;n++){const i=e[n];for(const r in i)if(r==="class")t.class!==i.class&&(t.class=Vo([t.class,i.class]));else if(r==="style")t.style=$o([t.style,i.style]);else if(hs(r)){const s=t[r],o=i[r];o&&s!==o&&!(R(s)&&s.includes(o))&&(t[r]=s?[].concat(s,o):o)}else r!==""&&(t[r]=i[r])}return t}function _e(e,t,n,i=null){de(e,t,7,[n,i])}const Wh=Cl();let Hh=0;function Yh(e,t,n){const i=e.type,r=(t?t.appContext:e.appContext)||Wh,s={uid:Hh++,vnode:e,type:i,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,scope:new yd(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Ll(i,r),emitsOptions:wl(i,r),emit:null,emitted:null,propsDefaults:dt,inheritAttrs:i.inheritAttrs,ctx:dt,data:dt,props:dt,attrs:dt,slots:dt,refs:dt,setupState:dt,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return s.ctx={_:s},s.root=t?t.root:s,s.emit=Xd.bind(null,s),e.ce&&e.ce(s),s}let At=null,ea,Hn,pc="__VUE_INSTANCE_SETTERS__";(Hn=so()[pc])||(Hn=so()[pc]=[]),Hn.push(e=>At=e),ea=e=>{Hn.length>1?Hn.forEach(t=>t(e)):Hn[0](e)};const ui=e=>{ea(e),e.scope.on()},Ln=()=>{At&&At.scope.off(),ea(null)};function Hl(e){return e.vnode.shapeFlag&4}let Hi=!1;function Kh(e,t=!1){Hi=t;const{props:n,children:i}=e.vnode,r=Hl(e);Dh(e,n,r,t),Mh(e,i);const s=r?Jh(e,t):void 0;return Hi=!1,s}function Jh(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=hl(new Proxy(e.ctx,_h));const{setup:i}=n;if(i){const r=e.setupContext=i.length>1?Zh(e):null;ui(e),vi();const s=ln(i,e,0,[e.props,r]);if(wi(),Ln(),Zc(s)){if(s.then(Ln,Ln),t)return s.then(o=>{yc(e,o,t)}).catch(o=>{bs(o,e,0)});e.asyncDep=s}else yc(e,s,t)}else Yl(e,t)}function yc(e,t,n){V(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:gt(t)&&(e.setupState=ml(t)),Yl(e,n)}let mc;function Yl(e,t,n){const i=e.type;if(!e.render){if(!t&&mc&&!i.render){const r=i.template||Qo(e).template;if(r){const{isCustomElement:s,compilerOptions:o}=e.appContext.config,{delimiters:a,compilerOptions:c}=i,l=Ft(Ft({isCustomElement:s,delimiters:a},o),c);i.render=mc(r,l)}}e.render=i.render||Ee}{ui(e),vi();try{vh(e)}finally{wi(),Ln()}}}function qh(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get(t,n){return zt(e,"get","$attrs"),t[n]}}))}function Zh(e){const t=n=>{e.exposed=n||{}};return{get attrs(){return qh(e)},slots:e.slots,emit:e.emit,expose:t}}function na(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(ml(hl(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Ri)return Ri[n](e)},has(t,n){return n in t||n in Ri}}))}function Xh(e,t=!0){return V(e)?e.displayName||e.name:e.name||t&&e.__name}function Qh(e){return V(e)&&"__vccOpts"in e}const Kl=(e,t)=>Wd(e,t,Hi),Gh=Symbol.for("v-scx"),tp=()=>Ir(Gh),ep="3.3.13",np="http://www.w3.org/2000/svg",Dn=typeof document<"u"?document:null,gc=Dn&&Dn.createElement("template"),ip={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,i)=>{const r=t?Dn.createElementNS(np,e):Dn.createElement(e,n?{is:n}:void 0);return e==="select"&&i&&i.multiple!=null&&r.setAttribute("multiple",i.multiple),r},createText:e=>Dn.createTextNode(e),createComment:e=>Dn.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Dn.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,i,r,s){const o=n?n.previousSibling:t.lastChild;if(r&&(r===s||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===s||!(r=r.nextSibling)););else{gc.innerHTML=i?`<svg>${e}</svg>`:e;const a=gc.content;if(i){const c=a.firstChild;for(;c.firstChild;)a.appendChild(c.firstChild);a.removeChild(c)}t.insertBefore(a,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},rp=Symbol("_vtc");function sp(e,t,n){const i=e[rp];i&&(t=(t?[t,...i]:[...i]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const op=Symbol("_vod"),ap=Symbol("");function cp(e,t,n){const i=e.style,r=Bt(n);if(n&&!r){if(t&&!Bt(t))for(const s in t)n[s]==null&&bo(i,s,"");for(const s in n)bo(i,s,n[s])}else{const s=i.display;if(r){if(t!==n){const o=i[ap];o&&(n+=";"+o),i.cssText=n}}else t&&e.removeAttribute("style");op in e&&(i.display=s)}}const bc=/\s*!important$/;function bo(e,t,n){if(R(n))n.forEach(i=>bo(e,t,i));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const i=lp(e,t);bc.test(n)?e.setProperty(_i(i),n.replace(bc,""),"important"):e[i]=n}}const _c=["Webkit","Moz","ms"],qs={};function lp(e,t){const n=qs[t];if(n)return n;let i=Ce(t);if(i!=="filter"&&i in e)return qs[t]=i;i=ms(i);for(let r=0;r<_c.length;r++){const s=_c[r]+i;if(s in e)return qs[t]=s}return t}const vc="http://www.w3.org/1999/xlink";function up(e,t,n,i,r){if(i&&t.startsWith("xlink:"))n==null?e.removeAttributeNS(vc,t.slice(6,t.length)):e.setAttributeNS(vc,t,n);else{const s=hd(t);n==null||s&&!Gc(n)?e.removeAttribute(t):e.setAttribute(t,s?"":n)}}function fp(e,t,n,i,r,s,o){if(t==="innerHTML"||t==="textContent"){i&&o(i,r,s),e[t]=n??"";return}const a=e.tagName;if(t==="value"&&a!=="PROGRESS"&&!a.includes("-")){e._value=n;const l=a==="OPTION"?e.getAttribute("value"):e.value,f=n??"";l!==f&&(e.value=f),n==null&&e.removeAttribute(t);return}let c=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Gc(n):n==null&&l==="string"?(n="",c=!0):l==="number"&&(n=0,c=!0)}try{e[t]=n}catch{}c&&e.removeAttribute(t)}function dp(e,t,n,i){e.addEventListener(t,n,i)}function hp(e,t,n,i){e.removeEventListener(t,n,i)}const wc=Symbol("_vei");function pp(e,t,n,i,r=null){const s=e[wc]||(e[wc]={}),o=s[t];if(i&&o)o.value=i;else{const[a,c]=yp(t);if(i){const l=s[t]=bp(i,r);dp(e,a,l,c)}else o&&(hp(e,a,o,c),s[t]=void 0)}}const Sc=/(?:Once|Passive|Capture)$/;function yp(e){let t;if(Sc.test(e)){t={};let i;for(;i=e.match(Sc);)e=e.slice(0,e.length-i[0].length),t[i[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):_i(e.slice(2)),t]}let Zs=0;const mp=Promise.resolve(),gp=()=>Zs||(mp.then(()=>Zs=0),Zs=Date.now());function bp(e,t){const n=i=>{if(!i._vts)i._vts=Date.now();else if(i._vts<=n.attached)return;de(_p(i,n.value),t,5,[i])};return n.value=e,n.attached=gp(),n}function _p(e,t){if(R(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(i=>r=>!r._stopped&&i&&i(r))}else return t}const Ic=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,vp=(e,t,n,i,r=!1,s,o,a,c)=>{t==="class"?sp(e,i,r):t==="style"?cp(e,n,i):hs(t)?Uo(t)||pp(e,t,n,i,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):wp(e,t,i,r))?fp(e,t,i,s,o,a,c):(t==="true-value"?e._trueValue=i:t==="false-value"&&(e._falseValue=i),up(e,t,i,r))};function wp(e,t,n,i){if(i)return!!(t==="innerHTML"||t==="textContent"||t in e&&Ic(t)&&V(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return Ic(t)&&Bt(n)?!1:t in e}const Sp=Ft({patchProp:vp},ip);let Bc;function Ip(){return Bc||(Bc=Eh(Sp))}const Bp=(...e)=>{const t=Ip().createApp(...e),{mount:n}=t;return t.mount=i=>{const r=Op(i);if(!r)return;const s=t._component;!V(s)&&!s.render&&!s.template&&(s.template=r.innerHTML),r.innerHTML="";const o=n(r,!1,r instanceof SVGElement);return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),o},t};function Op(e){return Bt(e)?document.querySelector(e):e}function Ap(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Jl={exports:{}},it={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ot=typeof Symbol=="function"&&Symbol.for,ia=Ot?Symbol.for("react.element"):60103,ra=Ot?Symbol.for("react.portal"):60106,Os=Ot?Symbol.for("react.fragment"):60107,As=Ot?Symbol.for("react.strict_mode"):60108,Fs=Ot?Symbol.for("react.profiler"):60114,Ds=Ot?Symbol.for("react.provider"):60109,Ts=Ot?Symbol.for("react.context"):60110,sa=Ot?Symbol.for("react.async_mode"):60111,xs=Ot?Symbol.for("react.concurrent_mode"):60111,Ms=Ot?Symbol.for("react.forward_ref"):60112,Ns=Ot?Symbol.for("react.suspense"):60113,Fp=Ot?Symbol.for("react.suspense_list"):60120,Es=Ot?Symbol.for("react.memo"):60115,Cs=Ot?Symbol.for("react.lazy"):60116,Dp=Ot?Symbol.for("react.block"):60121,Tp=Ot?Symbol.for("react.fundamental"):60117,xp=Ot?Symbol.for("react.responder"):60118,Mp=Ot?Symbol.for("react.scope"):60119;function Gt(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case ia:switch(e=e.type,e){case sa:case xs:case Os:case Fs:case As:case Ns:return e;default:switch(e=e&&e.$$typeof,e){case Ts:case Ms:case Cs:case Es:case Ds:return e;default:return t}}case ra:return t}}}function ql(e){return Gt(e)===xs}it.AsyncMode=sa;it.ConcurrentMode=xs;it.ContextConsumer=Ts;it.ContextProvider=Ds;it.Element=ia;it.ForwardRef=Ms;it.Fragment=Os;it.Lazy=Cs;it.Memo=Es;it.Portal=ra;it.Profiler=Fs;it.StrictMode=As;it.Suspense=Ns;it.isAsyncMode=function(e){return ql(e)||Gt(e)===sa};it.isConcurrentMode=ql;it.isContextConsumer=function(e){return Gt(e)===Ts};it.isContextProvider=function(e){return Gt(e)===Ds};it.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===ia};it.isForwardRef=function(e){return Gt(e)===Ms};it.isFragment=function(e){return Gt(e)===Os};it.isLazy=function(e){return Gt(e)===Cs};it.isMemo=function(e){return Gt(e)===Es};it.isPortal=function(e){return Gt(e)===ra};it.isProfiler=function(e){return Gt(e)===Fs};it.isStrictMode=function(e){return Gt(e)===As};it.isSuspense=function(e){return Gt(e)===Ns};it.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===Os||e===xs||e===Fs||e===As||e===Ns||e===Fp||typeof e=="object"&&e!==null&&(e.$$typeof===Cs||e.$$typeof===Es||e.$$typeof===Ds||e.$$typeof===Ts||e.$$typeof===Ms||e.$$typeof===Tp||e.$$typeof===xp||e.$$typeof===Mp||e.$$typeof===Dp)};it.typeOf=Gt;Jl.exports=it;var Np=Jl.exports,Zl=Np,Ep={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},Cp={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Xl={};Xl[Zl.ForwardRef]=Ep;Xl[Zl.Memo]=Cp;var Ql={exports:{}},rt={};/*
object-assign
(c) Sindre Sorhus
@license MIT
*/var Oc=Object.getOwnPropertySymbols,Rp=Object.prototype.hasOwnProperty,Lp=Object.prototype.propertyIsEnumerable;function Up(e){if(e==null)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}function Pp(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de",Object.getOwnPropertyNames(e)[0]==="5")return!1;for(var t={},n=0;n<10;n++)t["_"+String.fromCharCode(n)]=n;var i=Object.getOwnPropertyNames(t).map(function(s){return t[s]});if(i.join("")!=="**********")return!1;var r={};return"abcdefghijklmnopqrst".split("").forEach(function(s){r[s]=s}),Object.keys(Object.assign({},r)).join("")==="abcdefghijklmnopqrst"}catch{return!1}}var jp=Pp()?Object.assign:function(e,t){for(var n,i=Up(e),r,s=1;s<arguments.length;s++){n=Object(arguments[s]);for(var o in n)Rp.call(n,o)&&(i[o]=n[o]);if(Oc){r=Oc(n);for(var a=0;a<r.length;a++)Lp.call(n,r[a])&&(i[r[a]]=n[r[a]])}}return i};/** @license React v16.14.0
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var oa=jp,pe=typeof Symbol=="function"&&Symbol.for,Gi=pe?Symbol.for("react.element"):60103,$p=pe?Symbol.for("react.portal"):60106,Vp=pe?Symbol.for("react.fragment"):60107,zp=pe?Symbol.for("react.strict_mode"):60108,kp=pe?Symbol.for("react.profiler"):60114,Wp=pe?Symbol.for("react.provider"):60109,Hp=pe?Symbol.for("react.context"):60110,Yp=pe?Symbol.for("react.forward_ref"):60112,Kp=pe?Symbol.for("react.suspense"):60113,Jp=pe?Symbol.for("react.memo"):60115,qp=pe?Symbol.for("react.lazy"):60116,Ac=typeof Symbol=="function"&&Symbol.iterator;function tr(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Gl={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},tu={};function Si(e,t,n){this.props=e,this.context=t,this.refs=tu,this.updater=n||Gl}Si.prototype.isReactComponent={};Si.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error(tr(85));this.updater.enqueueSetState(this,e,t,"setState")};Si.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function eu(){}eu.prototype=Si.prototype;function aa(e,t,n){this.props=e,this.context=t,this.refs=tu,this.updater=n||Gl}var ca=aa.prototype=new eu;ca.constructor=aa;oa(ca,Si.prototype);ca.isPureReactComponent=!0;var la={current:null},nu=Object.prototype.hasOwnProperty,iu={key:!0,ref:!0,__self:!0,__source:!0};function ru(e,t,n){var i,r={},s=null,o=null;if(t!=null)for(i in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(s=""+t.key),t)nu.call(t,i)&&!iu.hasOwnProperty(i)&&(r[i]=t[i]);var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){for(var c=Array(a),l=0;l<a;l++)c[l]=arguments[l+2];r.children=c}if(e&&e.defaultProps)for(i in a=e.defaultProps,a)r[i]===void 0&&(r[i]=a[i]);return{$$typeof:Gi,type:e,key:s,ref:o,props:r,_owner:la.current}}function Zp(e,t){return{$$typeof:Gi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function ua(e){return typeof e=="object"&&e!==null&&e.$$typeof===Gi}function Xp(e){var t={"=":"=0",":":"=2"};return"$"+(""+e).replace(/[=:]/g,function(n){return t[n]})}var su=/\/+/g,Ur=[];function ou(e,t,n,i){if(Ur.length){var r=Ur.pop();return r.result=e,r.keyPrefix=t,r.func=n,r.context=i,r.count=0,r}return{result:e,keyPrefix:t,func:n,context:i,count:0}}function au(e){e.result=null,e.keyPrefix=null,e.func=null,e.context=null,e.count=0,10>Ur.length&&Ur.push(e)}function _o(e,t,n,i){var r=typeof e;(r==="undefined"||r==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(r){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case Gi:case $p:s=!0}}if(s)return n(i,e,t===""?"."+Xs(e,0):t),1;if(s=0,t=t===""?".":t+":",Array.isArray(e))for(var o=0;o<e.length;o++){r=e[o];var a=t+Xs(r,o);s+=_o(r,a,n,i)}else if(e===null||typeof e!="object"?a=null:(a=Ac&&e[Ac]||e["@@iterator"],a=typeof a=="function"?a:null),typeof a=="function")for(e=a.call(e),o=0;!(r=e.next()).done;)r=r.value,a=t+Xs(r,o++),s+=_o(r,a,n,i);else if(r==="object")throw n=""+e,Error(tr(31,n==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":n,""));return s}function vo(e,t,n){return e==null?0:_o(e,"",t,n)}function Xs(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Xp(e.key):t.toString(36)}function Qp(e,t){e.func.call(e.context,t,e.count++)}function Gp(e,t,n){var i=e.result,r=e.keyPrefix;e=e.func.call(e.context,t,e.count++),Array.isArray(e)?wo(e,i,n,function(s){return s}):e!=null&&(ua(e)&&(e=Zp(e,r+(!e.key||t&&t.key===e.key?"":(""+e.key).replace(su,"$&/")+"/")+n)),i.push(e))}function wo(e,t,n,i,r){var s="";n!=null&&(s=(""+n).replace(su,"$&/")+"/"),t=ou(t,s,i,r),vo(e,Gp,t),au(t)}var cu={current:null};function Qe(){var e=cu.current;if(e===null)throw Error(tr(321));return e}var ty={ReactCurrentDispatcher:cu,ReactCurrentBatchConfig:{suspense:null},ReactCurrentOwner:la,IsSomeRendererActing:{current:!1},assign:oa};rt.Children={map:function(e,t,n){if(e==null)return e;var i=[];return wo(e,i,null,t,n),i},forEach:function(e,t,n){if(e==null)return e;t=ou(null,null,t,n),vo(e,Qp,t),au(t)},count:function(e){return vo(e,function(){return null},null)},toArray:function(e){var t=[];return wo(e,t,null,function(n){return n}),t},only:function(e){if(!ua(e))throw Error(tr(143));return e}};rt.Component=Si;rt.Fragment=Vp;rt.Profiler=kp;rt.PureComponent=aa;rt.StrictMode=zp;rt.Suspense=Kp;rt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ty;rt.cloneElement=function(e,t,n){if(e==null)throw Error(tr(267,e));var i=oa({},e.props),r=e.key,s=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,o=la.current),t.key!==void 0&&(r=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(c in t)nu.call(t,c)&&!iu.hasOwnProperty(c)&&(i[c]=t[c]===void 0&&a!==void 0?a[c]:t[c])}var c=arguments.length-2;if(c===1)i.children=n;else if(1<c){a=Array(c);for(var l=0;l<c;l++)a[l]=arguments[l+2];i.children=a}return{$$typeof:Gi,type:e.type,key:r,ref:s,props:i,_owner:o}};rt.createContext=function(e,t){return t===void 0&&(t=null),e={$$typeof:Hp,_calculateChangedBits:t,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null},e.Provider={$$typeof:Wp,_context:e},e.Consumer=e};rt.createElement=ru;rt.createFactory=function(e){var t=ru.bind(null,e);return t.type=e,t};rt.createRef=function(){return{current:null}};rt.forwardRef=function(e){return{$$typeof:Yp,render:e}};rt.isValidElement=ua;rt.lazy=function(e){return{$$typeof:qp,_ctor:e,_status:-1,_result:null}};rt.memo=function(e,t){return{$$typeof:Jp,type:e,compare:t===void 0?null:t}};rt.useCallback=function(e,t){return Qe().useCallback(e,t)};rt.useContext=function(e,t){return Qe().useContext(e,t)};rt.useDebugValue=function(){};rt.useEffect=function(e,t){return Qe().useEffect(e,t)};rt.useImperativeHandle=function(e,t,n){return Qe().useImperativeHandle(e,t,n)};rt.useLayoutEffect=function(e,t){return Qe().useLayoutEffect(e,t)};rt.useMemo=function(e,t){return Qe().useMemo(e,t)};rt.useReducer=function(e,t,n){return Qe().useReducer(e,t,n)};rt.useRef=function(e){return Qe().useRef(e)};rt.useState=function(e){return Qe().useState(e)};rt.version="16.14.0";Ql.exports=rt;var ey=Ql.exports;const ny=Ap(ey);function D(e,t,n,i){function r(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function a(f){try{l(i.next(f))}catch(p){o(p)}}function c(f){try{l(i.throw(f))}catch(p){o(p)}}function l(f){f.done?s(f.value):r(f.value).then(a,c)}l((i=i.apply(e,t||[])).next())})}function Fc(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],i=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&i>=e.length&&(e=void 0),{value:e&&e[i++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function $(e){return this instanceof $?(this.v=e,this):new $(e)}function Me(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var i=n.apply(e,t||[]),r,s=[];return r={},o("next"),o("throw"),o("return"),r[Symbol.asyncIterator]=function(){return this},r;function o(m){i[m]&&(r[m]=function(I){return new Promise(function(k,W){s.push([m,I,k,W])>1||a(m,I)})})}function a(m,I){try{c(i[m](I))}catch(k){p(s[0][3],k)}}function c(m){m.value instanceof $?Promise.resolve(m.value.v).then(l,f):p(s[0][2],m)}function l(m){a("next",m)}function f(m){a("throw",m)}function p(m,I){m(I),s.shift(),s.length&&a(s[0][0],s[0][1])}}function Or(e){var t,n;return t={},i("next"),i("throw",function(r){throw r}),i("return"),t[Symbol.iterator]=function(){return this},t;function i(r,s){t[r]=e[r]?function(o){return(n=!n)?{value:$(e[r](o)),done:!1}:s?s(o):o}:s}}function Un(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof Fc=="function"?Fc(e):e[Symbol.iterator](),n={},i("next"),i("throw"),i("return"),n[Symbol.asyncIterator]=function(){return this},n);function i(s){n[s]=e[s]&&function(o){return new Promise(function(a,c){o=e[s](o),r(a,c,o.done,o.value)})}}function r(s,o,a,c){Promise.resolve(c).then(function(l){s({value:l,done:a})},o)}}const iy=new TextDecoder("utf-8"),So=e=>iy.decode(e),ry=new TextEncoder,fa=e=>ry.encode(e),[nb,sy]=(()=>{const e=()=>{throw new Error("BigInt is not available in this environment")};function t(){throw e()}return t.asIntN=()=>{throw e()},t.asUintN=()=>{throw e()},typeof BigInt<"u"?[BigInt,!0]:[t,!1]})(),[er,ib]=(()=>{const e=()=>{throw new Error("BigInt64Array is not available in this environment")};class t{static get BYTES_PER_ELEMENT(){return 8}static of(){throw e()}static from(){throw e()}constructor(){throw e()}}return typeof BigInt64Array<"u"?[BigInt64Array,!0]:[t,!1]})(),[nr,rb]=(()=>{const e=()=>{throw new Error("BigUint64Array is not available in this environment")};class t{static get BYTES_PER_ELEMENT(){return 8}static of(){throw e()}static from(){throw e()}constructor(){throw e()}}return typeof BigUint64Array<"u"?[BigUint64Array,!0]:[t,!1]})(),oy=e=>typeof e=="number",lu=e=>typeof e=="boolean",St=e=>typeof e=="function",Wt=e=>e!=null&&Object(e)===e,dn=e=>Wt(e)&&St(e.then),ir=e=>Wt(e)&&St(e[Symbol.iterator]),Ii=e=>Wt(e)&&St(e[Symbol.asyncIterator]),Io=e=>Wt(e)&&Wt(e.schema),uu=e=>Wt(e)&&"done"in e&&"value"in e,fu=e=>Wt(e)&&St(e.stat)&&oy(e.fd),du=e=>Wt(e)&&da(e.body),Rs=e=>"_getDOMStream"in e&&"_getNodeStream"in e,ay=e=>Wt(e)&&St(e.abort)&&St(e.getWriter)&&!Rs(e),da=e=>Wt(e)&&St(e.cancel)&&St(e.getReader)&&!Rs(e),cy=e=>Wt(e)&&St(e.end)&&St(e.write)&&lu(e.writable)&&!Rs(e),hu=e=>Wt(e)&&St(e.read)&&St(e.pipe)&&lu(e.readable)&&!Rs(e),ly=e=>Wt(e)&&St(e.clear)&&St(e.bytes)&&St(e.position)&&St(e.setPosition)&&St(e.capacity)&&St(e.getBufferIdentifier)&&St(e.createLong),ha=typeof SharedArrayBuffer<"u"?SharedArrayBuffer:ArrayBuffer;function uy(e){const t=e[0]?[e[0]]:[];let n,i,r,s;for(let o,a,c=0,l=0,f=e.length;++c<f;){if(o=t[l],a=e[c],!o||!a||o.buffer!==a.buffer||a.byteOffset<o.byteOffset){a&&(t[++l]=a);continue}if({byteOffset:n,byteLength:r}=o,{byteOffset:i,byteLength:s}=a,n+r<i||i+s<n){a&&(t[++l]=a);continue}t[l]=new Uint8Array(o.buffer,n,i-n+s)}return t}function Dc(e,t,n=0,i=t.byteLength){const r=e.byteLength,s=new Uint8Array(e.buffer,e.byteOffset,r),o=new Uint8Array(t.buffer,t.byteOffset,Math.min(i,r));return s.set(o,n),e}function Re(e,t){const n=uy(e),i=n.reduce((f,p)=>f+p.byteLength,0);let r,s,o,a=0,c=-1;const l=Math.min(t||Number.POSITIVE_INFINITY,i);for(const f=n.length;++c<f;){if(r=n[c],s=r.subarray(0,Math.min(r.length,l-a)),l<=a+s.length){s.length<r.length?n[c]=r.subarray(s.length):s.length===r.length&&c++,o?Dc(o,s,a):o=s;break}Dc(o||(o=new Uint8Array(l)),s,a),a+=s.length}return[o||new Uint8Array(0),n.slice(c),i-(o?o.byteLength:0)]}function ot(e,t){let n=uu(t)?t.value:t;return n instanceof e?e===Uint8Array?new e(n.buffer,n.byteOffset,n.byteLength):n:n?(typeof n=="string"&&(n=fa(n)),n instanceof ArrayBuffer?new e(n):n instanceof ha?new e(n):ly(n)?ot(e,n.bytes()):ArrayBuffer.isView(n)?n.byteLength<=0?new e(0):new e(n.buffer,n.byteOffset,n.byteLength/e.BYTES_PER_ELEMENT):e.from(n)):new e(0)}const Ti=e=>ot(Int32Array,e),Z=e=>ot(Uint8Array,e),Bo=e=>(e.next(),e);function*fy(e,t){const n=function*(r){yield r},i=typeof t=="string"||ArrayBuffer.isView(t)||t instanceof ArrayBuffer||t instanceof ha?n(t):ir(t)?t:n(t);return yield*Bo(function*(r){let s=null;do s=r.next(yield ot(e,s));while(!s.done)}(i[Symbol.iterator]())),new e}const dy=e=>fy(Uint8Array,e);function pu(e,t){return Me(this,arguments,function*(){if(dn(t))return yield $(yield $(yield*Or(Un(pu(e,yield $(t))))));const i=function(o){return Me(this,arguments,function*(){yield yield $(yield $(o))})},r=function(o){return Me(this,arguments,function*(){yield $(yield*Or(Un(Bo(function*(a){let c=null;do c=a.next(yield c==null?void 0:c.value);while(!c.done)}(o[Symbol.iterator]())))))})},s=typeof t=="string"||ArrayBuffer.isView(t)||t instanceof ArrayBuffer||t instanceof ha?i(t):ir(t)?r(t):Ii(t)?t:i(t);return yield $(yield*Or(Un(Bo(function(o){return Me(this,arguments,function*(){let a=null;do a=yield $(o.next(yield yield $(ot(e,a))));while(!a.done)})}(s[Symbol.asyncIterator]()))))),yield $(new e)})}const hy=e=>pu(Uint8Array,e);function pa(e,t,n){if(e!==0){n=n.slice(0,t+1);for(let i=-1;++i<=t;)n[i]+=e}return n}function py(e,t){let n=0;const i=e.length;if(i!==t.length)return!1;if(i>0)do if(e[n]!==t[n])return!1;while(++n<i);return!0}const ee={fromIterable(e){return br(yy(e))},fromAsyncIterable(e){return br(my(e))},fromDOMStream(e){return br(gy(e))},fromNodeStream(e){return br(_y(e))},toDOMStream(e,t){throw new Error('"toDOMStream" not available in this environment')},toNodeStream(e,t){throw new Error('"toNodeStream" not available in this environment')}},br=e=>(e.next(),e);function*yy(e){let t,n=!1,i=[],r,s,o,a=0;function c(){return s==="peek"?Re(i,o)[0]:([r,i,a]=Re(i,o),r)}({cmd:s,size:o}=yield null);const l=dy(e)[Symbol.iterator]();try{do if({done:t,value:r}=Number.isNaN(o-a)?l.next():l.next(o-a),!t&&r.byteLength>0&&(i.push(r),a+=r.byteLength),t||o<=a)do({cmd:s,size:o}=yield c());while(o<a);while(!t)}catch(f){(n=!0)&&typeof l.throw=="function"&&l.throw(f)}finally{n===!1&&typeof l.return=="function"&&l.return(null)}return null}function my(e){return Me(this,arguments,function*(){let n,i=!1,r=[],s,o,a,c=0;function l(){return o==="peek"?Re(r,a)[0]:([s,r,c]=Re(r,a),s)}({cmd:o,size:a}=yield yield $(null));const f=hy(e)[Symbol.asyncIterator]();try{do if({done:n,value:s}=Number.isNaN(a-c)?yield $(f.next()):yield $(f.next(a-c)),!n&&s.byteLength>0&&(r.push(s),c+=s.byteLength),n||a<=c)do({cmd:o,size:a}=yield yield $(l()));while(a<c);while(!n)}catch(p){(i=!0)&&typeof f.throw=="function"&&(yield $(f.throw(p)))}finally{i===!1&&typeof f.return=="function"&&(yield $(f.return(new Uint8Array(0))))}return yield $(null)})}function gy(e){return Me(this,arguments,function*(){let n=!1,i=!1,r=[],s,o,a,c=0;function l(){return o==="peek"?Re(r,a)[0]:([s,r,c]=Re(r,a),s)}({cmd:o,size:a}=yield yield $(null));const f=new by(e);try{do if({done:n,value:s}=Number.isNaN(a-c)?yield $(f.read()):yield $(f.read(a-c)),!n&&s.byteLength>0&&(r.push(Z(s)),c+=s.byteLength),n||a<=c)do({cmd:o,size:a}=yield yield $(l()));while(a<c);while(!n)}catch(p){(i=!0)&&(yield $(f.cancel(p)))}finally{i===!1?yield $(f.cancel()):e.locked&&f.releaseLock()}return yield $(null)})}class by{constructor(t){this.source=t,this.reader=null,this.reader=this.source.getReader(),this.reader.closed.catch(()=>{})}get closed(){return this.reader?this.reader.closed.catch(()=>{}):Promise.resolve()}releaseLock(){this.reader&&this.reader.releaseLock(),this.reader=null}cancel(t){return D(this,void 0,void 0,function*(){const{reader:n,source:i}=this;n&&(yield n.cancel(t).catch(()=>{})),i&&i.locked&&this.releaseLock()})}read(t){return D(this,void 0,void 0,function*(){if(t===0)return{done:this.reader==null,value:new Uint8Array(0)};const n=yield this.reader.read();return!n.done&&(n.value=Z(n)),n})}}const Qs=(e,t)=>{const n=r=>i([t,r]);let i;return[t,n,new Promise(r=>(i=r)&&e.once(t,n))]};function _y(e){return Me(this,arguments,function*(){const n=[];let i="error",r=!1,s=null,o,a,c=0,l=[],f;function p(){return o==="peek"?Re(l,a)[0]:([f,l,c]=Re(l,a),f)}if({cmd:o,size:a}=yield yield $(null),e.isTTY)return yield yield $(new Uint8Array(0)),yield $(null);try{n[0]=Qs(e,"end"),n[1]=Qs(e,"error");do{if(n[2]=Qs(e,"readable"),[i,s]=yield $(Promise.race(n.map(I=>I[2]))),i==="error")break;if((r=i==="end")||(Number.isFinite(a-c)?(f=Z(e.read(a-c)),f.byteLength<a-c&&(f=Z(e.read()))):f=Z(e.read()),f.byteLength>0&&(l.push(f),c+=f.byteLength)),r||a<=c)do({cmd:o,size:a}=yield yield $(p()));while(a<c)}while(!r)}finally{yield $(m(n,i==="error"?s:null))}return yield $(null);function m(I,k){return f=l=null,new Promise((W,bt)=>{for(const[ht,wt]of I)e.off(ht,wt);try{const ht=e.destroy;ht&&ht.call(e,k),k=void 0}catch(ht){k=ht||k}finally{k!=null?bt(k):W()}})}})}var Zt;(function(e){e[e.V1=0]="V1",e[e.V2=1]="V2",e[e.V3=2]="V3",e[e.V4=3]="V4",e[e.V5=4]="V5"})(Zt||(Zt={}));var Xt;(function(e){e[e.Sparse=0]="Sparse",e[e.Dense=1]="Dense"})(Xt||(Xt={}));var Vt;(function(e){e[e.HALF=0]="HALF",e[e.SINGLE=1]="SINGLE",e[e.DOUBLE=2]="DOUBLE"})(Vt||(Vt={}));var Ze;(function(e){e[e.DAY=0]="DAY",e[e.MILLISECOND=1]="MILLISECOND"})(Ze||(Ze={}));var X;(function(e){e[e.SECOND=0]="SECOND",e[e.MILLISECOND=1]="MILLISECOND",e[e.MICROSECOND=2]="MICROSECOND",e[e.NANOSECOND=3]="NANOSECOND"})(X||(X={}));var hn;(function(e){e[e.YEAR_MONTH=0]="YEAR_MONTH",e[e.DAY_TIME=1]="DAY_TIME",e[e.MONTH_DAY_NANO=2]="MONTH_DAY_NANO"})(hn||(hn={}));var G;(function(e){e[e.NONE=0]="NONE",e[e.Schema=1]="Schema",e[e.DictionaryBatch=2]="DictionaryBatch",e[e.RecordBatch=3]="RecordBatch",e[e.Tensor=4]="Tensor",e[e.SparseTensor=5]="SparseTensor"})(G||(G={}));var h;(function(e){e[e.NONE=0]="NONE",e[e.Null=1]="Null",e[e.Int=2]="Int",e[e.Float=3]="Float",e[e.Binary=4]="Binary",e[e.Utf8=5]="Utf8",e[e.Bool=6]="Bool",e[e.Decimal=7]="Decimal",e[e.Date=8]="Date",e[e.Time=9]="Time",e[e.Timestamp=10]="Timestamp",e[e.Interval=11]="Interval",e[e.List=12]="List",e[e.Struct=13]="Struct",e[e.Union=14]="Union",e[e.FixedSizeBinary=15]="FixedSizeBinary",e[e.FixedSizeList=16]="FixedSizeList",e[e.Map=17]="Map",e[e.Dictionary=-1]="Dictionary",e[e.Int8=-2]="Int8",e[e.Int16=-3]="Int16",e[e.Int32=-4]="Int32",e[e.Int64=-5]="Int64",e[e.Uint8=-6]="Uint8",e[e.Uint16=-7]="Uint16",e[e.Uint32=-8]="Uint32",e[e.Uint64=-9]="Uint64",e[e.Float16=-10]="Float16",e[e.Float32=-11]="Float32",e[e.Float64=-12]="Float64",e[e.DateDay=-13]="DateDay",e[e.DateMillisecond=-14]="DateMillisecond",e[e.TimestampSecond=-15]="TimestampSecond",e[e.TimestampMillisecond=-16]="TimestampMillisecond",e[e.TimestampMicrosecond=-17]="TimestampMicrosecond",e[e.TimestampNanosecond=-18]="TimestampNanosecond",e[e.TimeSecond=-19]="TimeSecond",e[e.TimeMillisecond=-20]="TimeMillisecond",e[e.TimeMicrosecond=-21]="TimeMicrosecond",e[e.TimeNanosecond=-22]="TimeNanosecond",e[e.DenseUnion=-23]="DenseUnion",e[e.SparseUnion=-24]="SparseUnion",e[e.IntervalDayTime=-25]="IntervalDayTime",e[e.IntervalYearMonth=-26]="IntervalYearMonth"})(h||(h={}));var Ve;(function(e){e[e.OFFSET=0]="OFFSET",e[e.DATA=1]="DATA",e[e.VALIDITY=2]="VALIDITY",e[e.TYPE=3]="TYPE"})(Ve||(Ve={}));const vy=void 0;function Yi(e){if(e===null)return"null";if(e===vy)return"undefined";switch(typeof e){case"number":return`${e}`;case"bigint":return`${e}`;case"string":return`"${e}"`}return typeof e[Symbol.toPrimitive]=="function"?e[Symbol.toPrimitive]("string"):ArrayBuffer.isView(e)?e instanceof er||e instanceof nr?`[${[...e].map(t=>Yi(t))}]`:`[${e}]`:ArrayBuffer.isView(e)?`[${e}]`:JSON.stringify(e,(t,n)=>typeof n=="bigint"?`${n}`:n)}const wy=Symbol.for("isArrowBigNum");function ye(e,...t){return t.length===0?Object.setPrototypeOf(ot(this.TypedArray,e),this.constructor.prototype):Object.setPrototypeOf(new this.TypedArray(e,...t),this.constructor.prototype)}ye.prototype[wy]=!0;ye.prototype.toJSON=function(){return`"${Pn(this)}"`};ye.prototype.valueOf=function(){return yu(this)};ye.prototype.toString=function(){return Pn(this)};ye.prototype[Symbol.toPrimitive]=function(e="default"){switch(e){case"number":return yu(this);case"string":return Pn(this);case"default":return Oo(this)}return Pn(this)};function ii(...e){return ye.apply(this,e)}function ri(...e){return ye.apply(this,e)}function Ki(...e){return ye.apply(this,e)}Object.setPrototypeOf(ii.prototype,Object.create(Int32Array.prototype));Object.setPrototypeOf(ri.prototype,Object.create(Uint32Array.prototype));Object.setPrototypeOf(Ki.prototype,Object.create(Uint32Array.prototype));Object.assign(ii.prototype,ye.prototype,{constructor:ii,signed:!0,TypedArray:Int32Array,BigIntArray:er});Object.assign(ri.prototype,ye.prototype,{constructor:ri,signed:!1,TypedArray:Uint32Array,BigIntArray:nr});Object.assign(Ki.prototype,ye.prototype,{constructor:Ki,signed:!0,TypedArray:Uint32Array,BigIntArray:nr});function yu(e){const{buffer:t,byteOffset:n,length:i,signed:r}=e,s=new nr(t,n,i),o=r&&s[s.length-1]&BigInt(1)<<BigInt(63);let a=BigInt(o?1:0),c=BigInt(0);if(o){for(const l of s)a+=~l*(BigInt(1)<<BigInt(32)*c++);a*=BigInt(-1)}else for(const l of s)a+=l*(BigInt(1)<<BigInt(32)*c++);return a}let Pn,Oo;sy?(Oo=e=>e.byteLength===8?new e.BigIntArray(e.buffer,e.byteOffset,1)[0]:Gs(e),Pn=e=>e.byteLength===8?`${new e.BigIntArray(e.buffer,e.byteOffset,1)[0]}`:Gs(e)):(Pn=Gs,Oo=Pn);function Gs(e){let t="";const n=new Uint32Array(2);let i=new Uint16Array(e.buffer,e.byteOffset,e.byteLength/2);const r=new Uint32Array((i=new Uint16Array(i).reverse()).buffer);let s=-1;const o=i.length-1;do{for(n[0]=i[s=0];s<o;)i[s++]=n[1]=n[0]/10,n[0]=(n[0]-n[1]*10<<16)+i[s];i[s]=n[1]=n[0]/10,n[0]=n[0]-n[1]*10,t=`${n[0]}${t}`}while(r[0]||r[1]||r[2]||r[3]);return t??"0"}class ya{static new(t,n){switch(n){case!0:return new ii(t);case!1:return new ri(t)}switch(t.constructor){case Int8Array:case Int16Array:case Int32Array:case er:return new ii(t)}return t.byteLength===16?new Ki(t):new ri(t)}static signed(t){return new ii(t)}static unsigned(t){return new ri(t)}static decimal(t){return new Ki(t)}constructor(t,n){return ya.new(t,n)}}var mu,gu,bu,_u,vu,wu,Su,Iu,Bu,Ou,Au,Fu,Du,Tu,xu,Mu,Nu,Eu,Cu;class T{static isNull(t){return(t==null?void 0:t.typeId)===h.Null}static isInt(t){return(t==null?void 0:t.typeId)===h.Int}static isFloat(t){return(t==null?void 0:t.typeId)===h.Float}static isBinary(t){return(t==null?void 0:t.typeId)===h.Binary}static isUtf8(t){return(t==null?void 0:t.typeId)===h.Utf8}static isBool(t){return(t==null?void 0:t.typeId)===h.Bool}static isDecimal(t){return(t==null?void 0:t.typeId)===h.Decimal}static isDate(t){return(t==null?void 0:t.typeId)===h.Date}static isTime(t){return(t==null?void 0:t.typeId)===h.Time}static isTimestamp(t){return(t==null?void 0:t.typeId)===h.Timestamp}static isInterval(t){return(t==null?void 0:t.typeId)===h.Interval}static isList(t){return(t==null?void 0:t.typeId)===h.List}static isStruct(t){return(t==null?void 0:t.typeId)===h.Struct}static isUnion(t){return(t==null?void 0:t.typeId)===h.Union}static isFixedSizeBinary(t){return(t==null?void 0:t.typeId)===h.FixedSizeBinary}static isFixedSizeList(t){return(t==null?void 0:t.typeId)===h.FixedSizeList}static isMap(t){return(t==null?void 0:t.typeId)===h.Map}static isDictionary(t){return(t==null?void 0:t.typeId)===h.Dictionary}static isDenseUnion(t){return T.isUnion(t)&&t.mode===Xt.Dense}static isSparseUnion(t){return T.isUnion(t)&&t.mode===Xt.Sparse}get typeId(){return h.NONE}}mu=Symbol.toStringTag;T[mu]=(e=>(e.children=null,e.ArrayType=Array,e[Symbol.toStringTag]="DataType"))(T.prototype);let pn=class extends T{toString(){return"Null"}get typeId(){return h.Null}};gu=Symbol.toStringTag;pn[gu]=(e=>e[Symbol.toStringTag]="Null")(pn.prototype);class yn extends T{constructor(t,n){super(),this.isSigned=t,this.bitWidth=n}get typeId(){return h.Int}get ArrayType(){switch(this.bitWidth){case 8:return this.isSigned?Int8Array:Uint8Array;case 16:return this.isSigned?Int16Array:Uint16Array;case 32:return this.isSigned?Int32Array:Uint32Array;case 64:return this.isSigned?er:nr}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}toString(){return`${this.isSigned?"I":"Ui"}nt${this.bitWidth}`}}bu=Symbol.toStringTag;yn[bu]=(e=>(e.isSigned=null,e.bitWidth=null,e[Symbol.toStringTag]="Int"))(yn.prototype);class Ji extends yn{constructor(){super(!0,32)}get ArrayType(){return Int32Array}}Object.defineProperty(Ji.prototype,"ArrayType",{value:Int32Array});class qi extends T{constructor(t){super(),this.precision=t}get typeId(){return h.Float}get ArrayType(){switch(this.precision){case Vt.HALF:return Uint16Array;case Vt.SINGLE:return Float32Array;case Vt.DOUBLE:return Float64Array}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}toString(){return`Float${this.precision<<5||16}`}}_u=Symbol.toStringTag;qi[_u]=(e=>(e.precision=null,e[Symbol.toStringTag]="Float"))(qi.prototype);let Pr=class extends T{constructor(){super()}get typeId(){return h.Binary}toString(){return"Binary"}};vu=Symbol.toStringTag;Pr[vu]=(e=>(e.ArrayType=Uint8Array,e[Symbol.toStringTag]="Binary"))(Pr.prototype);let jr=class extends T{constructor(){super()}get typeId(){return h.Utf8}toString(){return"Utf8"}};wu=Symbol.toStringTag;jr[wu]=(e=>(e.ArrayType=Uint8Array,e[Symbol.toStringTag]="Utf8"))(jr.prototype);let $r=class extends T{constructor(){super()}get typeId(){return h.Bool}toString(){return"Bool"}};Su=Symbol.toStringTag;$r[Su]=(e=>(e.ArrayType=Uint8Array,e[Symbol.toStringTag]="Bool"))($r.prototype);let Vr=class extends T{constructor(t,n,i=128){super(),this.scale=t,this.precision=n,this.bitWidth=i}get typeId(){return h.Decimal}toString(){return`Decimal[${this.precision}e${this.scale>0?"+":""}${this.scale}]`}};Iu=Symbol.toStringTag;Vr[Iu]=(e=>(e.scale=null,e.precision=null,e.ArrayType=Uint32Array,e[Symbol.toStringTag]="Decimal"))(Vr.prototype);class zr extends T{constructor(t){super(),this.unit=t}get typeId(){return h.Date}toString(){return`Date${(this.unit+1)*32}<${Ze[this.unit]}>`}}Bu=Symbol.toStringTag;zr[Bu]=(e=>(e.unit=null,e.ArrayType=Int32Array,e[Symbol.toStringTag]="Date"))(zr.prototype);class Zi extends T{constructor(t,n){super(),this.unit=t,this.bitWidth=n}get typeId(){return h.Time}toString(){return`Time${this.bitWidth}<${X[this.unit]}>`}get ArrayType(){switch(this.bitWidth){case 32:return Int32Array;case 64:return er}throw new Error(`Unrecognized ${this[Symbol.toStringTag]} type`)}}Ou=Symbol.toStringTag;Zi[Ou]=(e=>(e.unit=null,e.bitWidth=null,e[Symbol.toStringTag]="Time"))(Zi.prototype);class kr extends T{constructor(t,n){super(),this.unit=t,this.timezone=n}get typeId(){return h.Timestamp}toString(){return`Timestamp<${X[this.unit]}${this.timezone?`, ${this.timezone}`:""}>`}}Au=Symbol.toStringTag;kr[Au]=(e=>(e.unit=null,e.timezone=null,e.ArrayType=Int32Array,e[Symbol.toStringTag]="Timestamp"))(kr.prototype);class Wr extends T{constructor(t){super(),this.unit=t}get typeId(){return h.Interval}toString(){return`Interval<${hn[this.unit]}>`}}Fu=Symbol.toStringTag;Wr[Fu]=(e=>(e.unit=null,e.ArrayType=Int32Array,e[Symbol.toStringTag]="Interval"))(Wr.prototype);let Hr=class extends T{constructor(t){super(),this.children=[t]}get typeId(){return h.List}toString(){return`List<${this.valueType}>`}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}};Du=Symbol.toStringTag;Hr[Du]=(e=>(e.children=null,e[Symbol.toStringTag]="List"))(Hr.prototype);class Et extends T{constructor(t){super(),this.children=t}get typeId(){return h.Struct}toString(){return`Struct<{${this.children.map(t=>`${t.name}:${t.type}`).join(", ")}}>`}}Tu=Symbol.toStringTag;Et[Tu]=(e=>(e.children=null,e[Symbol.toStringTag]="Struct"))(Et.prototype);class Yr extends T{constructor(t,n,i){super(),this.mode=t,this.children=i,this.typeIds=n=Int32Array.from(n),this.typeIdToChildIndex=n.reduce((r,s,o)=>(r[s]=o)&&r||r,Object.create(null))}get typeId(){return h.Union}toString(){return`${this[Symbol.toStringTag]}<${this.children.map(t=>`${t.type}`).join(" | ")}>`}}xu=Symbol.toStringTag;Yr[xu]=(e=>(e.mode=null,e.typeIds=null,e.children=null,e.typeIdToChildIndex=null,e.ArrayType=Int8Array,e[Symbol.toStringTag]="Union"))(Yr.prototype);let Kr=class extends T{constructor(t){super(),this.byteWidth=t}get typeId(){return h.FixedSizeBinary}toString(){return`FixedSizeBinary[${this.byteWidth}]`}};Mu=Symbol.toStringTag;Kr[Mu]=(e=>(e.byteWidth=null,e.ArrayType=Uint8Array,e[Symbol.toStringTag]="FixedSizeBinary"))(Kr.prototype);let Jr=class extends T{constructor(t,n){super(),this.listSize=t,this.children=[n]}get typeId(){return h.FixedSizeList}get valueType(){return this.children[0].type}get valueField(){return this.children[0]}get ArrayType(){return this.valueType.ArrayType}toString(){return`FixedSizeList[${this.listSize}]<${this.valueType}>`}};Nu=Symbol.toStringTag;Jr[Nu]=(e=>(e.children=null,e.listSize=null,e[Symbol.toStringTag]="FixedSizeList"))(Jr.prototype);class qr extends T{constructor(t,n=!1){super(),this.children=[t],this.keysSorted=n}get typeId(){return h.Map}get keyType(){return this.children[0].type.children[0].type}get valueType(){return this.children[0].type.children[1].type}get childType(){return this.children[0].type}toString(){return`Map<{${this.children[0].type.children.map(t=>`${t.name}:${t.type}`).join(", ")}}>`}}Eu=Symbol.toStringTag;qr[Eu]=(e=>(e.children=null,e.keysSorted=null,e[Symbol.toStringTag]="Map_"))(qr.prototype);const Sy=(e=>()=>++e)(-1);class fi extends T{constructor(t,n,i,r){super(),this.indices=n,this.dictionary=t,this.isOrdered=r||!1,this.id=i==null?Sy():typeof i=="number"?i:i.low}get typeId(){return h.Dictionary}get children(){return this.dictionary.children}get valueType(){return this.dictionary}get ArrayType(){return this.dictionary.ArrayType}toString(){return`Dictionary<${this.indices}, ${this.dictionary}>`}}Cu=Symbol.toStringTag;fi[Cu]=(e=>(e.id=null,e.indices=null,e.isOrdered=null,e.dictionary=null,e[Symbol.toStringTag]="Dictionary"))(fi.prototype);function ze(e){const t=e;switch(e.typeId){case h.Decimal:return e.bitWidth/32;case h.Timestamp:return 2;case h.Date:return 1+t.unit;case h.Interval:return 1+t.unit;case h.FixedSizeList:return t.listSize;case h.FixedSizeBinary:return t.byteWidth;default:return 1}}class J{visitMany(t,...n){return t.map((i,r)=>this.visit(i,...n.map(s=>s[r])))}visit(...t){return this.getVisitFn(t[0],!1).apply(this,t)}getVisitFn(t,n=!0){return Iy(this,t,n)}getVisitFnByTypeId(t,n=!0){return Kn(this,t,n)}visitNull(t,...n){return null}visitBool(t,...n){return null}visitInt(t,...n){return null}visitFloat(t,...n){return null}visitUtf8(t,...n){return null}visitBinary(t,...n){return null}visitFixedSizeBinary(t,...n){return null}visitDate(t,...n){return null}visitTimestamp(t,...n){return null}visitTime(t,...n){return null}visitDecimal(t,...n){return null}visitList(t,...n){return null}visitStruct(t,...n){return null}visitUnion(t,...n){return null}visitDictionary(t,...n){return null}visitInterval(t,...n){return null}visitFixedSizeList(t,...n){return null}visitMap(t,...n){return null}}function Iy(e,t,n=!0){return typeof t=="number"?Kn(e,t,n):typeof t=="string"&&t in h?Kn(e,h[t],n):t&&t instanceof T?Kn(e,Tc(t),n):t!=null&&t.type&&t.type instanceof T?Kn(e,Tc(t.type),n):Kn(e,h.NONE,n)}function Kn(e,t,n=!0){let i=null;switch(t){case h.Null:i=e.visitNull;break;case h.Bool:i=e.visitBool;break;case h.Int:i=e.visitInt;break;case h.Int8:i=e.visitInt8||e.visitInt;break;case h.Int16:i=e.visitInt16||e.visitInt;break;case h.Int32:i=e.visitInt32||e.visitInt;break;case h.Int64:i=e.visitInt64||e.visitInt;break;case h.Uint8:i=e.visitUint8||e.visitInt;break;case h.Uint16:i=e.visitUint16||e.visitInt;break;case h.Uint32:i=e.visitUint32||e.visitInt;break;case h.Uint64:i=e.visitUint64||e.visitInt;break;case h.Float:i=e.visitFloat;break;case h.Float16:i=e.visitFloat16||e.visitFloat;break;case h.Float32:i=e.visitFloat32||e.visitFloat;break;case h.Float64:i=e.visitFloat64||e.visitFloat;break;case h.Utf8:i=e.visitUtf8;break;case h.Binary:i=e.visitBinary;break;case h.FixedSizeBinary:i=e.visitFixedSizeBinary;break;case h.Date:i=e.visitDate;break;case h.DateDay:i=e.visitDateDay||e.visitDate;break;case h.DateMillisecond:i=e.visitDateMillisecond||e.visitDate;break;case h.Timestamp:i=e.visitTimestamp;break;case h.TimestampSecond:i=e.visitTimestampSecond||e.visitTimestamp;break;case h.TimestampMillisecond:i=e.visitTimestampMillisecond||e.visitTimestamp;break;case h.TimestampMicrosecond:i=e.visitTimestampMicrosecond||e.visitTimestamp;break;case h.TimestampNanosecond:i=e.visitTimestampNanosecond||e.visitTimestamp;break;case h.Time:i=e.visitTime;break;case h.TimeSecond:i=e.visitTimeSecond||e.visitTime;break;case h.TimeMillisecond:i=e.visitTimeMillisecond||e.visitTime;break;case h.TimeMicrosecond:i=e.visitTimeMicrosecond||e.visitTime;break;case h.TimeNanosecond:i=e.visitTimeNanosecond||e.visitTime;break;case h.Decimal:i=e.visitDecimal;break;case h.List:i=e.visitList;break;case h.Struct:i=e.visitStruct;break;case h.Union:i=e.visitUnion;break;case h.DenseUnion:i=e.visitDenseUnion||e.visitUnion;break;case h.SparseUnion:i=e.visitSparseUnion||e.visitUnion;break;case h.Dictionary:i=e.visitDictionary;break;case h.Interval:i=e.visitInterval;break;case h.IntervalDayTime:i=e.visitIntervalDayTime||e.visitInterval;break;case h.IntervalYearMonth:i=e.visitIntervalYearMonth||e.visitInterval;break;case h.FixedSizeList:i=e.visitFixedSizeList;break;case h.Map:i=e.visitMap;break}if(typeof i=="function")return i;if(!n)return()=>null;throw new Error(`Unrecognized type '${h[t]}'`)}function Tc(e){switch(e.typeId){case h.Null:return h.Null;case h.Int:{const{bitWidth:t,isSigned:n}=e;switch(t){case 8:return n?h.Int8:h.Uint8;case 16:return n?h.Int16:h.Uint16;case 32:return n?h.Int32:h.Uint32;case 64:return n?h.Int64:h.Uint64}return h.Int}case h.Float:switch(e.precision){case Vt.HALF:return h.Float16;case Vt.SINGLE:return h.Float32;case Vt.DOUBLE:return h.Float64}return h.Float;case h.Binary:return h.Binary;case h.Utf8:return h.Utf8;case h.Bool:return h.Bool;case h.Decimal:return h.Decimal;case h.Time:switch(e.unit){case X.SECOND:return h.TimeSecond;case X.MILLISECOND:return h.TimeMillisecond;case X.MICROSECOND:return h.TimeMicrosecond;case X.NANOSECOND:return h.TimeNanosecond}return h.Time;case h.Timestamp:switch(e.unit){case X.SECOND:return h.TimestampSecond;case X.MILLISECOND:return h.TimestampMillisecond;case X.MICROSECOND:return h.TimestampMicrosecond;case X.NANOSECOND:return h.TimestampNanosecond}return h.Timestamp;case h.Date:switch(e.unit){case Ze.DAY:return h.DateDay;case Ze.MILLISECOND:return h.DateMillisecond}return h.Date;case h.Interval:switch(e.unit){case hn.DAY_TIME:return h.IntervalDayTime;case hn.YEAR_MONTH:return h.IntervalYearMonth}return h.Interval;case h.Map:return h.Map;case h.List:return h.List;case h.Struct:return h.Struct;case h.Union:switch(e.mode){case Xt.Dense:return h.DenseUnion;case Xt.Sparse:return h.SparseUnion}return h.Union;case h.FixedSizeBinary:return h.FixedSizeBinary;case h.FixedSizeList:return h.FixedSizeList;case h.Dictionary:return h.Dictionary}throw new Error(`Unrecognized type '${h[e.typeId]}'`)}J.prototype.visitInt8=null;J.prototype.visitInt16=null;J.prototype.visitInt32=null;J.prototype.visitInt64=null;J.prototype.visitUint8=null;J.prototype.visitUint16=null;J.prototype.visitUint32=null;J.prototype.visitUint64=null;J.prototype.visitFloat16=null;J.prototype.visitFloat32=null;J.prototype.visitFloat64=null;J.prototype.visitDateDay=null;J.prototype.visitDateMillisecond=null;J.prototype.visitTimestampSecond=null;J.prototype.visitTimestampMillisecond=null;J.prototype.visitTimestampMicrosecond=null;J.prototype.visitTimestampNanosecond=null;J.prototype.visitTimeSecond=null;J.prototype.visitTimeMillisecond=null;J.prototype.visitTimeMicrosecond=null;J.prototype.visitTimeNanosecond=null;J.prototype.visitDenseUnion=null;J.prototype.visitSparseUnion=null;J.prototype.visitIntervalDayTime=null;J.prototype.visitIntervalYearMonth=null;const Ru=new Float64Array(1),Yn=new Uint32Array(Ru.buffer);function Lu(e){const t=(e&31744)>>10,n=(e&1023)/1024,i=Math.pow(-1,(e&32768)>>15);switch(t){case 31:return i*(n?Number.NaN:1/0);case 0:return i*(n?6103515625e-14*n:0)}return i*Math.pow(2,t-15)*(1+n)}function By(e){if(e!==e)return 32256;Ru[0]=e;const t=(Yn[1]&2147483648)>>16&65535;let n=Yn[1]&2146435072,i=0;return n>=1089470464?Yn[0]>0?n=31744:(n=(n&2080374784)>>16,i=(Yn[1]&1048575)>>10):n<=1056964608?(i=1048576+(Yn[1]&1048575),i=1048576+(i<<(n>>20)-998)>>21,n=0):(n=n-1056964608>>10,i=(Yn[1]&1048575)+512>>10),t|n|i&65535}class L extends J{}function j(e){return(t,n,i)=>{if(t.setValid(n,i!=null))return e(t,n,i)}}const Oy=(e,t,n)=>{e[t]=Math.trunc(n/864e5)},ma=(e,t,n)=>{e[t]=Math.trunc(n%4294967296),e[t+1]=Math.trunc(n/4294967296)},Ay=(e,t,n)=>{e[t]=Math.trunc(n*1e3%4294967296),e[t+1]=Math.trunc(n*1e3/4294967296)},Fy=(e,t,n)=>{e[t]=Math.trunc(n*1e6%4294967296),e[t+1]=Math.trunc(n*1e6/4294967296)},Uu=(e,t,n,i)=>{if(n+1<t.length){const{[n]:r,[n+1]:s}=t;e.set(i.subarray(0,s-r),r)}},Dy=({offset:e,values:t},n,i)=>{const r=e+n;i?t[r>>3]|=1<<r%8:t[r>>3]&=~(1<<r%8)},Ge=({values:e},t,n)=>{e[t]=n},ga=({values:e},t,n)=>{e[t]=n},Pu=({values:e},t,n)=>{e[t]=By(n)},Ty=(e,t,n)=>{switch(e.type.precision){case Vt.HALF:return Pu(e,t,n);case Vt.SINGLE:case Vt.DOUBLE:return ga(e,t,n)}},ju=({values:e},t,n)=>{Oy(e,t,n.valueOf())},$u=({values:e},t,n)=>{ma(e,t*2,n.valueOf())},xy=({stride:e,values:t},n,i)=>{t.set(i.subarray(0,e),e*n)},My=({values:e,valueOffsets:t},n,i)=>Uu(e,t,n,i),Ny=({values:e,valueOffsets:t},n,i)=>{Uu(e,t,n,fa(i))},Ey=(e,t,n)=>{e.type.unit===Ze.DAY?ju(e,t,n):$u(e,t,n)},Vu=({values:e},t,n)=>ma(e,t*2,n/1e3),zu=({values:e},t,n)=>ma(e,t*2,n),ku=({values:e},t,n)=>Ay(e,t*2,n),Wu=({values:e},t,n)=>Fy(e,t*2,n),Cy=(e,t,n)=>{switch(e.type.unit){case X.SECOND:return Vu(e,t,n);case X.MILLISECOND:return zu(e,t,n);case X.MICROSECOND:return ku(e,t,n);case X.NANOSECOND:return Wu(e,t,n)}},Hu=({values:e},t,n)=>{e[t]=n},Yu=({values:e},t,n)=>{e[t]=n},Ku=({values:e},t,n)=>{e[t]=n},Ju=({values:e},t,n)=>{e[t]=n},Ry=(e,t,n)=>{switch(e.type.unit){case X.SECOND:return Hu(e,t,n);case X.MILLISECOND:return Yu(e,t,n);case X.MICROSECOND:return Ku(e,t,n);case X.NANOSECOND:return Ju(e,t,n)}},Ly=({values:e,stride:t},n,i)=>{e.set(i.subarray(0,t),t*n)},Uy=(e,t,n)=>{const i=e.children[0],r=e.valueOffsets,s=se.getVisitFn(i);if(Array.isArray(n))for(let o=-1,a=r[t],c=r[t+1];a<c;)s(i,a++,n[++o]);else for(let o=-1,a=r[t],c=r[t+1];a<c;)s(i,a++,n.get(++o))},Py=(e,t,n)=>{const i=e.children[0],{valueOffsets:r}=e,s=se.getVisitFn(i);let{[t]:o,[t+1]:a}=r;const c=n instanceof Map?n.entries():Object.entries(n);for(const l of c)if(s(i,o,l),++o>=a)break},jy=(e,t)=>(n,i,r,s)=>i&&n(i,e,t[s]),$y=(e,t)=>(n,i,r,s)=>i&&n(i,e,t.get(s)),Vy=(e,t)=>(n,i,r,s)=>i&&n(i,e,t.get(r.name)),zy=(e,t)=>(n,i,r,s)=>i&&n(i,e,t[r.name]),ky=(e,t,n)=>{const i=e.type.children.map(s=>se.getVisitFn(s.type)),r=n instanceof Map?Vy(t,n):n instanceof tt?$y(t,n):Array.isArray(n)?jy(t,n):zy(t,n);e.type.children.forEach((s,o)=>r(i[o],e.children[o],s,o))},Wy=(e,t,n)=>{e.type.mode===Xt.Dense?qu(e,t,n):Zu(e,t,n)},qu=(e,t,n)=>{const i=e.type.typeIdToChildIndex[e.typeIds[t]],r=e.children[i];se.visit(r,e.valueOffsets[t],n)},Zu=(e,t,n)=>{const i=e.type.typeIdToChildIndex[e.typeIds[t]],r=e.children[i];se.visit(r,t,n)},Hy=(e,t,n)=>{var i;(i=e.dictionary)===null||i===void 0||i.set(e.values[t],n)},Yy=(e,t,n)=>{e.type.unit===hn.DAY_TIME?Xu(e,t,n):Qu(e,t,n)},Xu=({values:e},t,n)=>{e.set(n.subarray(0,2),2*t)},Qu=({values:e},t,n)=>{e[t]=n[0]*12+n[1]%12},Ky=(e,t,n)=>{const{stride:i}=e,r=e.children[0],s=se.getVisitFn(r);if(Array.isArray(n))for(let o=-1,a=t*i;++o<i;)s(r,a+o,n[o]);else for(let o=-1,a=t*i;++o<i;)s(r,a+o,n.get(o))};L.prototype.visitBool=j(Dy);L.prototype.visitInt=j(Ge);L.prototype.visitInt8=j(Ge);L.prototype.visitInt16=j(Ge);L.prototype.visitInt32=j(Ge);L.prototype.visitInt64=j(Ge);L.prototype.visitUint8=j(Ge);L.prototype.visitUint16=j(Ge);L.prototype.visitUint32=j(Ge);L.prototype.visitUint64=j(Ge);L.prototype.visitFloat=j(Ty);L.prototype.visitFloat16=j(Pu);L.prototype.visitFloat32=j(ga);L.prototype.visitFloat64=j(ga);L.prototype.visitUtf8=j(Ny);L.prototype.visitBinary=j(My);L.prototype.visitFixedSizeBinary=j(xy);L.prototype.visitDate=j(Ey);L.prototype.visitDateDay=j(ju);L.prototype.visitDateMillisecond=j($u);L.prototype.visitTimestamp=j(Cy);L.prototype.visitTimestampSecond=j(Vu);L.prototype.visitTimestampMillisecond=j(zu);L.prototype.visitTimestampMicrosecond=j(ku);L.prototype.visitTimestampNanosecond=j(Wu);L.prototype.visitTime=j(Ry);L.prototype.visitTimeSecond=j(Hu);L.prototype.visitTimeMillisecond=j(Yu);L.prototype.visitTimeMicrosecond=j(Ku);L.prototype.visitTimeNanosecond=j(Ju);L.prototype.visitDecimal=j(Ly);L.prototype.visitList=j(Uy);L.prototype.visitStruct=j(ky);L.prototype.visitUnion=j(Wy);L.prototype.visitDenseUnion=j(qu);L.prototype.visitSparseUnion=j(Zu);L.prototype.visitDictionary=j(Hy);L.prototype.visitInterval=j(Yy);L.prototype.visitIntervalDayTime=j(Xu);L.prototype.visitIntervalYearMonth=j(Qu);L.prototype.visitFixedSizeList=j(Ky);L.prototype.visitMap=j(Py);const se=new L,ce=Symbol.for("parent"),si=Symbol.for("rowIndex");class ba{constructor(t,n){return this[ce]=t,this[si]=n,new Proxy(this,new qy)}toArray(){return Object.values(this.toJSON())}toJSON(){const t=this[si],n=this[ce],i=n.type.children,r={};for(let s=-1,o=i.length;++s<o;)r[i[s].name]=Ht.visit(n.children[s],t);return r}toString(){return`{${[...this].map(([t,n])=>`${Yi(t)}: ${Yi(n)}`).join(", ")}}`}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}[Symbol.iterator](){return new Jy(this[ce],this[si])}}class Jy{constructor(t,n){this.childIndex=0,this.children=t.children,this.rowIndex=n,this.childFields=t.type.children,this.numChildren=this.childFields.length}[Symbol.iterator](){return this}next(){const t=this.childIndex;return t<this.numChildren?(this.childIndex=t+1,{done:!1,value:[this.childFields[t].name,Ht.visit(this.children[t],this.rowIndex)]}):{done:!0,value:null}}}Object.defineProperties(ba.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[ce]:{writable:!0,enumerable:!1,configurable:!1,value:null},[si]:{writable:!0,enumerable:!1,configurable:!1,value:-1}});class qy{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(t){return t[ce].type.children.map(n=>n.name)}has(t,n){return t[ce].type.children.findIndex(i=>i.name===n)!==-1}getOwnPropertyDescriptor(t,n){if(t[ce].type.children.findIndex(i=>i.name===n)!==-1)return{writable:!0,enumerable:!0,configurable:!0}}get(t,n){if(Reflect.has(t,n))return t[n];const i=t[ce].type.children.findIndex(r=>r.name===n);if(i!==-1){const r=Ht.visit(t[ce].children[i],t[si]);return Reflect.set(t,n,r),r}}set(t,n,i){const r=t[ce].type.children.findIndex(s=>s.name===n);return r!==-1?(se.visit(t[ce].children[r],t[si],i),Reflect.set(t,n,i)):Reflect.has(t,n)||typeof n=="symbol"?Reflect.set(t,n,i):!1}}class x extends J{}function U(e){return(t,n)=>t.getValid(n)?e(t,n):null}const Zy=(e,t)=>864e5*e[t],_a=(e,t)=>4294967296*e[t+1]+(e[t]>>>0),Xy=(e,t)=>4294967296*(e[t+1]/1e3)+(e[t]>>>0)/1e3,Qy=(e,t)=>4294967296*(e[t+1]/1e6)+(e[t]>>>0)/1e6,Gu=e=>new Date(e),Gy=(e,t)=>Gu(Zy(e,t)),tm=(e,t)=>Gu(_a(e,t)),em=(e,t)=>null,tf=(e,t,n)=>{if(n+1>=t.length)return null;const i=t[n],r=t[n+1];return e.subarray(i,r)},nm=({offset:e,values:t},n)=>{const i=e+n;return(t[i>>3]&1<<i%8)!==0},ef=({values:e},t)=>Gy(e,t),nf=({values:e},t)=>tm(e,t*2),bn=({stride:e,values:t},n)=>t[e*n],im=({stride:e,values:t},n)=>Lu(t[e*n]),rf=({values:e},t)=>e[t],rm=({stride:e,values:t},n)=>t.subarray(e*n,e*(n+1)),sm=({values:e,valueOffsets:t},n)=>tf(e,t,n),om=({values:e,valueOffsets:t},n)=>{const i=tf(e,t,n);return i!==null?So(i):null},am=({values:e},t)=>e[t],cm=({type:e,values:t},n)=>e.precision!==Vt.HALF?t[n]:Lu(t[n]),lm=(e,t)=>e.type.unit===Ze.DAY?ef(e,t):nf(e,t),sf=({values:e},t)=>1e3*_a(e,t*2),of=({values:e},t)=>_a(e,t*2),af=({values:e},t)=>Xy(e,t*2),cf=({values:e},t)=>Qy(e,t*2),um=(e,t)=>{switch(e.type.unit){case X.SECOND:return sf(e,t);case X.MILLISECOND:return of(e,t);case X.MICROSECOND:return af(e,t);case X.NANOSECOND:return cf(e,t)}},lf=({values:e},t)=>e[t],uf=({values:e},t)=>e[t],ff=({values:e},t)=>e[t],df=({values:e},t)=>e[t],fm=(e,t)=>{switch(e.type.unit){case X.SECOND:return lf(e,t);case X.MILLISECOND:return uf(e,t);case X.MICROSECOND:return ff(e,t);case X.NANOSECOND:return df(e,t)}},dm=({values:e,stride:t},n)=>ya.decimal(e.subarray(t*n,t*(n+1))),hm=(e,t)=>{const{valueOffsets:n,stride:i,children:r}=e,{[t*i]:s,[t*i+1]:o}=n,c=r[0].slice(s,o-s);return new tt([c])},pm=(e,t)=>{const{valueOffsets:n,children:i}=e,{[t]:r,[t+1]:s}=n,o=i[0];return new va(o.slice(r,s-r))},ym=(e,t)=>new ba(e,t),mm=(e,t)=>e.type.mode===Xt.Dense?hf(e,t):pf(e,t),hf=(e,t)=>{const n=e.type.typeIdToChildIndex[e.typeIds[t]],i=e.children[n];return Ht.visit(i,e.valueOffsets[t])},pf=(e,t)=>{const n=e.type.typeIdToChildIndex[e.typeIds[t]],i=e.children[n];return Ht.visit(i,t)},gm=(e,t)=>{var n;return(n=e.dictionary)===null||n===void 0?void 0:n.get(e.values[t])},bm=(e,t)=>e.type.unit===hn.DAY_TIME?yf(e,t):mf(e,t),yf=({values:e},t)=>e.subarray(2*t,2*(t+1)),mf=({values:e},t)=>{const n=e[t],i=new Int32Array(2);return i[0]=Math.trunc(n/12),i[1]=Math.trunc(n%12),i},_m=(e,t)=>{const{stride:n,children:i}=e,s=i[0].slice(t*n,n);return new tt([s])};x.prototype.visitNull=U(em);x.prototype.visitBool=U(nm);x.prototype.visitInt=U(am);x.prototype.visitInt8=U(bn);x.prototype.visitInt16=U(bn);x.prototype.visitInt32=U(bn);x.prototype.visitInt64=U(rf);x.prototype.visitUint8=U(bn);x.prototype.visitUint16=U(bn);x.prototype.visitUint32=U(bn);x.prototype.visitUint64=U(rf);x.prototype.visitFloat=U(cm);x.prototype.visitFloat16=U(im);x.prototype.visitFloat32=U(bn);x.prototype.visitFloat64=U(bn);x.prototype.visitUtf8=U(om);x.prototype.visitBinary=U(sm);x.prototype.visitFixedSizeBinary=U(rm);x.prototype.visitDate=U(lm);x.prototype.visitDateDay=U(ef);x.prototype.visitDateMillisecond=U(nf);x.prototype.visitTimestamp=U(um);x.prototype.visitTimestampSecond=U(sf);x.prototype.visitTimestampMillisecond=U(of);x.prototype.visitTimestampMicrosecond=U(af);x.prototype.visitTimestampNanosecond=U(cf);x.prototype.visitTime=U(fm);x.prototype.visitTimeSecond=U(lf);x.prototype.visitTimeMillisecond=U(uf);x.prototype.visitTimeMicrosecond=U(ff);x.prototype.visitTimeNanosecond=U(df);x.prototype.visitDecimal=U(dm);x.prototype.visitList=U(hm);x.prototype.visitStruct=U(ym);x.prototype.visitUnion=U(mm);x.prototype.visitDenseUnion=U(hf);x.prototype.visitSparseUnion=U(pf);x.prototype.visitDictionary=U(gm);x.prototype.visitInterval=U(bm);x.prototype.visitIntervalDayTime=U(yf);x.prototype.visitIntervalYearMonth=U(mf);x.prototype.visitFixedSizeList=U(_m);x.prototype.visitMap=U(pm);const Ht=new x,Oe=Symbol.for("keys"),oi=Symbol.for("vals");class va{constructor(t){return this[Oe]=new tt([t.children[0]]).memoize(),this[oi]=t.children[1],new Proxy(this,new wm)}[Symbol.iterator](){return new vm(this[Oe],this[oi])}get size(){return this[Oe].length}toArray(){return Object.values(this.toJSON())}toJSON(){const t=this[Oe],n=this[oi],i={};for(let r=-1,s=t.length;++r<s;)i[t.get(r)]=Ht.visit(n,r);return i}toString(){return`{${[...this].map(([t,n])=>`${Yi(t)}: ${Yi(n)}`).join(", ")}}`}[Symbol.for("nodejs.util.inspect.custom")](){return this.toString()}}class vm{constructor(t,n){this.keys=t,this.vals=n,this.keyIndex=0,this.numKeys=t.length}[Symbol.iterator](){return this}next(){const t=this.keyIndex;return t===this.numKeys?{done:!0,value:null}:(this.keyIndex++,{done:!1,value:[this.keys.get(t),Ht.visit(this.vals,t)]})}}class wm{isExtensible(){return!1}deleteProperty(){return!1}preventExtensions(){return!0}ownKeys(t){return t[Oe].toArray().map(String)}has(t,n){return t[Oe].includes(n)}getOwnPropertyDescriptor(t,n){if(t[Oe].indexOf(n)!==-1)return{writable:!0,enumerable:!0,configurable:!0}}get(t,n){if(Reflect.has(t,n))return t[n];const i=t[Oe].indexOf(n);if(i!==-1){const r=Ht.visit(Reflect.get(t,oi),i);return Reflect.set(t,n,r),r}}set(t,n,i){const r=t[Oe].indexOf(n);return r!==-1?(se.visit(Reflect.get(t,oi),r,i),Reflect.set(t,n,i)):Reflect.has(t,n)?Reflect.set(t,n,i):!1}}Object.defineProperties(va.prototype,{[Symbol.toStringTag]:{enumerable:!1,configurable:!1,value:"Row"},[Oe]:{writable:!0,enumerable:!1,configurable:!1,value:null},[oi]:{writable:!0,enumerable:!1,configurable:!1,value:null}});let xc;function gf(e,t,n,i){const{length:r=0}=e;let s=typeof t!="number"?0:t,o=typeof n!="number"?r:n;return s<0&&(s=(s%r+r)%r),o<0&&(o=(o%r+r)%r),o<s&&(xc=s,s=o,o=xc),o>r&&(o=r),i?i(e,s,o):[s,o]}const Mc=e=>e!==e;function Bi(e){if(typeof e!=="object"||e===null)return Mc(e)?Mc:n=>n===e;if(e instanceof Date){const n=e.valueOf();return i=>i instanceof Date?i.valueOf()===n:!1}return ArrayBuffer.isView(e)?n=>n?py(e,n):!1:e instanceof Map?Im(e):Array.isArray(e)?Sm(e):e instanceof tt?Bm(e):Om(e,!0)}function Sm(e){const t=[];for(let n=-1,i=e.length;++n<i;)t[n]=Bi(e[n]);return Ls(t)}function Im(e){let t=-1;const n=[];for(const i of e.values())n[++t]=Bi(i);return Ls(n)}function Bm(e){const t=[];for(let n=-1,i=e.length;++n<i;)t[n]=Bi(e.get(n));return Ls(t)}function Om(e,t=!1){const n=Object.keys(e);if(!t&&n.length===0)return()=>!1;const i=[];for(let r=-1,s=n.length;++r<s;)i[r]=Bi(e[n[r]]);return Ls(i,n)}function Ls(e,t){return n=>{if(!n||typeof n!="object")return!1;switch(n.constructor){case Array:return Am(e,n);case Map:return Nc(e,n,n.keys());case va:case ba:case Object:case void 0:return Nc(e,n,t||Object.keys(n))}return n instanceof tt?Fm(e,n):!1}}function Am(e,t){const n=e.length;if(t.length!==n)return!1;for(let i=-1;++i<n;)if(!e[i](t[i]))return!1;return!0}function Fm(e,t){const n=e.length;if(t.length!==n)return!1;for(let i=-1;++i<n;)if(!e[i](t.get(i)))return!1;return!0}function Nc(e,t,n){const i=n[Symbol.iterator](),r=t instanceof Map?t.keys():Object.keys(t)[Symbol.iterator](),s=t instanceof Map?t.values():Object.values(t)[Symbol.iterator]();let o=0;const a=e.length;let c=s.next(),l=i.next(),f=r.next();for(;o<a&&!l.done&&!f.done&&!c.done&&!(l.value!==f.value||!e[o](c.value));++o,l=i.next(),f=r.next(),c=s.next());return o===a&&l.done&&f.done&&c.done?!0:(i.return&&i.return(),r.return&&r.return(),s.return&&s.return(),!1)}function bf(e,t,n,i){return(n&1<<i)!==0}function Dm(e,t,n,i){return(n&1<<i)>>i}function wa(e,t,n){const i=n.byteLength+7&-8;if(e>0||n.byteLength<i){const r=new Uint8Array(i);return r.set(e%8===0?n.subarray(e>>3):Zr(new Sa(n,e,t,null,bf)).subarray(0,i)),r}return n}function Zr(e){const t=[];let n=0,i=0,r=0;for(const o of e)o&&(r|=1<<i),++i===8&&(t[n++]=r,r=i=0);(n===0||i>0)&&(t[n++]=r);const s=new Uint8Array(t.length+7&-8);return s.set(t),s}class Sa{constructor(t,n,i,r,s){this.bytes=t,this.length=i,this.context=r,this.get=s,this.bit=n%8,this.byteIndex=n>>3,this.byte=t[this.byteIndex++],this.index=0}next(){return this.index<this.length?(this.bit===8&&(this.bit=0,this.byte=this.bytes[this.byteIndex++]),{value:this.get(this.context,this.index++,this.byte,this.bit++)}):{done:!0,value:null}}[Symbol.iterator](){return this}}function Ao(e,t,n){if(n-t<=0)return 0;if(n-t<8){let s=0;for(const o of new Sa(e,t,n-t,e,Dm))s+=o;return s}const i=n>>3<<3,r=t+(t%8===0?0:8-t%8);return Ao(e,t,r)+Ao(e,i,n)+Tm(e,r>>3,i-r>>3)}function Tm(e,t,n){let i=0,r=Math.trunc(t);const s=new DataView(e.buffer,e.byteOffset,e.byteLength),o=n===void 0?e.byteLength:r+n;for(;o-r>=4;)i+=to(s.getUint32(r)),r+=4;for(;o-r>=2;)i+=to(s.getUint16(r)),r+=2;for(;o-r>=1;)i+=to(s.getUint8(r)),r+=1;return i}function to(e){let t=Math.trunc(e);return t=t-(t>>>1&1431655765),t=(t&858993459)+(t>>>2&858993459),(t+(t>>>4)&252645135)*16843009>>>24}const xm=-1;class ut{constructor(t,n,i,r,s,o=[],a){this.type=t,this.children=o,this.dictionary=a,this.offset=Math.floor(Math.max(n||0,0)),this.length=Math.floor(Math.max(i||0,0)),this._nullCount=Math.floor(Math.max(r||0,-1));let c;s instanceof ut?(this.stride=s.stride,this.values=s.values,this.typeIds=s.typeIds,this.nullBitmap=s.nullBitmap,this.valueOffsets=s.valueOffsets):(this.stride=ze(t),s&&((c=s[0])&&(this.valueOffsets=c),(c=s[1])&&(this.values=c),(c=s[2])&&(this.nullBitmap=c),(c=s[3])&&(this.typeIds=c))),this.nullable=this._nullCount!==0&&this.nullBitmap&&this.nullBitmap.byteLength>0}get typeId(){return this.type.typeId}get ArrayType(){return this.type.ArrayType}get buffers(){return[this.valueOffsets,this.values,this.nullBitmap,this.typeIds]}get byteLength(){let t=0;const{valueOffsets:n,values:i,nullBitmap:r,typeIds:s}=this;return n&&(t+=n.byteLength),i&&(t+=i.byteLength),r&&(t+=r.byteLength),s&&(t+=s.byteLength),this.children.reduce((o,a)=>o+a.byteLength,t)}get nullCount(){let t=this._nullCount,n;return t<=xm&&(n=this.nullBitmap)&&(this._nullCount=t=this.length-Ao(n,this.offset,this.offset+this.length)),t}getValid(t){if(this.nullable&&this.nullCount>0){const n=this.offset+t;return(this.nullBitmap[n>>3]&1<<n%8)!==0}return!0}setValid(t,n){if(!this.nullable)return n;if(!this.nullBitmap||this.nullBitmap.byteLength<=t>>3){const{nullBitmap:c}=this._changeLengthAndBackfillNullBitmap(this.length);Object.assign(this,{nullBitmap:c,_nullCount:0})}const{nullBitmap:i,offset:r}=this,s=r+t>>3,o=(r+t)%8,a=i[s]>>o&1;return n?a===0&&(i[s]|=1<<o,this._nullCount=this.nullCount+1):a===1&&(i[s]&=~(1<<o),this._nullCount=this.nullCount-1),n}clone(t=this.type,n=this.offset,i=this.length,r=this._nullCount,s=this,o=this.children){return new ut(t,n,i,r,s,o,this.dictionary)}slice(t,n){const{stride:i,typeId:r,children:s}=this,o=+(this._nullCount===0)-1,a=r===16?i:1,c=this._sliceBuffers(t,n,i,r);return this.clone(this.type,this.offset+t,n,o,c,s.length===0||this.valueOffsets?s:this._sliceChildren(s,a*t,a*n))}_changeLengthAndBackfillNullBitmap(t){if(this.typeId===h.Null)return this.clone(this.type,0,t,0);const{length:n,nullCount:i}=this,r=new Uint8Array((t+63&-64)>>3).fill(255,0,n>>3);r[n>>3]=(1<<n-(n&-8))-1,i>0&&r.set(wa(this.offset,n,this.nullBitmap),0);const s=this.buffers;return s[Ve.VALIDITY]=r,this.clone(this.type,0,t,i+(t-n),s)}_sliceBuffers(t,n,i,r){let s;const{buffers:o}=this;return(s=o[Ve.TYPE])&&(o[Ve.TYPE]=s.subarray(t,t+n)),(s=o[Ve.OFFSET])&&(o[Ve.OFFSET]=s.subarray(t,t+n+1))||(s=o[Ve.DATA])&&(o[Ve.DATA]=r===6?s:s.subarray(i*t,i*(t+n))),o}_sliceChildren(t,n,i){return t.map(r=>r.slice(n,i))}}ut.prototype.children=Object.freeze([]);class Ui extends J{visit(t){return this.getVisitFn(t.type).call(this,t)}visitNull(t){const{["type"]:n,["offset"]:i=0,["length"]:r=0}=t;return new ut(n,i,r,0)}visitBool(t){const{["type"]:n,["offset"]:i=0}=t,r=Z(t.nullBitmap),s=ot(n.ArrayType,t.data),{["length"]:o=s.length>>3,["nullCount"]:a=t.nullBitmap?-1:0}=t;return new ut(n,i,o,a,[void 0,s,r])}visitInt(t){const{["type"]:n,["offset"]:i=0}=t,r=Z(t.nullBitmap),s=ot(n.ArrayType,t.data),{["length"]:o=s.length,["nullCount"]:a=t.nullBitmap?-1:0}=t;return new ut(n,i,o,a,[void 0,s,r])}visitFloat(t){const{["type"]:n,["offset"]:i=0}=t,r=Z(t.nullBitmap),s=ot(n.ArrayType,t.data),{["length"]:o=s.length,["nullCount"]:a=t.nullBitmap?-1:0}=t;return new ut(n,i,o,a,[void 0,s,r])}visitUtf8(t){const{["type"]:n,["offset"]:i=0}=t,r=Z(t.data),s=Z(t.nullBitmap),o=Ti(t.valueOffsets),{["length"]:a=o.length-1,["nullCount"]:c=t.nullBitmap?-1:0}=t;return new ut(n,i,a,c,[o,r,s])}visitBinary(t){const{["type"]:n,["offset"]:i=0}=t,r=Z(t.data),s=Z(t.nullBitmap),o=Ti(t.valueOffsets),{["length"]:a=o.length-1,["nullCount"]:c=t.nullBitmap?-1:0}=t;return new ut(n,i,a,c,[o,r,s])}visitFixedSizeBinary(t){const{["type"]:n,["offset"]:i=0}=t,r=Z(t.nullBitmap),s=ot(n.ArrayType,t.data),{["length"]:o=s.length/ze(n),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new ut(n,i,o,a,[void 0,s,r])}visitDate(t){const{["type"]:n,["offset"]:i=0}=t,r=Z(t.nullBitmap),s=ot(n.ArrayType,t.data),{["length"]:o=s.length/ze(n),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new ut(n,i,o,a,[void 0,s,r])}visitTimestamp(t){const{["type"]:n,["offset"]:i=0}=t,r=Z(t.nullBitmap),s=ot(n.ArrayType,t.data),{["length"]:o=s.length/ze(n),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new ut(n,i,o,a,[void 0,s,r])}visitTime(t){const{["type"]:n,["offset"]:i=0}=t,r=Z(t.nullBitmap),s=ot(n.ArrayType,t.data),{["length"]:o=s.length/ze(n),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new ut(n,i,o,a,[void 0,s,r])}visitDecimal(t){const{["type"]:n,["offset"]:i=0}=t,r=Z(t.nullBitmap),s=ot(n.ArrayType,t.data),{["length"]:o=s.length/ze(n),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new ut(n,i,o,a,[void 0,s,r])}visitList(t){const{["type"]:n,["offset"]:i=0,["child"]:r}=t,s=Z(t.nullBitmap),o=Ti(t.valueOffsets),{["length"]:a=o.length-1,["nullCount"]:c=t.nullBitmap?-1:0}=t;return new ut(n,i,a,c,[o,void 0,s],[r])}visitStruct(t){const{["type"]:n,["offset"]:i=0,["children"]:r=[]}=t,s=Z(t.nullBitmap),{length:o=r.reduce((c,{length:l})=>Math.max(c,l),0),nullCount:a=t.nullBitmap?-1:0}=t;return new ut(n,i,o,a,[void 0,void 0,s],r)}visitUnion(t){const{["type"]:n,["offset"]:i=0,["children"]:r=[]}=t,s=Z(t.nullBitmap),o=ot(n.ArrayType,t.typeIds),{["length"]:a=o.length,["nullCount"]:c=t.nullBitmap?-1:0}=t;if(T.isSparseUnion(n))return new ut(n,i,a,c,[void 0,void 0,s,o],r);const l=Ti(t.valueOffsets);return new ut(n,i,a,c,[l,void 0,s,o],r)}visitDictionary(t){const{["type"]:n,["offset"]:i=0}=t,r=Z(t.nullBitmap),s=ot(n.indices.ArrayType,t.data),{["dictionary"]:o=new tt([new Ui().visit({type:n.dictionary})])}=t,{["length"]:a=s.length,["nullCount"]:c=t.nullBitmap?-1:0}=t;return new ut(n,i,a,c,[void 0,s,r],[],o)}visitInterval(t){const{["type"]:n,["offset"]:i=0}=t,r=Z(t.nullBitmap),s=ot(n.ArrayType,t.data),{["length"]:o=s.length/ze(n),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new ut(n,i,o,a,[void 0,s,r])}visitFixedSizeList(t){const{["type"]:n,["offset"]:i=0,["child"]:r=new Ui().visit({type:n.valueType})}=t,s=Z(t.nullBitmap),{["length"]:o=r.length/ze(n),["nullCount"]:a=t.nullBitmap?-1:0}=t;return new ut(n,i,o,a,[void 0,void 0,s],[r])}visitMap(t){const{["type"]:n,["offset"]:i=0,["child"]:r=new Ui().visit({type:n.childType})}=t,s=Z(t.nullBitmap),o=Ti(t.valueOffsets),{["length"]:a=o.length-1,["nullCount"]:c=t.nullBitmap?-1:0}=t;return new ut(n,i,a,c,[o,void 0,s],[r])}}function K(e){return new Ui().visit(e)}class Ec{constructor(t=0,n){this.numChunks=t,this.getChunkIterator=n,this.chunkIndex=0,this.chunkIterator=this.getChunkIterator(0)}next(){for(;this.chunkIndex<this.numChunks;){const t=this.chunkIterator.next();if(!t.done)return t;++this.chunkIndex<this.numChunks&&(this.chunkIterator=this.getChunkIterator(this.chunkIndex))}return{done:!0,value:null}}[Symbol.iterator](){return this}}function _f(e){return e.reduce((t,n)=>t+n.nullCount,0)}function vf(e){return e.reduce((t,n,i)=>(t[i+1]=t[i]+n.length,t),new Uint32Array(e.length+1))}function wf(e,t,n,i){const r=[];for(let s=-1,o=e.length;++s<o;){const a=e[s],c=t[s],{length:l}=a;if(c>=i)break;if(n>=c+l)continue;if(c>=n&&c+l<=i){r.push(a);continue}const f=Math.max(0,n-c),p=Math.min(i-c,l);r.push(a.slice(f,p-f))}return r.length===0&&r.push(e[0].slice(0,0)),r}function Ia(e,t,n,i){let r=0,s=0,o=t.length-1;do{if(r>=o-1)return n<t[o]?i(e,r,n-t[r]):null;s=r+Math.trunc((o-r)*.5),n<t[s]?o=s:r=s}while(r<o)}function Ba(e,t){return e.getValid(t)}function ai(e){function t(n,i,r){return e(n[i],r)}return function(n){const i=this.data;return Ia(i,this._offsets,n,t)}}function Sf(e){let t;function n(i,r,s){return e(i[r],s,t)}return function(i,r){const s=this.data;t=r;const o=Ia(s,this._offsets,i,n);return t=void 0,o}}function If(e){let t;function n(i,r,s){let o=s,a=0,c=0;for(let l=r-1,f=i.length;++l<f;){const p=i[l];if(~(a=e(p,t,o)))return c+a;o=0,c+=p.length}return-1}return function(i,r){t=i;const s=this.data,o=typeof r!="number"?n(s,0,0):Ia(s,this._offsets,r,n);return t=void 0,o}}class M extends J{}function Mm(e,t){return t===null&&e.length>0?0:-1}function Nm(e,t){const{nullBitmap:n}=e;if(!n||e.nullCount<=0)return-1;let i=0;for(const r of new Sa(n,e.offset+(t||0),e.length,n,bf)){if(!r)return i;++i}return-1}function z(e,t,n){if(t===void 0)return-1;if(t===null)return Nm(e,n);const i=Ht.getVisitFn(e),r=Bi(t);for(let s=(n||0)-1,o=e.length;++s<o;)if(r(i(e,s)))return s;return-1}function Bf(e,t,n){const i=Ht.getVisitFn(e),r=Bi(t);for(let s=(n||0)-1,o=e.length;++s<o;)if(r(i(e,s)))return s;return-1}M.prototype.visitNull=Mm;M.prototype.visitBool=z;M.prototype.visitInt=z;M.prototype.visitInt8=z;M.prototype.visitInt16=z;M.prototype.visitInt32=z;M.prototype.visitInt64=z;M.prototype.visitUint8=z;M.prototype.visitUint16=z;M.prototype.visitUint32=z;M.prototype.visitUint64=z;M.prototype.visitFloat=z;M.prototype.visitFloat16=z;M.prototype.visitFloat32=z;M.prototype.visitFloat64=z;M.prototype.visitUtf8=z;M.prototype.visitBinary=z;M.prototype.visitFixedSizeBinary=z;M.prototype.visitDate=z;M.prototype.visitDateDay=z;M.prototype.visitDateMillisecond=z;M.prototype.visitTimestamp=z;M.prototype.visitTimestampSecond=z;M.prototype.visitTimestampMillisecond=z;M.prototype.visitTimestampMicrosecond=z;M.prototype.visitTimestampNanosecond=z;M.prototype.visitTime=z;M.prototype.visitTimeSecond=z;M.prototype.visitTimeMillisecond=z;M.prototype.visitTimeMicrosecond=z;M.prototype.visitTimeNanosecond=z;M.prototype.visitDecimal=z;M.prototype.visitList=z;M.prototype.visitStruct=z;M.prototype.visitUnion=z;M.prototype.visitDenseUnion=Bf;M.prototype.visitSparseUnion=Bf;M.prototype.visitDictionary=z;M.prototype.visitInterval=z;M.prototype.visitIntervalDayTime=z;M.prototype.visitIntervalYearMonth=z;M.prototype.visitFixedSizeList=z;M.prototype.visitMap=z;const Xr=new M;class N extends J{}function P(e){const{type:t}=e;if(e.nullCount===0&&e.stride===1&&(t.typeId===h.Timestamp||t instanceof yn&&t.bitWidth!==64||t instanceof Zi&&t.bitWidth!==64||t instanceof qi&&t.precision!==Vt.HALF))return new Ec(e.data.length,i=>{const r=e.data[i];return r.values.subarray(0,r.length)[Symbol.iterator]()});let n=0;return new Ec(e.data.length,i=>{const s=e.data[i].length,o=e.slice(n,n+s);return n+=s,new Em(o)})}class Em{constructor(t){this.vector=t,this.index=0}next(){return this.index<this.vector.length?{value:this.vector.get(this.index++)}:{done:!0,value:null}}[Symbol.iterator](){return this}}N.prototype.visitNull=P;N.prototype.visitBool=P;N.prototype.visitInt=P;N.prototype.visitInt8=P;N.prototype.visitInt16=P;N.prototype.visitInt32=P;N.prototype.visitInt64=P;N.prototype.visitUint8=P;N.prototype.visitUint16=P;N.prototype.visitUint32=P;N.prototype.visitUint64=P;N.prototype.visitFloat=P;N.prototype.visitFloat16=P;N.prototype.visitFloat32=P;N.prototype.visitFloat64=P;N.prototype.visitUtf8=P;N.prototype.visitBinary=P;N.prototype.visitFixedSizeBinary=P;N.prototype.visitDate=P;N.prototype.visitDateDay=P;N.prototype.visitDateMillisecond=P;N.prototype.visitTimestamp=P;N.prototype.visitTimestampSecond=P;N.prototype.visitTimestampMillisecond=P;N.prototype.visitTimestampMicrosecond=P;N.prototype.visitTimestampNanosecond=P;N.prototype.visitTime=P;N.prototype.visitTimeSecond=P;N.prototype.visitTimeMillisecond=P;N.prototype.visitTimeMicrosecond=P;N.prototype.visitTimeNanosecond=P;N.prototype.visitDecimal=P;N.prototype.visitList=P;N.prototype.visitStruct=P;N.prototype.visitUnion=P;N.prototype.visitDenseUnion=P;N.prototype.visitSparseUnion=P;N.prototype.visitDictionary=P;N.prototype.visitInterval=P;N.prototype.visitIntervalDayTime=P;N.prototype.visitIntervalYearMonth=P;N.prototype.visitFixedSizeList=P;N.prototype.visitMap=P;const Oa=new N,Cm=(e,t)=>e+t;class _n extends J{visitNull(t,n){return 0}visitInt(t,n){return t.type.bitWidth/8}visitFloat(t,n){return t.type.ArrayType.BYTES_PER_ELEMENT}visitBool(t,n){return 1/8}visitDecimal(t,n){return t.type.bitWidth/8}visitDate(t,n){return(t.type.unit+1)*4}visitTime(t,n){return t.type.bitWidth/8}visitTimestamp(t,n){return t.type.unit===X.SECOND?4:8}visitInterval(t,n){return(t.type.unit+1)*4}visitStruct(t,n){return t.children.reduce((i,r)=>i+Le.visit(r,n),0)}visitFixedSizeBinary(t,n){return t.type.byteWidth}visitMap(t,n){return 8+t.children.reduce((i,r)=>i+Le.visit(r,n),0)}visitDictionary(t,n){var i;return t.type.indices.bitWidth/8+(((i=t.dictionary)===null||i===void 0?void 0:i.getByteLength(t.values[n]))||0)}}const Rm=({valueOffsets:e},t)=>8+(e[t+1]-e[t]),Lm=({valueOffsets:e},t)=>8+(e[t+1]-e[t]),Um=({valueOffsets:e,stride:t,children:n},i)=>{const r=n[0],{[i*t]:s}=e,{[i*t+1]:o}=e,a=Le.getVisitFn(r.type),c=r.slice(s,o-s);let l=8;for(let f=-1,p=o-s;++f<p;)l+=a(c,f);return l},Pm=({stride:e,children:t},n)=>{const i=t[0],r=i.slice(n*e,e),s=Le.getVisitFn(i.type);let o=0;for(let a=-1,c=r.length;++a<c;)o+=s(r,a);return o},jm=(e,t)=>e.type.mode===Xt.Dense?Of(e,t):Af(e,t),Of=({type:e,children:t,typeIds:n,valueOffsets:i},r)=>{const s=e.typeIdToChildIndex[n[r]];return 8+Le.visit(t[s],i[r])},Af=({children:e},t)=>4+Le.visitMany(e,e.map(()=>t)).reduce(Cm,0);_n.prototype.visitUtf8=Rm;_n.prototype.visitBinary=Lm;_n.prototype.visitList=Um;_n.prototype.visitFixedSizeList=Pm;_n.prototype.visitUnion=jm;_n.prototype.visitDenseUnion=Of;_n.prototype.visitSparseUnion=Af;const Le=new _n;var Ff;const Df={},Tf={};class tt{constructor(t){var n,i,r;const s=t[0]instanceof tt?t.flatMap(a=>a.data):t;if(s.length===0||s.some(a=>!(a instanceof ut)))throw new TypeError("Vector constructor expects an Array of Data instances.");const o=(n=s[0])===null||n===void 0?void 0:n.type;switch(s.length){case 0:this._offsets=[0];break;case 1:{const{get:a,set:c,indexOf:l,byteLength:f}=Df[o.typeId],p=s[0];this.isValid=m=>Ba(p,m),this.get=m=>a(p,m),this.set=(m,I)=>c(p,m,I),this.indexOf=m=>l(p,m),this.getByteLength=m=>f(p,m),this._offsets=[0,p.length];break}default:Object.setPrototypeOf(this,Tf[o.typeId]),this._offsets=vf(s);break}this.data=s,this.type=o,this.stride=ze(o),this.numChildren=(r=(i=o.children)===null||i===void 0?void 0:i.length)!==null&&r!==void 0?r:0,this.length=this._offsets[this._offsets.length-1]}get byteLength(){return this._byteLength===-1&&(this._byteLength=this.data.reduce((t,n)=>t+n.byteLength,0)),this._byteLength}get nullCount(){return this._nullCount===-1&&(this._nullCount=_f(this.data)),this._nullCount}get ArrayType(){return this.type.ArrayType}get[Symbol.toStringTag](){return`${this.VectorName}<${this.type[Symbol.toStringTag]}>`}get VectorName(){return`${h[this.type.typeId]}Vector`}isValid(t){return!1}get(t){return null}set(t,n){}indexOf(t,n){return-1}includes(t,n){return this.indexOf(t,n)>0}getByteLength(t){return 0}[Symbol.iterator](){return Oa.visit(this)}concat(...t){return new tt(this.data.concat(t.flatMap(n=>n.data).flat(Number.POSITIVE_INFINITY)))}slice(t,n){return new tt(gf(this,t,n,({data:i,_offsets:r},s,o)=>wf(i,r,s,o)))}toJSON(){return[...this]}toArray(){const{type:t,data:n,length:i,stride:r,ArrayType:s}=this;switch(t.typeId){case h.Int:case h.Float:case h.Decimal:case h.Time:case h.Timestamp:switch(n.length){case 0:return new s;case 1:return n[0].values.subarray(0,i*r);default:return n.reduce((o,{values:a,length:c})=>(o.array.set(a.subarray(0,c*r),o.offset),o.offset+=c*r,o),{array:new s(i*r),offset:0}).array}}return[...this]}toString(){return`[${[...this].join(",")}]`}getChild(t){var n;return this.getChildAt((n=this.type.children)===null||n===void 0?void 0:n.findIndex(i=>i.name===t))}getChildAt(t){return t>-1&&t<this.numChildren?new tt(this.data.map(({children:n})=>n[t])):null}get isMemoized(){return T.isDictionary(this.type)?this.data[0].dictionary.isMemoized:!1}memoize(){if(T.isDictionary(this.type)){const t=new Qr(this.data[0].dictionary),n=this.data.map(i=>{const r=i.clone();return r.dictionary=t,r});return new tt(n)}return new Qr(this)}unmemoize(){if(T.isDictionary(this.type)&&this.isMemoized){const t=this.data[0].dictionary.unmemoize(),n=this.data.map(i=>{const r=i.clone();return r.dictionary=t,r});return new tt(n)}return this}}Ff=Symbol.toStringTag;tt[Ff]=(e=>{e.type=T.prototype,e.data=[],e.length=0,e.stride=1,e.numChildren=0,e._nullCount=-1,e._byteLength=-1,e._offsets=new Uint32Array([0]),e[Symbol.isConcatSpreadable]=!0;const t=Object.keys(h).map(n=>h[n]).filter(n=>typeof n=="number"&&n!==h.NONE);for(const n of t){const i=Ht.getVisitFnByTypeId(n),r=se.getVisitFnByTypeId(n),s=Xr.getVisitFnByTypeId(n),o=Le.getVisitFnByTypeId(n);Df[n]={get:i,set:r,indexOf:s,byteLength:o},Tf[n]=Object.create(e,{isValid:{value:ai(Ba)},get:{value:ai(Ht.getVisitFnByTypeId(n))},set:{value:Sf(se.getVisitFnByTypeId(n))},indexOf:{value:If(Xr.getVisitFnByTypeId(n))},getByteLength:{value:ai(Le.getVisitFnByTypeId(n))}})}return"Vector"})(tt.prototype);class Qr extends tt{constructor(t){super(t.data);const n=this.get,i=this.set,r=this.slice,s=new Array(this.length);Object.defineProperty(this,"get",{value(o){const a=s[o];if(a!==void 0)return a;const c=n.call(this,o);return s[o]=c,c}}),Object.defineProperty(this,"set",{value(o,a){i.call(this,o,a),s[o]=a}}),Object.defineProperty(this,"slice",{value:(o,a)=>new Qr(r.call(this,o,a))}),Object.defineProperty(this,"isMemoized",{value:!0}),Object.defineProperty(this,"unmemoize",{value:()=>new tt(this.data)}),Object.defineProperty(this,"memoize",{value:()=>this})}}class Fo{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}offset(){return this.bb.readInt64(this.bb_pos)}metaDataLength(){return this.bb.readInt32(this.bb_pos+8)}bodyLength(){return this.bb.readInt64(this.bb_pos+16)}static sizeOf(){return 24}static createBlock(t,n,i,r){return t.prep(8,24),t.writeInt64(r),t.pad(4),t.writeInt32(i),t.writeInt64(n),t.offset()}}const eo=2,Ae=4,He=4,at=4,on=new Int32Array(2),Cc=new Float32Array(on.buffer),Rc=new Float64Array(on.buffer),_r=new Uint16Array(new Uint8Array([1,0]).buffer)[0]===1;let Je=class Do{constructor(t,n){this.low=t|0,this.high=n|0}static create(t,n){return t==0&&n==0?Do.ZERO:new Do(t,n)}toFloat64(){return(this.low>>>0)+this.high*4294967296}equals(t){return this.low==t.low&&this.high==t.high}};Je.ZERO=new Je(0,0);var To;(function(e){e[e.UTF8_BYTES=1]="UTF8_BYTES",e[e.UTF16_STRING=2]="UTF16_STRING"})(To||(To={}));let di=class xf{constructor(t){this.bytes_=t,this.position_=0}static allocate(t){return new xf(new Uint8Array(t))}clear(){this.position_=0}bytes(){return this.bytes_}position(){return this.position_}setPosition(t){this.position_=t}capacity(){return this.bytes_.length}readInt8(t){return this.readUint8(t)<<24>>24}readUint8(t){return this.bytes_[t]}readInt16(t){return this.readUint16(t)<<16>>16}readUint16(t){return this.bytes_[t]|this.bytes_[t+1]<<8}readInt32(t){return this.bytes_[t]|this.bytes_[t+1]<<8|this.bytes_[t+2]<<16|this.bytes_[t+3]<<24}readUint32(t){return this.readInt32(t)>>>0}readInt64(t){return new Je(this.readInt32(t),this.readInt32(t+4))}readUint64(t){return new Je(this.readUint32(t),this.readUint32(t+4))}readFloat32(t){return on[0]=this.readInt32(t),Cc[0]}readFloat64(t){return on[_r?0:1]=this.readInt32(t),on[_r?1:0]=this.readInt32(t+4),Rc[0]}writeInt8(t,n){this.bytes_[t]=n}writeUint8(t,n){this.bytes_[t]=n}writeInt16(t,n){this.bytes_[t]=n,this.bytes_[t+1]=n>>8}writeUint16(t,n){this.bytes_[t]=n,this.bytes_[t+1]=n>>8}writeInt32(t,n){this.bytes_[t]=n,this.bytes_[t+1]=n>>8,this.bytes_[t+2]=n>>16,this.bytes_[t+3]=n>>24}writeUint32(t,n){this.bytes_[t]=n,this.bytes_[t+1]=n>>8,this.bytes_[t+2]=n>>16,this.bytes_[t+3]=n>>24}writeInt64(t,n){this.writeInt32(t,n.low),this.writeInt32(t+4,n.high)}writeUint64(t,n){this.writeUint32(t,n.low),this.writeUint32(t+4,n.high)}writeFloat32(t,n){Cc[0]=n,this.writeInt32(t,on[0])}writeFloat64(t,n){Rc[0]=n,this.writeInt32(t,on[_r?0:1]),this.writeInt32(t+4,on[_r?1:0])}getBufferIdentifier(){if(this.bytes_.length<this.position_+Ae+He)throw new Error("FlatBuffers: ByteBuffer is too short to contain an identifier.");let t="";for(let n=0;n<He;n++)t+=String.fromCharCode(this.readInt8(this.position_+Ae+n));return t}__offset(t,n){const i=t-this.readInt32(t);return n<this.readInt16(i)?this.readInt16(i+n):0}__union(t,n){return t.bb_pos=n+this.readInt32(n),t.bb=this,t}__string(t,n){t+=this.readInt32(t);const i=this.readInt32(t);let r="",s=0;if(t+=Ae,n===To.UTF8_BYTES)return this.bytes_.subarray(t,t+i);for(;s<i;){let o;const a=this.readUint8(t+s++);if(a<192)o=a;else{const c=this.readUint8(t+s++);if(a<224)o=(a&31)<<6|c&63;else{const l=this.readUint8(t+s++);if(a<240)o=(a&15)<<12|(c&63)<<6|l&63;else{const f=this.readUint8(t+s++);o=(a&7)<<18|(c&63)<<12|(l&63)<<6|f&63}}}o<65536?r+=String.fromCharCode(o):(o-=65536,r+=String.fromCharCode((o>>10)+55296,(o&1023)+56320))}return r}__union_with_string(t,n){return typeof t=="string"?this.__string(n):this.__union(t,n)}__indirect(t){return t+this.readInt32(t)}__vector(t){return t+this.readInt32(t)+Ae}__vector_len(t){return this.readInt32(t+this.readInt32(t))}__has_identifier(t){if(t.length!=He)throw new Error("FlatBuffers: file identifier must be length "+He);for(let n=0;n<He;n++)if(t.charCodeAt(n)!=this.readInt8(this.position()+Ae+n))return!1;return!0}createLong(t,n){return Je.create(t,n)}createScalarList(t,n){const i=[];for(let r=0;r<n;++r)t(r)!==null&&i.push(t(r));return i}createObjList(t,n){const i=[];for(let r=0;r<n;++r){const s=t(r);s!==null&&i.push(s.unpack())}return i}},Mf=class Nf{constructor(t){this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null;let n;t?n=t:n=1024,this.bb=di.allocate(n),this.space=n}clear(){this.bb.clear(),this.space=this.bb.capacity(),this.minalign=1,this.vtable=null,this.vtable_in_use=0,this.isNested=!1,this.object_start=0,this.vtables=[],this.vector_num_elems=0,this.force_defaults=!1,this.string_maps=null}forceDefaults(t){this.force_defaults=t}dataBuffer(){return this.bb}asUint8Array(){return this.bb.bytes().subarray(this.bb.position(),this.bb.position()+this.offset())}prep(t,n){t>this.minalign&&(this.minalign=t);const i=~(this.bb.capacity()-this.space+n)+1&t-1;for(;this.space<i+t+n;){const r=this.bb.capacity();this.bb=Nf.growByteBuffer(this.bb),this.space+=this.bb.capacity()-r}this.pad(i)}pad(t){for(let n=0;n<t;n++)this.bb.writeInt8(--this.space,0)}writeInt8(t){this.bb.writeInt8(this.space-=1,t)}writeInt16(t){this.bb.writeInt16(this.space-=2,t)}writeInt32(t){this.bb.writeInt32(this.space-=4,t)}writeInt64(t){this.bb.writeInt64(this.space-=8,t)}writeFloat32(t){this.bb.writeFloat32(this.space-=4,t)}writeFloat64(t){this.bb.writeFloat64(this.space-=8,t)}addInt8(t){this.prep(1,0),this.writeInt8(t)}addInt16(t){this.prep(2,0),this.writeInt16(t)}addInt32(t){this.prep(4,0),this.writeInt32(t)}addInt64(t){this.prep(8,0),this.writeInt64(t)}addFloat32(t){this.prep(4,0),this.writeFloat32(t)}addFloat64(t){this.prep(8,0),this.writeFloat64(t)}addFieldInt8(t,n,i){(this.force_defaults||n!=i)&&(this.addInt8(n),this.slot(t))}addFieldInt16(t,n,i){(this.force_defaults||n!=i)&&(this.addInt16(n),this.slot(t))}addFieldInt32(t,n,i){(this.force_defaults||n!=i)&&(this.addInt32(n),this.slot(t))}addFieldInt64(t,n,i){(this.force_defaults||!n.equals(i))&&(this.addInt64(n),this.slot(t))}addFieldFloat32(t,n,i){(this.force_defaults||n!=i)&&(this.addFloat32(n),this.slot(t))}addFieldFloat64(t,n,i){(this.force_defaults||n!=i)&&(this.addFloat64(n),this.slot(t))}addFieldOffset(t,n,i){(this.force_defaults||n!=i)&&(this.addOffset(n),this.slot(t))}addFieldStruct(t,n,i){n!=i&&(this.nested(n),this.slot(t))}nested(t){if(t!=this.offset())throw new Error("FlatBuffers: struct must be serialized inline.")}notNested(){if(this.isNested)throw new Error("FlatBuffers: object serialization must not be nested.")}slot(t){this.vtable!==null&&(this.vtable[t]=this.offset())}offset(){return this.bb.capacity()-this.space}static growByteBuffer(t){const n=t.capacity();if(n&3221225472)throw new Error("FlatBuffers: cannot grow buffer beyond 2 gigabytes.");const i=n<<1,r=di.allocate(i);return r.setPosition(i-n),r.bytes().set(t.bytes(),i-n),r}addOffset(t){this.prep(Ae,0),this.writeInt32(this.offset()-t+Ae)}startObject(t){this.notNested(),this.vtable==null&&(this.vtable=[]),this.vtable_in_use=t;for(let n=0;n<t;n++)this.vtable[n]=0;this.isNested=!0,this.object_start=this.offset()}endObject(){if(this.vtable==null||!this.isNested)throw new Error("FlatBuffers: endObject called without startObject");this.addInt32(0);const t=this.offset();let n=this.vtable_in_use-1;for(;n>=0&&this.vtable[n]==0;n--);const i=n+1;for(;n>=0;n--)this.addInt16(this.vtable[n]!=0?t-this.vtable[n]:0);const r=2;this.addInt16(t-this.object_start);const s=(i+r)*eo;this.addInt16(s);let o=0;const a=this.space;t:for(n=0;n<this.vtables.length;n++){const c=this.bb.capacity()-this.vtables[n];if(s==this.bb.readInt16(c)){for(let l=eo;l<s;l+=eo)if(this.bb.readInt16(a+l)!=this.bb.readInt16(c+l))continue t;o=this.vtables[n];break}}return o?(this.space=this.bb.capacity()-t,this.bb.writeInt32(this.space,o-t)):(this.vtables.push(this.offset()),this.bb.writeInt32(this.bb.capacity()-t,this.offset()-t)),this.isNested=!1,t}finish(t,n,i){const r=i?at:0;if(n){const s=n;if(this.prep(this.minalign,Ae+He+r),s.length!=He)throw new Error("FlatBuffers: file identifier must be length "+He);for(let o=He-1;o>=0;o--)this.writeInt8(s.charCodeAt(o))}this.prep(this.minalign,Ae+r),this.addOffset(t),r&&this.addInt32(this.bb.capacity()-this.space),this.bb.setPosition(this.space)}finishSizePrefixed(t,n){this.finish(t,n,!0)}requiredField(t,n){const i=this.bb.capacity()-t,r=i-this.bb.readInt32(i);if(!(this.bb.readInt16(r+n)!=0))throw new Error("FlatBuffers: field "+n+" must be set")}startVector(t,n,i){this.notNested(),this.vector_num_elems=n,this.prep(Ae,t*n),this.prep(i,t*n)}endVector(){return this.writeInt32(this.vector_num_elems),this.offset()}createSharedString(t){if(!t)return 0;if(this.string_maps||(this.string_maps=new Map),this.string_maps.has(t))return this.string_maps.get(t);const n=this.createString(t);return this.string_maps.set(t,n),n}createString(t){if(!t)return 0;let n;if(t instanceof Uint8Array)n=t;else{n=[];let i=0;for(;i<t.length;){let r;const s=t.charCodeAt(i++);if(s<55296||s>=56320)r=s;else{const o=t.charCodeAt(i++);r=(s<<10)+o+-56613888}r<128?n.push(r):(r<2048?n.push(r>>6&31|192):(r<65536?n.push(r>>12&15|224):n.push(r>>18&7|240,r>>12&63|128),n.push(r>>6&63|128)),n.push(r&63|128))}}this.addInt8(0),this.startVector(1,n.length,1),this.bb.setPosition(this.space-=n.length);for(let i=0,r=this.space,s=this.bb.bytes();i<n.length;i++)s[r++]=n[i];return this.endVector()}createLong(t,n){return Je.create(t,n)}createObjectOffset(t){return t===null?0:typeof t=="string"?this.createString(t):t.pack(this)}createObjectOffsetList(t){const n=[];for(let i=0;i<t.length;++i){const r=t[i];if(r!==null)n.push(this.createObjectOffset(r));else throw new Error("FlatBuffers: Argument for createObjectOffsetList cannot contain null.")}return n}createStructOffsetList(t,n){return n(this,t.length),this.createObjectOffsetList(t),this.endVector()}};class It{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsKeyValue(t,n){return(n||new It).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsKeyValue(t,n){return t.setPosition(t.position()+at),(n||new It).__init(t.readInt32(t.position())+t.position(),t)}key(t){const n=this.bb.__offset(this.bb_pos,4);return n?this.bb.__string(this.bb_pos+n,t):null}value(t){const n=this.bb.__offset(this.bb_pos,6);return n?this.bb.__string(this.bb_pos+n,t):null}static startKeyValue(t){t.startObject(2)}static addKey(t,n){t.addFieldOffset(0,n,0)}static addValue(t,n){t.addFieldOffset(1,n,0)}static endKeyValue(t){return t.endObject()}static createKeyValue(t,n,i){return It.startKeyValue(t),It.addKey(t,n),It.addValue(t,i),It.endKeyValue(t)}}var hi;(function(e){e[e.V1=0]="V1",e[e.V2=1]="V2",e[e.V3=2]="V3",e[e.V4=3]="V4",e[e.V5=4]="V5"})(hi||(hi={}));var pi;(function(e){e[e.Little=0]="Little",e[e.Big=1]="Big"})(pi||(pi={}));var Gr;(function(e){e[e.DenseArray=0]="DenseArray"})(Gr||(Gr={}));class qt{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsInt(t,n){return(n||new qt).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsInt(t,n){return t.setPosition(t.position()+at),(n||new qt).__init(t.readInt32(t.position())+t.position(),t)}bitWidth(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}isSigned(){const t=this.bb.__offset(this.bb_pos,6);return t?!!this.bb.readInt8(this.bb_pos+t):!1}static startInt(t){t.startObject(2)}static addBitWidth(t,n){t.addFieldInt32(0,n,0)}static addIsSigned(t,n){t.addFieldInt8(1,+n,0)}static endInt(t){return t.endObject()}static createInt(t,n,i){return qt.startInt(t),qt.addBitWidth(t,n),qt.addIsSigned(t,i),qt.endInt(t)}}class Ye{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsDictionaryEncoding(t,n){return(n||new Ye).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDictionaryEncoding(t,n){return t.setPosition(t.position()+at),(n||new Ye).__init(t.readInt32(t.position())+t.position(),t)}id(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):this.bb.createLong(0,0)}indexType(t){const n=this.bb.__offset(this.bb_pos,6);return n?(t||new qt).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}isOrdered(){const t=this.bb.__offset(this.bb_pos,8);return t?!!this.bb.readInt8(this.bb_pos+t):!1}dictionaryKind(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt16(this.bb_pos+t):Gr.DenseArray}static startDictionaryEncoding(t){t.startObject(4)}static addId(t,n){t.addFieldInt64(0,n,t.createLong(0,0))}static addIndexType(t,n){t.addFieldOffset(1,n,0)}static addIsOrdered(t,n){t.addFieldInt8(2,+n,0)}static addDictionaryKind(t,n){t.addFieldInt16(3,n,Gr.DenseArray)}static endDictionaryEncoding(t){return t.endObject()}}class Tn{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsBinary(t,n){return(n||new Tn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsBinary(t,n){return t.setPosition(t.position()+at),(n||new Tn).__init(t.readInt32(t.position())+t.position(),t)}static startBinary(t){t.startObject(0)}static endBinary(t){return t.endObject()}static createBinary(t){return Tn.startBinary(t),Tn.endBinary(t)}}class xn{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsBool(t,n){return(n||new xn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsBool(t,n){return t.setPosition(t.position()+at),(n||new xn).__init(t.readInt32(t.position())+t.position(),t)}static startBool(t){t.startObject(0)}static endBool(t){return t.endObject()}static createBool(t){return xn.startBool(t),xn.endBool(t)}}var ts;(function(e){e[e.DAY=0]="DAY",e[e.MILLISECOND=1]="MILLISECOND"})(ts||(ts={}));let Ar=class Jn{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsDate(t,n){return(n||new Jn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDate(t,n){return t.setPosition(t.position()+at),(n||new Jn).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):ts.MILLISECOND}static startDate(t){t.startObject(1)}static addUnit(t,n){t.addFieldInt16(0,n,ts.MILLISECOND)}static endDate(t){return t.endObject()}static createDate(t,n){return Jn.startDate(t),Jn.addUnit(t,n),Jn.endDate(t)}};class Pt{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsDecimal(t,n){return(n||new Pt).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDecimal(t,n){return t.setPosition(t.position()+at),(n||new Pt).__init(t.readInt32(t.position())+t.position(),t)}precision(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}scale(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb_pos+t):0}bitWidth(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readInt32(this.bb_pos+t):128}static startDecimal(t){t.startObject(3)}static addPrecision(t,n){t.addFieldInt32(0,n,0)}static addScale(t,n){t.addFieldInt32(1,n,0)}static addBitWidth(t,n){t.addFieldInt32(2,n,128)}static endDecimal(t){return t.endObject()}static createDecimal(t,n,i,r){return Pt.startDecimal(t),Pt.addPrecision(t,n),Pt.addScale(t,i),Pt.addBitWidth(t,r),Pt.endDecimal(t)}}var yi;(function(e){e[e.SECOND=0]="SECOND",e[e.MILLISECOND=1]="MILLISECOND",e[e.MICROSECOND=2]="MICROSECOND",e[e.NANOSECOND=3]="NANOSECOND"})(yi||(yi={}));class Fe{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsFixedSizeBinary(t,n){return(n||new Fe).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFixedSizeBinary(t,n){return t.setPosition(t.position()+at),(n||new Fe).__init(t.readInt32(t.position())+t.position(),t)}byteWidth(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}static startFixedSizeBinary(t){t.startObject(1)}static addByteWidth(t,n){t.addFieldInt32(0,n,0)}static endFixedSizeBinary(t){return t.endObject()}static createFixedSizeBinary(t,n){return Fe.startFixedSizeBinary(t),Fe.addByteWidth(t,n),Fe.endFixedSizeBinary(t)}}class De{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsFixedSizeList(t,n){return(n||new De).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFixedSizeList(t,n){return t.setPosition(t.position()+at),(n||new De).__init(t.readInt32(t.position())+t.position(),t)}listSize(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt32(this.bb_pos+t):0}static startFixedSizeList(t){t.startObject(1)}static addListSize(t,n){t.addFieldInt32(0,n,0)}static endFixedSizeList(t){return t.endObject()}static createFixedSizeList(t,n){return De.startFixedSizeList(t),De.addListSize(t,n),De.endFixedSizeList(t)}}var es;(function(e){e[e.HALF=0]="HALF",e[e.SINGLE=1]="SINGLE",e[e.DOUBLE=2]="DOUBLE"})(es||(es={}));class Te{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsFloatingPoint(t,n){return(n||new Te).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFloatingPoint(t,n){return t.setPosition(t.position()+at),(n||new Te).__init(t.readInt32(t.position())+t.position(),t)}precision(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):es.HALF}static startFloatingPoint(t){t.startObject(1)}static addPrecision(t,n){t.addFieldInt16(0,n,es.HALF)}static endFloatingPoint(t){return t.endObject()}static createFloatingPoint(t,n){return Te.startFloatingPoint(t),Te.addPrecision(t,n),Te.endFloatingPoint(t)}}var ns;(function(e){e[e.YEAR_MONTH=0]="YEAR_MONTH",e[e.DAY_TIME=1]="DAY_TIME",e[e.MONTH_DAY_NANO=2]="MONTH_DAY_NANO"})(ns||(ns={}));class xe{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsInterval(t,n){return(n||new xe).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsInterval(t,n){return t.setPosition(t.position()+at),(n||new xe).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):ns.YEAR_MONTH}static startInterval(t){t.startObject(1)}static addUnit(t,n){t.addFieldInt16(0,n,ns.YEAR_MONTH)}static endInterval(t){return t.endObject()}static createInterval(t,n){return xe.startInterval(t),xe.addUnit(t,n),xe.endInterval(t)}}class Mn{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsList(t,n){return(n||new Mn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsList(t,n){return t.setPosition(t.position()+at),(n||new Mn).__init(t.readInt32(t.position())+t.position(),t)}static startList(t){t.startObject(0)}static endList(t){return t.endObject()}static createList(t){return Mn.startList(t),Mn.endList(t)}}let Fr=class qn{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsMap(t,n){return(n||new qn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsMap(t,n){return t.setPosition(t.position()+at),(n||new qn).__init(t.readInt32(t.position())+t.position(),t)}keysSorted(){const t=this.bb.__offset(this.bb_pos,4);return t?!!this.bb.readInt8(this.bb_pos+t):!1}static startMap(t){t.startObject(1)}static addKeysSorted(t,n){t.addFieldInt8(0,+n,0)}static endMap(t){return t.endObject()}static createMap(t,n){return qn.startMap(t),qn.addKeysSorted(t,n),qn.endMap(t)}};class Nn{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsNull(t,n){return(n||new Nn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsNull(t,n){return t.setPosition(t.position()+at),(n||new Nn).__init(t.readInt32(t.position())+t.position(),t)}static startNull(t){t.startObject(0)}static endNull(t){return t.endObject()}static createNull(t){return Nn.startNull(t),Nn.endNull(t)}}class En{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsStruct_(t,n){return(n||new En).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsStruct_(t,n){return t.setPosition(t.position()+at),(n||new En).__init(t.readInt32(t.position())+t.position(),t)}static startStruct_(t){t.startObject(0)}static endStruct_(t){return t.endObject()}static createStruct_(t){return En.startStruct_(t),En.endStruct_(t)}}class ne{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsTime(t,n){return(n||new ne).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTime(t,n){return t.setPosition(t.position()+at),(n||new ne).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):yi.MILLISECOND}bitWidth(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt32(this.bb_pos+t):32}static startTime(t){t.startObject(2)}static addUnit(t,n){t.addFieldInt16(0,n,yi.MILLISECOND)}static addBitWidth(t,n){t.addFieldInt32(1,n,32)}static endTime(t){return t.endObject()}static createTime(t,n,i){return ne.startTime(t),ne.addUnit(t,n),ne.addBitWidth(t,i),ne.endTime(t)}}class ie{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsTimestamp(t,n){return(n||new ie).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsTimestamp(t,n){return t.setPosition(t.position()+at),(n||new ie).__init(t.readInt32(t.position())+t.position(),t)}unit(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):yi.SECOND}timezone(t){const n=this.bb.__offset(this.bb_pos,6);return n?this.bb.__string(this.bb_pos+n,t):null}static startTimestamp(t){t.startObject(2)}static addUnit(t,n){t.addFieldInt16(0,n,yi.SECOND)}static addTimezone(t,n){t.addFieldOffset(1,n,0)}static endTimestamp(t){return t.endObject()}static createTimestamp(t,n,i){return ie.startTimestamp(t),ie.addUnit(t,n),ie.addTimezone(t,i),ie.endTimestamp(t)}}var is;(function(e){e[e.Sparse=0]="Sparse",e[e.Dense=1]="Dense"})(is||(is={}));class jt{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsUnion(t,n){return(n||new jt).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsUnion(t,n){return t.setPosition(t.position()+at),(n||new jt).__init(t.readInt32(t.position())+t.position(),t)}mode(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):is.Sparse}typeIds(t){const n=this.bb.__offset(this.bb_pos,6);return n?this.bb.readInt32(this.bb.__vector(this.bb_pos+n)+t*4):0}typeIdsLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}typeIdsArray(){const t=this.bb.__offset(this.bb_pos,6);return t?new Int32Array(this.bb.bytes().buffer,this.bb.bytes().byteOffset+this.bb.__vector(this.bb_pos+t),this.bb.__vector_len(this.bb_pos+t)):null}static startUnion(t){t.startObject(2)}static addMode(t,n){t.addFieldInt16(0,n,is.Sparse)}static addTypeIds(t,n){t.addFieldOffset(1,n,0)}static createTypeIdsVector(t,n){t.startVector(4,n.length,4);for(let i=n.length-1;i>=0;i--)t.addInt32(n[i]);return t.endVector()}static startTypeIdsVector(t,n){t.startVector(4,n,4)}static endUnion(t){return t.endObject()}static createUnion(t,n,i){return jt.startUnion(t),jt.addMode(t,n),jt.addTypeIds(t,i),jt.endUnion(t)}}class Cn{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsUtf8(t,n){return(n||new Cn).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsUtf8(t,n){return t.setPosition(t.position()+at),(n||new Cn).__init(t.readInt32(t.position())+t.position(),t)}static startUtf8(t){t.startObject(0)}static endUtf8(t){return t.endObject()}static createUtf8(t){return Cn.startUtf8(t),Cn.endUtf8(t)}}var pt;(function(e){e[e.NONE=0]="NONE",e[e.Null=1]="Null",e[e.Int=2]="Int",e[e.FloatingPoint=3]="FloatingPoint",e[e.Binary=4]="Binary",e[e.Utf8=5]="Utf8",e[e.Bool=6]="Bool",e[e.Decimal=7]="Decimal",e[e.Date=8]="Date",e[e.Time=9]="Time",e[e.Timestamp=10]="Timestamp",e[e.Interval=11]="Interval",e[e.List=12]="List",e[e.Struct_=13]="Struct_",e[e.Union=14]="Union",e[e.FixedSizeBinary=15]="FixedSizeBinary",e[e.FixedSizeList=16]="FixedSizeList",e[e.Map=17]="Map",e[e.Duration=18]="Duration",e[e.LargeBinary=19]="LargeBinary",e[e.LargeUtf8=20]="LargeUtf8",e[e.LargeList=21]="LargeList"})(pt||(pt={}));let te=class Dr{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsField(t,n){return(n||new Dr).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsField(t,n){return t.setPosition(t.position()+at),(n||new Dr).__init(t.readInt32(t.position())+t.position(),t)}name(t){const n=this.bb.__offset(this.bb_pos,4);return n?this.bb.__string(this.bb_pos+n,t):null}nullable(){const t=this.bb.__offset(this.bb_pos,6);return t?!!this.bb.readInt8(this.bb_pos+t):!1}typeType(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.readUint8(this.bb_pos+t):pt.NONE}type(t){const n=this.bb.__offset(this.bb_pos,10);return n?this.bb.__union(t,this.bb_pos+n):null}dictionary(t){const n=this.bb.__offset(this.bb_pos,12);return n?(t||new Ye).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}children(t,n){const i=this.bb.__offset(this.bb_pos,14);return i?(n||new Dr).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+i)+t*4),this.bb):null}childrenLength(){const t=this.bb.__offset(this.bb_pos,14);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,n){const i=this.bb.__offset(this.bb_pos,16);return i?(n||new It).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+i)+t*4),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,16);return t?this.bb.__vector_len(this.bb_pos+t):0}static startField(t){t.startObject(7)}static addName(t,n){t.addFieldOffset(0,n,0)}static addNullable(t,n){t.addFieldInt8(1,+n,0)}static addTypeType(t,n){t.addFieldInt8(2,n,pt.NONE)}static addType(t,n){t.addFieldOffset(3,n,0)}static addDictionary(t,n){t.addFieldOffset(4,n,0)}static addChildren(t,n){t.addFieldOffset(5,n,0)}static createChildrenVector(t,n){t.startVector(4,n.length,4);for(let i=n.length-1;i>=0;i--)t.addOffset(n[i]);return t.endVector()}static startChildrenVector(t,n){t.startVector(4,n,4)}static addCustomMetadata(t,n){t.addFieldOffset(6,n,0)}static createCustomMetadataVector(t,n){t.startVector(4,n.length,4);for(let i=n.length-1;i>=0;i--)t.addOffset(n[i]);return t.endVector()}static startCustomMetadataVector(t,n){t.startVector(4,n,4)}static endField(t){return t.endObject()}},we=class je{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsSchema(t,n){return(n||new je).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsSchema(t,n){return t.setPosition(t.position()+at),(n||new je).__init(t.readInt32(t.position())+t.position(),t)}endianness(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):pi.Little}fields(t,n){const i=this.bb.__offset(this.bb_pos,6);return i?(n||new te).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+i)+t*4),this.bb):null}fieldsLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,n){const i=this.bb.__offset(this.bb_pos,8);return i?(n||new It).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+i)+t*4),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}features(t){const n=this.bb.__offset(this.bb_pos,10);return n?this.bb.readInt64(this.bb.__vector(this.bb_pos+n)+t*8):this.bb.createLong(0,0)}featuresLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__vector_len(this.bb_pos+t):0}static startSchema(t){t.startObject(4)}static addEndianness(t,n){t.addFieldInt16(0,n,pi.Little)}static addFields(t,n){t.addFieldOffset(1,n,0)}static createFieldsVector(t,n){t.startVector(4,n.length,4);for(let i=n.length-1;i>=0;i--)t.addOffset(n[i]);return t.endVector()}static startFieldsVector(t,n){t.startVector(4,n,4)}static addCustomMetadata(t,n){t.addFieldOffset(2,n,0)}static createCustomMetadataVector(t,n){t.startVector(4,n.length,4);for(let i=n.length-1;i>=0;i--)t.addOffset(n[i]);return t.endVector()}static startCustomMetadataVector(t,n){t.startVector(4,n,4)}static addFeatures(t,n){t.addFieldOffset(3,n,0)}static createFeaturesVector(t,n){t.startVector(8,n.length,8);for(let i=n.length-1;i>=0;i--)t.addInt64(n[i]);return t.endVector()}static startFeaturesVector(t,n){t.startVector(8,n,8)}static endSchema(t){return t.endObject()}static finishSchemaBuffer(t,n){t.finish(n)}static finishSizePrefixedSchemaBuffer(t,n){t.finish(n,void 0,!0)}static createSchema(t,n,i,r,s){return je.startSchema(t),je.addEndianness(t,n),je.addFields(t,i),je.addCustomMetadata(t,r),je.addFeatures(t,s),je.endSchema(t)}};class Jt{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsFooter(t,n){return(n||new Jt).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsFooter(t,n){return t.setPosition(t.position()+at),(n||new Jt).__init(t.readInt32(t.position())+t.position(),t)}version(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):hi.V1}schema(t){const n=this.bb.__offset(this.bb_pos,6);return n?(t||new we).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}dictionaries(t,n){const i=this.bb.__offset(this.bb_pos,8);return i?(n||new Fo).__init(this.bb.__vector(this.bb_pos+i)+t*24,this.bb):null}dictionariesLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}recordBatches(t,n){const i=this.bb.__offset(this.bb_pos,10);return i?(n||new Fo).__init(this.bb.__vector(this.bb_pos+i)+t*24,this.bb):null}recordBatchesLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.__vector_len(this.bb_pos+t):0}customMetadata(t,n){const i=this.bb.__offset(this.bb_pos,12);return i?(n||new It).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+i)+t*4),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}static startFooter(t){t.startObject(5)}static addVersion(t,n){t.addFieldInt16(0,n,hi.V1)}static addSchema(t,n){t.addFieldOffset(1,n,0)}static addDictionaries(t,n){t.addFieldOffset(2,n,0)}static startDictionariesVector(t,n){t.startVector(24,n,8)}static addRecordBatches(t,n){t.addFieldOffset(3,n,0)}static startRecordBatchesVector(t,n){t.startVector(24,n,8)}static addCustomMetadata(t,n){t.addFieldOffset(4,n,0)}static createCustomMetadataVector(t,n){t.startVector(4,n.length,4);for(let i=n.length-1;i>=0;i--)t.addOffset(n[i]);return t.endVector()}static startCustomMetadataVector(t,n){t.startVector(4,n,4)}static endFooter(t){return t.endObject()}static finishFooterBuffer(t,n){t.finish(n)}static finishSizePrefixedFooterBuffer(t,n){t.finish(n,void 0,!0)}}class et{constructor(t=[],n,i){this.fields=t||[],this.metadata=n||new Map,i||(i=xo(t)),this.dictionaries=i}get[Symbol.toStringTag](){return"Schema"}get names(){return this.fields.map(t=>t.name)}toString(){return`Schema<{ ${this.fields.map((t,n)=>`${n}: ${t}`).join(", ")} }>`}select(t){const n=new Set(t),i=this.fields.filter(r=>n.has(r.name));return new et(i,this.metadata)}selectAt(t){const n=t.map(i=>this.fields[i]).filter(Boolean);return new et(n,this.metadata)}assign(...t){const n=t[0]instanceof et?t[0]:Array.isArray(t[0])?new et(t[0]):new et(t),i=[...this.fields],r=vr(vr(new Map,this.metadata),n.metadata),s=n.fields.filter(a=>{const c=i.findIndex(l=>l.name===a.name);return~c?(i[c]=a.clone({metadata:vr(vr(new Map,i[c].metadata),a.metadata)}))&&!1:!0}),o=xo(s,new Map);return new et([...i,...s],r,new Map([...this.dictionaries,...o]))}}et.prototype.fields=null;et.prototype.metadata=null;et.prototype.dictionaries=null;class ft{constructor(t,n,i=!1,r){this.name=t,this.type=n,this.nullable=i,this.metadata=r||new Map}static new(...t){let[n,i,r,s]=t;return t[0]&&typeof t[0]=="object"&&({name:n}=t[0],i===void 0&&(i=t[0].type),r===void 0&&(r=t[0].nullable),s===void 0&&(s=t[0].metadata)),new ft(`${n}`,i,r,s)}get typeId(){return this.type.typeId}get[Symbol.toStringTag](){return"Field"}toString(){return`${this.name}: ${this.type}`}clone(...t){let[n,i,r,s]=t;return!t[0]||typeof t[0]!="object"?[n=this.name,i=this.type,r=this.nullable,s=this.metadata]=t:{name:n=this.name,type:i=this.type,nullable:r=this.nullable,metadata:s=this.metadata}=t[0],ft.new(n,i,r,s)}}ft.prototype.type=null;ft.prototype.name=null;ft.prototype.nullable=null;ft.prototype.metadata=null;function vr(e,t){return new Map([...e||new Map,...t||new Map])}function xo(e,t=new Map){for(let n=-1,i=e.length;++n<i;){const s=e[n].type;if(T.isDictionary(s)){if(!t.has(s.id))t.set(s.id,s.dictionary);else if(t.get(s.id)!==s.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}s.children&&s.children.length>0&&xo(s.children,t)}return t}var Lc=Je,$m=Mf,Vm=di;class Xi{constructor(t,n=Zt.V4,i,r){this.schema=t,this.version=n,i&&(this._recordBatches=i),r&&(this._dictionaryBatches=r)}static decode(t){t=new Vm(Z(t));const n=Jt.getRootAsFooter(t),i=et.decode(n.schema());return new zm(i,n)}static encode(t){const n=new $m,i=et.encode(n,t.schema);Jt.startRecordBatchesVector(n,t.numRecordBatches);for(const o of[...t.recordBatches()].slice().reverse())mn.encode(n,o);const r=n.endVector();Jt.startDictionariesVector(n,t.numDictionaries);for(const o of[...t.dictionaryBatches()].slice().reverse())mn.encode(n,o);const s=n.endVector();return Jt.startFooter(n),Jt.addSchema(n,i),Jt.addVersion(n,Zt.V4),Jt.addRecordBatches(n,r),Jt.addDictionaries(n,s),Jt.finishFooterBuffer(n,Jt.endFooter(n)),n.asUint8Array()}get numRecordBatches(){return this._recordBatches.length}get numDictionaries(){return this._dictionaryBatches.length}*recordBatches(){for(let t,n=-1,i=this.numRecordBatches;++n<i;)(t=this.getRecordBatch(n))&&(yield t)}*dictionaryBatches(){for(let t,n=-1,i=this.numDictionaries;++n<i;)(t=this.getDictionaryBatch(n))&&(yield t)}getRecordBatch(t){return t>=0&&t<this.numRecordBatches&&this._recordBatches[t]||null}getDictionaryBatch(t){return t>=0&&t<this.numDictionaries&&this._dictionaryBatches[t]||null}}class zm extends Xi{constructor(t,n){super(t,n.version()),this._footer=n}get numRecordBatches(){return this._footer.recordBatchesLength()}get numDictionaries(){return this._footer.dictionariesLength()}getRecordBatch(t){if(t>=0&&t<this.numRecordBatches){const n=this._footer.recordBatches(t);if(n)return mn.decode(n)}return null}getDictionaryBatch(t){if(t>=0&&t<this.numDictionaries){const n=this._footer.dictionaries(t);if(n)return mn.decode(n)}return null}}class mn{constructor(t,n,i){this.metaDataLength=t,this.offset=typeof i=="number"?i:i.low,this.bodyLength=typeof n=="number"?n:n.low}static decode(t){return new mn(t.metaDataLength(),t.bodyLength(),t.offset())}static encode(t,n){const{metaDataLength:i}=n,r=new Lc(n.offset,0),s=new Lc(n.bodyLength,0);return Fo.createBlock(t,r,i,s)}}const yt=Object.freeze({done:!0,value:void 0});class Uc{constructor(t){this._json=t}get schema(){return this._json.schema}get batches(){return this._json.batches||[]}get dictionaries(){return this._json.dictionaries||[]}}class Aa{tee(){return this._getDOMStream().tee()}pipe(t,n){return this._getNodeStream().pipe(t,n)}pipeTo(t,n){return this._getDOMStream().pipeTo(t,n)}pipeThrough(t,n){return this._getDOMStream().pipeThrough(t,n)}_getDOMStream(){return this._DOMStream||(this._DOMStream=this.toDOMStream())}_getNodeStream(){return this._nodeStream||(this._nodeStream=this.toNodeStream())}}class km extends Aa{constructor(){super(),this._values=[],this.resolvers=[],this._closedPromise=new Promise(t=>this._closedPromiseResolve=t)}get closed(){return this._closedPromise}cancel(t){return D(this,void 0,void 0,function*(){yield this.return(t)})}write(t){this._ensureOpen()&&(this.resolvers.length<=0?this._values.push(t):this.resolvers.shift().resolve({done:!1,value:t}))}abort(t){this._closedPromiseResolve&&(this.resolvers.length<=0?this._error={error:t}:this.resolvers.shift().reject({done:!0,value:t}))}close(){if(this._closedPromiseResolve){const{resolvers:t}=this;for(;t.length>0;)t.shift().resolve(yt);this._closedPromiseResolve(),this._closedPromiseResolve=void 0}}[Symbol.asyncIterator](){return this}toDOMStream(t){return ee.toDOMStream(this._closedPromiseResolve||this._error?this:this._values,t)}toNodeStream(t){return ee.toNodeStream(this._closedPromiseResolve||this._error?this:this._values,t)}throw(t){return D(this,void 0,void 0,function*(){return yield this.abort(t),yt})}return(t){return D(this,void 0,void 0,function*(){return yield this.close(),yt})}read(t){return D(this,void 0,void 0,function*(){return(yield this.next(t,"read")).value})}peek(t){return D(this,void 0,void 0,function*(){return(yield this.next(t,"peek")).value})}next(...t){return this._values.length>0?Promise.resolve({done:!1,value:this._values.shift()}):this._error?Promise.reject({done:!0,value:this._error.error}):this._closedPromiseResolve?new Promise((n,i)=>{this.resolvers.push({resolve:n,reject:i})}):Promise.resolve(yt)}_ensureOpen(){if(this._closedPromiseResolve)return!0;throw new Error("AsyncQueue is closed")}}class Tr extends km{write(t){if((t=Z(t)).byteLength>0)return super.write(t)}toString(t=!1){return t?So(this.toUint8Array(!0)):this.toUint8Array(!1).then(So)}toUint8Array(t=!1){return t?Re(this._values)[0]:D(this,void 0,void 0,function*(){var n,i;const r=[];let s=0;try{for(var o=Un(this),a;a=yield o.next(),!a.done;){const c=a.value;r.push(c),s+=c.byteLength}}catch(c){n={error:c}}finally{try{a&&!a.done&&(i=o.return)&&(yield i.call(o))}finally{if(n)throw n.error}}return Re(r,s)[0]})}}class rs{constructor(t){t&&(this.source=new Wm(ee.fromIterable(t)))}[Symbol.iterator](){return this}next(t){return this.source.next(t)}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}peek(t){return this.source.peek(t)}read(t){return this.source.read(t)}}class mi{constructor(t){t instanceof mi?this.source=t.source:t instanceof Tr?this.source=new An(ee.fromAsyncIterable(t)):hu(t)?this.source=new An(ee.fromNodeStream(t)):da(t)?this.source=new An(ee.fromDOMStream(t)):du(t)?this.source=new An(ee.fromDOMStream(t.body)):ir(t)?this.source=new An(ee.fromIterable(t)):dn(t)?this.source=new An(ee.fromAsyncIterable(t)):Ii(t)&&(this.source=new An(ee.fromAsyncIterable(t)))}[Symbol.asyncIterator](){return this}next(t){return this.source.next(t)}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}get closed(){return this.source.closed}cancel(t){return this.source.cancel(t)}peek(t){return this.source.peek(t)}read(t){return this.source.read(t)}}class Wm{constructor(t){this.source=t}cancel(t){this.return(t)}peek(t){return this.next(t,"peek").value}read(t){return this.next(t,"read").value}next(t,n="read"){return this.source.next({cmd:n,size:t})}throw(t){return Object.create(this.source.throw&&this.source.throw(t)||yt)}return(t){return Object.create(this.source.return&&this.source.return(t)||yt)}}class An{constructor(t){this.source=t,this._closedPromise=new Promise(n=>this._closedPromiseResolve=n)}cancel(t){return D(this,void 0,void 0,function*(){yield this.return(t)})}get closed(){return this._closedPromise}read(t){return D(this,void 0,void 0,function*(){return(yield this.next(t,"read")).value})}peek(t){return D(this,void 0,void 0,function*(){return(yield this.next(t,"peek")).value})}next(t,n="read"){return D(this,void 0,void 0,function*(){return yield this.source.next({cmd:n,size:t})})}throw(t){return D(this,void 0,void 0,function*(){const n=this.source.throw&&(yield this.source.throw(t))||yt;return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(n)})}return(t){return D(this,void 0,void 0,function*(){const n=this.source.return&&(yield this.source.return(t))||yt;return this._closedPromiseResolve&&this._closedPromiseResolve(),this._closedPromiseResolve=void 0,Object.create(n)})}}class Pc extends rs{constructor(t,n){super(),this.position=0,this.buffer=Z(t),this.size=typeof n>"u"?this.buffer.byteLength:n}readInt32(t){const{buffer:n,byteOffset:i}=this.readAt(t,4);return new DataView(n,i).getInt32(0,!0)}seek(t){return this.position=Math.min(t,this.size),t<this.size}read(t){const{buffer:n,size:i,position:r}=this;return n&&r<i?(typeof t!="number"&&(t=Number.POSITIVE_INFINITY),this.position=Math.min(i,r+Math.min(i-r,t)),n.subarray(r,this.position)):null}readAt(t,n){const i=this.buffer,r=Math.min(this.size,t+n);return i?i.subarray(t,r):new Uint8Array(n)}close(){this.buffer&&(this.buffer=null)}throw(t){return this.close(),{done:!0,value:t}}return(t){return this.close(),{done:!0,value:t}}}class ss extends mi{constructor(t,n){super(),this.position=0,this._handle=t,typeof n=="number"?this.size=n:this._pending=D(this,void 0,void 0,function*(){this.size=(yield t.stat()).size,delete this._pending})}readInt32(t){return D(this,void 0,void 0,function*(){const{buffer:n,byteOffset:i}=yield this.readAt(t,4);return new DataView(n,i).getInt32(0,!0)})}seek(t){return D(this,void 0,void 0,function*(){return this._pending&&(yield this._pending),this.position=Math.min(t,this.size),t<this.size})}read(t){return D(this,void 0,void 0,function*(){this._pending&&(yield this._pending);const{_handle:n,size:i,position:r}=this;if(n&&r<i){typeof t!="number"&&(t=Number.POSITIVE_INFINITY);let s=r,o=0,a=0;const c=Math.min(i,s+Math.min(i-s,t)),l=new Uint8Array(Math.max(0,(this.position=c)-s));for(;(s+=a)<c&&(o+=a)<l.byteLength;)({bytesRead:a}=yield n.read(l,o,l.byteLength-o,s));return l}return null})}readAt(t,n){return D(this,void 0,void 0,function*(){this._pending&&(yield this._pending);const{_handle:i,size:r}=this;if(i&&t+n<r){const s=Math.min(r,t+n),o=new Uint8Array(s-t);return(yield i.read(o,0,n,t)).buffer}return new Uint8Array(n)})}close(){return D(this,void 0,void 0,function*(){const t=this._handle;this._handle=null,t&&(yield t.close())})}throw(t){return D(this,void 0,void 0,function*(){return yield this.close(),{done:!0,value:t}})}return(t){return D(this,void 0,void 0,function*(){return yield this.close(),{done:!0,value:t}})}}const Hm=65536;function Qn(e){return e<0&&(e=4294967295+e+1),`0x${e.toString(16)}`}const gi=8,Fa=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8];class Ef{constructor(t){this.buffer=t}high(){return this.buffer[1]}low(){return this.buffer[0]}_times(t){const n=new Uint32Array([this.buffer[1]>>>16,this.buffer[1]&65535,this.buffer[0]>>>16,this.buffer[0]&65535]),i=new Uint32Array([t.buffer[1]>>>16,t.buffer[1]&65535,t.buffer[0]>>>16,t.buffer[0]&65535]);let r=n[3]*i[3];this.buffer[0]=r&65535;let s=r>>>16;return r=n[2]*i[3],s+=r,r=n[3]*i[2]>>>0,s+=r,this.buffer[0]+=s<<16,this.buffer[1]=s>>>0<r?Hm:0,this.buffer[1]+=s>>>16,this.buffer[1]+=n[1]*i[3]+n[2]*i[2]+n[3]*i[1],this.buffer[1]+=n[0]*i[3]+n[1]*i[2]+n[2]*i[1]+n[3]*i[0]<<16,this}_plus(t){const n=this.buffer[0]+t.buffer[0]>>>0;this.buffer[1]+=t.buffer[1],n<this.buffer[0]>>>0&&++this.buffer[1],this.buffer[0]=n}lessThan(t){return this.buffer[1]<t.buffer[1]||this.buffer[1]===t.buffer[1]&&this.buffer[0]<t.buffer[0]}equals(t){return this.buffer[1]===t.buffer[1]&&this.buffer[0]==t.buffer[0]}greaterThan(t){return t.lessThan(this)}hex(){return`${Qn(this.buffer[1])} ${Qn(this.buffer[0])}`}}class nt extends Ef{times(t){return this._times(t),this}plus(t){return this._plus(t),this}static from(t,n=new Uint32Array(2)){return nt.fromString(typeof t=="string"?t:t.toString(),n)}static fromNumber(t,n=new Uint32Array(2)){return nt.fromString(t.toString(),n)}static fromString(t,n=new Uint32Array(2)){const i=t.length,r=new nt(n);for(let s=0;s<i;){const o=gi<i-s?gi:i-s,a=new nt(new Uint32Array([Number.parseInt(t.slice(s,s+o),10),0])),c=new nt(new Uint32Array([Fa[o],0]));r.times(c),r.plus(a),s+=o}return r}static convertArray(t){const n=new Uint32Array(t.length*2);for(let i=-1,r=t.length;++i<r;)nt.from(t[i],new Uint32Array(n.buffer,n.byteOffset+2*i*4,2));return n}static multiply(t,n){return new nt(new Uint32Array(t.buffer)).times(n)}static add(t,n){return new nt(new Uint32Array(t.buffer)).plus(n)}}class Ut extends Ef{negate(){return this.buffer[0]=~this.buffer[0]+1,this.buffer[1]=~this.buffer[1],this.buffer[0]==0&&++this.buffer[1],this}times(t){return this._times(t),this}plus(t){return this._plus(t),this}lessThan(t){const n=this.buffer[1]<<0,i=t.buffer[1]<<0;return n<i||n===i&&this.buffer[0]<t.buffer[0]}static from(t,n=new Uint32Array(2)){return Ut.fromString(typeof t=="string"?t:t.toString(),n)}static fromNumber(t,n=new Uint32Array(2)){return Ut.fromString(t.toString(),n)}static fromString(t,n=new Uint32Array(2)){const i=t.startsWith("-"),r=t.length,s=new Ut(n);for(let o=i?1:0;o<r;){const a=gi<r-o?gi:r-o,c=new Ut(new Uint32Array([Number.parseInt(t.slice(o,o+a),10),0])),l=new Ut(new Uint32Array([Fa[a],0]));s.times(l),s.plus(c),o+=a}return i?s.negate():s}static convertArray(t){const n=new Uint32Array(t.length*2);for(let i=-1,r=t.length;++i<r;)Ut.from(t[i],new Uint32Array(n.buffer,n.byteOffset+2*i*4,2));return n}static multiply(t,n){return new Ut(new Uint32Array(t.buffer)).times(n)}static add(t,n){return new Ut(new Uint32Array(t.buffer)).plus(n)}}class Se{constructor(t){this.buffer=t}high(){return new Ut(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2))}low(){return new Ut(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset,2))}negate(){return this.buffer[0]=~this.buffer[0]+1,this.buffer[1]=~this.buffer[1],this.buffer[2]=~this.buffer[2],this.buffer[3]=~this.buffer[3],this.buffer[0]==0&&++this.buffer[1],this.buffer[1]==0&&++this.buffer[2],this.buffer[2]==0&&++this.buffer[3],this}times(t){const n=new nt(new Uint32Array([this.buffer[3],0])),i=new nt(new Uint32Array([this.buffer[2],0])),r=new nt(new Uint32Array([this.buffer[1],0])),s=new nt(new Uint32Array([this.buffer[0],0])),o=new nt(new Uint32Array([t.buffer[3],0])),a=new nt(new Uint32Array([t.buffer[2],0])),c=new nt(new Uint32Array([t.buffer[1],0])),l=new nt(new Uint32Array([t.buffer[0],0]));let f=nt.multiply(s,l);this.buffer[0]=f.low();const p=new nt(new Uint32Array([f.high(),0]));return f=nt.multiply(r,l),p.plus(f),f=nt.multiply(s,c),p.plus(f),this.buffer[1]=p.low(),this.buffer[3]=p.lessThan(f)?1:0,this.buffer[2]=p.high(),new nt(new Uint32Array(this.buffer.buffer,this.buffer.byteOffset+8,2)).plus(nt.multiply(i,l)).plus(nt.multiply(r,c)).plus(nt.multiply(s,a)),this.buffer[3]+=nt.multiply(n,l).plus(nt.multiply(i,c)).plus(nt.multiply(r,a)).plus(nt.multiply(s,o)).low(),this}plus(t){const n=new Uint32Array(4);return n[3]=this.buffer[3]+t.buffer[3]>>>0,n[2]=this.buffer[2]+t.buffer[2]>>>0,n[1]=this.buffer[1]+t.buffer[1]>>>0,n[0]=this.buffer[0]+t.buffer[0]>>>0,n[0]<this.buffer[0]>>>0&&++n[1],n[1]<this.buffer[1]>>>0&&++n[2],n[2]<this.buffer[2]>>>0&&++n[3],this.buffer[3]=n[3],this.buffer[2]=n[2],this.buffer[1]=n[1],this.buffer[0]=n[0],this}hex(){return`${Qn(this.buffer[3])} ${Qn(this.buffer[2])} ${Qn(this.buffer[1])} ${Qn(this.buffer[0])}`}static multiply(t,n){return new Se(new Uint32Array(t.buffer)).times(n)}static add(t,n){return new Se(new Uint32Array(t.buffer)).plus(n)}static from(t,n=new Uint32Array(4)){return Se.fromString(typeof t=="string"?t:t.toString(),n)}static fromNumber(t,n=new Uint32Array(4)){return Se.fromString(t.toString(),n)}static fromString(t,n=new Uint32Array(4)){const i=t.startsWith("-"),r=t.length,s=new Se(n);for(let o=i?1:0;o<r;){const a=gi<r-o?gi:r-o,c=new Se(new Uint32Array([Number.parseInt(t.slice(o,o+a),10),0,0,0])),l=new Se(new Uint32Array([Fa[a],0,0,0]));s.times(l),s.plus(c),o+=a}return i?s.negate():s}static convertArray(t){const n=new Uint32Array(t.length*4);for(let i=-1,r=t.length;++i<r;)Se.from(t[i],new Uint32Array(n.buffer,n.byteOffset+4*4*i,4));return n}}class Cf extends J{constructor(t,n,i,r){super(),this.nodesIndex=-1,this.buffersIndex=-1,this.bytes=t,this.nodes=n,this.buffers=i,this.dictionaries=r}visit(t){return super.visit(t instanceof ft?t.type:t)}visitNull(t,{length:n}=this.nextFieldNode()){return K({type:t,length:n})}visitBool(t,{length:n,nullCount:i}=this.nextFieldNode()){return K({type:t,length:n,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitInt(t,{length:n,nullCount:i}=this.nextFieldNode()){return K({type:t,length:n,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitFloat(t,{length:n,nullCount:i}=this.nextFieldNode()){return K({type:t,length:n,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitUtf8(t,{length:n,nullCount:i}=this.nextFieldNode()){return K({type:t,length:n,nullCount:i,nullBitmap:this.readNullBitmap(t,i),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitBinary(t,{length:n,nullCount:i}=this.nextFieldNode()){return K({type:t,length:n,nullCount:i,nullBitmap:this.readNullBitmap(t,i),valueOffsets:this.readOffsets(t),data:this.readData(t)})}visitFixedSizeBinary(t,{length:n,nullCount:i}=this.nextFieldNode()){return K({type:t,length:n,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitDate(t,{length:n,nullCount:i}=this.nextFieldNode()){return K({type:t,length:n,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitTimestamp(t,{length:n,nullCount:i}=this.nextFieldNode()){return K({type:t,length:n,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitTime(t,{length:n,nullCount:i}=this.nextFieldNode()){return K({type:t,length:n,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitDecimal(t,{length:n,nullCount:i}=this.nextFieldNode()){return K({type:t,length:n,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitList(t,{length:n,nullCount:i}=this.nextFieldNode()){return K({type:t,length:n,nullCount:i,nullBitmap:this.readNullBitmap(t,i),valueOffsets:this.readOffsets(t),child:this.visit(t.children[0])})}visitStruct(t,{length:n,nullCount:i}=this.nextFieldNode()){return K({type:t,length:n,nullCount:i,nullBitmap:this.readNullBitmap(t,i),children:this.visitMany(t.children)})}visitUnion(t){return t.mode===Xt.Sparse?this.visitSparseUnion(t):this.visitDenseUnion(t)}visitDenseUnion(t,{length:n,nullCount:i}=this.nextFieldNode()){return K({type:t,length:n,nullCount:i,nullBitmap:this.readNullBitmap(t,i),typeIds:this.readTypeIds(t),valueOffsets:this.readOffsets(t),children:this.visitMany(t.children)})}visitSparseUnion(t,{length:n,nullCount:i}=this.nextFieldNode()){return K({type:t,length:n,nullCount:i,nullBitmap:this.readNullBitmap(t,i),typeIds:this.readTypeIds(t),children:this.visitMany(t.children)})}visitDictionary(t,{length:n,nullCount:i}=this.nextFieldNode()){return K({type:t,length:n,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t.indices),dictionary:this.readDictionary(t)})}visitInterval(t,{length:n,nullCount:i}=this.nextFieldNode()){return K({type:t,length:n,nullCount:i,nullBitmap:this.readNullBitmap(t,i),data:this.readData(t)})}visitFixedSizeList(t,{length:n,nullCount:i}=this.nextFieldNode()){return K({type:t,length:n,nullCount:i,nullBitmap:this.readNullBitmap(t,i),child:this.visit(t.children[0])})}visitMap(t,{length:n,nullCount:i}=this.nextFieldNode()){return K({type:t,length:n,nullCount:i,nullBitmap:this.readNullBitmap(t,i),valueOffsets:this.readOffsets(t),child:this.visit(t.children[0])})}nextFieldNode(){return this.nodes[++this.nodesIndex]}nextBufferRange(){return this.buffers[++this.buffersIndex]}readNullBitmap(t,n,i=this.nextBufferRange()){return n>0&&this.readData(t,i)||new Uint8Array(0)}readOffsets(t,n){return this.readData(t,n)}readTypeIds(t,n){return this.readData(t,n)}readData(t,{length:n,offset:i}=this.nextBufferRange()){return this.bytes.subarray(i,i+n)}readDictionary(t){return this.dictionaries.get(t.id)}}class Ym extends Cf{constructor(t,n,i,r){super(new Uint8Array(0),n,i,r),this.sources=t}readNullBitmap(t,n,{offset:i}=this.nextBufferRange()){return n<=0?new Uint8Array(0):Zr(this.sources[i])}readOffsets(t,{offset:n}=this.nextBufferRange()){return ot(Uint8Array,ot(Int32Array,this.sources[n]))}readTypeIds(t,{offset:n}=this.nextBufferRange()){return ot(Uint8Array,ot(t.ArrayType,this.sources[n]))}readData(t,{offset:n}=this.nextBufferRange()){const{sources:i}=this;return T.isTimestamp(t)||(T.isInt(t)||T.isTime(t))&&t.bitWidth===64||T.isDate(t)&&t.unit===Ze.MILLISECOND?ot(Uint8Array,Ut.convertArray(i[n])):T.isDecimal(t)?ot(Uint8Array,Se.convertArray(i[n])):T.isBinary(t)||T.isFixedSizeBinary(t)?Km(i[n]):T.isBool(t)?Zr(i[n]):T.isUtf8(t)?fa(i[n].join("")):ot(Uint8Array,ot(t.ArrayType,i[n].map(r=>+r)))}}function Km(e){const t=e.join(""),n=new Uint8Array(t.length/2);for(let i=0;i<t.length;i+=2)n[i>>1]=Number.parseInt(t.slice(i,i+2),16);return n}class E extends J{compareSchemas(t,n){return t===n||n instanceof t.constructor&&this.compareManyFields(t.fields,n.fields)}compareManyFields(t,n){return t===n||Array.isArray(t)&&Array.isArray(n)&&t.length===n.length&&t.every((i,r)=>this.compareFields(i,n[r]))}compareFields(t,n){return t===n||n instanceof t.constructor&&t.name===n.name&&t.nullable===n.nullable&&this.visit(t.type,n.type)}}function Yt(e,t){return t instanceof e.constructor}function rr(e,t){return e===t||Yt(e,t)}function tn(e,t){return e===t||Yt(e,t)&&e.bitWidth===t.bitWidth&&e.isSigned===t.isSigned}function Us(e,t){return e===t||Yt(e,t)&&e.precision===t.precision}function Jm(e,t){return e===t||Yt(e,t)&&e.byteWidth===t.byteWidth}function Da(e,t){return e===t||Yt(e,t)&&e.unit===t.unit}function sr(e,t){return e===t||Yt(e,t)&&e.unit===t.unit&&e.timezone===t.timezone}function or(e,t){return e===t||Yt(e,t)&&e.unit===t.unit&&e.bitWidth===t.bitWidth}function qm(e,t){return e===t||Yt(e,t)&&e.children.length===t.children.length&&gn.compareManyFields(e.children,t.children)}function Zm(e,t){return e===t||Yt(e,t)&&e.children.length===t.children.length&&gn.compareManyFields(e.children,t.children)}function Ta(e,t){return e===t||Yt(e,t)&&e.mode===t.mode&&e.typeIds.every((n,i)=>n===t.typeIds[i])&&gn.compareManyFields(e.children,t.children)}function Xm(e,t){return e===t||Yt(e,t)&&e.id===t.id&&e.isOrdered===t.isOrdered&&gn.visit(e.indices,t.indices)&&gn.visit(e.dictionary,t.dictionary)}function xa(e,t){return e===t||Yt(e,t)&&e.unit===t.unit}function Qm(e,t){return e===t||Yt(e,t)&&e.listSize===t.listSize&&e.children.length===t.children.length&&gn.compareManyFields(e.children,t.children)}function Gm(e,t){return e===t||Yt(e,t)&&e.keysSorted===t.keysSorted&&e.children.length===t.children.length&&gn.compareManyFields(e.children,t.children)}E.prototype.visitNull=rr;E.prototype.visitBool=rr;E.prototype.visitInt=tn;E.prototype.visitInt8=tn;E.prototype.visitInt16=tn;E.prototype.visitInt32=tn;E.prototype.visitInt64=tn;E.prototype.visitUint8=tn;E.prototype.visitUint16=tn;E.prototype.visitUint32=tn;E.prototype.visitUint64=tn;E.prototype.visitFloat=Us;E.prototype.visitFloat16=Us;E.prototype.visitFloat32=Us;E.prototype.visitFloat64=Us;E.prototype.visitUtf8=rr;E.prototype.visitBinary=rr;E.prototype.visitFixedSizeBinary=Jm;E.prototype.visitDate=Da;E.prototype.visitDateDay=Da;E.prototype.visitDateMillisecond=Da;E.prototype.visitTimestamp=sr;E.prototype.visitTimestampSecond=sr;E.prototype.visitTimestampMillisecond=sr;E.prototype.visitTimestampMicrosecond=sr;E.prototype.visitTimestampNanosecond=sr;E.prototype.visitTime=or;E.prototype.visitTimeSecond=or;E.prototype.visitTimeMillisecond=or;E.prototype.visitTimeMicrosecond=or;E.prototype.visitTimeNanosecond=or;E.prototype.visitDecimal=rr;E.prototype.visitList=qm;E.prototype.visitStruct=Zm;E.prototype.visitUnion=Ta;E.prototype.visitDenseUnion=Ta;E.prototype.visitSparseUnion=Ta;E.prototype.visitDictionary=Xm;E.prototype.visitInterval=xa;E.prototype.visitIntervalDayTime=xa;E.prototype.visitIntervalYearMonth=xa;E.prototype.visitFixedSizeList=Qm;E.prototype.visitMap=Gm;const gn=new E;function Mo(e,t){return gn.compareSchemas(e,t)}function no(e,t){return tg(e,t.map(n=>n.data.concat()))}function tg(e,t){const n=[...e.fields],i=[],r={numBatches:t.reduce((p,m)=>Math.max(p,m.length),0)};let s=0,o=0,a=-1;const c=t.length;let l,f=[];for(;r.numBatches-- >0;){for(o=Number.POSITIVE_INFINITY,a=-1;++a<c;)f[a]=l=t[a].shift(),o=Math.min(o,l?l.length:o);Number.isFinite(o)&&(f=eg(n,o,f,t,r),o>0&&(i[s++]=K({type:new Et(n),length:o,nullCount:0,children:f.slice()})))}return[e=e.assign(n),i.map(p=>new $t(e,p))]}function eg(e,t,n,i,r){var s;const o=(t+63&-64)>>3;for(let a=-1,c=i.length;++a<c;){const l=n[a],f=l==null?void 0:l.length;if(f>=t)f===t?n[a]=l:(n[a]=l.slice(0,t),r.numBatches=Math.max(r.numBatches,i[a].unshift(l.slice(t,f-t))));else{const p=e[a];e[a]=p.clone({nullable:!0}),n[a]=(s=l==null?void 0:l._changeLengthAndBackfillNullBitmap(t))!==null&&s!==void 0?s:K({type:p.type,length:t,nullCount:t,nullBitmap:new Uint8Array(o)})}}return n}var Rf;class Nt{constructor(...t){var n,i;if(t.length===0)return this.batches=[],this.schema=new et([]),this._offsets=[0],this;let r,s;t[0]instanceof et&&(r=t.shift()),t[t.length-1]instanceof Uint32Array&&(s=t.pop());const o=c=>{if(c){if(c instanceof $t)return[c];if(c instanceof Nt)return c.batches;if(c instanceof ut){if(c.type instanceof Et)return[new $t(new et(c.type.children),c)]}else{if(Array.isArray(c))return c.flatMap(l=>o(l));if(typeof c[Symbol.iterator]=="function")return[...c].flatMap(l=>o(l));if(typeof c=="object"){const l=Object.keys(c),f=l.map(I=>new tt([c[I]])),p=new et(l.map((I,k)=>new ft(String(I),f[k].type))),[,m]=no(p,f);return m.length===0?[new $t(c)]:m}}}return[]},a=t.flatMap(c=>o(c));if(r=(i=r??((n=a[0])===null||n===void 0?void 0:n.schema))!==null&&i!==void 0?i:new et([]),!(r instanceof et))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");for(const c of a){if(!(c instanceof $t))throw new TypeError("Table constructor expects a [Schema, RecordBatch[]] pair.");if(!Mo(r,c.schema))throw new TypeError("Table and inner RecordBatch schemas must be equivalent.")}this.schema=r,this.batches=a,this._offsets=s??vf(this.data)}get data(){return this.batches.map(({data:t})=>t)}get numCols(){return this.schema.fields.length}get numRows(){return this.data.reduce((t,n)=>t+n.length,0)}get nullCount(){return this._nullCount===-1&&(this._nullCount=_f(this.data)),this._nullCount}isValid(t){return!1}get(t){return null}set(t,n){}indexOf(t,n){return-1}getByteLength(t){return 0}[Symbol.iterator](){return this.batches.length>0?Oa.visit(new tt(this.data)):new Array(0)[Symbol.iterator]()}toArray(){return[...this]}toString(){return`[
  ${this.toArray().join(`,
  `)}
]`}concat(...t){const n=this.schema,i=this.data.concat(t.flatMap(({data:r})=>r));return new Nt(n,i.map(r=>new $t(n,r)))}slice(t,n){const i=this.schema;[t,n]=gf({length:this.numRows},t,n);const r=wf(this.data,this._offsets,t,n);return new Nt(i,r.map(s=>new $t(i,s)))}getChild(t){return this.getChildAt(this.schema.fields.findIndex(n=>n.name===t))}getChildAt(t){if(t>-1&&t<this.schema.fields.length){const n=this.data.map(i=>i.children[t]);if(n.length===0){const{type:i}=this.schema.fields[t],r=K({type:i,length:0,nullCount:0});n.push(r._changeLengthAndBackfillNullBitmap(this.numRows))}return new tt(n)}return null}setChild(t,n){var i;return this.setChildAt((i=this.schema.fields)===null||i===void 0?void 0:i.findIndex(r=>r.name===t),n)}setChildAt(t,n){let i=this.schema,r=[...this.batches];if(t>-1&&t<this.numCols){n||(n=new tt([K({type:new pn,length:this.numRows})]));const s=i.fields.slice(),o=s[t].clone({type:n.type}),a=this.schema.fields.map((c,l)=>this.getChildAt(l));[s[t],a[t]]=[o,n],[i,r]=no(i,a)}return new Nt(i,r)}select(t){const n=this.schema.fields.reduce((i,r,s)=>i.set(r.name,s),new Map);return this.selectAt(t.map(i=>n.get(i)).filter(i=>i>-1))}selectAt(t){const n=this.schema.selectAt(t),i=this.batches.map(r=>r.selectAt(t));return new Nt(n,i)}assign(t){const n=this.schema.fields,[i,r]=t.schema.fields.reduce((a,c,l)=>{const[f,p]=a,m=n.findIndex(I=>I.name===c.name);return~m?p[m]=l:f.push(l),a},[[],[]]),s=this.schema.assign(t.schema),o=[...n.map((a,c)=>[c,r[c]]).map(([a,c])=>c===void 0?this.getChildAt(a):t.getChildAt(c)),...i.map(a=>t.getChildAt(a))].filter(Boolean);return new Nt(...no(s,o))}}Rf=Symbol.toStringTag;Nt[Rf]=(e=>(e.schema=null,e.batches=[],e._offsets=new Uint32Array([0]),e._nullCount=-1,e[Symbol.isConcatSpreadable]=!0,e.isValid=ai(Ba),e.get=ai(Ht.getVisitFn(h.Struct)),e.set=Sf(se.getVisitFn(h.Struct)),e.indexOf=If(Xr.getVisitFn(h.Struct)),e.getByteLength=ai(Le.getVisitFn(h.Struct)),"Table"))(Nt.prototype);var Lf;let $t=class Ni{constructor(...t){switch(t.length){case 2:{if([this.schema]=t,!(this.schema instanceof et))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");if([,this.data=K({nullCount:0,type:new Et(this.schema.fields),children:this.schema.fields.map(n=>K({type:n.type,nullCount:0}))})]=t,!(this.data instanceof ut))throw new TypeError("RecordBatch constructor expects a [Schema, Data] pair.");[this.schema,this.data]=jc(this.schema,this.data.children);break}case 1:{const[n]=t,{fields:i,children:r,length:s}=Object.keys(n).reduce((c,l,f)=>(c.children[f]=n[l],c.length=Math.max(c.length,n[l].length),c.fields[f]=ft.new({name:l,type:n[l].type,nullable:!0}),c),{length:0,fields:new Array,children:new Array}),o=new et(i),a=K({type:new Et(i),length:s,children:r,nullCount:0});[this.schema,this.data]=jc(o,a.children,s);break}default:throw new TypeError("RecordBatch constructor expects an Object mapping names to child Data, or a [Schema, Data] pair.")}}get dictionaries(){return this._dictionaries||(this._dictionaries=Uf(this.schema.fields,this.data.children))}get numCols(){return this.schema.fields.length}get numRows(){return this.data.length}get nullCount(){return this.data.nullCount}isValid(t){return this.data.getValid(t)}get(t){return Ht.visit(this.data,t)}set(t,n){return se.visit(this.data,t,n)}indexOf(t,n){return Xr.visit(this.data,t,n)}getByteLength(t){return Le.visit(this.data,t)}[Symbol.iterator](){return Oa.visit(new tt([this.data]))}toArray(){return[...this]}concat(...t){return new Nt(this.schema,[this,...t])}slice(t,n){const[i]=new tt([this.data]).slice(t,n).data;return new Ni(this.schema,i)}getChild(t){var n;return this.getChildAt((n=this.schema.fields)===null||n===void 0?void 0:n.findIndex(i=>i.name===t))}getChildAt(t){return t>-1&&t<this.schema.fields.length?new tt([this.data.children[t]]):null}setChild(t,n){var i;return this.setChildAt((i=this.schema.fields)===null||i===void 0?void 0:i.findIndex(r=>r.name===t),n)}setChildAt(t,n){let i=this.schema,r=this.data;if(t>-1&&t<this.numCols){n||(n=new tt([K({type:new pn,length:this.numRows})]));const s=i.fields.slice(),o=r.children.slice(),a=s[t].clone({type:n.type});[s[t],o[t]]=[a,n.data[0]],i=new et(s,new Map(this.schema.metadata)),r=K({type:new Et(s),children:o})}return new Ni(i,r)}select(t){const n=this.schema.select(t),i=new Et(n.fields),r=[];for(const s of t){const o=this.schema.fields.findIndex(a=>a.name===s);~o&&(r[o]=this.data.children[o])}return new Ni(n,K({type:i,length:this.numRows,children:r}))}selectAt(t){const n=this.schema.selectAt(t),i=t.map(s=>this.data.children[s]).filter(Boolean),r=K({type:new Et(n.fields),length:this.numRows,children:i});return new Ni(n,r)}};Lf=Symbol.toStringTag;$t[Lf]=(e=>(e._nullCount=-1,e[Symbol.isConcatSpreadable]=!0,"RecordBatch"))($t.prototype);function jc(e,t,n=t.reduce((i,r)=>Math.max(i,r.length),0)){var i;const r=[...e.fields],s=[...t],o=(n+63&-64)>>3;for(const[a,c]of e.fields.entries()){const l=t[a];(!l||l.length!==n)&&(r[a]=c.clone({nullable:!0}),s[a]=(i=l==null?void 0:l._changeLengthAndBackfillNullBitmap(n))!==null&&i!==void 0?i:K({type:c.type,length:n,nullCount:n,nullBitmap:new Uint8Array(o)}))}return[e.assign(r),K({type:new Et(r),length:n,children:s})]}function Uf(e,t,n=new Map){for(let i=-1,r=e.length;++i<r;){const o=e[i].type,a=t[i];if(T.isDictionary(o)){if(!n.has(o.id))a.dictionary&&n.set(o.id,a.dictionary);else if(n.get(o.id)!==a.dictionary)throw new Error("Cannot create Schema containing two different dictionaries with the same Id")}o.children&&o.children.length>0&&Uf(o.children,a.children,n)}return n}class Ma extends $t{constructor(t){const n=t.fields.map(r=>K({type:r.type})),i=K({type:new Et(t.fields),nullCount:0,children:n});super(t,i)}}var os;(function(e){e[e.BUFFER=0]="BUFFER"})(os||(os={}));var as;(function(e){e[e.LZ4_FRAME=0]="LZ4_FRAME",e[e.ZSTD=1]="ZSTD"})(as||(as={}));class an{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsBodyCompression(t,n){return(n||new an).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsBodyCompression(t,n){return t.setPosition(t.position()+at),(n||new an).__init(t.readInt32(t.position())+t.position(),t)}codec(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt8(this.bb_pos+t):as.LZ4_FRAME}method(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readInt8(this.bb_pos+t):os.BUFFER}static startBodyCompression(t){t.startObject(2)}static addCodec(t,n){t.addFieldInt8(0,n,as.LZ4_FRAME)}static addMethod(t,n){t.addFieldInt8(1,n,os.BUFFER)}static endBodyCompression(t){return t.endObject()}static createBodyCompression(t,n,i){return an.startBodyCompression(t),an.addCodec(t,n),an.addMethod(t,i),an.endBodyCompression(t)}}class Pf{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}offset(){return this.bb.readInt64(this.bb_pos)}length(){return this.bb.readInt64(this.bb_pos+8)}static sizeOf(){return 16}static createBuffer(t,n,i){return t.prep(8,16),t.writeInt64(i),t.writeInt64(n),t.offset()}}let jf=class{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}length(){return this.bb.readInt64(this.bb_pos)}nullCount(){return this.bb.readInt64(this.bb_pos+8)}static sizeOf(){return 16}static createFieldNode(t,n,i){return t.prep(8,16),t.writeInt64(i),t.writeInt64(n),t.offset()}},ke=class No{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsRecordBatch(t,n){return(n||new No).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsRecordBatch(t,n){return t.setPosition(t.position()+at),(n||new No).__init(t.readInt32(t.position())+t.position(),t)}length(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):this.bb.createLong(0,0)}nodes(t,n){const i=this.bb.__offset(this.bb_pos,6);return i?(n||new jf).__init(this.bb.__vector(this.bb_pos+i)+t*16,this.bb):null}nodesLength(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.__vector_len(this.bb_pos+t):0}buffers(t,n){const i=this.bb.__offset(this.bb_pos,8);return i?(n||new Pf).__init(this.bb.__vector(this.bb_pos+i)+t*16,this.bb):null}buffersLength(){const t=this.bb.__offset(this.bb_pos,8);return t?this.bb.__vector_len(this.bb_pos+t):0}compression(t){const n=this.bb.__offset(this.bb_pos,10);return n?(t||new an).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}static startRecordBatch(t){t.startObject(4)}static addLength(t,n){t.addFieldInt64(0,n,t.createLong(0,0))}static addNodes(t,n){t.addFieldOffset(1,n,0)}static startNodesVector(t,n){t.startVector(16,n,8)}static addBuffers(t,n){t.addFieldOffset(2,n,0)}static startBuffersVector(t,n){t.startVector(16,n,8)}static addCompression(t,n){t.addFieldOffset(3,n,0)}static endRecordBatch(t){return t.endObject()}},Zn=class Eo{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsDictionaryBatch(t,n){return(n||new Eo).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsDictionaryBatch(t,n){return t.setPosition(t.position()+at),(n||new Eo).__init(t.readInt32(t.position())+t.position(),t)}id(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt64(this.bb_pos+t):this.bb.createLong(0,0)}data(t){const n=this.bb.__offset(this.bb_pos,6);return n?(t||new ke).__init(this.bb.__indirect(this.bb_pos+n),this.bb):null}isDelta(){const t=this.bb.__offset(this.bb_pos,8);return t?!!this.bb.readInt8(this.bb_pos+t):!1}static startDictionaryBatch(t){t.startObject(3)}static addId(t,n){t.addFieldInt64(0,n,t.createLong(0,0))}static addData(t,n){t.addFieldOffset(1,n,0)}static addIsDelta(t,n){t.addFieldInt8(2,+n,0)}static endDictionaryBatch(t){return t.endObject()}};var cs;(function(e){e[e.NONE=0]="NONE",e[e.Schema=1]="Schema",e[e.DictionaryBatch=2]="DictionaryBatch",e[e.RecordBatch=3]="RecordBatch",e[e.Tensor=4]="Tensor",e[e.SparseTensor=5]="SparseTensor"})(cs||(cs={}));let rn=class ve{constructor(){this.bb=null,this.bb_pos=0}__init(t,n){return this.bb_pos=t,this.bb=n,this}static getRootAsMessage(t,n){return(n||new ve).__init(t.readInt32(t.position())+t.position(),t)}static getSizePrefixedRootAsMessage(t,n){return t.setPosition(t.position()+at),(n||new ve).__init(t.readInt32(t.position())+t.position(),t)}version(){const t=this.bb.__offset(this.bb_pos,4);return t?this.bb.readInt16(this.bb_pos+t):hi.V1}headerType(){const t=this.bb.__offset(this.bb_pos,6);return t?this.bb.readUint8(this.bb_pos+t):cs.NONE}header(t){const n=this.bb.__offset(this.bb_pos,8);return n?this.bb.__union(t,this.bb_pos+n):null}bodyLength(){const t=this.bb.__offset(this.bb_pos,10);return t?this.bb.readInt64(this.bb_pos+t):this.bb.createLong(0,0)}customMetadata(t,n){const i=this.bb.__offset(this.bb_pos,12);return i?(n||new It).__init(this.bb.__indirect(this.bb.__vector(this.bb_pos+i)+t*4),this.bb):null}customMetadataLength(){const t=this.bb.__offset(this.bb_pos,12);return t?this.bb.__vector_len(this.bb_pos+t):0}static startMessage(t){t.startObject(5)}static addVersion(t,n){t.addFieldInt16(0,n,hi.V1)}static addHeaderType(t,n){t.addFieldInt8(1,n,cs.NONE)}static addHeader(t,n){t.addFieldOffset(2,n,0)}static addBodyLength(t,n){t.addFieldInt64(3,n,t.createLong(0,0))}static addCustomMetadata(t,n){t.addFieldOffset(4,n,0)}static createCustomMetadataVector(t,n){t.startVector(4,n.length,4);for(let i=n.length-1;i>=0;i--)t.addOffset(n[i]);return t.endVector()}static startCustomMetadataVector(t,n){t.startVector(4,n,4)}static endMessage(t){return t.endObject()}static finishMessageBuffer(t,n){t.finish(n)}static finishSizePrefixedMessageBuffer(t,n){t.finish(n,void 0,!0)}static createMessage(t,n,i,r,s,o){return ve.startMessage(t),ve.addVersion(t,n),ve.addHeaderType(t,i),ve.addHeader(t,r),ve.addBodyLength(t,s),ve.addCustomMetadata(t,o),ve.endMessage(t)}};var ng=Je;class ig extends J{visit(t,n){return t==null||n==null?void 0:super.visit(t,n)}visitNull(t,n){return Nn.startNull(n),Nn.endNull(n)}visitInt(t,n){return qt.startInt(n),qt.addBitWidth(n,t.bitWidth),qt.addIsSigned(n,t.isSigned),qt.endInt(n)}visitFloat(t,n){return Te.startFloatingPoint(n),Te.addPrecision(n,t.precision),Te.endFloatingPoint(n)}visitBinary(t,n){return Tn.startBinary(n),Tn.endBinary(n)}visitBool(t,n){return xn.startBool(n),xn.endBool(n)}visitUtf8(t,n){return Cn.startUtf8(n),Cn.endUtf8(n)}visitDecimal(t,n){return Pt.startDecimal(n),Pt.addScale(n,t.scale),Pt.addPrecision(n,t.precision),Pt.addBitWidth(n,t.bitWidth),Pt.endDecimal(n)}visitDate(t,n){return Ar.startDate(n),Ar.addUnit(n,t.unit),Ar.endDate(n)}visitTime(t,n){return ne.startTime(n),ne.addUnit(n,t.unit),ne.addBitWidth(n,t.bitWidth),ne.endTime(n)}visitTimestamp(t,n){const i=t.timezone&&n.createString(t.timezone)||void 0;return ie.startTimestamp(n),ie.addUnit(n,t.unit),i!==void 0&&ie.addTimezone(n,i),ie.endTimestamp(n)}visitInterval(t,n){return xe.startInterval(n),xe.addUnit(n,t.unit),xe.endInterval(n)}visitList(t,n){return Mn.startList(n),Mn.endList(n)}visitStruct(t,n){return En.startStruct_(n),En.endStruct_(n)}visitUnion(t,n){jt.startTypeIdsVector(n,t.typeIds.length);const i=jt.createTypeIdsVector(n,t.typeIds);return jt.startUnion(n),jt.addMode(n,t.mode),jt.addTypeIds(n,i),jt.endUnion(n)}visitDictionary(t,n){const i=this.visit(t.indices,n);return Ye.startDictionaryEncoding(n),Ye.addId(n,new ng(t.id,0)),Ye.addIsOrdered(n,t.isOrdered),i!==void 0&&Ye.addIndexType(n,i),Ye.endDictionaryEncoding(n)}visitFixedSizeBinary(t,n){return Fe.startFixedSizeBinary(n),Fe.addByteWidth(n,t.byteWidth),Fe.endFixedSizeBinary(n)}visitFixedSizeList(t,n){return De.startFixedSizeList(n),De.addListSize(n,t.listSize),De.endFixedSizeList(n)}visitMap(t,n){return Fr.startMap(n),Fr.addKeysSorted(n,t.keysSorted),Fr.endMap(n)}}const io=new ig;function rg(e,t=new Map){return new et(og(e,t),xr(e.customMetadata),t)}function $f(e){return new Qt(e.count,Vf(e.columns),zf(e.columns))}function sg(e){return new Ue($f(e.data),e.id,e.isDelta)}function og(e,t){return(e.fields||[]).filter(Boolean).map(n=>ft.fromJSON(n,t))}function $c(e,t){return(e.children||[]).filter(Boolean).map(n=>ft.fromJSON(n,t))}function Vf(e){return(e||[]).reduce((t,n)=>[...t,new Vn(n.count,ag(n.VALIDITY)),...Vf(n.children)],[])}function zf(e,t=[]){for(let n=-1,i=(e||[]).length;++n<i;){const r=e[n];r.VALIDITY&&t.push(new Ne(t.length,r.VALIDITY.length)),r.TYPE&&t.push(new Ne(t.length,r.TYPE.length)),r.OFFSET&&t.push(new Ne(t.length,r.OFFSET.length)),r.DATA&&t.push(new Ne(t.length,r.DATA.length)),t=zf(r.children,t)}return t}function ag(e){return(e||[]).reduce((t,n)=>t+ +(n===0),0)}function cg(e,t){let n,i,r,s,o,a;return!t||!(s=e.dictionary)?(o=zc(e,$c(e,t)),r=new ft(e.name,o,e.nullable,xr(e.customMetadata))):t.has(n=s.id)?(i=(i=s.indexType)?Vc(i):new Ji,a=new fi(t.get(n),i,n,s.isOrdered),r=new ft(e.name,a,e.nullable,xr(e.customMetadata))):(i=(i=s.indexType)?Vc(i):new Ji,t.set(n,o=zc(e,$c(e,t))),a=new fi(o,i,n,s.isOrdered),r=new ft(e.name,a,e.nullable,xr(e.customMetadata))),r||null}function xr(e){return new Map(Object.entries(e||{}))}function Vc(e){return new yn(e.isSigned,e.bitWidth)}function zc(e,t){const n=e.type.name;switch(n){case"NONE":return new pn;case"null":return new pn;case"binary":return new Pr;case"utf8":return new jr;case"bool":return new $r;case"list":return new Hr((t||[])[0]);case"struct":return new Et(t||[]);case"struct_":return new Et(t||[])}switch(n){case"int":{const i=e.type;return new yn(i.isSigned,i.bitWidth)}case"floatingpoint":{const i=e.type;return new qi(Vt[i.precision])}case"decimal":{const i=e.type;return new Vr(i.scale,i.precision,i.bitWidth)}case"date":{const i=e.type;return new zr(Ze[i.unit])}case"time":{const i=e.type;return new Zi(X[i.unit],i.bitWidth)}case"timestamp":{const i=e.type;return new kr(X[i.unit],i.timezone)}case"interval":{const i=e.type;return new Wr(hn[i.unit])}case"union":{const i=e.type;return new Yr(Xt[i.mode],i.typeIds||[],t||[])}case"fixedsizebinary":{const i=e.type;return new Kr(i.byteWidth)}case"fixedsizelist":{const i=e.type;return new Jr(i.listSize,(t||[])[0])}case"map":{const i=e.type;return new qr((t||[])[0],i.keysSorted)}}throw new Error(`Unrecognized type: "${n}"`)}var $n=Je,lg=Mf,ug=di;class Ct{constructor(t,n,i,r){this._version=n,this._headerType=i,this.body=new Uint8Array(0),r&&(this._createHeader=()=>r),this._bodyLength=typeof t=="number"?t:t.low}static fromJSON(t,n){const i=new Ct(0,Zt.V4,n);return i._createHeader=fg(t,n),i}static decode(t){t=new ug(Z(t));const n=rn.getRootAsMessage(t),i=n.bodyLength(),r=n.version(),s=n.headerType(),o=new Ct(i,r,s);return o._createHeader=dg(n,s),o}static encode(t){const n=new lg;let i=-1;return t.isSchema()?i=et.encode(n,t.header()):t.isRecordBatch()?i=Qt.encode(n,t.header()):t.isDictionaryBatch()&&(i=Ue.encode(n,t.header())),rn.startMessage(n),rn.addVersion(n,Zt.V4),rn.addHeader(n,i),rn.addHeaderType(n,t.headerType),rn.addBodyLength(n,new $n(t.bodyLength,0)),rn.finishMessageBuffer(n,rn.endMessage(n)),n.asUint8Array()}static from(t,n=0){if(t instanceof et)return new Ct(0,Zt.V4,G.Schema,t);if(t instanceof Qt)return new Ct(n,Zt.V4,G.RecordBatch,t);if(t instanceof Ue)return new Ct(n,Zt.V4,G.DictionaryBatch,t);throw new Error(`Unrecognized Message header: ${t}`)}get type(){return this.headerType}get version(){return this._version}get headerType(){return this._headerType}get bodyLength(){return this._bodyLength}header(){return this._createHeader()}isSchema(){return this.headerType===G.Schema}isRecordBatch(){return this.headerType===G.RecordBatch}isDictionaryBatch(){return this.headerType===G.DictionaryBatch}}class Qt{constructor(t,n,i){this._nodes=n,this._buffers=i,this._length=typeof t=="number"?t:t.low}get nodes(){return this._nodes}get length(){return this._length}get buffers(){return this._buffers}}class Ue{constructor(t,n,i=!1){this._data=t,this._isDelta=i,this._id=typeof n=="number"?n:n.low}get id(){return this._id}get data(){return this._data}get isDelta(){return this._isDelta}get length(){return this.data.length}get nodes(){return this.data.nodes}get buffers(){return this.data.buffers}}class Ne{constructor(t,n){this.offset=typeof t=="number"?t:t.low,this.length=typeof n=="number"?n:n.low}}class Vn{constructor(t,n){this.length=typeof t=="number"?t:t.low,this.nullCount=typeof n=="number"?n:n.low}}function fg(e,t){return()=>{switch(t){case G.Schema:return et.fromJSON(e);case G.RecordBatch:return Qt.fromJSON(e);case G.DictionaryBatch:return Ue.fromJSON(e)}throw new Error(`Unrecognized Message type: { name: ${G[t]}, type: ${t} }`)}}function dg(e,t){return()=>{switch(t){case G.Schema:return et.decode(e.header(new we));case G.RecordBatch:return Qt.decode(e.header(new ke),e.version());case G.DictionaryBatch:return Ue.decode(e.header(new Zn),e.version())}throw new Error(`Unrecognized Message type: { name: ${G[t]}, type: ${t} }`)}}ft.encode=Ig;ft.decode=wg;ft.fromJSON=cg;et.encode=Sg;et.decode=hg;et.fromJSON=rg;Qt.encode=Bg;Qt.decode=pg;Qt.fromJSON=$f;Ue.encode=Og;Ue.decode=yg;Ue.fromJSON=sg;Vn.encode=Ag;Vn.decode=gg;Ne.encode=Fg;Ne.decode=mg;function hg(e,t=new Map){const n=vg(e,t);return new et(n,Mr(e),t)}function pg(e,t=Zt.V4){if(e.compression()!==null)throw new Error("Record batch compression not implemented");return new Qt(e.length(),bg(e),_g(e,t))}function yg(e,t=Zt.V4){return new Ue(Qt.decode(e.data(),t),e.id(),e.isDelta())}function mg(e){return new Ne(e.offset(),e.length())}function gg(e){return new Vn(e.length(),e.nullCount())}function bg(e){const t=[];for(let n,i=-1,r=-1,s=e.nodesLength();++i<s;)(n=e.nodes(i))&&(t[++r]=Vn.decode(n));return t}function _g(e,t){const n=[];for(let i,r=-1,s=-1,o=e.buffersLength();++r<o;)(i=e.buffers(r))&&(t<Zt.V4&&(i.bb_pos+=8*(r+1)),n[++s]=Ne.decode(i));return n}function vg(e,t){const n=[];for(let i,r=-1,s=-1,o=e.fieldsLength();++r<o;)(i=e.fields(r))&&(n[++s]=ft.decode(i,t));return n}function kc(e,t){const n=[];for(let i,r=-1,s=-1,o=e.childrenLength();++r<o;)(i=e.children(r))&&(n[++s]=ft.decode(i,t));return n}function wg(e,t){let n,i,r,s,o,a;return!t||!(a=e.dictionary())?(r=Hc(e,kc(e,t)),i=new ft(e.name(),r,e.nullable(),Mr(e))):t.has(n=a.id().low)?(s=(s=a.indexType())?Wc(s):new Ji,o=new fi(t.get(n),s,n,a.isOrdered()),i=new ft(e.name(),o,e.nullable(),Mr(e))):(s=(s=a.indexType())?Wc(s):new Ji,t.set(n,r=Hc(e,kc(e,t))),o=new fi(r,s,n,a.isOrdered()),i=new ft(e.name(),o,e.nullable(),Mr(e))),i||null}function Mr(e){const t=new Map;if(e)for(let n,i,r=-1,s=Math.trunc(e.customMetadataLength());++r<s;)(n=e.customMetadata(r))&&(i=n.key())!=null&&t.set(i,n.value());return t}function Wc(e){return new yn(e.isSigned(),e.bitWidth())}function Hc(e,t){const n=e.typeType();switch(n){case pt.NONE:return new pn;case pt.Null:return new pn;case pt.Binary:return new Pr;case pt.Utf8:return new jr;case pt.Bool:return new $r;case pt.List:return new Hr((t||[])[0]);case pt.Struct_:return new Et(t||[])}switch(n){case pt.Int:{const i=e.type(new qt);return new yn(i.isSigned(),i.bitWidth())}case pt.FloatingPoint:{const i=e.type(new Te);return new qi(i.precision())}case pt.Decimal:{const i=e.type(new Pt);return new Vr(i.scale(),i.precision(),i.bitWidth())}case pt.Date:{const i=e.type(new Ar);return new zr(i.unit())}case pt.Time:{const i=e.type(new ne);return new Zi(i.unit(),i.bitWidth())}case pt.Timestamp:{const i=e.type(new ie);return new kr(i.unit(),i.timezone())}case pt.Interval:{const i=e.type(new xe);return new Wr(i.unit())}case pt.Union:{const i=e.type(new jt);return new Yr(i.mode(),i.typeIdsArray()||[],t||[])}case pt.FixedSizeBinary:{const i=e.type(new Fe);return new Kr(i.byteWidth())}case pt.FixedSizeList:{const i=e.type(new De);return new Jr(i.listSize(),(t||[])[0])}case pt.Map:{const i=e.type(new Fr);return new qr((t||[])[0],i.keysSorted())}}throw new Error(`Unrecognized type: "${pt[n]}" (${n})`)}function Sg(e,t){const n=t.fields.map(s=>ft.encode(e,s));we.startFieldsVector(e,n.length);const i=we.createFieldsVector(e,n),r=t.metadata&&t.metadata.size>0?we.createCustomMetadataVector(e,[...t.metadata].map(([s,o])=>{const a=e.createString(`${s}`),c=e.createString(`${o}`);return It.startKeyValue(e),It.addKey(e,a),It.addValue(e,c),It.endKeyValue(e)})):-1;return we.startSchema(e),we.addFields(e,i),we.addEndianness(e,Dg?pi.Little:pi.Big),r!==-1&&we.addCustomMetadata(e,r),we.endSchema(e)}function Ig(e,t){let n=-1,i=-1,r=-1;const s=t.type;let o=t.typeId;T.isDictionary(s)?(o=s.dictionary.typeId,r=io.visit(s,e),i=io.visit(s.dictionary,e)):i=io.visit(s,e);const a=(s.children||[]).map(f=>ft.encode(e,f)),c=te.createChildrenVector(e,a),l=t.metadata&&t.metadata.size>0?te.createCustomMetadataVector(e,[...t.metadata].map(([f,p])=>{const m=e.createString(`${f}`),I=e.createString(`${p}`);return It.startKeyValue(e),It.addKey(e,m),It.addValue(e,I),It.endKeyValue(e)})):-1;return t.name&&(n=e.createString(t.name)),te.startField(e),te.addType(e,i),te.addTypeType(e,o),te.addChildren(e,c),te.addNullable(e,!!t.nullable),n!==-1&&te.addName(e,n),r!==-1&&te.addDictionary(e,r),l!==-1&&te.addCustomMetadata(e,l),te.endField(e)}function Bg(e,t){const n=t.nodes||[],i=t.buffers||[];ke.startNodesVector(e,n.length);for(const o of n.slice().reverse())Vn.encode(e,o);const r=e.endVector();ke.startBuffersVector(e,i.length);for(const o of i.slice().reverse())Ne.encode(e,o);const s=e.endVector();return ke.startRecordBatch(e),ke.addLength(e,new $n(t.length,0)),ke.addNodes(e,r),ke.addBuffers(e,s),ke.endRecordBatch(e)}function Og(e,t){const n=Qt.encode(e,t.data);return Zn.startDictionaryBatch(e),Zn.addId(e,new $n(t.id,0)),Zn.addIsDelta(e,t.isDelta),Zn.addData(e,n),Zn.endDictionaryBatch(e)}function Ag(e,t){return jf.createFieldNode(e,new $n(t.length,0),new $n(t.nullCount,0))}function Fg(e,t){return Pf.createBuffer(e,new $n(t.offset,0),new $n(t.length,0))}const Dg=(()=>{const e=new ArrayBuffer(2);return new DataView(e).setInt16(0,256,!0),new Int16Array(e)[0]===256})(),Na=e=>`Expected ${G[e]} Message in stream, but was null or length 0.`,Ea=e=>`Header pointer of flatbuffer-encoded ${G[e]} Message is null or length 0.`,kf=(e,t)=>`Expected to read ${e} metadata bytes, but only read ${t}.`,Wf=(e,t)=>`Expected to read ${e} bytes for message body, but only read ${t}.`;class Hf{constructor(t){this.source=t instanceof rs?t:new rs(t)}[Symbol.iterator](){return this}next(){let t;return(t=this.readMetadataLength()).done||t.value===-1&&(t=this.readMetadataLength()).done||(t=this.readMetadata(t.value)).done?yt:t}throw(t){return this.source.throw(t)}return(t){return this.source.return(t)}readMessage(t){let n;if((n=this.next()).done)return null;if(t!=null&&n.value.headerType!==t)throw new Error(Na(t));return n.value}readMessageBody(t){if(t<=0)return new Uint8Array(0);const n=Z(this.source.read(t));if(n.byteLength<t)throw new Error(Wf(t,n.byteLength));return n.byteOffset%8===0&&n.byteOffset+n.byteLength<=n.buffer.byteLength?n:n.slice()}readSchema(t=!1){const n=G.Schema,i=this.readMessage(n),r=i==null?void 0:i.header();if(t&&!r)throw new Error(Ea(n));return r}readMetadataLength(){const t=this.source.read(Ps),n=t&&new di(t),i=(n==null?void 0:n.readInt32(0))||0;return{done:i===0,value:i}}readMetadata(t){const n=this.source.read(t);if(!n)return yt;if(n.byteLength<t)throw new Error(kf(t,n.byteLength));return{done:!1,value:Ct.decode(n)}}}class Tg{constructor(t,n){this.source=t instanceof mi?t:fu(t)?new ss(t,n):new mi(t)}[Symbol.asyncIterator](){return this}next(){return D(this,void 0,void 0,function*(){let t;return(t=yield this.readMetadataLength()).done||t.value===-1&&(t=yield this.readMetadataLength()).done||(t=yield this.readMetadata(t.value)).done?yt:t})}throw(t){return D(this,void 0,void 0,function*(){return yield this.source.throw(t)})}return(t){return D(this,void 0,void 0,function*(){return yield this.source.return(t)})}readMessage(t){return D(this,void 0,void 0,function*(){let n;if((n=yield this.next()).done)return null;if(t!=null&&n.value.headerType!==t)throw new Error(Na(t));return n.value})}readMessageBody(t){return D(this,void 0,void 0,function*(){if(t<=0)return new Uint8Array(0);const n=Z(yield this.source.read(t));if(n.byteLength<t)throw new Error(Wf(t,n.byteLength));return n.byteOffset%8===0&&n.byteOffset+n.byteLength<=n.buffer.byteLength?n:n.slice()})}readSchema(t=!1){return D(this,void 0,void 0,function*(){const n=G.Schema,i=yield this.readMessage(n),r=i==null?void 0:i.header();if(t&&!r)throw new Error(Ea(n));return r})}readMetadataLength(){return D(this,void 0,void 0,function*(){const t=yield this.source.read(Ps),n=t&&new di(t),i=(n==null?void 0:n.readInt32(0))||0;return{done:i===0,value:i}})}readMetadata(t){return D(this,void 0,void 0,function*(){const n=yield this.source.read(t);if(!n)return yt;if(n.byteLength<t)throw new Error(kf(t,n.byteLength));return{done:!1,value:Ct.decode(n)}})}}class xg extends Hf{constructor(t){super(new Uint8Array(0)),this._schema=!1,this._body=[],this._batchIndex=0,this._dictionaryIndex=0,this._json=t instanceof Uc?t:new Uc(t)}next(){const{_json:t}=this;if(!this._schema)return this._schema=!0,{done:!1,value:Ct.fromJSON(t.schema,G.Schema)};if(this._dictionaryIndex<t.dictionaries.length){const n=t.dictionaries[this._dictionaryIndex++];return this._body=n.data.columns,{done:!1,value:Ct.fromJSON(n,G.DictionaryBatch)}}if(this._batchIndex<t.batches.length){const n=t.batches[this._batchIndex++];return this._body=n.columns,{done:!1,value:Ct.fromJSON(n,G.RecordBatch)}}return this._body=[],yt}readMessageBody(t){return n(this._body);function n(i){return(i||[]).reduce((r,s)=>[...r,...s.VALIDITY&&[s.VALIDITY]||[],...s.TYPE&&[s.TYPE]||[],...s.OFFSET&&[s.OFFSET]||[],...s.DATA&&[s.DATA]||[],...n(s.children)],[])}}readMessage(t){let n;if((n=this.next()).done)return null;if(t!=null&&n.value.headerType!==t)throw new Error(Na(t));return n.value}readSchema(){const t=G.Schema,n=this.readMessage(t),i=n==null?void 0:n.header();if(!n||!i)throw new Error(Ea(t));return i}}const Ps=4,Co="ARROW1",Qi=new Uint8Array(Co.length);for(let e=0;e<Co.length;e+=1)Qi[e]=Co.codePointAt(e);function Ca(e,t=0){for(let n=-1,i=Qi.length;++n<i;)if(Qi[n]!==e[t+n])return!1;return!0}const ar=Qi.length,Yf=ar+Ps,Mg=ar*2+Ps;class qe extends Aa{constructor(t){super(),this._impl=t}get closed(){return this._impl.closed}get schema(){return this._impl.schema}get autoDestroy(){return this._impl.autoDestroy}get dictionaries(){return this._impl.dictionaries}get numDictionaries(){return this._impl.numDictionaries}get numRecordBatches(){return this._impl.numRecordBatches}get footer(){return this._impl.isFile()?this._impl.footer:null}isSync(){return this._impl.isSync()}isAsync(){return this._impl.isAsync()}isFile(){return this._impl.isFile()}isStream(){return this._impl.isStream()}next(){return this._impl.next()}throw(t){return this._impl.throw(t)}return(t){return this._impl.return(t)}cancel(){return this._impl.cancel()}reset(t){return this._impl.reset(t),this._DOMStream=void 0,this._nodeStream=void 0,this}open(t){const n=this._impl.open(t);return dn(n)?n.then(()=>this):this}readRecordBatch(t){return this._impl.isFile()?this._impl.readRecordBatch(t):null}[Symbol.iterator](){return this._impl[Symbol.iterator]()}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}toDOMStream(){return ee.toDOMStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this})}toNodeStream(){return ee.toNodeStream(this.isSync()?{[Symbol.iterator]:()=>this}:{[Symbol.asyncIterator]:()=>this},{objectMode:!0})}static throughNode(t){throw new Error('"throughNode" not available in this environment')}static throughDOM(t,n){throw new Error('"throughDOM" not available in this environment')}static from(t){return t instanceof qe?t:Io(t)?Rg(t):fu(t)?Pg(t):dn(t)?D(this,void 0,void 0,function*(){return yield qe.from(yield t)}):du(t)||da(t)||hu(t)||Ii(t)?Ug(new mi(t)):Lg(new rs(t))}static readAll(t){return t instanceof qe?t.isSync()?Yc(t):Kc(t):Io(t)||ArrayBuffer.isView(t)||ir(t)||uu(t)?Yc(t):Kc(t)}}class ls extends qe{constructor(t){super(t),this._impl=t}readAll(){return[...this]}[Symbol.iterator](){return this._impl[Symbol.iterator]()}[Symbol.asyncIterator](){return Me(this,arguments,function*(){yield $(yield*Or(Un(this[Symbol.iterator]())))})}}class us extends qe{constructor(t){super(t),this._impl=t}readAll(){var t,n;return D(this,void 0,void 0,function*(){const i=new Array;try{for(var r=Un(this),s;s=yield r.next(),!s.done;){const o=s.value;i.push(o)}}catch(o){t={error:o}}finally{try{s&&!s.done&&(n=r.return)&&(yield n.call(r))}finally{if(t)throw t.error}}return i})}[Symbol.iterator](){throw new Error("AsyncRecordBatchStreamReader is not Iterable")}[Symbol.asyncIterator](){return this._impl[Symbol.asyncIterator]()}}class Kf extends ls{constructor(t){super(t),this._impl=t}}class Ng extends us{constructor(t){super(t),this._impl=t}}class Jf{constructor(t=new Map){this.closed=!1,this.autoDestroy=!0,this._dictionaryIndex=0,this._recordBatchIndex=0,this.dictionaries=t}get numDictionaries(){return this._dictionaryIndex}get numRecordBatches(){return this._recordBatchIndex}isSync(){return!1}isAsync(){return!1}isFile(){return!1}isStream(){return!1}reset(t){return this._dictionaryIndex=0,this._recordBatchIndex=0,this.schema=t,this.dictionaries=new Map,this}_loadRecordBatch(t,n){const i=this._loadVectors(t,n,this.schema.fields),r=K({type:new Et(this.schema.fields),length:t.length,children:i});return new $t(this.schema,r)}_loadDictionaryBatch(t,n){const{id:i,isDelta:r}=t,{dictionaries:s,schema:o}=this,a=s.get(i);if(r||!a){const c=o.dictionaries.get(i),l=this._loadVectors(t.data,n,[c]);return(a&&r?a.concat(new tt(l)):new tt(l)).memoize()}return a.memoize()}_loadVectors(t,n,i){return new Cf(n,t.nodes,t.buffers,this.dictionaries).visitMany(i)}}class fs extends Jf{constructor(t,n){super(n),this._reader=Io(t)?new xg(this._handle=t):new Hf(this._handle=t)}isSync(){return!0}isStream(){return!0}[Symbol.iterator](){return this}cancel(){!this.closed&&(this.closed=!0)&&(this.reset()._reader.return(),this._reader=null,this.dictionaries=null)}open(t){return this.closed||(this.autoDestroy=Zf(this,t),this.schema||(this.schema=this._reader.readSchema())||this.cancel()),this}throw(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.throw(t):yt}return(t){return!this.closed&&this.autoDestroy&&(this.closed=!0)?this.reset()._reader.return(t):yt}next(){if(this.closed)return yt;let t;const{_reader:n}=this;for(;t=this._readNextMessageAndValidate();)if(t.isSchema())this.reset(t.header());else if(t.isRecordBatch()){this._recordBatchIndex++;const i=t.header(),r=n.readMessageBody(t.bodyLength);return{done:!1,value:this._loadRecordBatch(i,r)}}else if(t.isDictionaryBatch()){this._dictionaryIndex++;const i=t.header(),r=n.readMessageBody(t.bodyLength),s=this._loadDictionaryBatch(i,r);this.dictionaries.set(i.id,s)}return this.schema&&this._recordBatchIndex===0?(this._recordBatchIndex++,{done:!1,value:new Ma(this.schema)}):this.return()}_readNextMessageAndValidate(t){return this._reader.readMessage(t)}}class ds extends Jf{constructor(t,n){super(n),this._reader=new Tg(this._handle=t)}isAsync(){return!0}isStream(){return!0}[Symbol.asyncIterator](){return this}cancel(){return D(this,void 0,void 0,function*(){!this.closed&&(this.closed=!0)&&(yield this.reset()._reader.return(),this._reader=null,this.dictionaries=null)})}open(t){return D(this,void 0,void 0,function*(){return this.closed||(this.autoDestroy=Zf(this,t),this.schema||(this.schema=yield this._reader.readSchema())||(yield this.cancel())),this})}throw(t){return D(this,void 0,void 0,function*(){return!this.closed&&this.autoDestroy&&(this.closed=!0)?yield this.reset()._reader.throw(t):yt})}return(t){return D(this,void 0,void 0,function*(){return!this.closed&&this.autoDestroy&&(this.closed=!0)?yield this.reset()._reader.return(t):yt})}next(){return D(this,void 0,void 0,function*(){if(this.closed)return yt;let t;const{_reader:n}=this;for(;t=yield this._readNextMessageAndValidate();)if(t.isSchema())yield this.reset(t.header());else if(t.isRecordBatch()){this._recordBatchIndex++;const i=t.header(),r=yield n.readMessageBody(t.bodyLength);return{done:!1,value:this._loadRecordBatch(i,r)}}else if(t.isDictionaryBatch()){this._dictionaryIndex++;const i=t.header(),r=yield n.readMessageBody(t.bodyLength),s=this._loadDictionaryBatch(i,r);this.dictionaries.set(i.id,s)}return this.schema&&this._recordBatchIndex===0?(this._recordBatchIndex++,{done:!1,value:new Ma(this.schema)}):yield this.return()})}_readNextMessageAndValidate(t){return D(this,void 0,void 0,function*(){return yield this._reader.readMessage(t)})}}class qf extends fs{constructor(t,n){super(t instanceof Pc?t:new Pc(t),n)}get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}isSync(){return!0}isFile(){return!0}open(t){if(!this.closed&&!this._footer){this.schema=(this._footer=this._readFooter()).schema;for(const n of this._footer.dictionaryBatches())n&&this._readDictionaryBatch(this._dictionaryIndex++)}return super.open(t)}readRecordBatch(t){var n;if(this.closed)return null;this._footer||this.open();const i=(n=this._footer)===null||n===void 0?void 0:n.getRecordBatch(t);if(i&&this._handle.seek(i.offset)){const r=this._reader.readMessage(G.RecordBatch);if(r!=null&&r.isRecordBatch()){const s=r.header(),o=this._reader.readMessageBody(r.bodyLength);return this._loadRecordBatch(s,o)}}return null}_readDictionaryBatch(t){var n;const i=(n=this._footer)===null||n===void 0?void 0:n.getDictionaryBatch(t);if(i&&this._handle.seek(i.offset)){const r=this._reader.readMessage(G.DictionaryBatch);if(r!=null&&r.isDictionaryBatch()){const s=r.header(),o=this._reader.readMessageBody(r.bodyLength),a=this._loadDictionaryBatch(s,o);this.dictionaries.set(s.id,a)}}}_readFooter(){const{_handle:t}=this,n=t.size-Yf,i=t.readInt32(n),r=t.readAt(n-i,i);return Xi.decode(r)}_readNextMessageAndValidate(t){var n;if(this._footer||this.open(),this._footer&&this._recordBatchIndex<this.numRecordBatches){const i=(n=this._footer)===null||n===void 0?void 0:n.getRecordBatch(this._recordBatchIndex);if(i&&this._handle.seek(i.offset))return this._reader.readMessage(t)}return null}}class Eg extends ds{constructor(t,...n){const i=typeof n[0]!="number"?n.shift():void 0,r=n[0]instanceof Map?n.shift():void 0;super(t instanceof ss?t:new ss(t,i),r)}get footer(){return this._footer}get numDictionaries(){return this._footer?this._footer.numDictionaries:0}get numRecordBatches(){return this._footer?this._footer.numRecordBatches:0}isFile(){return!0}isAsync(){return!0}open(t){const n=Object.create(null,{open:{get:()=>super.open}});return D(this,void 0,void 0,function*(){if(!this.closed&&!this._footer){this.schema=(this._footer=yield this._readFooter()).schema;for(const i of this._footer.dictionaryBatches())i&&(yield this._readDictionaryBatch(this._dictionaryIndex++))}return yield n.open.call(this,t)})}readRecordBatch(t){var n;return D(this,void 0,void 0,function*(){if(this.closed)return null;this._footer||(yield this.open());const i=(n=this._footer)===null||n===void 0?void 0:n.getRecordBatch(t);if(i&&(yield this._handle.seek(i.offset))){const r=yield this._reader.readMessage(G.RecordBatch);if(r!=null&&r.isRecordBatch()){const s=r.header(),o=yield this._reader.readMessageBody(r.bodyLength);return this._loadRecordBatch(s,o)}}return null})}_readDictionaryBatch(t){var n;return D(this,void 0,void 0,function*(){const i=(n=this._footer)===null||n===void 0?void 0:n.getDictionaryBatch(t);if(i&&(yield this._handle.seek(i.offset))){const r=yield this._reader.readMessage(G.DictionaryBatch);if(r!=null&&r.isDictionaryBatch()){const s=r.header(),o=yield this._reader.readMessageBody(r.bodyLength),a=this._loadDictionaryBatch(s,o);this.dictionaries.set(s.id,a)}}})}_readFooter(){return D(this,void 0,void 0,function*(){const{_handle:t}=this;t._pending&&(yield t._pending);const n=t.size-Yf,i=yield t.readInt32(n),r=yield t.readAt(n-i,i);return Xi.decode(r)})}_readNextMessageAndValidate(t){return D(this,void 0,void 0,function*(){if(this._footer||(yield this.open()),this._footer&&this._recordBatchIndex<this.numRecordBatches){const n=this._footer.getRecordBatch(this._recordBatchIndex);if(n&&(yield this._handle.seek(n.offset)))return yield this._reader.readMessage(t)}return null})}}class Cg extends fs{constructor(t,n){super(t,n)}_loadVectors(t,n,i){return new Ym(n,t.nodes,t.buffers,this.dictionaries).visitMany(i)}}function Zf(e,t){return t&&typeof t.autoDestroy=="boolean"?t.autoDestroy:e.autoDestroy}function*Yc(e){const t=qe.from(e);try{if(!t.open({autoDestroy:!1}).closed)do yield t;while(!t.reset().open().closed)}finally{t.cancel()}}function Kc(e){return Me(this,arguments,function*(){const n=yield $(qe.from(e));try{if(!(yield $(n.open({autoDestroy:!1}))).closed)do yield yield $(n);while(!(yield $(n.reset().open())).closed)}finally{yield $(n.cancel())}})}function Rg(e){return new ls(new Cg(e))}function Lg(e){const t=e.peek(ar+7&-8);return t&&t.byteLength>=4?Ca(t)?new Kf(new qf(e.read())):new ls(new fs(e)):new ls(new fs(function*(){}()))}function Ug(e){return D(this,void 0,void 0,function*(){const t=yield e.peek(ar+7&-8);return t&&t.byteLength>=4?Ca(t)?new Kf(new qf(yield e.read())):new us(new ds(e)):new us(new ds(function(){return Me(this,arguments,function*(){})}()))})}function Pg(e){return D(this,void 0,void 0,function*(){const{size:t}=yield e.stat(),n=new ss(e,t);return t>=Mg&&Ca(yield n.readAt(0,ar+7&-8))?new Ng(new Eg(n)):new us(new ds(n))})}class vt extends J{constructor(){super(),this._byteLength=0,this._nodes=[],this._buffers=[],this._bufferRegions=[]}static assemble(...t){const n=r=>r.flatMap(s=>Array.isArray(s)?n(s):s instanceof $t?s.data.children:s.data),i=new vt;return i.visitMany(n(t)),i}visit(t){if(t instanceof tt)return this.visitMany(t.data),this;const{type:n}=t;if(!T.isDictionary(n)){const{length:i,nullCount:r}=t;if(i>2147483647)throw new RangeError("Cannot write arrays larger than 2^31 - 1 in length");T.isNull(n)||he.call(this,r<=0?new Uint8Array(0):wa(t.offset,i,t.nullBitmap)),this.nodes.push(new Vn(i,r))}return super.visit(t)}visitNull(t){return this}visitDictionary(t){return this.visit(t.clone(t.type.indices))}get nodes(){return this._nodes}get buffers(){return this._buffers}get byteLength(){return this._byteLength}get bufferRegions(){return this._bufferRegions}}function he(e){const t=e.byteLength+7&-8;return this.buffers.push(e),this.bufferRegions.push(new Ne(this._byteLength,t)),this._byteLength+=t,this}function jg(e){const{type:t,length:n,typeIds:i,valueOffsets:r}=e;if(he.call(this,i),t.mode===Xt.Sparse)return Ro.call(this,e);if(t.mode===Xt.Dense){if(e.offset<=0)return he.call(this,r),Ro.call(this,e);{const s=i.reduce((f,p)=>Math.max(f,p),i[0]),o=new Int32Array(s+1),a=new Int32Array(s+1).fill(-1),c=new Int32Array(n),l=pa(-r[0],n,r);for(let f,p,m=-1;++m<n;)(p=a[f=i[m]])===-1&&(p=a[f]=l[f]),c[m]=l[m]-p,++o[f];he.call(this,c);for(let f,p=-1,m=t.children.length;++p<m;)if(f=e.children[p]){const I=t.typeIds[p],k=Math.min(n,o[I]);this.visit(f.slice(a[I],k))}}}return this}function $g(e){let t;return e.nullCount>=e.length?he.call(this,new Uint8Array(0)):(t=e.values)instanceof Uint8Array?he.call(this,wa(e.offset,e.length,t)):he.call(this,Zr(e.values))}function vn(e){return he.call(this,e.values.subarray(0,e.length*e.stride))}function Xf(e){const{length:t,values:n,valueOffsets:i}=e,r=i[0],s=i[t],o=Math.min(s-r,n.byteLength-r);return he.call(this,pa(-i[0],t,i)),he.call(this,n.subarray(r,r+o)),this}function Ra(e){const{length:t,valueOffsets:n}=e;return n&&he.call(this,pa(n[0],t,n)),this.visit(e.children[0])}function Ro(e){return this.visitMany(e.type.children.map((t,n)=>e.children[n]).filter(Boolean))[0]}vt.prototype.visitBool=$g;vt.prototype.visitInt=vn;vt.prototype.visitFloat=vn;vt.prototype.visitUtf8=Xf;vt.prototype.visitBinary=Xf;vt.prototype.visitFixedSizeBinary=vn;vt.prototype.visitDate=vn;vt.prototype.visitTimestamp=vn;vt.prototype.visitTime=vn;vt.prototype.visitDecimal=vn;vt.prototype.visitList=Ra;vt.prototype.visitStruct=Ro;vt.prototype.visitUnion=jg;vt.prototype.visitInterval=vn;vt.prototype.visitFixedSizeList=Ra;vt.prototype.visitMap=Ra;class Qf extends Aa{constructor(t){super(),this._position=0,this._started=!1,this._sink=new Tr,this._schema=null,this._dictionaryBlocks=[],this._recordBatchBlocks=[],this._dictionaryDeltaOffsets=new Map,Wt(t)||(t={autoDestroy:!0,writeLegacyIpcFormat:!1}),this._autoDestroy=typeof t.autoDestroy=="boolean"?t.autoDestroy:!0,this._writeLegacyIpcFormat=typeof t.writeLegacyIpcFormat=="boolean"?t.writeLegacyIpcFormat:!1}static throughNode(t){throw new Error('"throughNode" not available in this environment')}static throughDOM(t,n){throw new Error('"throughDOM" not available in this environment')}toString(t=!1){return this._sink.toString(t)}toUint8Array(t=!1){return this._sink.toUint8Array(t)}writeAll(t){return dn(t)?t.then(n=>this.writeAll(n)):Ii(t)?ja(this,t):Pa(this,t)}get closed(){return this._sink.closed}[Symbol.asyncIterator](){return this._sink[Symbol.asyncIterator]()}toDOMStream(t){return this._sink.toDOMStream(t)}toNodeStream(t){return this._sink.toNodeStream(t)}close(){return this.reset()._sink.close()}abort(t){return this.reset()._sink.abort(t)}finish(){return this._autoDestroy?this.close():this.reset(this._sink,this._schema),this}reset(t=this._sink,n=null){return t===this._sink||t instanceof Tr?this._sink=t:(this._sink=new Tr,t&&ay(t)?this.toDOMStream({type:"bytes"}).pipeTo(t):t&&cy(t)&&this.toNodeStream({objectMode:!1}).pipe(t)),this._started&&this._schema&&this._writeFooter(this._schema),this._started=!1,this._dictionaryBlocks=[],this._recordBatchBlocks=[],this._dictionaryDeltaOffsets=new Map,(!n||!Mo(n,this._schema))&&(n==null?(this._position=0,this._schema=null):(this._started=!0,this._schema=n,this._writeSchema(n))),this}write(t){let n=null;if(this._sink){if(t==null)return this.finish()&&void 0;if(t instanceof Nt&&!(n=t.schema))return this.finish()&&void 0;if(t instanceof $t&&!(n=t.schema))return this.finish()&&void 0}else throw new Error("RecordBatchWriter is closed");if(n&&!Mo(n,this._schema)){if(this._started&&this._autoDestroy)return this.close();this.reset(this._sink,n)}t instanceof $t?t instanceof Ma||this._writeRecordBatch(t):t instanceof Nt?this.writeAll(t.batches):ir(t)&&this.writeAll(t)}_writeMessage(t,n=8){const i=n-1,r=Ct.encode(t),s=r.byteLength,o=this._writeLegacyIpcFormat?4:8,a=s+o+i&~i,c=a-s-o;return t.headerType===G.RecordBatch?this._recordBatchBlocks.push(new mn(a,t.bodyLength,this._position)):t.headerType===G.DictionaryBatch&&this._dictionaryBlocks.push(new mn(a,t.bodyLength,this._position)),this._writeLegacyIpcFormat||this._write(Int32Array.of(-1)),this._write(Int32Array.of(a-o)),s>0&&this._write(r),this._writePadding(c)}_write(t){if(this._started){const n=Z(t);n&&n.byteLength>0&&(this._sink.write(n),this._position+=n.byteLength)}return this}_writeSchema(t){return this._writeMessage(Ct.from(t))}_writeFooter(t){return this._writeLegacyIpcFormat?this._write(Int32Array.of(0)):this._write(Int32Array.of(-1,0))}_writeMagic(){return this._write(Qi)}_writePadding(t){return t>0?this._write(new Uint8Array(t)):this}_writeRecordBatch(t){const{byteLength:n,nodes:i,bufferRegions:r,buffers:s}=vt.assemble(t),o=new Qt(t.numRows,i,r),a=Ct.from(o,n);return this._writeDictionaries(t)._writeMessage(a)._writeBodyBuffers(s)}_writeDictionaryBatch(t,n,i=!1){this._dictionaryDeltaOffsets.set(n,t.length+(this._dictionaryDeltaOffsets.get(n)||0));const{byteLength:r,nodes:s,bufferRegions:o,buffers:a}=vt.assemble(new tt([t])),c=new Qt(t.length,s,o),l=new Ue(c,n,i),f=Ct.from(l,r);return this._writeMessage(f)._writeBodyBuffers(a)}_writeBodyBuffers(t){let n,i,r;for(let s=-1,o=t.length;++s<o;)(n=t[s])&&(i=n.byteLength)>0&&(this._write(n),(r=(i+7&-8)-i)>0&&this._writePadding(r));return this}_writeDictionaries(t){for(let[n,i]of t.dictionaries){let r=this._dictionaryDeltaOffsets.get(n)||0;if(r===0||(i=i==null?void 0:i.slice(r)).length>0)for(const s of i.data)this._writeDictionaryBatch(s,n,r>0),r+=s.length}return this}}class La extends Qf{static writeAll(t,n){const i=new La(n);return dn(t)?t.then(r=>i.writeAll(r)):Ii(t)?ja(i,t):Pa(i,t)}}class Ua extends Qf{static writeAll(t){const n=new Ua;return dn(t)?t.then(i=>n.writeAll(i)):Ii(t)?ja(n,t):Pa(n,t)}constructor(){super(),this._autoDestroy=!0}_writeSchema(t){return this._writeMagic()._writePadding(2)}_writeFooter(t){const n=Xi.encode(new Xi(t,Zt.V4,this._recordBatchBlocks,this._dictionaryBlocks));return super._writeFooter(t)._write(n)._write(Int32Array.of(n.byteLength))._writeMagic()}}function Pa(e,t){let n=t;t instanceof Nt&&(n=t.batches,e.reset(void 0,t.schema));for(const i of n)e.write(i);return e.finish()}function ja(e,t){var n,i,r,s;return D(this,void 0,void 0,function*(){try{for(n=Un(t);i=yield n.next(),!i.done;){const o=i.value;e.write(o)}}catch(o){r={error:o}}finally{try{i&&!i.done&&(s=n.return)&&(yield s.call(n))}finally{if(r)throw r.error}}return e.finish()})}function Ei(e){const t=qe.from(e);return dn(t)?t.then(n=>Ei(n)):t.isAsync()?t.readAll().then(n=>new Nt(n)):new Nt(t.readAll())}function ro(e,t="stream"){return(t==="stream"?La:Ua).writeAll(e).toUint8Array(!0)}var Jc=function(){function e(t,n,i,r){var s=this;this.getCell=function(o,a){var c=o<s.headerRows&&a<s.headerColumns,l=o>=s.headerRows&&a<s.headerColumns,f=o<s.headerRows&&a>=s.headerColumns;if(c){var p=["blank"];return a>0&&p.push("level"+o),{type:"blank",classNames:p.join(" "),content:""}}else if(f){var m=a-s.headerColumns,p=["col_heading","level"+o,"col"+m];return{type:"columns",classNames:p.join(" "),content:s.getContent(s.columnsTable,m,o)}}else if(l){var I=o-s.headerRows,p=["row_heading","level"+a,"row"+I];return{type:"index",id:"T_".concat(s.uuid,"level").concat(a,"_row").concat(I),classNames:p.join(" "),content:s.getContent(s.indexTable,I,a)}}else{var I=o-s.headerRows,m=a-s.headerColumns,p=["data","row"+I,"col"+m],k=s.styler?s.getContent(s.styler.displayValuesTable,I,m):s.getContent(s.dataTable,I,m);return{type:"data",id:"T_".concat(s.uuid,"row").concat(I,"_col").concat(m),classNames:p.join(" "),content:k}}},this.getContent=function(o,a,c){var l=o.getChildAt(c);if(l===null)return"";var f=s.getColumnTypeId(o,c);switch(f){case h.Timestamp:return s.nanosToDate(l.get(a));default:return l.get(a)}},this.dataTable=Ei(t),this.indexTable=Ei(n),this.columnsTable=Ei(i),this.styler=r?{caption:r.caption,displayValuesTable:Ei(r.displayValues),styles:r.styles,uuid:r.uuid}:void 0}return Object.defineProperty(e.prototype,"rows",{get:function(){return this.indexTable.numRows+this.columnsTable.numCols},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"columns",{get:function(){return this.indexTable.numCols+this.columnsTable.numRows},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"headerRows",{get:function(){return this.rows-this.dataRows},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"headerColumns",{get:function(){return this.columns-this.dataColumns},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"dataRows",{get:function(){return this.dataTable.numRows},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"dataColumns",{get:function(){return this.dataTable.numCols},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"uuid",{get:function(){return this.styler&&this.styler.uuid},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"caption",{get:function(){return this.styler&&this.styler.caption},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"styles",{get:function(){return this.styler&&this.styler.styles},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"table",{get:function(){return this.dataTable},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"index",{get:function(){return this.indexTable},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"columnTable",{get:function(){return this.columnsTable},enumerable:!1,configurable:!0}),e.prototype.serialize=function(){return{data:ro(this.dataTable),index:ro(this.indexTable),columns:ro(this.columnsTable)}},e.prototype.getColumnTypeId=function(t,n){return t.schema.fields[n].type.typeId},e.prototype.nanosToDate=function(t){return new Date(t/1e6)},e}(),Pi=function(){return Pi=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++){t=arguments[n];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},Pi.apply(this,arguments)},ji;(function(e){e.COMPONENT_READY="streamlit:componentReady",e.SET_COMPONENT_VALUE="streamlit:setComponentValue",e.SET_FRAME_HEIGHT="streamlit:setFrameHeight"})(ji||(ji={}));var We=function(){function e(){}return e.API_VERSION=1,e.RENDER_EVENT="streamlit:render",e.events=new EventTarget,e.registeredMessageListener=!1,e.setComponentReady=function(){e.registeredMessageListener||(window.addEventListener("message",e.onMessageEvent),e.registeredMessageListener=!0),e.sendBackMsg(ji.COMPONENT_READY,{apiVersion:e.API_VERSION})},e.setFrameHeight=function(t){t===void 0&&(t=document.body.scrollHeight),t!==e.lastFrameHeight&&(e.lastFrameHeight=t,e.sendBackMsg(ji.SET_FRAME_HEIGHT,{height:t}))},e.setComponentValue=function(t){var n;t instanceof Jc?(n="dataframe",t=t.serialize()):zg(t)?(n="bytes",t=new Uint8Array(t.buffer)):t instanceof ArrayBuffer?(n="bytes",t=new Uint8Array(t)):n="json",e.sendBackMsg(ji.SET_COMPONENT_VALUE,{value:t,dataType:n})},e.onMessageEvent=function(t){var n=t.data.type;switch(n){case e.RENDER_EVENT:e.onRenderMessage(t.data);break}},e.onRenderMessage=function(t){var n=t.args;n==null&&(console.error("Got null args in onRenderMessage. This should never happen"),n={});var i=t.dfs&&t.dfs.length>0?e.argsDataframeToObject(t.dfs):{};n=Pi(Pi({},n),i);var r=!!t.disabled,s=t.theme;s&&Vg(s);var o={disabled:r,args:n,theme:s},a=new CustomEvent(e.RENDER_EVENT,{detail:o});e.events.dispatchEvent(a)},e.argsDataframeToObject=function(t){var n=t.map(function(i){var r=i.key,s=i.value;return[r,e.toArrowTable(s)]});return Object.fromEntries(n)},e.toArrowTable=function(t){var n,i=(n=t.data,n.data),r=n.index,s=n.columns,o=n.styler;return new Jc(i,r,s,o)},e.sendBackMsg=function(t,n){window.parent.postMessage(Pi({isStreamlitMessage:!0,type:t},n),"*")},e}(),Vg=function(e){var t=document.createElement("style");document.head.appendChild(t),t.innerHTML=`
    :root {
      --primary-color: `.concat(e.primaryColor,`;
      --background-color: `).concat(e.backgroundColor,`;
      --secondary-background-color: `).concat(e.secondaryBackgroundColor,`;
      --text-color: `).concat(e.textColor,`;
      --font: `).concat(e.font,`;
    }

    body {
      background-color: var(--background-color);
      color: var(--text-color);
    }
  `)};function zg(e){var t=!1;try{t=e instanceof BigInt64Array||e instanceof BigUint64Array}catch{}return e instanceof Int8Array||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array||t}var kg=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,r){i.__proto__=r}||function(i,r){for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(i[s]=r[s])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function i(){this.constructor=t}t.prototype=n===null?Object.create(n):(i.prototype=n.prototype,new i)}}();(function(e){kg(t,e);function t(){return e!==null&&e.apply(this,arguments)||this}return t.prototype.componentDidMount=function(){We.setFrameHeight()},t.prototype.componentDidUpdate=function(){We.setFrameHeight()},t})(ny.PureComponent);const Wg={__name:"StTheme",props:["theme"],setup(e){const t=e,n=Kl(()=>JSON.stringify(t.theme));return Sr(n,i=>{We.setComponentValue(n.value)},{immediate:!0}),(i,r)=>null}},Hg=Al({name:"WithStreamlitConnection",setup(){const e=tc(void 0),t=tc(""),n=i=>{const r=i;e.value=r.detail,t.value=""};return Tl(()=>{We.events.addEventListener(We.RENDER_EVENT,n),We.setComponentReady()}),xl(()=>{t.value!=""&&We.setFrameHeight()}),Xo(()=>{We.events.removeEventListener(We.RENDER_EVENT,n)}),Ml(i=>{t.value=String(i)}),{renderData:e,componentError:t}}}),Gf=(e,t)=>{const n=e.__vccOpts||e;for(const[i,r]of t)n[i]=r;return n},Yg=e=>(Qd("data-v-bcc69db2"),e=e(),Gd(),e),Kg={key:0},Jg=Yg(()=>Bs("h1",{class:"err__title"},"Component Error",-1)),qg={class:"err__msg"};function Zg(e,t,n,i,r,s){return ki(),go("div",null,[e.componentError!=""?(ki(),go("div",Kg,[Jg,Bs("div",qg,"Message: "+pd(e.componentError),1)])):e.renderData!=null?bh(e.$slots,"default",{key:1,theme:e.renderData.theme,disabled:e.renderData.disabled},void 0,!0):zh("",!0)])}const Xg=Gf(Hg,[["render",Zg],["__scopeId","data-v-bcc69db2"]]),Qg=Al({name:"App",components:{StTheme:Wg,WithStreamlitConnection:Xg}}),Gg={id:"app"};function tb(e,t,n,i,r,s){const o=ic("StTheme"),a=ic("WithStreamlitConnection");return ki(),go("div",Gg,[re(a,null,{default:Sl(({theme:c})=>[re(o,{theme:c},null,8,["theme"])]),_:1})])}const eb=Gf(Qg,[["render",tb]]);Bp(eb).mount("#app");
