import streamlit as st
import time
import logging
from utils.oauth_helper import JiraOAuthHelper
from urllib.parse import urlparse, parse_qs


def handle_oauth_callback():
    """Handle OAuth callback and extract authorization code."""
    query_params = st.query_params

    # Check if we have an authorization code
    if 'code' in query_params:
        authorization_code = query_params['code']
        state = query_params.get('state', '')

        # Skip state validation for now since Streamlit session state can be unreliable during redirects
        # In production, you'd want to implement a more robust state storage mechanism

        try:
            oauth_helper = JiraOAuthHelper()

            # Exchange code for tokens
            st.info("🔄 Processing authentication...")
            token_data = oauth_helper.exchange_code_for_tokens(authorization_code)

            # Get accessible resources to find cloud ID
            resources = oauth_helper.get_accessible_resources(token_data['access_token'])

            if resources:
                # Find the Jira site - look for any resource with Jira scopes
                jira_site = None
                for resource in resources:
                    resource_scopes = resource.get('scopes', [])
                    # Check if any Jira-related scopes are present
                    jira_scopes = [s for s in resource_scopes if 'jira' in s.lower()]
                    if jira_scopes:
                        jira_site = resource
                        break

                if jira_site:
                    # Store tokens and cloud ID
                    oauth_helper.store_tokens_in_session(token_data)
                    st.session_state.jira_cloud_id = jira_site['id']
                    st.session_state.jira_site_url = jira_site['url']
                    st.session_state.jira_site_name = jira_site['name']
                    st.session_state.jira_oauth_authenticated = True
                    st.session_state.jira_auth_popup_actioned = True
                    st.session_state.logged_in_user = jira_site['name']

                    # Clear OAuth-related session state
                    for key in ['jira_oauth_state', 'show_oauth_link', 'jira_auth_url', 'oauth_callback_processed']:
                        if key in st.session_state:
                            del st.session_state[key]

                    st.success(f"✅ Successfully authenticated with Jira site: {jira_site['name']}")
                    st.info(f"📋 Granted scopes: {', '.join(jira_site.get('scopes', []))}")
                    logging.info(f"OAuth authentication successful for site: {jira_site['name']}")

                    # Clear query parameters and redirect to main app
                    st.query_params.clear()
                    time.sleep(2)
                    st.rerun()

                else:
                    st.error("❌ No Jira site found in accessible resources")
                    st.info("Available resources:")
                    for resource in resources:
                        st.write(f"- {resource.get('name', 'Unknown')}: {resource.get('scopes', [])}")
                    logging.error("No Jira site found in accessible resources")
            else:
                st.error("❌ No accessible resources found")
                logging.error("No accessible resources found")

        except Exception as e:
            st.error(f"❌ OAuth authentication failed: {str(e)}")
            logging.error(f"OAuth authentication failed: {str(e)}")
            # Reset authentication state on error
            st.session_state.jira_oauth_authenticated = False
            st.session_state.jira_auth_popup_actioned = False

    elif 'error' in query_params:
        error = query_params.get('error', 'Unknown error')
        error_description = query_params.get('error_description', '')
        st.error(f"❌ OAuth error: {error}")
        if error_description:
            st.error(f"Description: {error_description}")
        logging.error(f"OAuth error: {error} - {error_description}")

        # Reset authentication state on error
        st.session_state.jira_oauth_authenticated = False
        st.session_state.jira_auth_popup_actioned = False


# Removed complex OAuth UI - keeping it simple with just callback handling
