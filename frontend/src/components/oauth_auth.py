import streamlit as st
import time
import logging
from utils.oauth_helper import JiraOAuthHelper
from urllib.parse import urlparse, parse_qs


def handle_oauth_callback():
    """Handle OAuth callback and extract authorization code."""
    query_params = st.query_params
    
    if 'code' in query_params and 'state' in query_params:
        authorization_code = query_params['code']
        state = query_params['state']
        
        # Verify state parameter
        if 'jira_oauth_state' in st.session_state and st.session_state.jira_oauth_state == state:
            try:
                oauth_helper = JiraOAuthHelper()
                
                # Exchange code for tokens
                token_data = oauth_helper.exchange_code_for_tokens(authorization_code)
                
                # Get accessible resources to find cloud ID
                resources = oauth_helper.get_accessible_resources(token_data['access_token'])
                
                if resources:
                    # Find the Jira site
                    jira_site = None
                    for resource in resources:
                        if 'jira' in resource.get('scopes', []):
                            jira_site = resource
                            break
                    
                    if jira_site:
                        # Store tokens and cloud ID
                        oauth_helper.store_tokens_in_session(token_data)
                        st.session_state.jira_cloud_id = jira_site['id']
                        st.session_state.jira_site_url = jira_site['url']
                        st.session_state.jira_site_name = jira_site['name']
                        st.session_state.jira_oauth_authenticated = True
                        st.session_state.jira_auth_popup_actioned = True
                        
                        # Clear the state
                        if 'jira_oauth_state' in st.session_state:
                            del st.session_state.jira_oauth_state
                        
                        st.success(f"✅ Successfully authenticated with Jira site: {jira_site['name']}")
                        logging.info(f"OAuth authentication successful for site: {jira_site['name']}")
                        
                        # Clear query parameters by rerunning
                        st.query_params.clear()
                        time.sleep(1)
                        st.rerun()
                        
                    else:
                        st.error("❌ No Jira site found in accessible resources")
                        logging.error("No Jira site found in accessible resources")
                else:
                    st.error("❌ No accessible resources found")
                    logging.error("No accessible resources found")
                    
            except Exception as e:
                st.error(f"❌ OAuth authentication failed: {str(e)}")
                logging.error(f"OAuth authentication failed: {str(e)}")
                
        else:
            st.error("❌ Invalid state parameter. Possible CSRF attack.")
            logging.error("Invalid state parameter in OAuth callback")
    
    elif 'error' in query_params:
        error = query_params.get('error', 'Unknown error')
        error_description = query_params.get('error_description', '')
        st.error(f"❌ OAuth error: {error} - {error_description}")
        logging.error(f"OAuth error: {error} - {error_description}")


def jira_oauth_auth(jira, JIRA_ENDPOINT):
    """
    Handle Jira OAuth 2.0 authentication flow.
    """
    # Initialize session state flags
    if "jira_oauth_authenticated" not in st.session_state:
        st.session_state.jira_oauth_authenticated = False
    if "jira_auth_popup_actioned" not in st.session_state:
        st.session_state.jira_auth_popup_actioned = False
    if "jira_cloud_id" not in st.session_state:
        st.session_state.jira_cloud_id = None
    
    # Check for OAuth callback first
    handle_oauth_callback()
    
    # If already authenticated, set up the Jira client
    if st.session_state.jira_oauth_authenticated and st.session_state.jira_cloud_id:
        token_data = st.session_state.get('jira_oauth_tokens')
        if token_data:
            jira.set_oauth_tokens(token_data, st.session_state.jira_cloud_id)
            st.session_state.logged_in_user = st.session_state.get('jira_site_name', 'OAuth User')
            return
    
    # Show authentication UI if not authenticated
    if not st.session_state.jira_auth_popup_actioned:
        st.markdown("""
        <style>
            .custom-title {
                font-size: 32px;
                color: white;
                background-color: red;
                padding: 10px 20px;
                border-radius: 10px;
                text-align: center;
            }
            .auth-container {
                background-color: #f0f2f6;
                padding: 20px;
                border-radius: 10px;
                margin: 20px 0;
            }
            .oauth-info {
                background-color: #e8f4fd;
                padding: 15px;
                border-radius: 8px;
                border-left: 4px solid #1f77b4;
                margin: 10px 0;
            }
        </style>
        """, unsafe_allow_html=True)
        
        st.markdown('<div class="custom-title">🔐 Jira Authentication Required</div>', unsafe_allow_html=True)
        
        with st.container():
            st.markdown('<div class="auth-container">', unsafe_allow_html=True)
            
            st.markdown("### OAuth 2.0 Authentication")
            st.markdown("""
            <div class="oauth-info">
            <strong>🔒 Secure OAuth 2.0 Flow</strong><br>
            We use OAuth 2.0 for secure authentication with your Jira instance. 
            This method is more secure than basic authentication and doesn't require 
            sharing your password.
            </div>
            """, unsafe_allow_html=True)
            
            st.markdown("**Steps:**")
            st.markdown("""
            1. Click the "Authenticate with Jira" button below
            2. You'll be redirected to Atlassian's secure login page
            3. Grant permissions to the sdlc-test application
            4. You'll be redirected back to this application
            """)
            
            col1, col2, col3 = st.columns([1, 2, 1])
            
            with col2:
                if st.button("🚀 Authenticate with Jira", type="primary", use_container_width=True):
                    try:
                        oauth_helper = JiraOAuthHelper()
                        
                        # Generate state for security
                        state = oauth_helper.generate_state()
                        st.session_state.jira_oauth_state = state
                        
                        # Generate authorization URL
                        auth_url = oauth_helper.get_authorization_url(state)
                        
                        st.markdown(f"""
                        <div style="text-align: center; margin: 20px 0;">
                            <p>🔄 Redirecting to Atlassian for authentication...</p>
                            <p><a href="{auth_url}" target="_self" style="
                                background-color: #0052CC;
                                color: white;
                                padding: 10px 20px;
                                text-decoration: none;
                                border-radius: 5px;
                                font-weight: bold;
                            ">Click here if not redirected automatically</a></p>
                        </div>
                        """, unsafe_allow_html=True)
                        
                        # JavaScript redirect
                        st.markdown(f"""
                        <script>
                        window.location.href = "{auth_url}";
                        </script>
                        """, unsafe_allow_html=True)
                        
                        logging.info("Redirecting user to OAuth authorization URL")
                        
                    except Exception as e:
                        st.error(f"❌ Failed to initiate OAuth flow: {str(e)}")
                        logging.error(f"Failed to initiate OAuth flow: {str(e)}")
            
            # Option for basic auth fallback (for development/testing)
            with st.expander("🔧 Advanced: Basic Auth (Not Recommended)", expanded=False):
                st.warning("⚠️ Basic authentication is less secure and may not work with all Jira Cloud instances.")
                
                user = st.text_input("Jira Username/Email", key="basic_auth_user")
                password = st.text_input("API Token/Password", type="password", key="basic_auth_password")
                
                if st.button("Use Basic Auth", key="basic_auth_submit"):
                    if user and password:
                        jira.set_credentials(user, password)
                        try:
                            if jira.authenticate_user():
                                st.session_state.jira_session = jira.login()
                                st.session_state.logged_in_user = user.strip()
                                st.session_state.jira_user_authenticated = True
                                st.session_state.jira_auth_popup_actioned = True
                                st.success("✅ Basic authentication successful!")
                                time.sleep(1)
                                st.rerun()
                            else:
                                st.error("❌ Invalid credentials.")
                        except Exception as e:
                            st.error(f"Authentication failed: {str(e)}")
                    else:
                        st.warning("Please enter both username and password.")
            
            st.markdown('</div>', unsafe_allow_html=True)


def check_oauth_authentication():
    """Check if user is authenticated via OAuth."""
    return (st.session_state.get('jira_oauth_authenticated', False) and 
            st.session_state.get('jira_cloud_id') is not None)


def logout_oauth():
    """Logout from OAuth session."""
    oauth_helper = JiraOAuthHelper()
    oauth_helper.clear_tokens_from_session()
    st.session_state.jira_auth_popup_actioned = False
    logging.info("User logged out from OAuth session")
    st.rerun()
