import streamlit as st
import time
import logging
from utils.oauth_helper import <PERSON>ra<PERSON><PERSON>Helper
from urllib.parse import urlparse, parse_qs


def handle_oauth_callback():
    """Handle OAuth callback and extract authorization code."""
    query_params = st.query_params

    # Only process if we have both code and state, and haven't processed this callback yet
    if ('code' in query_params and 'state' in query_params and
        not st.session_state.get('oauth_callback_processed', False)):

        authorization_code = query_params['code']
        state = query_params['state']

        # Mark as processed to prevent multiple processing
        st.session_state.oauth_callback_processed = True

        # Verify state parameter
        stored_state = st.session_state.get('jira_oauth_state')
        if stored_state and stored_state == state:
            try:
                oauth_helper = JiraOAuthHelper()

                # Exchange code for tokens
                token_data = oauth_helper.exchange_code_for_tokens(authorization_code)

                # Get accessible resources to find cloud ID
                resources = oauth_helper.get_accessible_resources(token_data['access_token'])

                if resources:
                    # Find the Jira site
                    jira_site = None
                    for resource in resources:
                        if 'jira' in resource.get('scopes', []):
                            jira_site = resource
                            break

                    if jira_site:
                        # Store tokens and cloud ID
                        oauth_helper.store_tokens_in_session(token_data)
                        st.session_state.jira_cloud_id = jira_site['id']
                        st.session_state.jira_site_url = jira_site['url']
                        st.session_state.jira_site_name = jira_site['name']
                        st.session_state.jira_oauth_authenticated = True
                        st.session_state.jira_auth_popup_actioned = True
                        st.session_state.logged_in_user = jira_site['name']

                        # Clear the state and callback processing flag
                        if 'jira_oauth_state' in st.session_state:
                            del st.session_state.jira_oauth_state

                        st.success(f"✅ Successfully authenticated with Jira site: {jira_site['name']}")
                        logging.info(f"OAuth authentication successful for site: {jira_site['name']}")

                        # Clear query parameters and redirect
                        st.query_params.clear()
                        time.sleep(1)
                        st.rerun()

                    else:
                        st.error("❌ No Jira site found in accessible resources")
                        logging.error("No Jira site found in accessible resources")
                else:
                    st.error("❌ No accessible resources found")
                    logging.error("No accessible resources found")

            except Exception as e:
                st.error(f"❌ OAuth authentication failed: {str(e)}")
                logging.error(f"OAuth authentication failed: {str(e)}")

        else:
            # Only log error if we actually have a stored state to compare
            if stored_state:
                st.error("❌ Invalid state parameter. Possible CSRF attack.")
                logging.error("Invalid state parameter in OAuth callback")
            else:
                logging.warning("No stored state found for OAuth callback")

    elif 'error' in query_params and not st.session_state.get('oauth_error_processed', False):
        st.session_state.oauth_error_processed = True
        error = query_params.get('error', 'Unknown error')
        error_description = query_params.get('error_description', '')
        st.error(f"❌ OAuth error: {error} - {error_description}")
        logging.error(f"OAuth error: {error} - {error_description}")


# Removed complex OAuth UI - keeping it simple with just callback handling
