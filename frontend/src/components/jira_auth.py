import streamlit as st
from components.dialogs import jira_auth_confirm
from components.oauth_auth import jira_oauth_auth, check_oauth_authentication
import os


def jira_auth(jira, JIRA_ENDPOINT):
    """
    Handle Jira authentication - OAuth 2.0 preferred, basic auth fallback.
    """
    # Check if OA<PERSON> is configured
    oauth_configured = all([
        os.getenv("JIRA_OAUTH_CLIENT_ID"),
        os.getenv("JIRA_OAUTH_CLIENT_SECRET"),
        os.getenv("JIRA_OAUTH_REDIRECT_URI")
    ])

    if oauth_configured:
        # Use OAuth 2.0 authentication
        jira_oauth_auth(jira, JIRA_ENDPOINT)
    else:
        # Fallback to basic auth
        if not st.session_state.jira_auth_popup_actioned:
            st.markdown(
                """
            <style>
            div[aria-label="dialog"] > button[aria-label="Close"] {
                            display: none;
                        }
            </style>
            """,
                unsafe_allow_html=True,
            )
            jira_auth_confirm(jira, <PERSON><PERSON><PERSON>_ENDPOINT)