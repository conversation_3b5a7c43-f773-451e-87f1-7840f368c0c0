import streamlit as st
from components.dialogs import jira_auth_confirm
from components.oauth_auth import handle_oauth_callback


def jira_auth(jira, JIRA_ENDPOINT):
    """
    Handle Jira authentication - check for OAuth callback first, then show dialog.
    """
    # Handle OAuth callback if present
    handle_oauth_callback()

    # Set up OAuth tokens if authenticated
    if st.session_state.get('jira_oauth_authenticated', False) and st.session_state.get('jira_cloud_id'):
        token_data = st.session_state.get('jira_oauth_tokens')
        if token_data:
            jira.set_oauth_tokens(token_data, st.session_state.jira_cloud_id)

    # Show authentication dialog if not authenticated
    if not st.session_state.get('jira_auth_popup_actioned', False):
        st.markdown(
            """
        <style>
        div[aria-label="dialog"] > button[aria-label="Close"] {
                        display: none;
                    }
        </style>
        """,
            unsafe_allow_html=True,
        )
        jira_auth_confirm(jira, JIRA_ENDPOINT)