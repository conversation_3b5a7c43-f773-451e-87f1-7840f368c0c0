import os
import requests
import secrets
import urllib.parse
import streamlit as st
import logging
from typing import Dict, Optional, Tu<PERSON>
from datetime import datetime, timedelta
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class JiraOAuthHelper:
    """
    Helper class for handling Jira OAuth 2.0 authentication flow.
    Supports authorization code flow with PKCE and token refresh.
    """
    
    def __init__(self):
        """Initialize OAuth helper with configuration from environment variables."""
        self.client_id = os.getenv("JIRA_OAUTH_CLIENT_ID")
        self.client_secret = os.getenv("JIRA_OAUTH_CLIENT_SECRET")
        self.redirect_uri = os.getenv("JIRA_OAUTH_REDIRECT_URI")
        self.scopes = os.getenv("JIRA_OAUTH_SCOPES")
        self.audience = os.getenv("JIRA_OAUTH_AUDIENCE")
        self.auth_url = os.getenv("JIRA_OAUTH_AUTH_URL")
        self.token_url = os.getenv("JIRA_OAUTH_TOKEN_URL")
        self.resource_url = os.getenv("JIRA_OAUTH_RESOURCE_URL")
        
        # Validate required configuration
        if not all([self.client_id, self.client_secret, self.redirect_uri, self.scopes]):
            raise ValueError("Missing required OAuth configuration. Check your .env file.")
    
    def generate_state(self) -> str:
        """Generate a secure random state parameter for OAuth flow."""
        return secrets.token_urlsafe(32)
    
    def get_authorization_url(self, state: str) -> str:
        """
        Generate the authorization URL for OAuth 2.0 flow.

        Args:
            state: Security state parameter

        Returns:
            Complete authorization URL
        """
        # Ensure scopes are properly formatted
        scopes = self.scopes.strip()

        params = {
            'audience': self.audience,
            'client_id': self.client_id,
            'scope': scopes,
            'redirect_uri': self.redirect_uri,
            'state': state,
            'response_type': 'code',
            'prompt': 'consent'
        }

        auth_url = f"{self.auth_url}?{urllib.parse.urlencode(params)}"
        logging.info(f"Generated OAuth URL with scopes: {scopes}")
        return auth_url
    
    def exchange_code_for_tokens(self, authorization_code: str) -> Dict:
        """
        Exchange authorization code for access and refresh tokens.
        
        Args:
            authorization_code: The authorization code from callback
            
        Returns:
            Dictionary containing tokens and metadata
        """
        payload = {
            'grant_type': 'authorization_code',
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'code': authorization_code,
            'redirect_uri': self.redirect_uri
        }
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        try:
            response = requests.post(self.token_url, json=payload, headers=headers, verify=False)
            response.raise_for_status()
            
            token_data = response.json()
            
            # Add expiration timestamp
            if 'expires_in' in token_data:
                expires_at = datetime.now() + timedelta(seconds=token_data['expires_in'])
                token_data['expires_at'] = expires_at.isoformat()
            
            logging.info("Successfully exchanged authorization code for tokens")
            return token_data
            
        except requests.exceptions.RequestException as e:
            logging.error(f"Failed to exchange authorization code: {str(e)}")
            raise Exception(f"Token exchange failed: {str(e)}")
    
    def refresh_access_token(self, refresh_token: str) -> Dict:
        """
        Refresh the access token using refresh token.
        
        Args:
            refresh_token: The refresh token
            
        Returns:
            Dictionary containing new tokens
        """
        payload = {
            'grant_type': 'refresh_token',
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'refresh_token': refresh_token
        }
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        try:
            response = requests.post(self.token_url, json=payload, headers=headers, verify=False)
            response.raise_for_status()
            
            token_data = response.json()
            
            # Add expiration timestamp
            if 'expires_in' in token_data:
                expires_at = datetime.now() + timedelta(seconds=token_data['expires_in'])
                token_data['expires_at'] = expires_at.isoformat()
            
            logging.info("Successfully refreshed access token")
            return token_data
            
        except requests.exceptions.RequestException as e:
            logging.error(f"Failed to refresh token: {str(e)}")
            raise Exception(f"Token refresh failed: {str(e)}")
    
    def get_accessible_resources(self, access_token: str) -> Dict:
        """
        Get list of accessible Atlassian resources (sites).
        
        Args:
            access_token: Valid access token
            
        Returns:
            List of accessible resources
        """
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Accept': 'application/json'
        }
        
        try:
            response = requests.get(self.resource_url, headers=headers, verify=False)
            response.raise_for_status()
            
            resources = response.json()
            logging.info(f"Retrieved {len(resources)} accessible resources")
            return resources
            
        except requests.exceptions.RequestException as e:
            logging.error(f"Failed to get accessible resources: {str(e)}")
            raise Exception(f"Failed to get resources: {str(e)}")
    
    def is_token_expired(self, token_data: Dict) -> bool:
        """
        Check if the access token is expired.
        
        Args:
            token_data: Token data dictionary
            
        Returns:
            True if token is expired, False otherwise
        """
        if 'expires_at' not in token_data:
            return True
        
        try:
            expires_at = datetime.fromisoformat(token_data['expires_at'])
            # Add 5 minute buffer
            return datetime.now() >= (expires_at - timedelta(minutes=5))
        except (ValueError, TypeError):
            return True
    
    def get_valid_access_token(self, token_data: Dict) -> Tuple[str, Dict]:
        """
        Get a valid access token, refreshing if necessary.
        
        Args:
            token_data: Current token data
            
        Returns:
            Tuple of (access_token, updated_token_data)
        """
        if not self.is_token_expired(token_data):
            return token_data['access_token'], token_data
        
        if 'refresh_token' not in token_data:
            raise Exception("Token expired and no refresh token available")
        
        # Refresh the token
        new_token_data = self.refresh_access_token(token_data['refresh_token'])
        
        # Merge with existing data, preserving refresh token if not returned
        if 'refresh_token' not in new_token_data and 'refresh_token' in token_data:
            new_token_data['refresh_token'] = token_data['refresh_token']
        
        return new_token_data['access_token'], new_token_data
    
    def store_tokens_in_session(self, token_data: Dict) -> None:
        """
        Store token data in Streamlit session state.
        
        Args:
            token_data: Token data to store
        """
        st.session_state.jira_oauth_tokens = token_data
        st.session_state.jira_oauth_authenticated = True
        logging.info("Stored OAuth tokens in session state")
    
    def get_tokens_from_session(self) -> Optional[Dict]:
        """
        Retrieve token data from Streamlit session state.
        
        Returns:
            Token data or None if not found
        """
        return st.session_state.get('jira_oauth_tokens')
    
    def clear_tokens_from_session(self) -> None:
        """Clear OAuth tokens from session state."""
        if 'jira_oauth_tokens' in st.session_state:
            del st.session_state.jira_oauth_tokens
        if 'jira_oauth_authenticated' in st.session_state:
            del st.session_state.jira_oauth_authenticated
        if 'jira_oauth_state' in st.session_state:
            del st.session_state.jira_oauth_state
        if 'jira_cloud_id' in st.session_state:
            del st.session_state.jira_cloud_id
        logging.info("Cleared OAuth tokens from session state")
