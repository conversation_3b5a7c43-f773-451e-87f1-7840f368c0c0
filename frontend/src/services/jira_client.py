import requests
import logging
import json
from requests.auth import HTTPBasicAuth
import base64
import streamlit as st
import os
from utils.oauth_helper import JiraOAuthHelper


class JiraClient:
    def __init__(self, base_url: str):
        """
        Initialize Jira client with base URL and OAuth support
        """
        self.base_url = base_url
        self.user = None
        self.password = None
        self.basic_auth = None
        self.headers = {}
        self.oauth_helper = JiraOAuthHelper()
        self.cloud_id = None
        self.use_oauth = True  # Default to OAuth, fallback to basic auth if needed
        
        
        
    def set_credentials(self, user: str, password: str):
        """Set basic auth credentials (fallback method)"""
        self.user = user
        self.password = password
        self.use_oauth = False
        # Encode credentials for Basic Auth
        self.basic_auth = base64.b64encode(f"{user}:{password}".encode()).decode()
        # Request Headers
        self.headers = {
            "Authorization": f"Basic {self.basic_auth}",
            "X-Atlassian-Token": "nocheck",
            "Content-Type": "application/json"
        }

    def set_oauth_tokens(self, token_data: dict, cloud_id: str):
        """Set OAuth tokens for authentication"""
        self.use_oauth = True
        self.cloud_id = cloud_id
        access_token, updated_tokens = self.oauth_helper.get_valid_access_token(token_data)

        # Update session state with refreshed tokens if they changed
        if updated_tokens != token_data:
            self.oauth_helper.store_tokens_in_session(updated_tokens)

        # Set headers for OAuth
        self.headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json",
            "Content-Type": "application/json"
        }

    def get_oauth_api_url(self, endpoint: str) -> str:
        """Construct OAuth API URL for Jira Cloud"""
        if not self.cloud_id:
            raise ValueError("Cloud ID not set. OAuth authentication required.")
        return f"https://api.atlassian.com/ex/jira/{self.cloud_id}{endpoint}"

    def get_api_url(self, endpoint: str) -> str:
        """Get the appropriate API URL based on auth method"""
        if self.use_oauth:
            return self.get_oauth_api_url(endpoint)
        else:
            return f"{self.base_url}{endpoint}"
        
    def authenticate_user(self) -> bool:
        """
        Authenticate Jira user using OAuth or basic auth.
        """
        try:
            if self.use_oauth:
                # For OAuth, we need to refresh tokens if needed
                token_data = self.oauth_helper.get_tokens_from_session()
                if not token_data:
                    st.warning("OAuth tokens not found. Please authenticate.")
                    return False

                # Refresh headers with valid token
                self.set_oauth_tokens(token_data, self.cloud_id)

            url = self.get_api_url("/rest/api/2/myself")
            response = requests.get(url, headers=self.headers, verify=False)

            if response.status_code == 200:
                user = response.json()
                logging.info(f"Authenticated Jira user: {user['displayName']}")
                st.success(f"Authenticated Jira user: **{user['displayName']}**")
                return True
            elif response.status_code == 401:
                if self.use_oauth:
                    logging.warning("OAuth authentication failed: Token may be invalid")
                    st.warning("OAuth authentication failed. Please re-authenticate.")
                else:
                    logging.warning("Authentication failed: Invalid username or password")
                    st.warning("Authentication failed: Invalid username or password")
                return False
            else:
                logging.error(f"Authentication failed: {response.status_code} - {response.text}")
                st.warning(f"Authentication failed: {response.text}")
                return False

        except Exception as e:
            logging.error(f"Authentication error: {str(e)}")
            st.error(f"Authentication error: {str(e)}")
            return False

    def get_accessible_issues(self, issues_types: list[str]) -> list[str]:
        """
        Fetch all jira ids that are accessible to the user
        """
        try:
            # Ensure we have valid authentication
            if self.use_oauth:
                token_data = self.oauth_helper.get_tokens_from_session()
                if token_data:
                    self.set_oauth_tokens(token_data, self.cloud_id)

            issue_type_query = ", ".join([f'"{type}"' for type in issues_types])
            jql = f"issuetype in ({issue_type_query}) order by updated DESC"

            url = self.get_api_url("/rest/api/2/search")
            start_at = 0
            all_issue_keys = []
            max_results = st.session_state.jira_ids_max_results

            while True:
                params = {
                    "jql": jql,
                    "fields": "key,issuetype",
                    "startAt": start_at,
                    "maxResults": max_results
                }
                response = requests.get(url, headers=self.headers, params=params, verify=False)
                if response.status_code == 200:
                    data = response.json()
                    issues = data.get("issues", [])
                    issue_keys = [issue["key"] for issue in issues]
                    all_issue_keys.extend(issue_keys)

                    if start_at + max_results >= data.get("total", 0):
                        break
                    else:
                        start_at += max_results
                else:
                    logging.error(f"Failed to fetch issues: {response.status_code} - {response.text}")
                    break

            logging.info(f"Total filtered issues fetched: {len(all_issue_keys)}")
            return all_issue_keys

        except requests.RequestException:
            logging.exception("Error occurred while fetching filtered issue.")
            return []

        
        

    def get_jira_issue(self):
        """Prompt user for Jira issue ID."""
        return input("Enter Jira Issue ID (e.g., AIHQE-2): ")

    def fetch_issue_details(self, jira_id):
        """Fetch issue details from Jira."""
        try:
            # Ensure we have valid authentication
            if self.use_oauth:
                token_data = self.oauth_helper.get_tokens_from_session()
                if token_data:
                    self.set_oauth_tokens(token_data, self.cloud_id)

            issue_url = self.get_api_url(f"/rest/api/2/issue/{jira_id}")
            print("issue_url: ", issue_url)
            response = requests.get(issue_url, headers=self.headers, verify=False)
            print("response: ", response.status_code)

            if response.status_code == 200:
                print("response: ", response.status_code)
                issue_data = response.json()
                project_name = issue_data["fields"]["project"]["name"]
                description = issue_data["fields"].get("description", "No Description Available")
                summary = issue_data["fields"]["summary"]
                st.write(f"🔹 Project: {project_name}")
                st.write(f"🔹 Summary: {summary}")
                st.write(f"🔹 Description: {description}")
                print("tet")
                return issue_data["key"], summary, description
            elif response.status_code == 401:
                print("Authentication failed: Invalid credentials or missing permissions.")
            else:
                print(f"Request failed ({response.status_code}): {response.text}")

        except Exception as e:
            logging.error(f"Error fetching issue details: {str(e)}")
            print(f"Error fetching issue details: {str(e)}")

        return None, None, None

    def update_jira_issue(self, headers, jira_id, agent_title, agent_description):
        """Update the Jira issue with the refined title and description."""
        try:
            # Use OAuth URL if available
            if self.use_oauth:
                issue_url = self.get_api_url(f"/rest/api/2/issue/{jira_id}")
            else:
                issue_url = f"{self.base_url}/rest/api/2/issue/{jira_id}"

            payload = {
                "fields": {
                    "summary": agent_title,
                    "description": agent_description
                }
            }

            response = requests.put(issue_url, headers=headers, data=json.dumps(payload), verify=False)
            if response.status_code == 204:
                browse_url = f"{self.base_url}browse/{jira_id}" if not self.use_oauth else f"https://aiordinate.atlassian.net/browse/{jira_id}"
                st.write(f"\n🔹Jira issue: {jira_id} updated successfully! {browse_url}")
            else:
                print(f"Failed to update issue ({response.status_code}): {response.text}")

        except Exception as e:
            logging.error(f"Error updating Jira issue: {str(e)}")
            print(f"Error updating Jira issue: {str(e)}")


    def login(self):
        """
        Authenticate with Jira - OAuth preferred, basic auth fallback.
        """
        if self.use_oauth:
            # For OAuth, we don't need a traditional login - tokens are handled separately
            token_data = self.oauth_helper.get_tokens_from_session()
            if token_data and self.cloud_id:
                self.set_oauth_tokens(token_data, self.cloud_id)
                return self.headers
            else:
                logging.warning("OAuth tokens not available for login")
                return None
        else:
            # Basic auth session login (fallback)
            url = f'{self.base_url}/rest/auth/1/session'
            auth_data = {
                'username': self.user,
                'password': self.password
            }

            response = requests.post(url, json=auth_data, verify=False)

            if response.status_code == 200:
                # Store session info and cookies
                self.session_info = response.json()['session']
                self.cookies = {'JSESSIONID': self.session_info['value']}
                logging.info("Session created:", self.session_info)
                return self.headers
            else:
                logging.info("Failed to create session:", response.text)
                return None

       