#!/usr/bin/env python3
"""
Test the complete OAuth flow to ensure it works correctly.
"""

import sys
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv('frontend/.env')

# Add the frontend src directory to the path
sys.path.append('frontend/src')

def test_oauth_url_generation():
    """Test OAuth URL generation."""
    print("🔧 Testing OAuth URL Generation...")
    
    try:
        from utils.oauth_helper import JiraOAuthHelper
        
        oauth_helper = JiraOAuthHelper()
        state = oauth_helper.generate_state()
        auth_url = oauth_helper.get_authorization_url(state)
        
        print(f"✅ State generated: {state[:20]}...")
        print(f"✅ Auth URL generated: {auth_url[:80]}...")
        
        # Verify URL contains required parameters
        required_params = ['client_id', 'redirect_uri', 'state', 'response_type', 'scope']
        for param in required_params:
            if param in auth_url:
                print(f"✅ URL contains {param}")
            else:
                print(f"❌ URL missing {param}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ OAuth URL generation failed: {str(e)}")
        return False

def test_dialog_integration():
    """Test that the dialog can generate OAuth URLs."""
    print("\n🔧 Testing Dialog Integration...")
    
    try:
        # Simulate what happens in the dialog
        from utils.oauth_helper import JiraOAuthHelper
        
        oauth_helper = JiraOAuthHelper()
        state = oauth_helper.generate_state()
        auth_url = oauth_helper.get_authorization_url(state)
        
        # Check if URL is properly formatted for HTML
        if 'https://auth.atlassian.com/authorize' in auth_url:
            print("✅ OAuth URL properly formatted for redirect")
            print(f"✅ Full URL: {auth_url}")
            return True
        else:
            print("❌ OAuth URL not properly formatted")
            return False
            
    except Exception as e:
        print(f"❌ Dialog integration test failed: {str(e)}")
        return False

def main():
    """Run OAuth flow tests."""
    print("🚀 Testing OAuth Flow Implementation...\n")
    
    tests = [
        ("OAuth URL Generation", test_oauth_url_generation),
        ("Dialog Integration", test_dialog_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))
    
    print("\n" + "="*50)
    print("📊 OAUTH FLOW TEST RESULTS")
    print("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Tests passed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("🎉 OAuth flow is working correctly!")
        print("\n📋 To test the complete flow:")
        print("1. Run: streamlit run frontend/src/app.py")
        print("2. Click 'Authenticate with Jira'")
        print("3. You should see a clickable link to authenticate")
        print("4. Click the link to go to Atlassian OAuth")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
