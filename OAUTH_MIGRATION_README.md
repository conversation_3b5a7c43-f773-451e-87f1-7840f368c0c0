# Jira OAuth 2.0 Migration Guide

This document describes the migration from basic authentication to OAuth 2.0 for Jira integration in the SDLC Agents application.

## 🔄 Migration Overview

The application has been successfully migrated from basic authentication to OAuth 2.0 for enhanced security and compliance with Atlassian's authentication standards.

### Key Changes

1. **Frontend Authentication**: Replaced username/password dialog with OAuth 2.0 flow
2. **Backend Integration**: Updated all Jira API calls to use Bearer tokens
3. **Token Management**: Implemented automatic token refresh and secure storage
4. **Fallback Support**: Maintained basic auth as fallback for development

## 🔧 Configuration

### Environment Variables

The following OAuth 2.0 configuration has been added to both `frontend/.env` and `backend/.env`:

```env
# Jira OAuth 2.0 Configuration
JIRA_OAUTH_CLIENT_ID=gpghE6Ro7JE3rccYFK8EmKur2llgGLje
JIRA_OAUTH_CLIENT_SECRET=ATOA29j6ucJzeUDNAZ4B9gtxRec-AHwkk1UQH9qvL6ClXc3zA-vxgcqXxaH3ivVVfOmcB375504B
JIRA_OAUTH_REDIRECT_URI=http://localhost:8501/oauth2callback
JIRA_OAUTH_SCOPES=read:jira-work manage:jira-project manage:jira-configuration read:jira-user write:jira-work manage:jira-webhook manage:jira-data-provider offline_access
JIRA_OAUTH_AUDIENCE=api.atlassian.com
JIRA_OAUTH_AUTH_URL=https://auth.atlassian.com/authorize
JIRA_OAUTH_TOKEN_URL=https://auth.atlassian.com/oauth/token
JIRA_OAUTH_RESOURCE_URL=https://api.atlassian.com/oauth/token/accessible-resources
```

### Jira App Configuration

The OAuth app "sdlc-test" is configured with:
- **Distribution**: Private
- **Callback URL**: `http://localhost:8501/oauth2callback`
- **Scopes**: All required Jira permissions for the application

## 🚀 How to Use

### 1. Starting the Application

```bash
cd frontend/src
streamlit run app.py
```

### 2. Authentication Flow

1. **Launch App**: Open the Streamlit application
2. **OAuth Prompt**: Click "🚀 Authenticate with Jira" button
3. **Atlassian Login**: You'll be redirected to Atlassian's secure login page
4. **Grant Permissions**: Authorize the "sdlc-test" application
5. **Automatic Redirect**: You'll be redirected back to the application
6. **Ready to Use**: Start using Jira features with OAuth authentication

### 3. Features Available

- ✅ **Secure Authentication**: OAuth 2.0 with automatic token refresh
- ✅ **Issue Access**: Browse and select Jira issues
- ✅ **Content Generation**: Use AI helpers (RAS, TCG) with OAuth
- ✅ **Issue Updates**: Push generated content back to Jira
- ✅ **Session Management**: Tokens stored securely in session state

## 🔒 Security Features

### Token Management
- **Automatic Refresh**: Tokens are refreshed automatically before expiration
- **Secure Storage**: Tokens stored in Streamlit session state (memory only)
- **Expiration Handling**: 5-minute buffer before token expiration
- **State Validation**: CSRF protection with secure state parameters

### API Security
- **Bearer Tokens**: All API calls use OAuth Bearer tokens
- **Cloud ID Validation**: Proper cloud ID resolution for multi-tenant support
- **Scope Enforcement**: Only requested scopes are granted and used

## 🛠️ Technical Implementation

### Frontend Changes

1. **OAuth Helper** (`utils/oauth_helper.py`):
   - Authorization URL generation
   - Token exchange and refresh
   - Resource discovery
   - Session management

2. **Authentication Component** (`components/oauth_auth.py`):
   - OAuth flow UI
   - Callback handling
   - Token storage
   - Fallback to basic auth

3. **Jira Client** (`services/jira_client.py`):
   - OAuth token support
   - Dynamic API URL construction
   - Automatic token refresh

### Backend Changes

1. **Jira Client** (`backend/common/jira/jira_client.py`):
   - OAuth token authentication
   - Bearer token headers
   - Cloud API URL construction

2. **Request Processor** (`backend/common/jira/jira_request_processor.py`):
   - OAuth-aware client initialization
   - Token-based authentication

3. **Push Functions** (`backend/PushtoJira/__init__.py`):
   - OAuth token parameter handling
   - Authentication method detection

## 🧪 Testing

Run the test suite to verify the implementation:

```bash
python3 test_oauth.py
```

Expected output:
```
🎯 Tests passed: 4/4
🎉 All tests passed! OAuth implementation is ready.
```

## 🔄 Fallback to Basic Auth

If OAuth is not configured or fails, the application automatically falls back to basic authentication:

1. **Missing OAuth Config**: If environment variables are missing
2. **OAuth Failure**: If OAuth flow encounters errors
3. **Development Mode**: For local development and testing

Access the basic auth option through the "Advanced: Basic Auth" expander in the authentication dialog.

## 📋 Troubleshooting

### Common Issues

1. **"OAuth tokens not found"**
   - Solution: Re-authenticate using the OAuth flow

2. **"Cloud ID not set"**
   - Solution: Complete the OAuth flow to get cloud ID

3. **"Token expired"**
   - Solution: Tokens should refresh automatically; re-authenticate if needed

4. **"No accessible resources"**
   - Solution: Verify app permissions and user access to Jira site

### Debug Information

Enable debug logging by setting:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 🎯 Benefits of OAuth 2.0

1. **Enhanced Security**: No password storage or transmission
2. **Compliance**: Meets Atlassian's security requirements
3. **User Experience**: Single sign-on with Atlassian account
4. **Token Management**: Automatic refresh and expiration handling
5. **Scope Control**: Granular permission management
6. **Audit Trail**: Better tracking of API usage

## 📞 Support

For issues or questions regarding the OAuth implementation:

1. Check the troubleshooting section above
2. Run the test suite to verify configuration
3. Review Atlassian's OAuth 2.0 documentation
4. Check application logs for detailed error messages

---

**Migration Status**: ✅ Complete
**OAuth App**: sdlc-test (Private Distribution)
**Callback URL**: http://localhost:8501/oauth2callback
**Scopes**: Full Jira access as specified in requirements
